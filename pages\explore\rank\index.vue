<template>
	<view class="page-content">
		<t-navbar nav-style="custom" :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 200})` }" :placeholder="false">
			<view v-if="scrollTop > 200" class="nav-title truncate">信息贡献排行</view>
		</t-navbar>
		<view class="bg"></view>
		<view class="content">
			<view class="top">
				<view class="rank-title">信息贡献排行</view>
				<view class="rank-star">
					<image v-for="item in 3" :key="item" src="/static/images/star.svg" mode="widthFix"></image>
				</view>
			</view>
			<view class="other">
				<view class="block" v-for="item in list" :key="item.rank">
					<template v-if="item.user_info">
						<view class="flex">
							<image v-if="item.rank_value < 4" class="rank-icon" :src="rankIcon(item.rank)" mode="widthFix"></image>
							<view v-else class="rank">{{ item.rank }}</view>
							<view class="user-info" @click="vk.navigateTo(`/pages/user/detail?id=${item.user_info._id}`)">
								<image class="avatar" :src="item.user_info.avatar" mode="aspectFill"></image>
								<text class="nickname">{{ item.user_info.nickname }}</text>
							</view>
						</view>
						<view class="count">
							<text>{{ item.count }}</text>
							<text>次</text>
						</view>
					</template>
					<template v-else>
						<view class="flex">
							<view class="rank">{{ item.rank }}</view>
							<view class="user-info">
								<image class="avatar" :src="defaultAvatar" mode="aspectFill"></image>
								<text class="nickname">虚拟待位</text>
							</view>
						</view>
						<view class="count">-</view>
					</template>
				</view>
			</view>
		</view>
		<t-bottom v-if="myself.count!==0">
			<view class="myself">
				<view class="flex">
					<image v-if="myself.rank_value < 4" class="rank-icon" :src="rankIcon(myself.rank)" mode="widthFix"></image>
					<view v-else class="rank">{{ myself.rank }}</view>
					<view class="user-info">
						<image class="avatar" :src="userInfo.avatar" mode="aspectFill"></image>
						<text class="nickname">{{ userInfo.nickname }}</text>
					</view>
				</view>
				<view class="count">
					<text>{{ myself.count }}</text>
					<text>次</text>
				</view>
			</view>
		</t-bottom>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				userInfo: vk.getVuex("$user.userInfo"),
				scrollTop: 0,
				defaultAvatar: "https://cdn.cometennis.cn/images/default-head.png",

				list: [],
				myself: {},
				myRankIndex: null,
			};
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop;
		},
		onShareAppMessage(e) {
			return {
				title: `网球信息贡献排行`,
			};
		},
		onLoad() {
			this.getRank();
		},
		methods: {
			rankIcon(rank) {
				return `/static/images/medal${Number(rank)}.svg`;
			},
			async getRank() {
				let data = await vk.callFunction({
					url: "client/public/info/rank/pub/infoEdit",
					title: "请求中...",
					data: {},
				});

				let list = [];
				data.rows.forEach((item, index) => {
					let obj = {
						rank_value: index + 1,
						rank: String(index + 1),
						user_info: item.user_info,
						count: item.count,
					};
					list.push(obj);
				});

				let length = 20 - list.length;
				if (list.length < 20) {
					for (let i = 0; i < length; i++) {
						list.push({
							rank: String(list.length + 1),
							user_info: null,
							count: null,
						});
					}
				}

				this.list = list;

				// 我的次数
				let count = await vk.callFunction({
					url: "client/public/info/rank/pub/oneself",
					title: "请求中...",
				});
				let myRank = data.rows.findIndex((item) => this.userInfo._id == item._id) + 1;
				this.myRankIndex = myRank;
				this.myself = {
					rank_value: myRank,
					rank: String(myRank),
					count: count.num,
				};
			},
		},
	};
</script>

<style lang="scss" scoped>
	.page-content {
		background-color: #f8f8f8;
	}

	.nav-title {
		width: 300rpx;
		position: absolute;
		left: 0;
		right: 0;
		margin: 0 auto;
		text-align: center;
		color: #333;
	}

	.bg {
		position: absolute;
		top: 0;
		width: 100%;
		height: 50vh;
		background: linear-gradient(to bottom, $primary-color, transparent);
	}

	.content {
		position: relative;
		z-index: 1;
		padding: 30rpx 30rpx 0;

		.top {
			width: 100%;
			height: 400rpx;
			padding-bottom: 80rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: flex-end;

			.rank-title {
				font-size: 60rpx;
				color: #fff;
				font-family: usic;
				text-shadow: 0 0 12rpx rgba(0, 0, 0, 0.3);
			}

			.rank-star {
				display: flex;
				align-items: center;
				margin-top: 30rpx;

				image {
					width: 48rpx;
				}
			}
		}

		.other {
			background-color: #fff;
			border-radius: 34rpx 34rpx 0 0;

			.block {
				padding: 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 2rpx solid #f8f8f8;
			}
		}
	}

	.myself {
		width: 100%;
		padding: 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.count {
		flex-shrink: 0;
		font-size: 28rpx;

		text {
			&:first-child {
				font-size: 40rpx;
				font-weight: bold;
				margin-right: 10rpx;
			}
		}
	}

	.rank {
		flex-shrink: 0;
		width: 48rpx;
		text-align: center;
		font-size: 32rpx;
		font-family: usic;
		margin-right: 20rpx;
	}

	.rank-icon {
		flex-shrink: 0;
		width: 48rpx;
		margin-right: 20rpx;
	}

	.user-info {
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.avatar {
			width: 68rpx;
			height: 68rpx;
			border-radius: 50%;
			flex-shrink: 0;
			background-color: #fff;
		}

		.nickname {
			font-size: 28rpx;
			margin-left: 16rpx;
			@include multiline(1);
		}
	}
</style>