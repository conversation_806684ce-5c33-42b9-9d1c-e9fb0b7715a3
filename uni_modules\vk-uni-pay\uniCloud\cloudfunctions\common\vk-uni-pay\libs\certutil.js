"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var t=e(require("fs")),r=e(require("crypto")),n=e(require("net")),i=e(require("util"));function s(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function a(e,t){return e(t={exports:{}},t.exports),t.exports}var o=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,u=Math.ceil,c=Math.floor,l="[BigNumber Error] ",f=l+"Number primitive has more than 15 significant digits: ",h=1e14,g=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],p=1e9;function y(e){var t=0|e;return e>0||e===t?t:t-1}function E(e){for(var t,r,n=1,i=e.length,s=e[0]+"";n<i;){for(r=14-(t=e[n++]+"").length;r--;t="0"+t);s+=t}for(i=s.length;48===s.charCodeAt(--i););return s.slice(0,i+1||1)}function d(e,t){var r,n,i=e.c,s=t.c,a=e.s,o=t.s,u=e.e,c=t.e;if(!a||!o)return null;if(r=i&&!i[0],n=s&&!s[0],r||n)return r?n?0:-o:a;if(a!=o)return a;if(r=a<0,n=u==c,!i||!s)return n?0:!i^r?1:-1;if(!n)return u>c^r?1:-1;for(o=(u=i.length)<(c=s.length)?u:c,a=0;a<o;a++)if(i[a]!=s[a])return i[a]>s[a]^r?1:-1;return u==c?0:u>c^r?1:-1}function S(e,t,r,n){if(e<t||e>r||e!==c(e))throw Error(l+(n||"Argument")+("number"==typeof e?e<t||e>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(e))}function m(e){var t=e.c.length-1;return y(e.e/14)==t&&e.c[t]%2!=0}function N(e,t){return(e.length>1?e.charAt(0)+"."+e.slice(1):e)+(t<0?"e":"e+")+t}function b(e,t,r){var n,i;if(t<0){for(i=r+".";++t;i+=r);e=i+e}else if(++t>(n=e.length)){for(i=r,t-=n;--t;i+=r);e+=i}else t<n&&(e=e.slice(0,t)+"."+e.slice(t));return e}var I=function e(t){var r,n,i,s,a,I,A,v,w,T=V.prototype={constructor:V,toString:null,valueOf:null},C=new V(1),R=20,U=4,O=-7,P=21,_=-1e7,B=1e7,D=!1,K=1,L=0,M={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},x="0123456789abcdefghijklmnopqrstuvwxyz",k=!0;function V(e,t){var r,s,a,u,l,h,g,p,y=this;if(!(y instanceof V))return new V(e,t);if(null==t){if(e&&!0===e._isBigNumber)return y.s=e.s,void(!e.c||e.e>B?y.c=y.e=null:e.e<_?y.c=[y.e=0]:(y.e=e.e,y.c=e.c.slice()));if((h="number"==typeof e)&&0*e==0){if(y.s=1/e<0?(e=-e,-1):1,e===~~e){for(u=0,l=e;l>=10;l/=10,u++);return void(u>B?y.c=y.e=null:(y.e=u,y.c=[e]))}p=String(e)}else{if(!o.test(p=String(e)))return i(y,p,h);y.s=45==p.charCodeAt(0)?(p=p.slice(1),-1):1}(u=p.indexOf("."))>-1&&(p=p.replace(".","")),(l=p.search(/e/i))>0?(u<0&&(u=l),u+=+p.slice(l+1),p=p.substring(0,l)):u<0&&(u=p.length)}else{if(S(t,2,x.length,"Base"),10==t&&k)return W(y=new V(e),R+y.e+1,U);if(p=String(e),h="number"==typeof e){if(0*e!=0)return i(y,p,h,t);if(y.s=1/e<0?(p=p.slice(1),-1):1,V.DEBUG&&p.replace(/^0\.0*|\./,"").length>15)throw Error(f+e)}else y.s=45===p.charCodeAt(0)?(p=p.slice(1),-1):1;for(r=x.slice(0,t),u=l=0,g=p.length;l<g;l++)if(r.indexOf(s=p.charAt(l))<0){if("."==s){if(l>u){u=g;continue}}else if(!a&&(p==p.toUpperCase()&&(p=p.toLowerCase())||p==p.toLowerCase()&&(p=p.toUpperCase()))){a=!0,l=-1,u=0;continue}return i(y,String(e),h,t)}h=!1,(u=(p=n(p,t,10,y.s)).indexOf("."))>-1?p=p.replace(".",""):u=p.length}for(l=0;48===p.charCodeAt(l);l++);for(g=p.length;48===p.charCodeAt(--g););if(p=p.slice(l,++g)){if(g-=l,h&&V.DEBUG&&g>15&&(e>9007199254740991||e!==c(e)))throw Error(f+y.s*e);if((u=u-l-1)>B)y.c=y.e=null;else if(u<_)y.c=[y.e=0];else{if(y.e=u,y.c=[],l=(u+1)%14,u<0&&(l+=14),l<g){for(l&&y.c.push(+p.slice(0,l)),g-=14;l<g;)y.c.push(+p.slice(l,l+=14));l=14-(p=p.slice(l)).length}else l-=g;for(;l--;p+="0");y.c.push(+p)}}else y.c=[y.e=0]}function G(e,t,r,n){var i,s,a,o,u;if(null==r?r=U:S(r,0,8),!e.c)return e.toString();if(i=e.c[0],a=e.e,null==t)u=E(e.c),u=1==n||2==n&&(a<=O||a>=P)?N(u,a):b(u,a,"0");else if(s=(e=W(new V(e),t,r)).e,o=(u=E(e.c)).length,1==n||2==n&&(t<=s||s<=O)){for(;o<t;u+="0",o++);u=N(u,s)}else if(t-=a,u=b(u,s,"0"),s+1>o){if(--t>0)for(u+=".";t--;u+="0");}else if((t+=s-o)>0)for(s+1==o&&(u+=".");t--;u+="0");return e.s<0&&i?"-"+u:u}function j(e,t){for(var r,n=1,i=new V(e[0]);n<e.length;n++){if(!(r=new V(e[n])).s){i=r;break}t.call(i,r)&&(i=r)}return i}function F(e,t,r){for(var n=1,i=t.length;!t[--i];t.pop());for(i=t[0];i>=10;i/=10,n++);return(r=n+14*r-1)>B?e.c=e.e=null:r<_?e.c=[e.e=0]:(e.e=r,e.c=t),e}function W(e,t,r,n){var i,s,a,o,l,f,p,y=e.c,E=g;if(y){e:{for(i=1,o=y[0];o>=10;o/=10,i++);if((s=t-i)<0)s+=14,a=t,p=(l=y[f=0])/E[i-a-1]%10|0;else if((f=u((s+1)/14))>=y.length){if(!n)break e;for(;y.length<=f;y.push(0));l=p=0,i=1,a=(s%=14)-14+1}else{for(l=o=y[f],i=1;o>=10;o/=10,i++);p=(a=(s%=14)-14+i)<0?0:l/E[i-a-1]%10|0}if(n=n||t<0||null!=y[f+1]||(a<0?l:l%E[i-a-1]),n=r<4?(p||n)&&(0==r||r==(e.s<0?3:2)):p>5||5==p&&(4==r||n||6==r&&(s>0?a>0?l/E[i-a]:0:y[f-1])%10&1||r==(e.s<0?8:7)),t<1||!y[0])return y.length=0,n?(t-=e.e+1,y[0]=E[(14-t%14)%14],e.e=-t||0):y[0]=e.e=0,e;if(0==s?(y.length=f,o=1,f--):(y.length=f+1,o=E[14-s],y[f]=a>0?c(l/E[i-a]%E[a])*o:0),n)for(;;){if(0==f){for(s=1,a=y[0];a>=10;a/=10,s++);for(a=y[0]+=o,o=1;a>=10;a/=10,o++);s!=o&&(e.e++,y[0]==h&&(y[0]=1));break}if(y[f]+=o,y[f]!=h)break;y[f--]=0,o=1}for(s=y.length;0===y[--s];y.pop());}e.e>B?e.c=e.e=null:e.e<_&&(e.c=[e.e=0])}return e}function $(e){var t,r=e.e;return null===r?e.toString():(t=E(e.c),t=r<=O||r>=P?N(t,r):b(t,r,"0"),e.s<0?"-"+t:t)}return V.clone=e,V.ROUND_UP=0,V.ROUND_DOWN=1,V.ROUND_CEIL=2,V.ROUND_FLOOR=3,V.ROUND_HALF_UP=4,V.ROUND_HALF_DOWN=5,V.ROUND_HALF_EVEN=6,V.ROUND_HALF_CEIL=7,V.ROUND_HALF_FLOOR=8,V.EUCLID=9,V.config=V.set=function(e){var t,r;if(null!=e){if("object"!=typeof e)throw Error(l+"Object expected: "+e);if(e.hasOwnProperty(t="DECIMAL_PLACES")&&(S(r=e[t],0,p,t),R=r),e.hasOwnProperty(t="ROUNDING_MODE")&&(S(r=e[t],0,8,t),U=r),e.hasOwnProperty(t="EXPONENTIAL_AT")&&((r=e[t])&&r.pop?(S(r[0],-p,0,t),S(r[1],0,p,t),O=r[0],P=r[1]):(S(r,-p,p,t),O=-(P=r<0?-r:r))),e.hasOwnProperty(t="RANGE"))if((r=e[t])&&r.pop)S(r[0],-p,-1,t),S(r[1],1,p,t),_=r[0],B=r[1];else{if(S(r,-p,p,t),!r)throw Error(l+t+" cannot be zero: "+r);_=-(B=r<0?-r:r)}if(e.hasOwnProperty(t="CRYPTO")){if((r=e[t])!==!!r)throw Error(l+t+" not true or false: "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw D=!r,Error(l+"crypto unavailable");D=r}else D=r}if(e.hasOwnProperty(t="MODULO_MODE")&&(S(r=e[t],0,9,t),K=r),e.hasOwnProperty(t="POW_PRECISION")&&(S(r=e[t],0,p,t),L=r),e.hasOwnProperty(t="FORMAT")){if("object"!=typeof(r=e[t]))throw Error(l+t+" not an object: "+r);M=r}if(e.hasOwnProperty(t="ALPHABET")){if("string"!=typeof(r=e[t])||/^.?$|[+\-.\s]|(.).*\1/.test(r))throw Error(l+t+" invalid: "+r);k="0123456789"==r.slice(0,10),x=r}}return{DECIMAL_PLACES:R,ROUNDING_MODE:U,EXPONENTIAL_AT:[O,P],RANGE:[_,B],CRYPTO:D,MODULO_MODE:K,POW_PRECISION:L,FORMAT:M,ALPHABET:x}},V.isBigNumber=function(e){if(!e||!0!==e._isBigNumber)return!1;if(!V.DEBUG)return!0;var t,r,n=e.c,i=e.e,s=e.s;e:if("[object Array]"=={}.toString.call(n)){if((1===s||-1===s)&&i>=-p&&i<=p&&i===c(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break e}if((t=(i+1)%14)<1&&(t+=14),String(n[0]).length==t){for(t=0;t<n.length;t++)if((r=n[t])<0||r>=h||r!==c(r))break e;if(0!==r)return!0}}}else if(null===n&&null===i&&(null===s||1===s||-1===s))return!0;throw Error(l+"Invalid BigNumber: "+e)},V.maximum=V.max=function(){return j(arguments,T.lt)},V.minimum=V.min=function(){return j(arguments,T.gt)},V.random=(s=9007199254740992*Math.random()&2097151?function(){return c(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(e){var t,r,n,i,a,o=0,f=[],h=new V(C);if(null==e?e=R:S(e,0,p),i=u(e/14),D)if(crypto.getRandomValues){for(t=crypto.getRandomValues(new Uint32Array(i*=2));o<i;)(a=131072*t[o]+(t[o+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),t[o]=r[0],t[o+1]=r[1]):(f.push(a%1e14),o+=2);o=i/2}else{if(!crypto.randomBytes)throw D=!1,Error(l+"crypto unavailable");for(t=crypto.randomBytes(i*=7);o<i;)(a=281474976710656*(31&t[o])+1099511627776*t[o+1]+4294967296*t[o+2]+16777216*t[o+3]+(t[o+4]<<16)+(t[o+5]<<8)+t[o+6])>=9e15?crypto.randomBytes(7).copy(t,o):(f.push(a%1e14),o+=7);o=i/7}if(!D)for(;o<i;)(a=s())<9e15&&(f[o++]=a%1e14);for(e%=14,(i=f[--o])&&e&&(a=g[14-e],f[o]=c(i/a)*a);0===f[o];f.pop(),o--);if(o<0)f=[n=0];else{for(n=-1;0===f[0];f.splice(0,1),n-=14);for(o=1,a=f[0];a>=10;a/=10,o++);o<14&&(n-=14-o)}return h.e=n,h.c=f,h}),V.sum=function(){for(var e=1,t=arguments,r=new V(t[0]);e<t.length;)r=r.plus(t[e++]);return r},n=function(){function e(e,t,r,n){for(var i,s,a=[0],o=0,u=e.length;o<u;){for(s=a.length;s--;a[s]*=t);for(a[0]+=n.indexOf(e.charAt(o++)),i=0;i<a.length;i++)a[i]>r-1&&(null==a[i+1]&&(a[i+1]=0),a[i+1]+=a[i]/r|0,a[i]%=r)}return a.reverse()}return function(t,n,i,s,a){var o,u,c,l,f,h,g,p,y=t.indexOf("."),d=R,S=U;for(y>=0&&(l=L,L=0,t=t.replace(".",""),h=(p=new V(n)).pow(t.length-y),L=l,p.c=e(b(E(h.c),h.e,"0"),10,i,"0123456789"),p.e=p.c.length),c=l=(g=e(t,n,i,a?(o=x,"0123456789"):(o="0123456789",x))).length;0==g[--l];g.pop());if(!g[0])return o.charAt(0);if(y<0?--c:(h.c=g,h.e=c,h.s=s,g=(h=r(h,p,d,S,i)).c,f=h.r,c=h.e),y=g[u=c+d+1],l=i/2,f=f||u<0||null!=g[u+1],f=S<4?(null!=y||f)&&(0==S||S==(h.s<0?3:2)):y>l||y==l&&(4==S||f||6==S&&1&g[u-1]||S==(h.s<0?8:7)),u<1||!g[0])t=f?b(o.charAt(1),-d,o.charAt(0)):o.charAt(0);else{if(g.length=u,f)for(--i;++g[--u]>i;)g[u]=0,u||(++c,g=[1].concat(g));for(l=g.length;!g[--l];);for(y=0,t="";y<=l;t+=o.charAt(g[y++]));t=b(t,c,o.charAt(0))}return t}}(),r=function(){function e(e,t,r){var n,i,s,a,o=0,u=e.length,c=t%1e7,l=t/1e7|0;for(e=e.slice();u--;)o=((i=c*(s=e[u]%1e7)+(n=l*s+(a=e[u]/1e7|0)*c)%1e7*1e7+o)/r|0)+(n/1e7|0)+l*a,e[u]=i%r;return o&&(e=[o].concat(e)),e}function t(e,t,r,n){var i,s;if(r!=n)s=r>n?1:-1;else for(i=s=0;i<r;i++)if(e[i]!=t[i]){s=e[i]>t[i]?1:-1;break}return s}function r(e,t,r,n){for(var i=0;r--;)e[r]-=i,i=e[r]<t[r]?1:0,e[r]=i*n+e[r]-t[r];for(;!e[0]&&e.length>1;e.splice(0,1));}return function(n,i,s,a,o){var u,l,f,g,p,E,d,S,m,N,b,I,A,v,w,T,C,R=n.s==i.s?1:-1,U=n.c,O=i.c;if(!(U&&U[0]&&O&&O[0]))return new V(n.s&&i.s&&(U?!O||U[0]!=O[0]:O)?U&&0==U[0]||!O?0*R:R/0:NaN);for(m=(S=new V(R)).c=[],R=s+(l=n.e-i.e)+1,o||(o=h,l=y(n.e/14)-y(i.e/14),R=R/14|0),f=0;O[f]==(U[f]||0);f++);if(O[f]>(U[f]||0)&&l--,R<0)m.push(1),g=!0;else{for(v=U.length,T=O.length,f=0,R+=2,(p=c(o/(O[0]+1)))>1&&(O=e(O,p,o),U=e(U,p,o),T=O.length,v=U.length),A=T,b=(N=U.slice(0,T)).length;b<T;N[b++]=0);C=O.slice(),C=[0].concat(C),w=O[0],O[1]>=o/2&&w++;do{if(p=0,(u=t(O,N,T,b))<0){if(I=N[0],T!=b&&(I=I*o+(N[1]||0)),(p=c(I/w))>1)for(p>=o&&(p=o-1),d=(E=e(O,p,o)).length,b=N.length;1==t(E,N,d,b);)p--,r(E,T<d?C:O,d,o),d=E.length,u=1;else 0==p&&(u=p=1),d=(E=O.slice()).length;if(d<b&&(E=[0].concat(E)),r(N,E,b,o),b=N.length,-1==u)for(;t(O,N,T,b)<1;)p++,r(N,T<b?C:O,b,o),b=N.length}else 0===u&&(p++,N=[0]);m[f++]=p,N[0]?N[b++]=U[A]||0:(N=[U[A]],b=1)}while((A++<v||null!=N[0])&&R--);g=null!=N[0],m[0]||m.splice(0,1)}if(o==h){for(f=1,R=m[0];R>=10;R/=10,f++);W(S,s+(S.e=f+14*l-1)+1,a,g)}else S.e=l,S.r=+g;return S}}(),a=/^(-?)0([xbo])(?=\w[\w.]*$)/i,I=/^([^.]+)\.$/,A=/^\.([^.]+)$/,v=/^-?(Infinity|NaN)$/,w=/^\s*\+(?=[\w.])|^\s+|\s+$/g,i=function(e,t,r,n){var i,s=r?t:t.replace(w,"");if(v.test(s))e.s=isNaN(s)?null:s<0?-1:1;else{if(!r&&(s=s.replace(a,(function(e,t,r){return i="x"==(r=r.toLowerCase())?16:"b"==r?2:8,n&&n!=i?e:t})),n&&(i=n,s=s.replace(I,"$1").replace(A,"0.$1")),t!=s))return new V(s,i);if(V.DEBUG)throw Error(l+"Not a"+(n?" base "+n:"")+" number: "+t);e.s=null}e.c=e.e=null},T.absoluteValue=T.abs=function(){var e=new V(this);return e.s<0&&(e.s=1),e},T.comparedTo=function(e,t){return d(this,new V(e,t))},T.decimalPlaces=T.dp=function(e,t){var r,n,i,s=this;if(null!=e)return S(e,0,p),null==t?t=U:S(t,0,8),W(new V(s),e+s.e+1,t);if(!(r=s.c))return null;if(n=14*((i=r.length-1)-y(this.e/14)),i=r[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},T.dividedBy=T.div=function(e,t){return r(this,new V(e,t),R,U)},T.dividedToIntegerBy=T.idiv=function(e,t){return r(this,new V(e,t),0,1)},T.exponentiatedBy=T.pow=function(e,t){var r,n,i,s,a,o,f,h,g=this;if((e=new V(e)).c&&!e.isInteger())throw Error(l+"Exponent not an integer: "+$(e));if(null!=t&&(t=new V(t)),a=e.e>14,!g.c||!g.c[0]||1==g.c[0]&&!g.e&&1==g.c.length||!e.c||!e.c[0])return h=new V(Math.pow(+$(g),a?e.s*(2-m(e)):+$(e))),t?h.mod(t):h;if(o=e.s<0,t){if(t.c?!t.c[0]:!t.s)return new V(NaN);(n=!o&&g.isInteger()&&t.isInteger())&&(g=g.mod(t))}else{if(e.e>9&&(g.e>0||g.e<-1||(0==g.e?g.c[0]>1||a&&g.c[1]>=24e7:g.c[0]<8e13||a&&g.c[0]<=9999975e7)))return s=g.s<0&&m(e)?-0:0,g.e>-1&&(s=1/s),new V(o?1/s:s);L&&(s=u(L/14+2))}for(a?(r=new V(.5),o&&(e.s=1),f=m(e)):f=(i=Math.abs(+$(e)))%2,h=new V(C);;){if(f){if(!(h=h.times(g)).c)break;s?h.c.length>s&&(h.c.length=s):n&&(h=h.mod(t))}if(i){if(0===(i=c(i/2)))break;f=i%2}else if(W(e=e.times(r),e.e+1,1),e.e>14)f=m(e);else{if(0===(i=+$(e)))break;f=i%2}g=g.times(g),s?g.c&&g.c.length>s&&(g.c.length=s):n&&(g=g.mod(t))}return n?h:(o&&(h=C.div(h)),t?h.mod(t):s?W(h,L,U,void 0):h)},T.integerValue=function(e){var t=new V(this);return null==e?e=U:S(e,0,8),W(t,t.e+1,e)},T.isEqualTo=T.eq=function(e,t){return 0===d(this,new V(e,t))},T.isFinite=function(){return!!this.c},T.isGreaterThan=T.gt=function(e,t){return d(this,new V(e,t))>0},T.isGreaterThanOrEqualTo=T.gte=function(e,t){return 1===(t=d(this,new V(e,t)))||0===t},T.isInteger=function(){return!!this.c&&y(this.e/14)>this.c.length-2},T.isLessThan=T.lt=function(e,t){return d(this,new V(e,t))<0},T.isLessThanOrEqualTo=T.lte=function(e,t){return-1===(t=d(this,new V(e,t)))||0===t},T.isNaN=function(){return!this.s},T.isNegative=function(){return this.s<0},T.isPositive=function(){return this.s>0},T.isZero=function(){return!!this.c&&0==this.c[0]},T.minus=function(e,t){var r,n,i,s,a=this,o=a.s;if(t=(e=new V(e,t)).s,!o||!t)return new V(NaN);if(o!=t)return e.s=-t,a.plus(e);var u=a.e/14,c=e.e/14,l=a.c,f=e.c;if(!u||!c){if(!l||!f)return l?(e.s=-t,e):new V(f?a:NaN);if(!l[0]||!f[0])return f[0]?(e.s=-t,e):new V(l[0]?a:3==U?-0:0)}if(u=y(u),c=y(c),l=l.slice(),o=u-c){for((s=o<0)?(o=-o,i=l):(c=u,i=f),i.reverse(),t=o;t--;i.push(0));i.reverse()}else for(n=(s=(o=l.length)<(t=f.length))?o:t,o=t=0;t<n;t++)if(l[t]!=f[t]){s=l[t]<f[t];break}if(s&&(i=l,l=f,f=i,e.s=-e.s),(t=(n=f.length)-(r=l.length))>0)for(;t--;l[r++]=0);for(t=h-1;n>o;){if(l[--n]<f[n]){for(r=n;r&&!l[--r];l[r]=t);--l[r],l[n]+=h}l[n]-=f[n]}for(;0==l[0];l.splice(0,1),--c);return l[0]?F(e,l,c):(e.s=3==U?-1:1,e.c=[e.e=0],e)},T.modulo=T.mod=function(e,t){var n,i,s=this;return e=new V(e,t),!s.c||!e.s||e.c&&!e.c[0]?new V(NaN):!e.c||s.c&&!s.c[0]?new V(s):(9==K?(i=e.s,e.s=1,n=r(s,e,0,3),e.s=i,n.s*=i):n=r(s,e,0,K),(e=s.minus(n.times(e))).c[0]||1!=K||(e.s=s.s),e)},T.multipliedBy=T.times=function(e,t){var r,n,i,s,a,o,u,c,l,f,g,p,E,d,S=this,m=S.c,N=(e=new V(e,t)).c;if(!(m&&N&&m[0]&&N[0]))return!S.s||!e.s||m&&!m[0]&&!N||N&&!N[0]&&!m?e.c=e.e=e.s=null:(e.s*=S.s,m&&N?(e.c=[0],e.e=0):e.c=e.e=null),e;for(n=y(S.e/14)+y(e.e/14),e.s*=S.s,(u=m.length)<(f=N.length)&&(E=m,m=N,N=E,i=u,u=f,f=i),i=u+f,E=[];i--;E.push(0));for(d=h,1e7,i=f;--i>=0;){for(r=0,g=N[i]%1e7,p=N[i]/1e7|0,s=i+(a=u);s>i;)r=((c=g*(c=m[--a]%1e7)+(o=p*c+(l=m[a]/1e7|0)*g)%1e7*1e7+E[s]+r)/d|0)+(o/1e7|0)+p*l,E[s--]=c%d;E[s]=r}return r?++n:E.splice(0,1),F(e,E,n)},T.negated=function(){var e=new V(this);return e.s=-e.s||null,e},T.plus=function(e,t){var r,n=this,i=n.s;if(t=(e=new V(e,t)).s,!i||!t)return new V(NaN);if(i!=t)return e.s=-t,n.minus(e);var s=n.e/14,a=e.e/14,o=n.c,u=e.c;if(!s||!a){if(!o||!u)return new V(i/0);if(!o[0]||!u[0])return u[0]?e:new V(o[0]?n:0*i)}if(s=y(s),a=y(a),o=o.slice(),i=s-a){for(i>0?(a=s,r=u):(i=-i,r=o),r.reverse();i--;r.push(0));r.reverse()}for((i=o.length)-(t=u.length)<0&&(r=u,u=o,o=r,t=i),i=0;t;)i=(o[--t]=o[t]+u[t]+i)/h|0,o[t]=h===o[t]?0:o[t]%h;return i&&(o=[i].concat(o),++a),F(e,o,a)},T.precision=T.sd=function(e,t){var r,n,i,s=this;if(null!=e&&e!==!!e)return S(e,1,p),null==t?t=U:S(t,0,8),W(new V(s),e,t);if(!(r=s.c))return null;if(n=14*(i=r.length-1)+1,i=r[i]){for(;i%10==0;i/=10,n--);for(i=r[0];i>=10;i/=10,n++);}return e&&s.e+1>n&&(n=s.e+1),n},T.shiftedBy=function(e){return S(e,-9007199254740991,9007199254740991),this.times("1e"+e)},T.squareRoot=T.sqrt=function(){var e,t,n,i,s,a=this,o=a.c,u=a.s,c=a.e,l=R+4,f=new V("0.5");if(1!==u||!o||!o[0])return new V(!u||u<0&&(!o||o[0])?NaN:o?a:1/0);if(0==(u=Math.sqrt(+$(a)))||u==1/0?(((t=E(o)).length+c)%2==0&&(t+="0"),u=Math.sqrt(+t),c=y((c+1)/2)-(c<0||c%2),n=new V(t=u==1/0?"5e"+c:(t=u.toExponential()).slice(0,t.indexOf("e")+1)+c)):n=new V(u+""),n.c[0])for((u=(c=n.e)+l)<3&&(u=0);;)if(s=n,n=f.times(s.plus(r(a,s,l,1))),E(s.c).slice(0,u)===(t=E(n.c)).slice(0,u)){if(n.e<c&&--u,"9999"!=(t=t.slice(u-3,u+1))&&(i||"4999"!=t)){+t&&(+t.slice(1)||"5"!=t.charAt(0))||(W(n,n.e+R+2,1),e=!n.times(n).eq(a));break}if(!i&&(W(s,s.e+R+2,0),s.times(s).eq(a))){n=s;break}l+=4,u+=4,i=1}return W(n,n.e+R+1,U,e)},T.toExponential=function(e,t){return null!=e&&(S(e,0,p),e++),G(this,e,t,1)},T.toFixed=function(e,t){return null!=e&&(S(e,0,p),e=e+this.e+1),G(this,e,t)},T.toFormat=function(e,t,r){var n,i=this;if(null==r)null!=e&&t&&"object"==typeof t?(r=t,t=null):e&&"object"==typeof e?(r=e,e=t=null):r=M;else if("object"!=typeof r)throw Error(l+"Argument not an object: "+r);if(n=i.toFixed(e,t),i.c){var s,a=n.split("."),o=+r.groupSize,u=+r.secondaryGroupSize,c=r.groupSeparator||"",f=a[0],h=a[1],g=i.s<0,p=g?f.slice(1):f,y=p.length;if(u&&(s=o,o=u,u=s,y-=s),o>0&&y>0){for(s=y%o||o,f=p.substr(0,s);s<y;s+=o)f+=c+p.substr(s,o);u>0&&(f+=c+p.slice(s)),g&&(f="-"+f)}n=h?f+(r.decimalSeparator||"")+((u=+r.fractionGroupSize)?h.replace(new RegExp("\\d{"+u+"}\\B","g"),"$&"+(r.fractionGroupSeparator||"")):h):f}return(r.prefix||"")+n+(r.suffix||"")},T.toFraction=function(e){var t,n,i,s,a,o,u,c,f,h,p,y,d=this,S=d.c;if(null!=e&&(!(u=new V(e)).isInteger()&&(u.c||1!==u.s)||u.lt(C)))throw Error(l+"Argument "+(u.isInteger()?"out of range: ":"not an integer: ")+$(u));if(!S)return new V(d);for(t=new V(C),f=n=new V(C),i=c=new V(C),y=E(S),a=t.e=y.length-d.e-1,t.c[0]=g[(o=a%14)<0?14+o:o],e=!e||u.comparedTo(t)>0?a>0?t:f:u,o=B,B=1/0,u=new V(y),c.c[0]=0;h=r(u,t,0,1),1!=(s=n.plus(h.times(i))).comparedTo(e);)n=i,i=s,f=c.plus(h.times(s=f)),c=s,t=u.minus(h.times(s=t)),u=s;return s=r(e.minus(n),i,0,1),c=c.plus(s.times(f)),n=n.plus(s.times(i)),c.s=f.s=d.s,p=r(f,i,a*=2,U).minus(d).abs().comparedTo(r(c,n,a,U).minus(d).abs())<1?[f,i]:[c,n],B=o,p},T.toNumber=function(){return+$(this)},T.toPrecision=function(e,t){return null!=e&&S(e,1,p),G(this,e,t,2)},T.toString=function(e){var t,r=this,i=r.s,s=r.e;return null===s?i?(t="Infinity",i<0&&(t="-"+t)):t="NaN":(null==e?t=s<=O||s>=P?N(E(r.c),s):b(E(r.c),s,"0"):10===e&&k?t=b(E((r=W(new V(r),R+s+1,U)).c),r.e,"0"):(S(e,2,x.length,"Base"),t=n(b(E(r.c),s,"0"),10,e,i,!0)),i<0&&r.c[0]&&(t="-"+t)),t},T.valueOf=T.toJSON=function(){return $(this)},T._isBigNumber=!0,T[Symbol.toStringTag]="BigNumber",T[Symbol.for("nodejs.util.inspect.custom")]=T.valueOf,null!=t&&V.set(t),V}(),A=Object.freeze({__proto__:null,BigNumber:I,default:I}),v=a((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.bytesFromIP=function(e){switch(n.isIP(e)){case 4:return Buffer.from(e.split(".").map(e=>parseInt(e,10)));case 6:const t=e.split(":"),r=Buffer.alloc(16);let n=0;""===t[t.length-1]&&(t[t.length-1]="0");for(let e=0;e<t.length;e++)""!==t[e]?(r.writeUInt16BE(parseInt(t[e],16),n),n+=2):e+1<t.length&&""!==t[e+1]&&(n=16-2*(t.length-e-1));return r;default:return null}},t.bytesToIP=function(e){switch(e.length){case 4:return[e[0],e[1],e[2],e[3]].join(".");case 16:const t=[];let r=-1,n=0,i=-1,s=0;for(let a=0;a<e.length;a+=2){const o=e[a]<<8|e[a+1];0===o?(n++,-1===r&&(r=t.length),n>s&&(s=n,i=r)):(r=-1,n=0),t.push(o.toString(16))}if(s>0){let e="";const r=t.slice(i+s);t.length=i,0===t.length&&(e+=":"),0===r.length&&(e+=":"),t.push(e,...r)}return t.join(":");default:return""}};const r=Object.create(null),i=/^[0-9.]+$/;function s(e,t){r[e]=t,r[t]=e}t.getOID=function(e){return i.test(e)&&""!==r[e]?e:null==r[e]?"":r[e]},t.getOIDName=function(e){return i.test(e)||""===r[e]?null==r[e]?e:r[e]:e},s("1.2.840.113549.1.1.1","rsaEncryption"),s("1.2.840.113549.1.1.4","md5WithRsaEncryption"),s("1.2.840.113549.1.1.5","sha1WithRsaEncryption"),s("1.2.840.113549.1.1.8","mgf1"),s("1.2.840.113549.1.1.10","RSASSA-PSS"),s("1.2.840.113549.1.1.11","sha256WithRsaEncryption"),s("1.2.840.113549.1.1.12","sha384WithRsaEncryption"),s("1.2.840.113549.1.1.13","sha512WithRsaEncryption"),s("1.2.840.10045.2.1","ecEncryption"),s("1.2.840.10045.4.1","ecdsaWithSha1"),s("1.2.840.10045.4.3.2","ecdsaWithSha256"),s("1.2.840.10045.4.3.3","ecdsaWithSha384"),s("1.2.840.10045.4.3.4","ecdsaWithSha512"),s("1.2.840.10040.4.3","dsaWithSha1"),s("2.16.84*********.4.3.2","dsaWithSha256"),s("1.3.14.3.2.7","desCBC"),s("1.3.14.3.2.26","sha1"),s("2.16.84*********.4.2.1","sha256"),s("2.16.84*********.4.2.2","sha384"),s("2.16.84*********.4.2.3","sha512"),s("1.2.840.113549.2.5","md5"),s("***********","X25519"),s("***********","X448"),s("***********","Ed25519"),s("***********","Ed448"),s("1.2.840.113549.1.7.1","data"),s("1.2.840.113549.1.7.2","signedData"),s("1.2.840.113549.1.7.3","envelopedData"),s("1.2.840.113549.1.7.4","signedAndEnvelopedData"),s("1.2.840.113549.1.7.5","digestedData"),s("1.2.840.113549.1.7.6","encryptedData"),s("1.2.840.113549.1.9.1","emailAddress"),s("1.2.840.113549.1.9.2","unstructuredName"),s("1.2.840.113549.1.9.3","contentType"),s("1.2.840.113549.1.9.4","messageDigest"),s("1.2.840.113549.1.9.5","signingTime"),s("1.2.840.113549.1.9.6","counterSignature"),s("1.2.840.113549.1.9.7","challengePassword"),s("1.2.840.113549.1.9.8","unstructuredAddress"),s("1.2.840.113549.1.9.14","extensionRequest"),s("1.2.840.113549.1.9.20","friendlyName"),s("1.2.840.113549.1.9.21","localKeyId"),s("1.2.840.113549.********","x509Certificate"),s("1.2.840.113549.*********.1","keyBag"),s("1.2.840.113549.*********.2","pkcs8ShroudedKeyBag"),s("1.2.840.113549.*********.3","certBag"),s("1.2.840.113549.*********.4","crlBag"),s("1.2.840.113549.*********.5","secretBag"),s("1.2.840.113549.*********.6","safeContentsBag"),s("1.2.840.113549.1.5.13","pkcs5PBES2"),s("1.2.840.113549.1.5.12","pkcs5PBKDF2"),s("1.2.840.113549.2.7","hmacWithSha1"),s("1.2.840.113549.2.9","hmacWithSha256"),s("1.2.840.113549.2.10","hmacWithSha384"),s("1.2.840.113549.2.11","hmacWithSha512"),s("1.2.840.113549.3.7","3desCBC"),s("2.16.84*********.4.1.2","aesCBC128"),s("2.16.84*********.4.1.42","aesCBC256"),s("*******","commonName"),s("*******","serialName"),s("*******","countryName"),s("*******","localityName"),s("*******","stateOrProvinceName"),s("********","organizationName"),s("2.5.4.11","organizationalUnitName"),s("2.5.4.15","businessCategory"),s("2.16.840.1.113730.1.1","nsCertType"),s("2.5.29.2","keyAttributes"),s("2.5.29.4","keyUsageRestriction"),s("2.5.29.6","subtreesConstraint"),s("2.5.29.9","subjectDirectoryAttributes"),s("2.5.29.14","subjectKeyIdentifier"),s("2.5.29.15","keyUsage"),s("2.5.29.16","privateKeyUsagePeriod"),s("2.5.29.17","subjectAltName"),s("2.5.29.18","issuerAltName"),s("2.5.29.19","basicConstraints"),s("2.5.29.20","cRLNumber"),s("2.5.29.21","cRLReason"),s("2.5.29.22","expirationDate"),s("2.5.29.23","instructionCode"),s("2.5.29.24","invalidityDate"),s("2.5.29.27","deltaCRLIndicator"),s("2.5.29.28","issuingDistributionPoint"),s("2.5.29.29","certificateIssuer"),s("2.5.29.30","nameConstraints"),s("2.5.29.31","cRLDistributionPoints"),s("2.5.29.32","certificatePolicies"),s("2.5.29.33","policyMappings"),s("2.5.29.35","authorityKeyIdentifier"),s("2.5.29.36","policyConstraints"),s("2.5.29.37","extKeyUsage"),s("2.5.29.46","freshestCRL"),s("2.5.29.54","inhibitAnyPolicy"),s("*******.4.1.311.********","jurisdictionST"),s("*******.4.1.311.********","jurisdictionC"),s("*******.4.1.11129.2.4.2","timestampList"),s("*******.*******.1","authorityInfoAccess"),s("*******.*******.1","serverAuth"),s("*******.*******.2","clientAuth"),s("*******.*******.3","codeSigning"),s("*******.*******.4","emailProtection"),s("*******.*******.8","timeStamping"),s("*******.********.1","authorityInfoAccessOcsp"),s("*******.********.2","authorityInfoAccessIssuers")}));s(v);v.bytesFromIP,v.bytesToIP,v.getOID,v.getOIDName;var w=a((function(e){!function(e){var t=function(e){var t,r=new Float64Array(16);if(e)for(t=0;t<e.length;t++)r[t]=e[t];return r},n=function(){throw new Error("no PRNG")},i=new Uint8Array(16),s=new Uint8Array(32);s[0]=9;var a=t(),o=t([1]),u=t([56129,1]),c=t([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),l=t([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),f=t([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),h=t([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),g=t([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function p(e,t,r,n){e[t]=r>>24&255,e[t+1]=r>>16&255,e[t+2]=r>>8&255,e[t+3]=255&r,e[t+4]=n>>24&255,e[t+5]=n>>16&255,e[t+6]=n>>8&255,e[t+7]=255&n}function y(e,t,r,n,i){var s,a=0;for(s=0;s<i;s++)a|=e[t+s]^r[n+s];return(1&a-1>>>8)-1}function E(e,t,r,n){return y(e,t,r,n,16)}function d(e,t,r,n){return y(e,t,r,n,32)}function S(e,t,r,n){!function(e,t,r,n){for(var i,s=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,u=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,c=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,l=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,f=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,g=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,p=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,y=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,E=255&r[16]|(255&r[17])<<8|(255&r[18])<<16|(255&r[19])<<24,d=255&r[20]|(255&r[21])<<8|(255&r[22])<<16|(255&r[23])<<24,S=255&r[24]|(255&r[25])<<8|(255&r[26])<<16|(255&r[27])<<24,m=255&r[28]|(255&r[29])<<8|(255&r[30])<<16|(255&r[31])<<24,N=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,b=s,I=a,A=o,v=u,w=c,T=l,C=f,R=h,U=g,O=p,P=y,_=E,B=d,D=S,K=m,L=N,M=0;M<20;M+=2)b^=(i=(B^=(i=(U^=(i=(w^=(i=b+B|0)<<7|i>>>25)+b|0)<<9|i>>>23)+w|0)<<13|i>>>19)+U|0)<<18|i>>>14,T^=(i=(I^=(i=(D^=(i=(O^=(i=T+I|0)<<7|i>>>25)+T|0)<<9|i>>>23)+O|0)<<13|i>>>19)+D|0)<<18|i>>>14,P^=(i=(C^=(i=(A^=(i=(K^=(i=P+C|0)<<7|i>>>25)+P|0)<<9|i>>>23)+K|0)<<13|i>>>19)+A|0)<<18|i>>>14,L^=(i=(_^=(i=(R^=(i=(v^=(i=L+_|0)<<7|i>>>25)+L|0)<<9|i>>>23)+v|0)<<13|i>>>19)+R|0)<<18|i>>>14,b^=(i=(v^=(i=(A^=(i=(I^=(i=b+v|0)<<7|i>>>25)+b|0)<<9|i>>>23)+I|0)<<13|i>>>19)+A|0)<<18|i>>>14,T^=(i=(w^=(i=(R^=(i=(C^=(i=T+w|0)<<7|i>>>25)+T|0)<<9|i>>>23)+C|0)<<13|i>>>19)+R|0)<<18|i>>>14,P^=(i=(O^=(i=(U^=(i=(_^=(i=P+O|0)<<7|i>>>25)+P|0)<<9|i>>>23)+_|0)<<13|i>>>19)+U|0)<<18|i>>>14,L^=(i=(K^=(i=(D^=(i=(B^=(i=L+K|0)<<7|i>>>25)+L|0)<<9|i>>>23)+B|0)<<13|i>>>19)+D|0)<<18|i>>>14;b=b+s|0,I=I+a|0,A=A+o|0,v=v+u|0,w=w+c|0,T=T+l|0,C=C+f|0,R=R+h|0,U=U+g|0,O=O+p|0,P=P+y|0,_=_+E|0,B=B+d|0,D=D+S|0,K=K+m|0,L=L+N|0,e[0]=b>>>0&255,e[1]=b>>>8&255,e[2]=b>>>16&255,e[3]=b>>>24&255,e[4]=I>>>0&255,e[5]=I>>>8&255,e[6]=I>>>16&255,e[7]=I>>>24&255,e[8]=A>>>0&255,e[9]=A>>>8&255,e[10]=A>>>16&255,e[11]=A>>>24&255,e[12]=v>>>0&255,e[13]=v>>>8&255,e[14]=v>>>16&255,e[15]=v>>>24&255,e[16]=w>>>0&255,e[17]=w>>>8&255,e[18]=w>>>16&255,e[19]=w>>>24&255,e[20]=T>>>0&255,e[21]=T>>>8&255,e[22]=T>>>16&255,e[23]=T>>>24&255,e[24]=C>>>0&255,e[25]=C>>>8&255,e[26]=C>>>16&255,e[27]=C>>>24&255,e[28]=R>>>0&255,e[29]=R>>>8&255,e[30]=R>>>16&255,e[31]=R>>>24&255,e[32]=U>>>0&255,e[33]=U>>>8&255,e[34]=U>>>16&255,e[35]=U>>>24&255,e[36]=O>>>0&255,e[37]=O>>>8&255,e[38]=O>>>16&255,e[39]=O>>>24&255,e[40]=P>>>0&255,e[41]=P>>>8&255,e[42]=P>>>16&255,e[43]=P>>>24&255,e[44]=_>>>0&255,e[45]=_>>>8&255,e[46]=_>>>16&255,e[47]=_>>>24&255,e[48]=B>>>0&255,e[49]=B>>>8&255,e[50]=B>>>16&255,e[51]=B>>>24&255,e[52]=D>>>0&255,e[53]=D>>>8&255,e[54]=D>>>16&255,e[55]=D>>>24&255,e[56]=K>>>0&255,e[57]=K>>>8&255,e[58]=K>>>16&255,e[59]=K>>>24&255,e[60]=L>>>0&255,e[61]=L>>>8&255,e[62]=L>>>16&255,e[63]=L>>>24&255}(e,t,r,n)}function m(e,t,r,n){!function(e,t,r,n){for(var i,s=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,a=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,o=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,u=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,c=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,l=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,f=255&t[0]|(255&t[1])<<8|(255&t[2])<<16|(255&t[3])<<24,h=255&t[4]|(255&t[5])<<8|(255&t[6])<<16|(255&t[7])<<24,g=255&t[8]|(255&t[9])<<8|(255&t[10])<<16|(255&t[11])<<24,p=255&t[12]|(255&t[13])<<8|(255&t[14])<<16|(255&t[15])<<24,y=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,E=255&r[16]|(255&r[17])<<8|(255&r[18])<<16|(255&r[19])<<24,d=255&r[20]|(255&r[21])<<8|(255&r[22])<<16|(255&r[23])<<24,S=255&r[24]|(255&r[25])<<8|(255&r[26])<<16|(255&r[27])<<24,m=255&r[28]|(255&r[29])<<8|(255&r[30])<<16|(255&r[31])<<24,N=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,b=0;b<20;b+=2)s^=(i=(d^=(i=(g^=(i=(c^=(i=s+d|0)<<7|i>>>25)+s|0)<<9|i>>>23)+c|0)<<13|i>>>19)+g|0)<<18|i>>>14,l^=(i=(a^=(i=(S^=(i=(p^=(i=l+a|0)<<7|i>>>25)+l|0)<<9|i>>>23)+p|0)<<13|i>>>19)+S|0)<<18|i>>>14,y^=(i=(f^=(i=(o^=(i=(m^=(i=y+f|0)<<7|i>>>25)+y|0)<<9|i>>>23)+m|0)<<13|i>>>19)+o|0)<<18|i>>>14,N^=(i=(E^=(i=(h^=(i=(u^=(i=N+E|0)<<7|i>>>25)+N|0)<<9|i>>>23)+u|0)<<13|i>>>19)+h|0)<<18|i>>>14,s^=(i=(u^=(i=(o^=(i=(a^=(i=s+u|0)<<7|i>>>25)+s|0)<<9|i>>>23)+a|0)<<13|i>>>19)+o|0)<<18|i>>>14,l^=(i=(c^=(i=(h^=(i=(f^=(i=l+c|0)<<7|i>>>25)+l|0)<<9|i>>>23)+f|0)<<13|i>>>19)+h|0)<<18|i>>>14,y^=(i=(p^=(i=(g^=(i=(E^=(i=y+p|0)<<7|i>>>25)+y|0)<<9|i>>>23)+E|0)<<13|i>>>19)+g|0)<<18|i>>>14,N^=(i=(m^=(i=(S^=(i=(d^=(i=N+m|0)<<7|i>>>25)+N|0)<<9|i>>>23)+d|0)<<13|i>>>19)+S|0)<<18|i>>>14;e[0]=s>>>0&255,e[1]=s>>>8&255,e[2]=s>>>16&255,e[3]=s>>>24&255,e[4]=l>>>0&255,e[5]=l>>>8&255,e[6]=l>>>16&255,e[7]=l>>>24&255,e[8]=y>>>0&255,e[9]=y>>>8&255,e[10]=y>>>16&255,e[11]=y>>>24&255,e[12]=N>>>0&255,e[13]=N>>>8&255,e[14]=N>>>16&255,e[15]=N>>>24&255,e[16]=f>>>0&255,e[17]=f>>>8&255,e[18]=f>>>16&255,e[19]=f>>>24&255,e[20]=h>>>0&255,e[21]=h>>>8&255,e[22]=h>>>16&255,e[23]=h>>>24&255,e[24]=g>>>0&255,e[25]=g>>>8&255,e[26]=g>>>16&255,e[27]=g>>>24&255,e[28]=p>>>0&255,e[29]=p>>>8&255,e[30]=p>>>16&255,e[31]=p>>>24&255}(e,t,r,n)}var N=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function b(e,t,r,n,i,s,a){var o,u,c=new Uint8Array(16),l=new Uint8Array(64);for(u=0;u<16;u++)c[u]=0;for(u=0;u<8;u++)c[u]=s[u];for(;i>=64;){for(S(l,c,a,N),u=0;u<64;u++)e[t+u]=r[n+u]^l[u];for(o=1,u=8;u<16;u++)o=o+(255&c[u])|0,c[u]=255&o,o>>>=8;i-=64,t+=64,n+=64}if(i>0)for(S(l,c,a,N),u=0;u<i;u++)e[t+u]=r[n+u]^l[u];return 0}function I(e,t,r,n,i){var s,a,o=new Uint8Array(16),u=new Uint8Array(64);for(a=0;a<16;a++)o[a]=0;for(a=0;a<8;a++)o[a]=n[a];for(;r>=64;){for(S(u,o,i,N),a=0;a<64;a++)e[t+a]=u[a];for(s=1,a=8;a<16;a++)s=s+(255&o[a])|0,o[a]=255&s,s>>>=8;r-=64,t+=64}if(r>0)for(S(u,o,i,N),a=0;a<r;a++)e[t+a]=u[a];return 0}function A(e,t,r,n,i){var s=new Uint8Array(32);m(s,n,i,N);for(var a=new Uint8Array(8),o=0;o<8;o++)a[o]=n[o+16];return I(e,t,r,a,s)}function v(e,t,r,n,i,s,a){var o=new Uint8Array(32);m(o,s,a,N);for(var u=new Uint8Array(8),c=0;c<8;c++)u[c]=s[c+16];return b(e,t,r,n,i,u,o)}var w=function(e){var t,r,n,i,s,a,o,u;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,t=255&e[0]|(255&e[1])<<8,this.r[0]=8191&t,r=255&e[2]|(255&e[3])<<8,this.r[1]=8191&(t>>>13|r<<3),n=255&e[4]|(255&e[5])<<8,this.r[2]=7939&(r>>>10|n<<6),i=255&e[6]|(255&e[7])<<8,this.r[3]=8191&(n>>>7|i<<9),s=255&e[8]|(255&e[9])<<8,this.r[4]=255&(i>>>4|s<<12),this.r[5]=s>>>1&8190,a=255&e[10]|(255&e[11])<<8,this.r[6]=8191&(s>>>14|a<<2),o=255&e[12]|(255&e[13])<<8,this.r[7]=8065&(a>>>11|o<<5),u=255&e[14]|(255&e[15])<<8,this.r[8]=8191&(o>>>8|u<<8),this.r[9]=u>>>5&127,this.pad[0]=255&e[16]|(255&e[17])<<8,this.pad[1]=255&e[18]|(255&e[19])<<8,this.pad[2]=255&e[20]|(255&e[21])<<8,this.pad[3]=255&e[22]|(255&e[23])<<8,this.pad[4]=255&e[24]|(255&e[25])<<8,this.pad[5]=255&e[26]|(255&e[27])<<8,this.pad[6]=255&e[28]|(255&e[29])<<8,this.pad[7]=255&e[30]|(255&e[31])<<8};function T(e,t,r,n,i,s){var a=new w(s);return a.update(r,n,i),a.finish(e,t),0}function C(e,t,r,n,i,s){var a=new Uint8Array(16);return T(a,0,r,n,i,s),E(e,t,a,0)}function R(e,t,r,n,i){var s;if(r<32)return-1;for(v(e,0,t,0,r,n,i),T(e,16,e,32,r-32,e),s=0;s<16;s++)e[s]=0;return 0}function U(e,t,r,n,i){var s,a=new Uint8Array(32);if(r<32)return-1;if(A(a,0,32,n,i),0!==C(t,16,t,32,r-32,a))return-1;for(v(e,0,t,0,r,n,i),s=0;s<32;s++)e[s]=0;return 0}function O(e,t){var r;for(r=0;r<16;r++)e[r]=0|t[r]}function P(e){var t,r,n=1;for(t=0;t<16;t++)r=e[t]+n+65535,n=Math.floor(r/65536),e[t]=r-65536*n;e[0]+=n-1+37*(n-1)}function _(e,t,r){for(var n,i=~(r-1),s=0;s<16;s++)n=i&(e[s]^t[s]),e[s]^=n,t[s]^=n}function B(e,r){var n,i,s,a=t(),o=t();for(n=0;n<16;n++)o[n]=r[n];for(P(o),P(o),P(o),i=0;i<2;i++){for(a[0]=o[0]-65517,n=1;n<15;n++)a[n]=o[n]-65535-(a[n-1]>>16&1),a[n-1]&=65535;a[15]=o[15]-32767-(a[14]>>16&1),s=a[15]>>16&1,a[14]&=65535,_(o,a,1-s)}for(n=0;n<16;n++)e[2*n]=255&o[n],e[2*n+1]=o[n]>>8}function D(e,t){var r=new Uint8Array(32),n=new Uint8Array(32);return B(r,e),B(n,t),d(r,0,n,0)}function K(e){var t=new Uint8Array(32);return B(t,e),1&t[0]}function L(e,t){var r;for(r=0;r<16;r++)e[r]=t[2*r]+(t[2*r+1]<<8);e[15]&=32767}function M(e,t,r){for(var n=0;n<16;n++)e[n]=t[n]+r[n]}function x(e,t,r){for(var n=0;n<16;n++)e[n]=t[n]-r[n]}function k(e,t,r){var n,i,s=0,a=0,o=0,u=0,c=0,l=0,f=0,h=0,g=0,p=0,y=0,E=0,d=0,S=0,m=0,N=0,b=0,I=0,A=0,v=0,w=0,T=0,C=0,R=0,U=0,O=0,P=0,_=0,B=0,D=0,K=0,L=r[0],M=r[1],x=r[2],k=r[3],V=r[4],G=r[5],j=r[6],F=r[7],W=r[8],$=r[9],Y=r[10],q=r[11],z=r[12],H=r[13],X=r[14],Q=r[15];s+=(n=t[0])*L,a+=n*M,o+=n*x,u+=n*k,c+=n*V,l+=n*G,f+=n*j,h+=n*F,g+=n*W,p+=n*$,y+=n*Y,E+=n*q,d+=n*z,S+=n*H,m+=n*X,N+=n*Q,a+=(n=t[1])*L,o+=n*M,u+=n*x,c+=n*k,l+=n*V,f+=n*G,h+=n*j,g+=n*F,p+=n*W,y+=n*$,E+=n*Y,d+=n*q,S+=n*z,m+=n*H,N+=n*X,b+=n*Q,o+=(n=t[2])*L,u+=n*M,c+=n*x,l+=n*k,f+=n*V,h+=n*G,g+=n*j,p+=n*F,y+=n*W,E+=n*$,d+=n*Y,S+=n*q,m+=n*z,N+=n*H,b+=n*X,I+=n*Q,u+=(n=t[3])*L,c+=n*M,l+=n*x,f+=n*k,h+=n*V,g+=n*G,p+=n*j,y+=n*F,E+=n*W,d+=n*$,S+=n*Y,m+=n*q,N+=n*z,b+=n*H,I+=n*X,A+=n*Q,c+=(n=t[4])*L,l+=n*M,f+=n*x,h+=n*k,g+=n*V,p+=n*G,y+=n*j,E+=n*F,d+=n*W,S+=n*$,m+=n*Y,N+=n*q,b+=n*z,I+=n*H,A+=n*X,v+=n*Q,l+=(n=t[5])*L,f+=n*M,h+=n*x,g+=n*k,p+=n*V,y+=n*G,E+=n*j,d+=n*F,S+=n*W,m+=n*$,N+=n*Y,b+=n*q,I+=n*z,A+=n*H,v+=n*X,w+=n*Q,f+=(n=t[6])*L,h+=n*M,g+=n*x,p+=n*k,y+=n*V,E+=n*G,d+=n*j,S+=n*F,m+=n*W,N+=n*$,b+=n*Y,I+=n*q,A+=n*z,v+=n*H,w+=n*X,T+=n*Q,h+=(n=t[7])*L,g+=n*M,p+=n*x,y+=n*k,E+=n*V,d+=n*G,S+=n*j,m+=n*F,N+=n*W,b+=n*$,I+=n*Y,A+=n*q,v+=n*z,w+=n*H,T+=n*X,C+=n*Q,g+=(n=t[8])*L,p+=n*M,y+=n*x,E+=n*k,d+=n*V,S+=n*G,m+=n*j,N+=n*F,b+=n*W,I+=n*$,A+=n*Y,v+=n*q,w+=n*z,T+=n*H,C+=n*X,R+=n*Q,p+=(n=t[9])*L,y+=n*M,E+=n*x,d+=n*k,S+=n*V,m+=n*G,N+=n*j,b+=n*F,I+=n*W,A+=n*$,v+=n*Y,w+=n*q,T+=n*z,C+=n*H,R+=n*X,U+=n*Q,y+=(n=t[10])*L,E+=n*M,d+=n*x,S+=n*k,m+=n*V,N+=n*G,b+=n*j,I+=n*F,A+=n*W,v+=n*$,w+=n*Y,T+=n*q,C+=n*z,R+=n*H,U+=n*X,O+=n*Q,E+=(n=t[11])*L,d+=n*M,S+=n*x,m+=n*k,N+=n*V,b+=n*G,I+=n*j,A+=n*F,v+=n*W,w+=n*$,T+=n*Y,C+=n*q,R+=n*z,U+=n*H,O+=n*X,P+=n*Q,d+=(n=t[12])*L,S+=n*M,m+=n*x,N+=n*k,b+=n*V,I+=n*G,A+=n*j,v+=n*F,w+=n*W,T+=n*$,C+=n*Y,R+=n*q,U+=n*z,O+=n*H,P+=n*X,_+=n*Q,S+=(n=t[13])*L,m+=n*M,N+=n*x,b+=n*k,I+=n*V,A+=n*G,v+=n*j,w+=n*F,T+=n*W,C+=n*$,R+=n*Y,U+=n*q,O+=n*z,P+=n*H,_+=n*X,B+=n*Q,m+=(n=t[14])*L,N+=n*M,b+=n*x,I+=n*k,A+=n*V,v+=n*G,w+=n*j,T+=n*F,C+=n*W,R+=n*$,U+=n*Y,O+=n*q,P+=n*z,_+=n*H,B+=n*X,D+=n*Q,N+=(n=t[15])*L,a+=38*(I+=n*x),o+=38*(A+=n*k),u+=38*(v+=n*V),c+=38*(w+=n*G),l+=38*(T+=n*j),f+=38*(C+=n*F),h+=38*(R+=n*W),g+=38*(U+=n*$),p+=38*(O+=n*Y),y+=38*(P+=n*q),E+=38*(_+=n*z),d+=38*(B+=n*H),S+=38*(D+=n*X),m+=38*(K+=n*Q),s=(n=(s+=38*(b+=n*M))+(i=1)+65535)-65536*(i=Math.floor(n/65536)),a=(n=a+i+65535)-65536*(i=Math.floor(n/65536)),o=(n=o+i+65535)-65536*(i=Math.floor(n/65536)),u=(n=u+i+65535)-65536*(i=Math.floor(n/65536)),c=(n=c+i+65535)-65536*(i=Math.floor(n/65536)),l=(n=l+i+65535)-65536*(i=Math.floor(n/65536)),f=(n=f+i+65535)-65536*(i=Math.floor(n/65536)),h=(n=h+i+65535)-65536*(i=Math.floor(n/65536)),g=(n=g+i+65535)-65536*(i=Math.floor(n/65536)),p=(n=p+i+65535)-65536*(i=Math.floor(n/65536)),y=(n=y+i+65535)-65536*(i=Math.floor(n/65536)),E=(n=E+i+65535)-65536*(i=Math.floor(n/65536)),d=(n=d+i+65535)-65536*(i=Math.floor(n/65536)),S=(n=S+i+65535)-65536*(i=Math.floor(n/65536)),m=(n=m+i+65535)-65536*(i=Math.floor(n/65536)),N=(n=N+i+65535)-65536*(i=Math.floor(n/65536)),s=(n=(s+=i-1+37*(i-1))+(i=1)+65535)-65536*(i=Math.floor(n/65536)),a=(n=a+i+65535)-65536*(i=Math.floor(n/65536)),o=(n=o+i+65535)-65536*(i=Math.floor(n/65536)),u=(n=u+i+65535)-65536*(i=Math.floor(n/65536)),c=(n=c+i+65535)-65536*(i=Math.floor(n/65536)),l=(n=l+i+65535)-65536*(i=Math.floor(n/65536)),f=(n=f+i+65535)-65536*(i=Math.floor(n/65536)),h=(n=h+i+65535)-65536*(i=Math.floor(n/65536)),g=(n=g+i+65535)-65536*(i=Math.floor(n/65536)),p=(n=p+i+65535)-65536*(i=Math.floor(n/65536)),y=(n=y+i+65535)-65536*(i=Math.floor(n/65536)),E=(n=E+i+65535)-65536*(i=Math.floor(n/65536)),d=(n=d+i+65535)-65536*(i=Math.floor(n/65536)),S=(n=S+i+65535)-65536*(i=Math.floor(n/65536)),m=(n=m+i+65535)-65536*(i=Math.floor(n/65536)),N=(n=N+i+65535)-65536*(i=Math.floor(n/65536)),s+=i-1+37*(i-1),e[0]=s,e[1]=a,e[2]=o,e[3]=u,e[4]=c,e[5]=l,e[6]=f,e[7]=h,e[8]=g,e[9]=p,e[10]=y,e[11]=E,e[12]=d,e[13]=S,e[14]=m,e[15]=N}function V(e,t){k(e,t,t)}function G(e,r){var n,i=t();for(n=0;n<16;n++)i[n]=r[n];for(n=253;n>=0;n--)V(i,i),2!==n&&4!==n&&k(i,i,r);for(n=0;n<16;n++)e[n]=i[n]}function j(e,r){var n,i=t();for(n=0;n<16;n++)i[n]=r[n];for(n=250;n>=0;n--)V(i,i),1!==n&&k(i,i,r);for(n=0;n<16;n++)e[n]=i[n]}function F(e,r,n){var i,s,a=new Uint8Array(32),o=new Float64Array(80),c=t(),l=t(),f=t(),h=t(),g=t(),p=t();for(s=0;s<31;s++)a[s]=r[s];for(a[31]=127&r[31]|64,a[0]&=248,L(o,n),s=0;s<16;s++)l[s]=o[s],h[s]=c[s]=f[s]=0;for(c[0]=h[0]=1,s=254;s>=0;--s)_(c,l,i=a[s>>>3]>>>(7&s)&1),_(f,h,i),M(g,c,f),x(c,c,f),M(f,l,h),x(l,l,h),V(h,g),V(p,c),k(c,f,c),k(f,l,g),M(g,c,f),x(c,c,f),V(l,c),x(f,h,p),k(c,f,u),M(c,c,h),k(f,f,c),k(c,h,p),k(h,l,o),V(l,g),_(c,l,i),_(f,h,i);for(s=0;s<16;s++)o[s+16]=c[s],o[s+32]=f[s],o[s+48]=l[s],o[s+64]=h[s];var y=o.subarray(32),E=o.subarray(16);return G(y,y),k(E,E,y),B(e,E),0}function W(e,t){return F(e,t,s)}function $(e,t){return n(t,32),W(e,t)}function Y(e,t,r){var n=new Uint8Array(32);return F(n,r,t),m(e,i,n,N)}w.prototype.blocks=function(e,t,r){for(var n,i,s,a,o,u,c,l,f,h,g,p,y,E,d,S,m,N,b,I=this.fin?0:2048,A=this.h[0],v=this.h[1],w=this.h[2],T=this.h[3],C=this.h[4],R=this.h[5],U=this.h[6],O=this.h[7],P=this.h[8],_=this.h[9],B=this.r[0],D=this.r[1],K=this.r[2],L=this.r[3],M=this.r[4],x=this.r[5],k=this.r[6],V=this.r[7],G=this.r[8],j=this.r[9];r>=16;)h=f=0,h+=(A+=8191&(n=255&e[t+0]|(255&e[t+1])<<8))*B,h+=(v+=8191&(n>>>13|(i=255&e[t+2]|(255&e[t+3])<<8)<<3))*(5*j),h+=(w+=8191&(i>>>10|(s=255&e[t+4]|(255&e[t+5])<<8)<<6))*(5*G),h+=(T+=8191&(s>>>7|(a=255&e[t+6]|(255&e[t+7])<<8)<<9))*(5*V),f=(h+=(C+=8191&(a>>>4|(o=255&e[t+8]|(255&e[t+9])<<8)<<12))*(5*k))>>>13,h&=8191,h+=(R+=o>>>1&8191)*(5*x),h+=(U+=8191&(o>>>14|(u=255&e[t+10]|(255&e[t+11])<<8)<<2))*(5*M),h+=(O+=8191&(u>>>11|(c=255&e[t+12]|(255&e[t+13])<<8)<<5))*(5*L),h+=(P+=8191&(c>>>8|(l=255&e[t+14]|(255&e[t+15])<<8)<<8))*(5*K),g=f+=(h+=(_+=l>>>5|I)*(5*D))>>>13,g+=A*D,g+=v*B,g+=w*(5*j),g+=T*(5*G),f=(g+=C*(5*V))>>>13,g&=8191,g+=R*(5*k),g+=U*(5*x),g+=O*(5*M),g+=P*(5*L),f+=(g+=_*(5*K))>>>13,g&=8191,p=f,p+=A*K,p+=v*D,p+=w*B,p+=T*(5*j),f=(p+=C*(5*G))>>>13,p&=8191,p+=R*(5*V),p+=U*(5*k),p+=O*(5*x),p+=P*(5*M),y=f+=(p+=_*(5*L))>>>13,y+=A*L,y+=v*K,y+=w*D,y+=T*B,f=(y+=C*(5*j))>>>13,y&=8191,y+=R*(5*G),y+=U*(5*V),y+=O*(5*k),y+=P*(5*x),E=f+=(y+=_*(5*M))>>>13,E+=A*M,E+=v*L,E+=w*K,E+=T*D,f=(E+=C*B)>>>13,E&=8191,E+=R*(5*j),E+=U*(5*G),E+=O*(5*V),E+=P*(5*k),d=f+=(E+=_*(5*x))>>>13,d+=A*x,d+=v*M,d+=w*L,d+=T*K,f=(d+=C*D)>>>13,d&=8191,d+=R*B,d+=U*(5*j),d+=O*(5*G),d+=P*(5*V),S=f+=(d+=_*(5*k))>>>13,S+=A*k,S+=v*x,S+=w*M,S+=T*L,f=(S+=C*K)>>>13,S&=8191,S+=R*D,S+=U*B,S+=O*(5*j),S+=P*(5*G),m=f+=(S+=_*(5*V))>>>13,m+=A*V,m+=v*k,m+=w*x,m+=T*M,f=(m+=C*L)>>>13,m&=8191,m+=R*K,m+=U*D,m+=O*B,m+=P*(5*j),N=f+=(m+=_*(5*G))>>>13,N+=A*G,N+=v*V,N+=w*k,N+=T*x,f=(N+=C*M)>>>13,N&=8191,N+=R*L,N+=U*K,N+=O*D,N+=P*B,b=f+=(N+=_*(5*j))>>>13,b+=A*j,b+=v*G,b+=w*V,b+=T*k,f=(b+=C*x)>>>13,b&=8191,b+=R*M,b+=U*L,b+=O*K,b+=P*D,A=h=8191&(f=(f=((f+=(b+=_*B)>>>13)<<2)+f|0)+(h&=8191)|0),v=g+=f>>>=13,w=p&=8191,T=y&=8191,C=E&=8191,R=d&=8191,U=S&=8191,O=m&=8191,P=N&=8191,_=b&=8191,t+=16,r-=16;this.h[0]=A,this.h[1]=v,this.h[2]=w,this.h[3]=T,this.h[4]=C,this.h[5]=R,this.h[6]=U,this.h[7]=O,this.h[8]=P,this.h[9]=_},w.prototype.finish=function(e,t){var r,n,i,s,a=new Uint16Array(10);if(this.leftover){for(s=this.leftover,this.buffer[s++]=1;s<16;s++)this.buffer[s]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(r=this.h[1]>>>13,this.h[1]&=8191,s=2;s<10;s++)this.h[s]+=r,r=this.h[s]>>>13,this.h[s]&=8191;for(this.h[0]+=5*r,r=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=r,r=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=r,a[0]=this.h[0]+5,r=a[0]>>>13,a[0]&=8191,s=1;s<10;s++)a[s]=this.h[s]+r,r=a[s]>>>13,a[s]&=8191;for(a[9]-=8192,n=(1^r)-1,s=0;s<10;s++)a[s]&=n;for(n=~n,s=0;s<10;s++)this.h[s]=this.h[s]&n|a[s];for(this.h[0]=65535&(this.h[0]|this.h[1]<<13),this.h[1]=65535&(this.h[1]>>>3|this.h[2]<<10),this.h[2]=65535&(this.h[2]>>>6|this.h[3]<<7),this.h[3]=65535&(this.h[3]>>>9|this.h[4]<<4),this.h[4]=65535&(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14),this.h[5]=65535&(this.h[6]>>>2|this.h[7]<<11),this.h[6]=65535&(this.h[7]>>>5|this.h[8]<<8),this.h[7]=65535&(this.h[8]>>>8|this.h[9]<<5),i=this.h[0]+this.pad[0],this.h[0]=65535&i,s=1;s<8;s++)i=(this.h[s]+this.pad[s]|0)+(i>>>16)|0,this.h[s]=65535&i;e[t+0]=this.h[0]>>>0&255,e[t+1]=this.h[0]>>>8&255,e[t+2]=this.h[1]>>>0&255,e[t+3]=this.h[1]>>>8&255,e[t+4]=this.h[2]>>>0&255,e[t+5]=this.h[2]>>>8&255,e[t+6]=this.h[3]>>>0&255,e[t+7]=this.h[3]>>>8&255,e[t+8]=this.h[4]>>>0&255,e[t+9]=this.h[4]>>>8&255,e[t+10]=this.h[5]>>>0&255,e[t+11]=this.h[5]>>>8&255,e[t+12]=this.h[6]>>>0&255,e[t+13]=this.h[6]>>>8&255,e[t+14]=this.h[7]>>>0&255,e[t+15]=this.h[7]>>>8&255},w.prototype.update=function(e,t,r){var n,i;if(this.leftover){for((i=16-this.leftover)>r&&(i=r),n=0;n<i;n++)this.buffer[this.leftover+n]=e[t+n];if(r-=i,t+=i,this.leftover+=i,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(r>=16&&(i=r-r%16,this.blocks(e,t,i),t+=i,r-=i),r){for(n=0;n<r;n++)this.buffer[this.leftover+n]=e[t+n];this.leftover+=r}};var q=R,z=U;var H=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function X(e,t,r,n){for(var i,s,a,o,u,c,l,f,h,g,p,y,E,d,S,m,N,b,I,A,v,w,T,C,R,U,O=new Int32Array(16),P=new Int32Array(16),_=e[0],B=e[1],D=e[2],K=e[3],L=e[4],M=e[5],x=e[6],k=e[7],V=t[0],G=t[1],j=t[2],F=t[3],W=t[4],$=t[5],Y=t[6],q=t[7],z=0;n>=128;){for(I=0;I<16;I++)A=8*I+z,O[I]=r[A+0]<<24|r[A+1]<<16|r[A+2]<<8|r[A+3],P[I]=r[A+4]<<24|r[A+5]<<16|r[A+6]<<8|r[A+7];for(I=0;I<80;I++)if(i=_,s=B,a=D,o=K,u=L,c=M,l=x,k,h=V,g=G,p=j,y=F,E=W,d=$,S=Y,q,T=65535&(w=q),C=w>>>16,R=65535&(v=k),U=v>>>16,T+=65535&(w=(W>>>14|L<<18)^(W>>>18|L<<14)^(L>>>9|W<<23)),C+=w>>>16,R+=65535&(v=(L>>>14|W<<18)^(L>>>18|W<<14)^(W>>>9|L<<23)),U+=v>>>16,T+=65535&(w=W&$^~W&Y),C+=w>>>16,R+=65535&(v=L&M^~L&x),U+=v>>>16,T+=65535&(w=H[2*I+1]),C+=w>>>16,R+=65535&(v=H[2*I]),U+=v>>>16,v=O[I%16],C+=(w=P[I%16])>>>16,R+=65535&v,U+=v>>>16,R+=(C+=(T+=65535&w)>>>16)>>>16,T=65535&(w=b=65535&T|C<<16),C=w>>>16,R=65535&(v=N=65535&R|(U+=R>>>16)<<16),U=v>>>16,T+=65535&(w=(V>>>28|_<<4)^(_>>>2|V<<30)^(_>>>7|V<<25)),C+=w>>>16,R+=65535&(v=(_>>>28|V<<4)^(V>>>2|_<<30)^(V>>>7|_<<25)),U+=v>>>16,C+=(w=V&G^V&j^G&j)>>>16,R+=65535&(v=_&B^_&D^B&D),U+=v>>>16,f=65535&(R+=(C+=(T+=65535&w)>>>16)>>>16)|(U+=R>>>16)<<16,m=65535&T|C<<16,T=65535&(w=y),C=w>>>16,R=65535&(v=o),U=v>>>16,C+=(w=b)>>>16,R+=65535&(v=N),U+=v>>>16,B=i,D=s,K=a,L=o=65535&(R+=(C+=(T+=65535&w)>>>16)>>>16)|(U+=R>>>16)<<16,M=u,x=c,k=l,_=f,G=h,j=g,F=p,W=y=65535&T|C<<16,$=E,Y=d,q=S,V=m,I%16==15)for(A=0;A<16;A++)v=O[A],T=65535&(w=P[A]),C=w>>>16,R=65535&v,U=v>>>16,v=O[(A+9)%16],T+=65535&(w=P[(A+9)%16]),C+=w>>>16,R+=65535&v,U+=v>>>16,N=O[(A+1)%16],T+=65535&(w=((b=P[(A+1)%16])>>>1|N<<31)^(b>>>8|N<<24)^(b>>>7|N<<25)),C+=w>>>16,R+=65535&(v=(N>>>1|b<<31)^(N>>>8|b<<24)^N>>>7),U+=v>>>16,N=O[(A+14)%16],C+=(w=((b=P[(A+14)%16])>>>19|N<<13)^(N>>>29|b<<3)^(b>>>6|N<<26))>>>16,R+=65535&(v=(N>>>19|b<<13)^(b>>>29|N<<3)^N>>>6),U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,O[A]=65535&R|U<<16,P[A]=65535&T|C<<16;T=65535&(w=V),C=w>>>16,R=65535&(v=_),U=v>>>16,v=e[0],C+=(w=t[0])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[0]=_=65535&R|U<<16,t[0]=V=65535&T|C<<16,T=65535&(w=G),C=w>>>16,R=65535&(v=B),U=v>>>16,v=e[1],C+=(w=t[1])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[1]=B=65535&R|U<<16,t[1]=G=65535&T|C<<16,T=65535&(w=j),C=w>>>16,R=65535&(v=D),U=v>>>16,v=e[2],C+=(w=t[2])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[2]=D=65535&R|U<<16,t[2]=j=65535&T|C<<16,T=65535&(w=F),C=w>>>16,R=65535&(v=K),U=v>>>16,v=e[3],C+=(w=t[3])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[3]=K=65535&R|U<<16,t[3]=F=65535&T|C<<16,T=65535&(w=W),C=w>>>16,R=65535&(v=L),U=v>>>16,v=e[4],C+=(w=t[4])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[4]=L=65535&R|U<<16,t[4]=W=65535&T|C<<16,T=65535&(w=$),C=w>>>16,R=65535&(v=M),U=v>>>16,v=e[5],C+=(w=t[5])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[5]=M=65535&R|U<<16,t[5]=$=65535&T|C<<16,T=65535&(w=Y),C=w>>>16,R=65535&(v=x),U=v>>>16,v=e[6],C+=(w=t[6])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[6]=x=65535&R|U<<16,t[6]=Y=65535&T|C<<16,T=65535&(w=q),C=w>>>16,R=65535&(v=k),U=v>>>16,v=e[7],C+=(w=t[7])>>>16,R+=65535&v,U+=v>>>16,U+=(R+=(C+=(T+=65535&w)>>>16)>>>16)>>>16,e[7]=k=65535&R|U<<16,t[7]=q=65535&T|C<<16,z+=128,n-=128}return n}function Q(e,t,r){var n,i=new Int32Array(8),s=new Int32Array(8),a=new Uint8Array(256),o=r;for(i[0]=1779033703,i[1]=3144134277,i[2]=1013904242,i[3]=2773480762,i[4]=1359893119,i[5]=2600822924,i[6]=528734635,i[7]=1541459225,s[0]=4089235720,s[1]=2227873595,s[2]=4271175723,s[3]=1595750129,s[4]=2917565137,s[5]=725511199,s[6]=4215389547,s[7]=327033209,X(i,s,t,r),r%=128,n=0;n<r;n++)a[n]=t[o-r+n];for(a[r]=128,a[(r=256-128*(r<112?1:0))-9]=0,p(a,r-8,o/536870912|0,o<<3),X(i,s,a,r),n=0;n<8;n++)p(e,8*n,i[n],s[n]);return 0}function J(e,r){var n=t(),i=t(),s=t(),a=t(),o=t(),u=t(),c=t(),f=t(),h=t();x(n,e[1],e[0]),x(h,r[1],r[0]),k(n,n,h),M(i,e[0],e[1]),M(h,r[0],r[1]),k(i,i,h),k(s,e[3],r[3]),k(s,s,l),k(a,e[2],r[2]),M(a,a,a),x(o,i,n),x(u,a,s),M(c,a,s),M(f,i,n),k(e[0],o,u),k(e[1],f,c),k(e[2],c,u),k(e[3],o,f)}function Z(e,t,r){var n;for(n=0;n<4;n++)_(e[n],t[n],r)}function ee(e,r){var n=t(),i=t(),s=t();G(s,r[2]),k(n,r[0],s),k(i,r[1],s),B(e,i),e[31]^=K(n)<<7}function te(e,t,r){var n,i;for(O(e[0],a),O(e[1],o),O(e[2],o),O(e[3],a),i=255;i>=0;--i)Z(e,t,n=r[i/8|0]>>(7&i)&1),J(t,e),J(e,e),Z(e,t,n)}function re(e,r){var n=[t(),t(),t(),t()];O(n[0],f),O(n[1],h),O(n[2],o),k(n[3],f,h),te(e,n,r)}function ne(e,r,i){var s,a=new Uint8Array(64),o=[t(),t(),t(),t()];for(i||n(r,32),Q(a,r,32),a[0]&=248,a[31]&=127,a[31]|=64,re(o,a),ee(e,o),s=0;s<32;s++)r[s+32]=e[s];return 0}var ie=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function se(e,t){var r,n,i,s;for(n=63;n>=32;--n){for(r=0,i=n-32,s=n-12;i<s;++i)t[i]+=r-16*t[n]*ie[i-(n-32)],r=Math.floor((t[i]+128)/256),t[i]-=256*r;t[i]+=r,t[n]=0}for(r=0,i=0;i<32;i++)t[i]+=r-(t[31]>>4)*ie[i],r=t[i]>>8,t[i]&=255;for(i=0;i<32;i++)t[i]-=r*ie[i];for(n=0;n<32;n++)t[n+1]+=t[n]>>8,e[n]=255&t[n]}function ae(e){var t,r=new Float64Array(64);for(t=0;t<64;t++)r[t]=e[t];for(t=0;t<64;t++)e[t]=0;se(e,r)}function oe(e,r,n,i){var s,a,o=new Uint8Array(64),u=new Uint8Array(64),c=new Uint8Array(64),l=new Float64Array(64),f=[t(),t(),t(),t()];Q(o,i,32),o[0]&=248,o[31]&=127,o[31]|=64;var h=n+64;for(s=0;s<n;s++)e[64+s]=r[s];for(s=0;s<32;s++)e[32+s]=o[32+s];for(Q(c,e.subarray(32),n+32),ae(c),re(f,c),ee(e,f),s=32;s<64;s++)e[s]=i[s];for(Q(u,e,n+64),ae(u),s=0;s<64;s++)l[s]=0;for(s=0;s<32;s++)l[s]=c[s];for(s=0;s<32;s++)for(a=0;a<32;a++)l[s+a]+=u[s]*o[a];return se(e.subarray(32),l),h}function ue(e,r,n,i){var s,u=new Uint8Array(32),l=new Uint8Array(64),f=[t(),t(),t(),t()],h=[t(),t(),t(),t()];if(n<64)return-1;if(function(e,r){var n=t(),i=t(),s=t(),u=t(),l=t(),f=t(),h=t();return O(e[2],o),L(e[1],r),V(s,e[1]),k(u,s,c),x(s,s,e[2]),M(u,e[2],u),V(l,u),V(f,l),k(h,f,l),k(n,h,s),k(n,n,u),j(n,n),k(n,n,s),k(n,n,u),k(n,n,u),k(e[0],n,u),V(i,e[0]),k(i,i,u),D(i,s)&&k(e[0],e[0],g),V(i,e[0]),k(i,i,u),D(i,s)?-1:(K(e[0])===r[31]>>7&&x(e[0],a,e[0]),k(e[3],e[0],e[1]),0)}(h,i))return-1;for(s=0;s<n;s++)e[s]=r[s];for(s=0;s<32;s++)e[s+32]=i[s];if(Q(l,e,n),ae(l),te(f,h,l),re(h,r.subarray(32)),J(f,h),ee(u,f),n-=64,d(r,0,u,0)){for(s=0;s<n;s++)e[s]=0;return-1}for(s=0;s<n;s++)e[s]=r[s+64];return n}function ce(e,t){if(32!==e.length)throw new Error("bad key size");if(24!==t.length)throw new Error("bad nonce size")}function le(){for(var e=0;e<arguments.length;e++)if(!(arguments[e]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function fe(e){for(var t=0;t<e.length;t++)e[t]=0}e.lowlevel={crypto_core_hsalsa20:m,crypto_stream_xor:v,crypto_stream:A,crypto_stream_salsa20_xor:b,crypto_stream_salsa20:I,crypto_onetimeauth:T,crypto_onetimeauth_verify:C,crypto_verify_16:E,crypto_verify_32:d,crypto_secretbox:R,crypto_secretbox_open:U,crypto_scalarmult:F,crypto_scalarmult_base:W,crypto_box_beforenm:Y,crypto_box_afternm:q,crypto_box:function(e,t,r,n,i,s){var a=new Uint8Array(32);return Y(a,i,s),q(e,t,r,n,a)},crypto_box_open:function(e,t,r,n,i,s){var a=new Uint8Array(32);return Y(a,i,s),z(e,t,r,n,a)},crypto_box_keypair:$,crypto_hash:Q,crypto_sign:oe,crypto_sign_keypair:ne,crypto_sign_open:ue,crypto_secretbox_KEYBYTES:32,crypto_secretbox_NONCEBYTES:24,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:24,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:64,crypto_sign_PUBLICKEYBYTES:32,crypto_sign_SECRETKEYBYTES:64,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:t,D:c,L:ie,pack25519:B,unpack25519:L,M:k,A:M,S:V,Z:x,pow2523:j,add:J,set25519:O,modL:se,scalarmult:te,scalarbase:re},e.randomBytes=function(e){var t=new Uint8Array(e);return n(t,e),t},e.secretbox=function(e,t,r){le(e,t,r),ce(r,t);for(var n=new Uint8Array(32+e.length),i=new Uint8Array(n.length),s=0;s<e.length;s++)n[s+32]=e[s];return R(i,n,n.length,t,r),i.subarray(16)},e.secretbox.open=function(e,t,r){le(e,t,r),ce(r,t);for(var n=new Uint8Array(16+e.length),i=new Uint8Array(n.length),s=0;s<e.length;s++)n[s+16]=e[s];return n.length<32||0!==U(i,n,n.length,t,r)?null:i.subarray(32)},e.secretbox.keyLength=32,e.secretbox.nonceLength=24,e.secretbox.overheadLength=16,e.scalarMult=function(e,t){if(le(e,t),32!==e.length)throw new Error("bad n size");if(32!==t.length)throw new Error("bad p size");var r=new Uint8Array(32);return F(r,e,t),r},e.scalarMult.base=function(e){if(le(e),32!==e.length)throw new Error("bad n size");var t=new Uint8Array(32);return W(t,e),t},e.scalarMult.scalarLength=32,e.scalarMult.groupElementLength=32,e.box=function(t,r,n,i){var s=e.box.before(n,i);return e.secretbox(t,r,s)},e.box.before=function(e,t){le(e,t),function(e,t){if(32!==e.length)throw new Error("bad public key size");if(32!==t.length)throw new Error("bad secret key size")}(e,t);var r=new Uint8Array(32);return Y(r,e,t),r},e.box.after=e.secretbox,e.box.open=function(t,r,n,i){var s=e.box.before(n,i);return e.secretbox.open(t,r,s)},e.box.open.after=e.secretbox.open,e.box.keyPair=function(){var e=new Uint8Array(32),t=new Uint8Array(32);return $(e,t),{publicKey:e,secretKey:t}},e.box.keyPair.fromSecretKey=function(e){if(le(e),32!==e.length)throw new Error("bad secret key size");var t=new Uint8Array(32);return W(t,e),{publicKey:t,secretKey:new Uint8Array(e)}},e.box.publicKeyLength=32,e.box.secretKeyLength=32,e.box.sharedKeyLength=32,e.box.nonceLength=24,e.box.overheadLength=e.secretbox.overheadLength,e.sign=function(e,t){if(le(e,t),64!==t.length)throw new Error("bad secret key size");var r=new Uint8Array(64+e.length);return oe(r,e,e.length,t),r},e.sign.open=function(e,t){if(le(e,t),32!==t.length)throw new Error("bad public key size");var r=new Uint8Array(e.length),n=ue(r,e,e.length,t);if(n<0)return null;for(var i=new Uint8Array(n),s=0;s<i.length;s++)i[s]=r[s];return i},e.sign.detached=function(t,r){for(var n=e.sign(t,r),i=new Uint8Array(64),s=0;s<i.length;s++)i[s]=n[s];return i},e.sign.detached.verify=function(e,t,r){if(le(e,t,r),64!==t.length)throw new Error("bad signature size");if(32!==r.length)throw new Error("bad public key size");var n,i=new Uint8Array(64+e.length),s=new Uint8Array(64+e.length);for(n=0;n<64;n++)i[n]=t[n];for(n=0;n<e.length;n++)i[n+64]=e[n];return ue(s,i,i.length,r)>=0},e.sign.keyPair=function(){var e=new Uint8Array(32),t=new Uint8Array(64);return ne(e,t),{publicKey:e,secretKey:t}},e.sign.keyPair.fromSecretKey=function(e){if(le(e),64!==e.length)throw new Error("bad secret key size");for(var t=new Uint8Array(32),r=0;r<t.length;r++)t[r]=e[32+r];return{publicKey:t,secretKey:new Uint8Array(e)}},e.sign.keyPair.fromSeed=function(e){if(le(e),32!==e.length)throw new Error("bad seed size");for(var t=new Uint8Array(32),r=new Uint8Array(64),n=0;n<32;n++)r[n]=e[n];return ne(t,r,!0),{publicKey:t,secretKey:r}},e.sign.publicKeyLength=32,e.sign.secretKeyLength=64,e.sign.seedLength=32,e.sign.signatureLength=64,e.hash=function(e){le(e);var t=new Uint8Array(64);return Q(t,e,e.length),t},e.hash.hashLength=64,e.verify=function(e,t){return le(e,t),0!==e.length&&0!==t.length&&(e.length===t.length&&0===y(e,0,t,0,e.length))},e.setPRNG=function(e){n=e},function(){var t="undefined"!=typeof self?self.crypto||self.msCrypto:null;if(t&&t.getRandomValues){e.setPRNG((function(e,r){var n,i=new Uint8Array(r);for(n=0;n<r;n+=65536)t.getRandomValues(i.subarray(n,n+Math.min(r-n,65536)));for(n=0;n<r;n++)e[n]=i[n];fe(i)}))}else(t=r)&&t.randomBytes&&e.setPRNG((function(e,r){var n,i=t.randomBytes(r);for(n=0;n<r;n++)e[n]=i[n];fe(i)}))}()}(e.exports?e.exports:self.nacl=self.nacl||{})})),T=a((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});t.BufferVisitor=class{constructor(e,t=0,r=0){this.start=t,this.end=r>t?r:t,this.buf=e}get length(){return this.buf.length}reset(e=0,t=0){return this.start=e,t>=this.start?this.end=t:this.end<this.start&&(this.end=this.start),this}walk(e){return this.start=this.end,this.end+=e,this}mustHas(e,t="Too few bytes to parse."){const r=this.end+e;if(r>this.buf.length){const e=new Error(t);throw e.available=this.buf.length,e.requested=r,e}return this.walk(0),this}mustWalk(e,t){return this.mustHas(e,t),this.walk(e),this}}}));s(T);T.BufferVisitor;var C=a((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});class r{static parse(e){const t=[],r=e.toString("utf8").split("\n").map(e=>e.trim()).filter(e=>""!==e&&!e.startsWith("#"));for(;r.length>0;)t.push(n(r));if(0===t.length)throw new Error("PEM: no block");return t}constructor(e,t){this.type=e,this.body=t,this.headers=Object.create(null)}get procType(){return this.getHeader("Proc-Type")}getHeader(e){const t=this.headers[e];return null==t?"":t}setHeader(e,t){if(e.includes(":"))throw new Error("pem: cannot encode a header key that contains a colon");if(""===e||""===t)throw new Error("pem: invalid header key or value");this.headers[e]=t}toString(){let e="-----BEGIN "+this.type+"-----\n";const t=Object.keys(this.headers);if(t.length>0){const r=this.procType;""!==r&&(e+=`Proc-Type: ${r}\n`),t.sort();for(const r of t)"Proc-Type"!==r&&(e+=`${r}: ${this.headers[r]}\n`);e+="\n"}const r=this.body.toString("base64");let n=0;for(;n<r.length;)e+=r.slice(n,n+64)+"\n",n+=64;return e+="-----END "+this.type+"-----\n",e}toBuffer(){return Buffer.from(this.toString(),"utf8")}valueOf(){return this.body}toJSON(){return{type:this.type,body:this.body,headers:this.headers}}[i.inspect.custom](e,t){return`<${this.constructor.name} ${i.inspect(this.toJSON(),t)}>`}}function n(e){let t=e.shift();if(null==t||!t.startsWith("-----BEGIN ")||!t.endsWith("-----"))throw new Error("pem: invalid BEGIN line");const n=t.slice("-----BEGIN ".length,t.length-"-----".length);if(""===n)throw new Error("pem: invalid type");const i=[];for(t=e.shift();null!=t&&t.includes(": ");){const r=t.split(": ");if(2!==r.length||""===r[0]||""===r[1])throw new Error("pem: invalid Header line");i.push(r),t=e.shift()}let s="";for(;null!=t&&!t.startsWith("-----END ");)s+=t,t=e.shift();if(null==t||t!==`-----END ${n}-----`)throw new Error("pem: invalid END line");const a=new r(n,Buffer.from(s,"base64"));if(""===s||a.body.toString("base64")!==s)throw new Error("pem: invalid base64 body");for(const e of i)a.setHeader(e[0],e[1]);return a}t.PEM=r}));s(C);C.PEM;var R=a((function(e,t){var r,n;Object.defineProperty(t,"__esModule",{value:!0}),function(e){e[e.UNIVERSAL=0]="UNIVERSAL",e[e.APPLICATION=64]="APPLICATION",e[e.CONTEXT_SPECIFIC=128]="CONTEXT_SPECIFIC",e[e.PRIVATE=192]="PRIVATE"}(r=t.Class||(t.Class={})),function(e){e[e.NONE=0]="NONE",e[e.BOOLEAN=1]="BOOLEAN",e[e.INTEGER=2]="INTEGER",e[e.BITSTRING=3]="BITSTRING",e[e.OCTETSTRING=4]="OCTETSTRING",e[e.NULL=5]="NULL",e[e.OID=6]="OID",e[e.ENUMERATED=10]="ENUMERATED",e[e.UTF8=12]="UTF8",e[e.SEQUENCE=16]="SEQUENCE",e[e.SET=17]="SET",e[e.NUMERICSTRING=18]="NUMERICSTRING",e[e.PRINTABLESTRING=19]="PRINTABLESTRING",e[e.T61STRING=20]="T61STRING",e[e.IA5STRING=22]="IA5STRING",e[e.UTCTIME=23]="UTCTIME",e[e.GENERALIZEDTIME=24]="GENERALIZEDTIME",e[e.GENERALSTRING=27]="GENERALSTRING"}(n=t.Tag||(t.Tag={}));class s{constructor(e,t){this.buf=e,this.bitLen=t}at(e){if(e<0||e>=this.bitLen||!Number.isInteger(e))return 0;const t=Math.floor(e/8),r=7-e%8;return this.buf[t]>>r&1}rightAlign(){const e=8-this.bitLen%8;if(8===e||0===this.buf.length)return this.buf;const t=Buffer.alloc(this.buf.length);t[0]=this.buf[0]>>e;for(let r=1;r<this.buf.length;r++)t[r]=this.buf[r-1]<<8-e,t[r]|=this.buf[r]>>e;return t}}t.BitString=s;class a{static Bool(e){const t=new a(r.UNIVERSAL,n.BOOLEAN,Buffer.from([e?255:0]));return t._value=e,t}static parseBool(e){if(!(e instanceof Buffer)||1!==e.length)throw new Error("ASN1 syntax error: invalid boolean");switch(e[0]){case 0:return!1;case 255:return!0;default:throw new Error("ASN1 syntax error: invalid boolean")}}static Integer(e){if(e instanceof Buffer){const t=new a(r.UNIVERSAL,n.INTEGER,e);return t._value=e.toString("hex"),t}if(!Number.isSafeInteger(e))throw new Error("ASN1 syntax error: invalid integer");let t;if(e>=-128&&e<128)t=Buffer.alloc(1),t.writeInt8(e,0);else if(e>=-32768&&e<32768)t=Buffer.alloc(2),t.writeIntBE(e,0,2);else if(e>=-8388608&&e<8388608)t=Buffer.alloc(3),t.writeIntBE(e,0,3);else if(e>=-2147483648&&e<2147483648)t=Buffer.alloc(4),t.writeIntBE(e,0,4);else if(e>=-549755813888&&e<549755813888)t=Buffer.alloc(5),t.writeIntBE(e,0,5);else{if(!(e>=-0x800000000000&&e<0x800000000000))throw new Error("ASN1 syntax error: invalid Integer");t=Buffer.alloc(6),t.writeIntBE(e,0,6)}const i=new a(r.UNIVERSAL,n.INTEGER,t);return i._value=e,i}static parseInteger(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid Integer");return e.length>6?e.toString("hex"):e.readIntBE(0,e.length)}static parseIntegerNum(e){const t=a.parseInteger(e);if("number"!=typeof t)throw new Error("ASN1 syntax error: invalid Integer number");return t}static parseIntegerStr(e){const t=a.parseInteger(e);return"number"==typeof t?t.toString(16):t}static BitString(e){e instanceof Buffer&&(e=new s(e,8*e.length));const t=8*e.buf.length-e.bitLen,i=Buffer.alloc(e.buf.length+1);return i.writeInt8(t,0),e.buf.copy(i,1),new a(r.UNIVERSAL,n.BITSTRING,i)}static parseBitString(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid BitString");const t=e[0];if(t>7||1===e.length&&t>0||0!=(e[e.length-1]&(1<<e[0])-1))throw new Error("ASN1 syntax error: invalid padding bits in BIT STRING");return new s(e.slice(1),8*(e.length-1)-t)}static Null(){const e=new a(r.UNIVERSAL,n.NULL,Buffer.alloc(0));return e._value=null,e}static parseNull(e){if(!(e instanceof Buffer)||0!==e.length)throw new Error("ASN1 syntax error: invalid null");return null}static OID(e){const t=e.split(".");if(0===t.length)throw new Error("ASN1 syntax error: invalid Object Identifier");const i=[];i.push(40*c(t[0])+c(t[1]));const s=[];for(let e=2;e<t.length;++e){let r=c(t[e]);for(s.length=0,s.push(127&r);r>127;)r>>>=7,s.unshift(127&r|128);i.push(...s)}const o=new a(r.UNIVERSAL,n.OID,Buffer.from(i));return o._value=e,o}static parseOID(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid OID");let t=Math.floor(e[0]/40)+"."+e[0]%40,r=0;for(let n=1;n<e.length;n++)e[n]>=128?(r+=127&e[n],r<<=7):(t+="."+(r+e[n]),r=0);return t}static UTF8(e){const t=new a(r.UNIVERSAL,n.UTF8,Buffer.from(e,"utf8"));return t._value=e,t}static parseUTF8(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static NumericString(e){if(!o(e))throw new Error("ASN1 syntax error: invalid NumericString");const t=new a(r.UNIVERSAL,n.NUMERICSTRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseNumericString(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");const t=e.toString("utf8");if(!o(t))throw new Error("ASN1 syntax error: invalid NumericString");return t}static PrintableString(e){const t=new a(r.UNIVERSAL,n.PRINTABLESTRING,Buffer.from(e,"utf8"));return t._value=e,t}static parsePrintableString(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static IA5String(e){if(!u(e))throw new Error("ASN1 syntax error: invalid IA5String");const t=new a(r.UNIVERSAL,n.IA5STRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseIA5String(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");const t=e.toString("utf8");if(!u(t))throw new Error("ASN1 syntax error: invalid IA5String");return t}static T61String(e){const t=new a(r.UNIVERSAL,n.T61STRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseT61String(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static GeneralString(e){const t=new a(r.UNIVERSAL,n.GENERALSTRING,Buffer.from(e,"utf8"));return t._value=e,t}static parseGeneralString(e){if(!(e instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return e.toString("utf8")}static UTCTime(e){let t="";const i=[];i.push((""+e.getUTCFullYear()).substr(2)),i.push(""+(e.getUTCMonth()+1)),i.push(""+e.getUTCDate()),i.push(""+e.getUTCHours()),i.push(""+e.getUTCMinutes()),i.push(""+e.getUTCSeconds());for(const e of i)e.length<2&&(t+="0"),t+=e;t+="Z";const s=new a(r.UNIVERSAL,n.UTCTIME,Buffer.from(t,"utf8"));return s._value=e,s}static parseUTCTime(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid UTC Time");const t=e.toString("utf8"),r=new Date;let n=c(t.substr(0,2));n=n>=50?1900+n:2e3+n;const i=c(t.substr(2,2))-1,s=c(t.substr(4,2)),a=c(t.substr(6,2)),o=c(t.substr(8,2));let u=0,l=0,f="";if(t.length>11&&(l=10,f=t.charAt(l),"+"!==f&&"-"!==f&&(u=c(t.substr(10,2)),l+=2)),r.setUTCFullYear(n,i,s),r.setUTCHours(a,o,u,0),l>0&&(f=t.charAt(l),"+"===f||"-"===f)){let e=60*c(t.substr(l+1,2))+c(t.substr(l+4,2));e*=6e4,"+"===f?r.setTime(+r-e):r.setTime(+r+e)}return r}static GeneralizedTime(e){let t="";const i=[];i.push(""+e.getUTCFullYear()),i.push(""+(e.getUTCMonth()+1)),i.push(""+e.getUTCDate()),i.push(""+e.getUTCHours()),i.push(""+e.getUTCMinutes()),i.push(""+e.getUTCSeconds());for(const e of i)e.length<2&&(t+="0"),t+=e;t+="Z";const s=new a(r.UNIVERSAL,n.GENERALIZEDTIME,Buffer.from(t,"utf8"));return s._value=e,s}static parseGeneralizedTime(e){if(!(e instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid Generalized Time");const t=e.toString("utf8"),r=new Date,n=c(t.substr(0,4)),i=c(t.substr(4,2))-1,s=c(t.substr(6,2)),a=c(t.substr(8,2)),o=c(t.substr(10,2)),u=c(t.substr(12,2));let l=0,f=0,h=!1;"Z"===t.charAt(t.length-1)&&(h=!0);const g=t.length-5,p=t.charAt(g);if("+"===p||"-"===p){f=60*c(t.substr(g+1,2))+c(t.substr(g+4,2)),f*=6e4,"+"===p&&(f*=-1),h=!0}return"."===t.charAt(14)&&(l=1e3*parseFloat(t.substr(14))),h?(r.setUTCFullYear(n,i,s),r.setUTCHours(a,o,u,l),r.setTime(+r+f)):(r.setFullYear(n,i,s),r.setHours(a,o,u,l)),r}static parseTime(e,t){switch(e){case n.UTCTIME:return a.parseUTCTime(t);case n.GENERALIZEDTIME:return a.parseGeneralizedTime(t);default:throw new Error("Invalid ASN1 time tag")}}static Set(e){const t=new a(r.UNIVERSAL,n.SET,Buffer.concat(e.map(e=>e.toDER())));return t._value=e,t}static Seq(e){const t=new a(r.UNIVERSAL,n.SEQUENCE,Buffer.concat(e.map(e=>e.toDER())));return t._value=e,t}static Spec(e,t,n=!0){const i=Array.isArray(t)?Buffer.concat(t.map(e=>e.toDER())):t.toDER();Array.isArray(t)&&(n=!0);const s=new a(r.CONTEXT_SPECIFIC,e,i,n);return s._value=t,s}static fromDER(e,t=!1){return a._fromDER(new T.BufferVisitor(e),t)}static parseDER(e,t,r){const n=a._fromDER(new T.BufferVisitor(e),!1);if(n.class!==t&&n.tag!==r)throw new Error(`invalid ASN.1 DER for class ${t} and tag ${r}`);return n}static parseDERWithTemplate(e,t){const r=a._fromDER(new T.BufferVisitor(e),!0),n={},i=r.validate(t,n);if(null!=i)throw i.data=r,i;return n}static _parseCompound(e,t){const r=[],n=e.length,i=new T.BufferVisitor(e);let s=0;for(;s<n;){const e=i.end;r.push(a._fromDER(i,t)),s+=i.end-e}return r}static _fromDER(e,t){if(!(e.buf instanceof Buffer)||0===e.length)throw new Error("ASN1 syntax error: invalid Generalized Time");e.mustWalk(1,"Too few bytes to read ASN.1 tag.");const r=e.start,i=e.buf[r],s=192&i,o=31&i,u=function(e){e.mustWalk(1,"Too few bytes to read ASN.1 value length.");const t=e.buf[e.start];if(0==(128&t))return t;const r=127&t;return e.mustWalk(r,"Too few bytes to read ASN.1 value length."),e.buf.readUIntBE(e.start,r)}(e);if(e.mustHas(u),0!==u&&o===n.NULL)throw new Error("invalid value length or NULL tag.");e.mustWalk(u);const c=32==(32&i),l=new a(s,o,e.buf.slice(e.start,e.end),c);return c&&t&&(l._value=a._parseCompound(l.bytes,t)),l._der=e.buf.slice(r,e.end),l}constructor(e,t,r,i=!1){this.class=e,this.tag=t,this.bytes=r,this.isCompound=i||t===n.SEQUENCE||t===n.SET,this._value=void 0,this._der=null}get value(){return void 0===this._value&&(this._value=this.valueOf()),this._value}get DER(){return null==this._der&&(this._der=this.toDER()),this._der}mustCompound(e="asn1 object value is not compound"){if(!this.isCompound||!Array.isArray(this.value)){const t=new Error(e);throw t.data=this.toJSON(),t}return this.value}equals(e){return e instanceof a&&(this.class===e.class&&this.tag===e.tag&&this.isCompound===e.isCompound&&!!this.bytes.equals(e.bytes))}toDER(){let e=this.class|this.tag;this.isCompound&&(e|=32);const t=function(e){if(e<=127)return 0;if(e<=255)return 1;if(e<=65535)return 2;if(e<=16777215)return 3;if(e<=4294967295)return 4;if(e<=0xffffffffff)return 5;if(e<=0xffffffffffff)return 6;throw new Error("invalid value length")}(this.bytes.length),r=Buffer.allocUnsafe(2+t+this.bytes.length);return r.writeInt8(e,0),0===t?(r.writeUInt8(this.bytes.length,1),this.bytes.copy(r,2)):(r.writeUInt8(128|t,1),r.writeUIntBE(this.bytes.length,2,t),this.bytes.copy(r,2+t)),r}valueOf(){if(this.isCompound)return a._parseCompound(this.bytes,!1);if(this.class!==r.UNIVERSAL)return this.bytes;switch(this.tag){case n.BOOLEAN:return a.parseBool(this.bytes);case n.INTEGER:return a.parseInteger(this.bytes);case n.BITSTRING:return a.parseBitString(this.bytes);case n.NULL:return a.parseNull(this.bytes);case n.OID:return a.parseOID(this.bytes);case n.UTF8:return a.parseUTF8(this.bytes);case n.NUMERICSTRING:return a.parseNumericString(this.bytes);case n.PRINTABLESTRING:return a.parsePrintableString(this.bytes);case n.T61STRING:return a.parseT61String(this.bytes);case n.IA5STRING:return a.parseIA5String(this.bytes);case n.GENERALSTRING:return a.parseGeneralString(this.bytes);case n.UTCTIME:return a.parseUTCTime(this.bytes);case n.GENERALIZEDTIME:return a.parseGeneralizedTime(this.bytes);default:return this.bytes}}validate(e,t={}){if(this.class!==e.class)return new Error(`ASN.1 object validate failure for ${e.name} : error class ${r[this.class]}`);if(!(Array.isArray(e.tag)?e.tag:[e.tag]).includes(this.tag))return new Error(`ASN.1 object validate failure for ${e.name}: error tag ${n[this.tag]}`);if(null!=e.capture&&(t[e.capture]=this),Array.isArray(e.value)){const r=this.mustCompound(e.name+" need compound ASN1 value");for(let n=0,i=0;n<e.value.length;n++)if(null!=r[i]){const s=r[i].validate(e.value[n],t);if(null==s)i++;else if(!0!==e.value[n].optional)return s}else if(!0!==e.value[n].optional)return new Error(`ASN.1 object validate failure for ${e.value[n].name}: not exists`)}else if(null!=e.value){const r=this.tag===n.BITSTRING?this.bytes.slice(1):this.bytes;return a.fromDER(r).validate(e.value,t)}return null}toJSON(){let e=this.value;return Array.isArray(e)&&(e=e.map(e=>e.toJSON())),{class:r[this.class],tag:this.class===r.UNIVERSAL?n[this.tag]:this.tag,value:e}}[i.inspect.custom](e,t){return t.depth<=2&&(t.depth=10),`<${this.constructor.name} ${i.inspect(this.toJSON(),t)}>`}}function o(e){for(const t of e){const e=t.charCodeAt(0);if(32!==e&&(e<48||e>57))return!1}return!0}function u(e){for(const t of e)if(t.charCodeAt(0)>=128)return!1;return!0}function c(e,t=10){const r=parseInt(e,t);if(Number.isNaN(r))throw new Error(`Invalid numeric string "${e}" in radix ${t}.`);return r}t.ASN1=a}));s(R);R.Class,R.Tag,R.BitString,R.ASN1;var U=a((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.BufferVisitor=T.BufferVisitor,t.PEM=C.PEM,t.ASN1=R.ASN1,t.Class=R.Class,t.Tag=R.Tag,t.BitString=R.BitString}));s(U);U.BufferVisitor,U.PEM,U.ASN1,U.Class,U.Tag,U.BitString;var O=a((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.publicKeyValidator={name:"PublicKeyInfo",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,capture:"publicKeyInfo",value:[{name:"PublicKeyInfo.AlgorithmIdentifier",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"PublicKeyAlgorithmIdentifier.algorithm",class:U.Class.UNIVERSAL,tag:U.Tag.OID,capture:"publicKeyOID"}]},{name:"PublicKeyInfo.PublicKey",class:U.Class.UNIVERSAL,tag:U.Tag.BITSTRING,capture:"publicKey"}]},t.privateKeyValidator={name:"PrivateKeyInfo",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,capture:"privateKeyInfo",value:[{name:"PrivateKeyInfo.Version",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.AlgorithmIdentifier",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"PrivateKeyAlgorithmIdentifier.algorithm",class:U.Class.UNIVERSAL,tag:U.Tag.OID,capture:"privateKeyOID"}]},{name:"PrivateKeyInfo.PrivateKey",class:U.Class.UNIVERSAL,tag:U.Tag.OCTETSTRING,capture:"privateKey"}]};const n={name:"RSAPublicKey",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"RSAPublicKey.modulus",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"publicKeyModulus"},{name:"RSAPublicKey.exponent",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"publicKeyExponent"}]},s={name:"RSAPrivateKey",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"RSAPrivateKey.version",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyVersion"},{name:"RSAPrivateKey.modulus",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyModulus"},{name:"RSAPrivateKey.publicExponent",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyPublicExponent"},{name:"RSAPrivateKey.privateExponent",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyPrivateExponent"},{name:"RSAPrivateKey.prime1",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyPrime1"},{name:"RSAPrivateKey.prime2",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyPrime2"},{name:"RSAPrivateKey.exponent1",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyExponent1"},{name:"RSAPrivateKey.exponent2",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyExponent2"},{name:"RSAPrivateKey.coefficient",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"privateKeyCoefficient"}]},a=[v.getOID("X25519"),v.getOID("X448"),v.getOID("Ed25519"),v.getOID("Ed448")];class o{constructor(e){const r={},n=e.validate(t.publicKeyValidator,r);if(null!=n)throw new Error("Cannot read X.509 public key: "+n.message);this.oid=U.ASN1.parseOID(r.publicKeyOID.bytes),this.algo=v.getOIDName(this.oid),this._pkcs8=e,this._keyRaw=U.ASN1.parseBitString(r.publicKey.bytes).buf,this._finalKey=this._keyRaw,this._finalPEM=""}static fromPEM(e){const t=U.PEM.parse(e)[0];if(t.procType.includes("ENCRYPTED"))throw new Error("Could not convert public key from PEM, PEM is encrypted.");const r=U.ASN1.fromDER(t.body,!0);switch(t.type){case"PUBLIC KEY":return new o(r);case"RSA PUBLIC KEY":const e=U.ASN1.Seq([U.ASN1.Seq([U.ASN1.OID(v.getOID("rsaEncryption")),U.ASN1.Null()]),U.ASN1.BitString(r.DER)]);return new o(e);default:throw new Error("Could not convert public key from PEM, recommend PKCS#8 PEM")}}static addVerifier(e,t){if(""===(e=v.getOID(e)))throw new Error("Invalid object identifier: "+e);if(null!=o._verifiers[e])throw new Error(`Verifier ${e} exists`);o._verifiers[e]=t}get keyRaw(){return this._finalKey}verify(e,t,n){const i=o._verifiers[this.oid];if(null!=i){const s=r.createHash(n).update(e).digest();return i.call(this,s,t)}const s=r.createVerify(n);return s.update(e),s.verify(this.toPEM(),t)}getFingerprint(e,t="PublicKey"){let n;switch(t){case"PublicKeyInfo":n=this._pkcs8.DER;break;case"PublicKey":n=this._keyRaw;break;default:throw new Error(`Unknown fingerprint type "${t}".`)}const i=r.createHash(e);return i.update(n),i.digest()}toASN1(){return this._pkcs8}toDER(){return this._pkcs8.DER}toPEM(){return""===this._finalPEM&&(this._finalPEM=new U.PEM("PUBLIC KEY",this._pkcs8.DER).toString()),this._finalPEM}toJSON(){return{oid:this.oid,algo:this.algo,publicKey:this._keyRaw}}[i.inspect.custom](e,t){return`<${this.constructor.name} ${i.inspect(this.toJSON(),t)}>`}}o._verifiers=Object.create(null),t.PublicKey=o;class u{constructor(e){const r=Object.create(null),n=e.validate(t.privateKeyValidator,r);if(null!=n)throw new Error("Cannot read X.509 private key: "+n.message);if(this.version=U.ASN1.parseIntegerNum(r.privateKeyVersion.bytes)+1,this.oid=U.ASN1.parseOID(r.privateKeyOID.bytes),this.algo=v.getOIDName(this.oid),this._pkcs8=e,this._keyRaw=r.privateKey.bytes,this._publicKeyRaw=null,this._finalKey=this._keyRaw,this._finalPEM="",a.includes(this.oid))if(this._finalKey=this._keyRaw=U.ASN1.parseDER(this._keyRaw,U.Class.UNIVERSAL,U.Tag.OCTETSTRING).bytes,"***********"===this.oid){const e=w.sign.keyPair.fromSeed(this._keyRaw);this._publicKeyRaw=Buffer.from(e.publicKey),this._finalKey=Buffer.from(e.secretKey)}else if(2===this.version)for(const t of e.mustCompound())t.class===U.Class.CONTEXT_SPECIFIC&&1===t.tag&&(this._publicKeyRaw=U.ASN1.parseBitString(t.bytes).buf,this._finalKey=Buffer.concat([this._keyRaw,this._publicKeyRaw]))}static fromPEM(e){const t=U.PEM.parse(e)[0];if(t.procType.includes("ENCRYPTED"))throw new Error("Could not convert private key from PEM, PEM is encrypted.");let r=U.ASN1.fromDER(t.body,!0);switch(t.type){case"PRIVATE KEY":return new u(r);case"RSA PRIVATE KEY":return r=U.ASN1.Seq([r.value[0],U.ASN1.Seq([U.ASN1.OID(v.getOID("rsaEncryption")),U.ASN1.Null()]),new U.ASN1(U.Class.UNIVERSAL,U.Tag.OCTETSTRING,r.DER)]),new u(r);default:throw new Error("Could not convert private key from PEM, recommend PKCS#8 PEM")}}static addSigner(e,t){if(""===(e=v.getOID(e)))throw new Error("Invalid object identifier: "+e);if(null!=u._signers[e])throw new Error(`Signer ${e} exists`);u._signers[e]=t}get keyRaw(){return this._finalKey}get publicKeyRaw(){return this._publicKeyRaw}sign(e,t){const n=u._signers[this.oid];if(null!=n){const i=r.createHash(t).update(e).digest();return n.call(this,i)}const i=r.createSign(t);return i.update(e),i.sign(this.toPEM())}toASN1(){return this._pkcs8}toDER(){return this._pkcs8.DER}toPEM(){return""===this._finalPEM&&(this._finalPEM=new U.PEM("PRIVATE KEY",this._pkcs8.DER).toString()),this._finalPEM}toJSON(){return{version:this.version,oid:this.oid,algo:this.algo,privateKey:this._keyRaw,publicKey:this._publicKeyRaw}}[i.inspect.custom](e,t){return`<${this.constructor.name} ${i.inspect(this.toJSON(),t)}>`}}u._signers=Object.create(null),t.PrivateKey=u;class c extends o{static fromPublicKey(e){return new c(e.toASN1())}constructor(e){if(super(e),v.getOID(this.oid)!==v.getOID("rsaEncryption"))throw new Error("Invalid RSA public key, unknown OID: "+this.oid);const t=Object.create(null);this._pkcs1=U.ASN1.fromDER(this._keyRaw,!0);const r=this._pkcs1.validate(n,t);if(null!=r)throw new Error("Cannot read RSA public key: "+r.message);this.modulus=U.ASN1.parseIntegerStr(t.publicKeyModulus.bytes),this.exponent=U.ASN1.parseIntegerNum(t.publicKeyExponent.bytes)}toASN1(){return this._pkcs1}toDER(){return this._keyRaw}toPEM(){return""===this._finalPEM&&(this._finalPEM=new U.PEM("RSA PUBLIC KEY",this._keyRaw).toString()),this._finalPEM}toPublicKeyPEM(){return new U.PEM("PUBLIC KEY",this._pkcs8.DER).toString()}toJSON(){return{oid:this.oid,algo:this.algo,modulus:f(this.modulus),exponent:this.exponent}}[i.inspect.custom](e,t){return`<${this.constructor.name} ${i.inspect(this.toJSON(),t)}>`}}t.RSAPublicKey=c;class l extends u{static fromPrivateKey(e){return new l(e.toASN1())}constructor(e){if(super(e),v.getOID(this.oid)!==v.getOID("rsaEncryption"))throw new Error("Invalid RSA private key, unknown OID: "+this.oid);const t=Object.create(null);this._pkcs1=U.ASN1.fromDER(this._keyRaw,!0);const r=this._pkcs1.validate(s,t);if(null!=r)throw new Error("Cannot read RSA private key: "+r.message);this.publicExponent=U.ASN1.parseIntegerNum(t.privateKeyPublicExponent.bytes),this.privateExponent=U.ASN1.parseIntegerStr(t.privateKeyPrivateExponent.bytes),this.modulus=U.ASN1.parseIntegerStr(t.privateKeyModulus.bytes),this.prime1=U.ASN1.parseIntegerStr(t.privateKeyPrime1.bytes),this.prime2=U.ASN1.parseIntegerStr(t.privateKeyPrime2.bytes),this.exponent1=U.ASN1.parseIntegerStr(t.privateKeyExponent1.bytes),this.exponent2=U.ASN1.parseIntegerStr(t.privateKeyExponent2.bytes),this.coefficient=U.ASN1.parseIntegerStr(t.privateKeyCoefficient.bytes)}toASN1(){return this._pkcs1}toDER(){return this._keyRaw}toPEM(){return""===this._finalPEM&&(this._finalPEM=new U.PEM("RSA PRIVATE KEY",this._keyRaw).toString()),this._finalPEM}toPrivateKeyPEM(){return new U.PEM("PRIVATE KEY",this._pkcs8.DER).toString()}toJSON(){return{version:this.version,oid:this.oid,algo:this.algo,publicExponent:this.publicExponent,privateExponent:f(this.privateExponent),modulus:f(this.modulus),prime1:f(this.prime1),prime2:f(this.prime2),exponent1:f(this.exponent1),exponent2:f(this.exponent2),coefficient:f(this.coefficient)}}[i.inspect.custom](e,t){return`<${this.constructor.name} ${i.inspect(this.toJSON(),t)}>`}}function f(e){return e.length%8!=0&&e.startsWith("00")?e.slice(2):e}t.RSAPrivateKey=l,o.addVerifier(v.getOID("Ed25519"),(function(e,t){return w.sign.detached.verify(e,t,this.keyRaw)})),u.addSigner(v.getOID("Ed25519"),(function(e){const t=this.keyRaw;if(64!==t.length)throw new Error("Invalid signing key.");return Buffer.from(w.sign.detached(e,t))}))}));s(O);O.publicKeyValidator,O.privateKeyValidator,O.PublicKey,O.PrivateKey,O.RSAPublicKey,O.RSAPrivateKey;var P=a((function(e,t){Object.defineProperty(t,"__esModule",{value:!0});const n=Object.create(null);n.CN=v.getOID("commonName"),n.commonName="CN",n.C=v.getOID("countryName"),n.countryName="C",n.L=v.getOID("localityName"),n.localityName="L",n.ST=v.getOID("stateOrProvinceName"),n.stateOrProvinceName="ST",n.O=v.getOID("organizationName"),n.organizationName="O",n.OU=v.getOID("organizationalUnitName"),n.organizationalUnitName="OU",n.E=v.getOID("emailAddress"),n.emailAddress="E";const s={name:"Certificate",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,capture:"tbsCertificate",value:[{name:"Certificate.TBSCertificate.version",class:U.Class.CONTEXT_SPECIFIC,tag:U.Tag.NONE,optional:!0,value:[{name:"Certificate.TBSCertificate.version.integer",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"certVersion"}]},{name:"Certificate.TBSCertificate.serialNumber",class:U.Class.UNIVERSAL,tag:U.Tag.INTEGER,capture:"certSerialNumber"},{name:"Certificate.TBSCertificate.signature",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate.signature.algorithm",class:U.Class.UNIVERSAL,tag:U.Tag.OID,capture:"certinfoSignatureOID"},{name:"Certificate.TBSCertificate.signature.parameters",class:U.Class.UNIVERSAL,tag:U.Tag.OCTETSTRING,optional:!0,capture:"certinfoSignatureParams"}]},{name:"Certificate.TBSCertificate.issuer",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,capture:"certIssuer"},{name:"Certificate.TBSCertificate.validity",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate.validity.notBefore",class:U.Class.UNIVERSAL,tag:[U.Tag.UTCTIME,U.Tag.GENERALIZEDTIME],capture:"certValidityNotBefore"},{name:"Certificate.TBSCertificate.validity.notAfter",class:U.Class.UNIVERSAL,tag:[U.Tag.UTCTIME,U.Tag.GENERALIZEDTIME],capture:"certValidityNotAfter"}]},{name:"Certificate.TBSCertificate.subject",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,capture:"certSubject"},O.publicKeyValidator,{name:"Certificate.TBSCertificate.issuerUniqueID",class:U.Class.CONTEXT_SPECIFIC,tag:U.Tag.BOOLEAN,optional:!0,value:[{name:"Certificate.TBSCertificate.issuerUniqueID.id",class:U.Class.UNIVERSAL,tag:U.Tag.BITSTRING,capture:"certIssuerUniqueId"}]},{name:"Certificate.TBSCertificate.subjectUniqueID",class:U.Class.CONTEXT_SPECIFIC,tag:U.Tag.INTEGER,optional:!0,value:[{name:"Certificate.TBSCertificate.subjectUniqueID.id",class:U.Class.UNIVERSAL,tag:U.Tag.BITSTRING,capture:"certSubjectUniqueId"}]},{name:"Certificate.TBSCertificate.extensions",class:U.Class.CONTEXT_SPECIFIC,tag:U.Tag.BITSTRING,capture:"certExtensions",optional:!0}]},{name:"Certificate.signatureAlgorithm",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"Certificate.signatureAlgorithm.algorithm",class:U.Class.UNIVERSAL,tag:U.Tag.OID,capture:"certSignatureOID"},{name:"Certificate.TBSCertificate.signature.parameters",class:U.Class.UNIVERSAL,tag:U.Tag.OCTETSTRING,optional:!0,capture:"certSignatureParams"}]},{name:"Certificate.signatureValue",class:U.Class.UNIVERSAL,tag:U.Tag.BITSTRING,capture:"certSignature"}]};class a{constructor(){this.attributes=[],this.uniqueId=null}get commonName(){return this.getFieldValue("commonName")}get organizationName(){return this.getFieldValue("organizationName")}get organizationalUnitName(){return this.getFieldValue("organizationalUnitName")}get countryName(){return this.getFieldValue("countryName")}get localityName(){return this.getFieldValue("localityName")}get serialName(){return this.getFieldValue("serialName")}getHash(){const e=r.createHash("sha1");for(const t of this.attributes)e.update(t.oid),e.update(t.value);return e.digest()}getField(e){for(const t of this.attributes)if(e===t.oid||e===t.name||e===t.shortName)return t;return null}addField(e){g([e]),this.attributes.push(e)}setAttrs(e){g(e),this.attributes=e}toJSON(){const e={};for(const t of this.attributes){const r=t.shortName;"string"==typeof r&&""!==r&&(e[r]=t.value)}return e.uniqueId=this.uniqueId,e.attributes=this.attributes,e}getFieldValue(e){const t=this.getField(e);return null!=t?t.value:""}}t.DistinguishedName=a;class o{static fromPEMs(e){const t=[],r=U.PEM.parse(e);for(const e of r){if("CERTIFICATE"!==e.type&&"X509 CERTIFICATE"!==e.type&&"TRUSTED CERTIFICATE"!==e.type)throw new Error("Could not convert certificate from PEM: invalid type");if(e.procType.includes("ENCRYPTED"))throw new Error("Could not convert certificate from PEM: PEM is encrypted.");const r=U.ASN1.fromDER(e.body);t.push(new o(r))}if(0===t.length)throw new Error("No Certificate");return t}static fromPEM(e){return o.fromPEMs(e)[0]}constructor(e){const t=Object.create(null),r=e.validate(s,t);if(null!=r)throw new Error("Cannot read X.509 certificate: "+r.message);if(this.raw=e.DER,this.version=null==t.certVersion?0:U.ASN1.parseIntegerNum(t.certVersion.bytes)+1,this.serialNumber=U.ASN1.parseIntegerStr(t.certSerialNumber.bytes),this.signatureOID=U.ASN1.parseOID(t.certSignatureOID.bytes),this.signatureAlgorithm=v.getOIDName(this.signatureOID),this.infoSignatureOID=U.ASN1.parseOID(t.certinfoSignatureOID.bytes),this.signature=U.ASN1.parseBitString(t.certSignature.bytes).buf,this.validFrom=U.ASN1.parseTime(t.certValidityNotBefore.tag,t.certValidityNotBefore.bytes),this.validTo=U.ASN1.parseTime(t.certValidityNotAfter.tag,t.certValidityNotAfter.bytes),this.issuer=new a,this.issuer.setAttrs(p(t.certIssuer)),null!=t.certIssuerUniqueId&&(this.issuer.uniqueId=U.ASN1.parseBitString(t.certIssuerUniqueId.bytes)),this.subject=new a,this.subject.setAttrs(p(t.certSubject)),null!=t.certSubjectUniqueId&&(this.subject.uniqueId=U.ASN1.parseBitString(t.certSubjectUniqueId.bytes)),this.extensions=[],this.subjectKeyIdentifier="",this.authorityKeyIdentifier="",this.ocspServer="",this.issuingCertificateURL="",this.isCA=!1,this.maxPathLen=-1,this.basicConstraintsValid=!1,this.keyUsage=0,this.dnsNames=[],this.emailAddresses=[],this.ipAddresses=[],this.uris=[],null!=t.certExtensions){this.extensions=function(e){const t=[];for(const r of e.mustCompound())for(const e of r.mustCompound())t.push(u(e));return t}(t.certExtensions);for(const e of this.extensions)if("string"==typeof e.subjectKeyIdentifier&&(this.subjectKeyIdentifier=e.subjectKeyIdentifier),"string"==typeof e.authorityKeyIdentifier&&(this.authorityKeyIdentifier=e.authorityKeyIdentifier),"string"==typeof e.authorityInfoAccessOcsp&&(this.ocspServer=e.authorityInfoAccessOcsp),"string"==typeof e.authorityInfoAccessIssuers&&(this.issuingCertificateURL=e.authorityInfoAccessIssuers),"boolean"==typeof e.basicConstraintsValid&&(this.isCA=e.isCA,this.maxPathLen=e.maxPathLen,this.basicConstraintsValid=e.basicConstraintsValid),"number"==typeof e.keyUsage&&(this.keyUsage=e.keyUsage),Array.isArray(e.altNames))for(const t of e.altNames)null!=t.dnsName&&this.dnsNames.push(t.dnsName),null!=t.email&&this.emailAddresses.push(t.email),null!=t.ip&&this.ipAddresses.push(t.ip),null!=t.uri&&this.uris.push(t.uri)}this.publicKey=new O.PublicKey(t.publicKeyInfo),this.publicKeyRaw=this.publicKey.toDER(),this.tbsCertificate=t.tbsCertificate}getExtension(e,t=""){for(const r of this.extensions)if(e===r.oid||e===r.name)return""===t?r:r[t];return null}checkSignature(e){if(3===this.version&&!this.basicConstraintsValid||this.basicConstraintsValid&&!this.isCA)return new Error("The parent constraint violation error");if(!0!==this.getExtension("keyUsage","keyCertSign"))return new Error("The parent constraint violation error");if(!e.isIssuer(this))return new Error("The parent certificate did not issue the given child certificate");const t=function(e){switch(v.getOIDName(e)){case"sha1WithRsaEncryption":return"sha1";case"md5WithRsaEncryption":return"md5";case"sha256WithRsaEncryption":return"sha256";case"sha384WithRsaEncryption":return"sha384";case"sha512WithRsaEncryption":return"sha512";case"RSASSA-PSS":return"sha256";case"ecdsaWithSha1":return"sha1";case"ecdsaWithSha256":return"sha256";case"ecdsaWithSha384":return"sha384";case"ecdsaWithSha512":return"sha512";case"dsaWithSha1":return"sha1";case"dsaWithSha256":return"sha256";default:return""}}(e.signatureOID);if(""===t)return new Error("Unknown child signature OID.");return!1===this.publicKey.verify(e.tbsCertificate.DER,e.signature,t)?new Error("Child signature not matched"):null}isIssuer(e){return this.issuer.getHash().equals(e.subject.getHash())}verifySubjectKeyIdentifier(){return this.publicKey.getFingerprint("sha1","PublicKey").toString("hex")===this.subjectKeyIdentifier}toJSON(){const e={};for(const t of Object.keys(this))e[t]=y(this[t]);return delete e.tbsCertificate,e}[i.inspect.custom](e,t){return t.depth<=2&&(t.depth=10),`<${this.constructor.name} ${i.inspect(this.toJSON(),t)}>`}}function u(e){const t={};switch(t.oid=U.ASN1.parseOID(e.value[0].bytes),t.critical=!1,e.value[1].tag===U.Tag.BOOLEAN?(t.critical=U.ASN1.parseBool(e.value[1].bytes),t.value=e.value[2].bytes):t.value=e.value[1].bytes,t.name=v.getOIDName(t.oid),t.name){case"keyUsage":!function(e){const t=U.ASN1.parseBitString(U.ASN1.fromDER(e.value).bytes);let r=0,n=0;e.keyUsage=0;for(let r=0;r<9;r++)0!==t.at(r)&&(e.keyUsage|=1<<r);t.buf.length>0&&(r=t.buf[0],n=t.buf.length>1?t.buf[1]:0);e.digitalSignature=128==(128&r),e.nonRepudiation=64==(64&r),e.keyEncipherment=32==(32&r),e.dataEncipherment=16==(16&r),e.keyAgreement=8==(8&r),e.keyCertSign=4==(4&r),e.cRLSign=2==(2&r),e.encipherOnly=1==(1&r),e.decipherOnly=128==(128&n)}(t);break;case"basicConstraints":!function(e){const t=U.ASN1.fromDER(e.value).mustCompound();t.length>0&&t[0].tag===U.Tag.BOOLEAN?e.isCA=U.ASN1.parseBool(t[0].bytes):e.isCA=!1;let r=null;t.length>0&&t[0].tag===U.Tag.INTEGER?r=t[0].bytes:t.length>1&&(r=t[1].bytes);e.maxPathLen=null!==r?U.ASN1.parseInteger(r):-1;e.basicConstraintsValid=!0}(t);break;case"extKeyUsage":!function(e){const t=U.ASN1.fromDER(e.value).mustCompound();for(const r of t)e[v.getOIDName(U.ASN1.parseOID(r.bytes))]=!0}(t);break;case"nsCertType":!function(e){const t=U.ASN1.parseBitString(U.ASN1.fromDER(e.value).bytes);let r=0;t.buf.length>0&&(r=t.buf[0]);e.client=128==(128&r),e.server=64==(64&r),e.email=32==(32&r),e.objsign=16==(16&r),e.reserved=8==(8&r),e.sslCA=4==(4&r),e.emailCA=2==(2&r),e.objCA=1==(1&r)}(t);break;case"subjectAltName":case"issuerAltName":c(t);break;case"subjectKeyIdentifier":!function(e){const t=U.ASN1.parseDERWithTemplate(e.value,l);e.subjectKeyIdentifier=t.subjectKeyIdentifier.bytes.toString("hex")}(t);break;case"authorityKeyIdentifier":!function(e){const t=U.ASN1.parseDERWithTemplate(e.value,f);e.authorityKeyIdentifier=t.authorityKeyIdentifier.bytes.toString("hex")}(t);break;case"authorityInfoAccess":!function(e){const t=U.ASN1.parseDERWithTemplate(e.value,h);null!=t.authorityInfoAccessOcsp&&(e.authorityInfoAccessOcsp=t.authorityInfoAccessOcsp.bytes.toString());null!=t.authorityInfoAccessIssuers&&(e.authorityInfoAccessIssuers=t.authorityInfoAccessIssuers.bytes.toString())}(t)}return t}function c(e){e.altNames=[];const t=U.ASN1.fromDER(e.value).mustCompound();for(const r of t){const t={tag:r.tag,value:r.bytes};switch(e.altNames.push(t),r.tag){case 1:t.email=r.bytes.toString();break;case 2:t.dnsName=r.bytes.toString();break;case 6:t.uri=r.bytes.toString();break;case 7:t.ip=v.bytesToIP(r.bytes);break;case 8:t.oid=U.ASN1.parseOID(r.bytes)}}}t.Certificate=o;const l={name:"subjectKeyIdentifier",class:U.Class.UNIVERSAL,tag:U.Tag.OCTETSTRING,capture:"subjectKeyIdentifier"};const f={name:"authorityKeyIdentifier",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"authorityKeyIdentifier.value",class:U.Class.CONTEXT_SPECIFIC,tag:U.Tag.NONE,capture:"authorityKeyIdentifier"}]};const h={name:"authorityInfoAccess",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,value:[{name:"authorityInfoAccess.authorityInfoAccessOcsp",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,optional:!0,value:[{name:"authorityInfoAccess.authorityInfoAccessOcsp.oid",class:U.Class.UNIVERSAL,tag:U.Tag.OID},{name:"authorityInfoAccess.authorityInfoAccessOcsp.value",class:U.Class.CONTEXT_SPECIFIC,tag:U.Tag.OID,capture:"authorityInfoAccessOcsp"}]},{name:"authorityInfoAccess.authorityInfoAccessIssuers",class:U.Class.UNIVERSAL,tag:U.Tag.SEQUENCE,optional:!0,value:[{name:"authorityInfoAccess.authorityInfoAccessIssuers.oid",class:U.Class.UNIVERSAL,tag:U.Tag.OID},{name:"authorityInfoAccess.authorityInfoAccessIssuers.value",class:U.Class.CONTEXT_SPECIFIC,tag:U.Tag.OID,capture:"authorityInfoAccessIssuers"}]}]};function g(e){for(const t of e){if(null!=t.name&&""!==t.name||(null!=t.oid&&(t.name=v.getOIDName(t.oid)),""===t.name&&null!=t.shortName&&(t.name=v.getOIDName(n[t.shortName]))),null==t.oid||""===t.oid){if(""===t.name)throw new Error("Attribute oid not specified.");t.oid=v.getOID(t.name)}if(null!=t.shortName&&""!==t.shortName||(t.shortName=null==n[t.name]?"":n[t.name]),null==t.value)throw new Error("Attribute value not specified.")}}function p(e){const t=[];for(const i of e.mustCompound())for(const e of i.mustCompound()){const i=e.mustCompound(),s={};s.oid=U.ASN1.parseOID(i[0].bytes),s.value=i[1].value,s.valueTag=i[1].tag,s.name=v.getOIDName(s.oid),s.shortName=(r=s.name,null==n[r]?"":n[r]),t.push(s)}var r;return t}function y(e){return null==e||e instanceof Buffer||"function"!=typeof e.toJSON?e:e.toJSON()}}));s(P);P.DistinguishedName,P.Certificate;var _=a((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.bytesFromIP=v.bytesFromIP,t.bytesToIP=v.bytesToIP,t.getOID=v.getOID,t.getOIDName=v.getOIDName,t.PublicKey=O.PublicKey,t.PrivateKey=O.PrivateKey,t.RSAPublicKey=O.RSAPublicKey,t.RSAPrivateKey=O.RSAPrivateKey,t.Certificate=P.Certificate,t.DistinguishedName=P.DistinguishedName}));s(_);_.bytesFromIP,_.bytesToIP,_.getOID,_.getOIDName,_.PublicKey,_.PrivateKey,_.RSAPublicKey,_.RSAPrivateKey,_.Certificate,_.DistinguishedName;var B,D=(B=A)&&B.default||B,K=a((function(e,n){function i(e,t=!1){if("string"==typeof e&&(e=Buffer.from(e)),t)return function(e){const t=_.Certificate.fromPEMs(e);let r="";return t.forEach(e=>{if(e.signatureOID.startsWith("1.2.840.113549.1.1")){const t=s(e);0===r.length?r+=t:r+="_"+t}}),r}(e);return s(_.Certificate.fromPEM(e))}function s(e){const{issuer:t,serialNumber:n}=e,i=t.attributes.reduceRight((e,t)=>{const{shortName:r,value:n}=t;return`${e}${r}=${n},`},"").slice(0,-1),s=new D(n,16).toString(10);return r.createHash("md5").update(i+s,"utf8").digest("hex")}Object.defineProperty(n,"__esModule",{value:!0}),n.loadPublicKey=n.loadPublicKeyFromPath=n.getSNFromPath=n.getSN=void 0,n.loadPublicKeyFromPath=function(e){const r=t.readFileSync(e);return _.Certificate.fromPEM(r).publicKeyRaw.toString("base64")},n.loadPublicKey=function(e){return"string"==typeof e&&(e=Buffer.from(e)),_.Certificate.fromPEM(e).publicKeyRaw.toString("base64")},n.getSNFromPath=function(e,r=!1){return i(t.readFileSync(e),r)},n.getSN=i}));s(K);K.loadPublicKey,K.loadPublicKeyFromPath,K.getSNFromPath,K.getSN;let L={wxpay:{}};L.wxpay.getSN=function(e){return"string"==typeof e&&(e=Buffer.from(e)),_.Certificate.fromPEM(e).serialNumber.toUpperCase()},L.alipay={},L.alipay.getSN=function(e,t){return K.getSN(e,t)};var M=L;module.exports=M;
