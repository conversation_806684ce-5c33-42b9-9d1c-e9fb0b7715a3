'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/pub/system 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let info = await vk.baseDao.findByWhereJson({
			dbName: "system-data",
			whereJson: {
				type,
			},
		});
		res.data = info
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}