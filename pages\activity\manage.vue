<template>
  <view class="page-container">
    <t-navbar title="管理球局" />

    <!-- Settings Section -->
    <view class="settings-section" v-if="activityInfo">
      <view class="section-title">活动设置</view>
      <view class="settings-list">
        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-label">是否审核</text>
            <text class="setting-desc">开启后需要审核才能加入活动</text>
          </view>
          <wd-switch v-model="activityInfo.apply" size="40rpx" @change="handleSettingChange($event, 'apply')" active-color="#0171BC" />
        </view>

        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-label">是否公开</text>
            <text class="setting-desc">开启后活动将在公开列表中显示</text>
          </view>
          <wd-switch v-model="activityInfo.open" size="40rpx" @change="handleSettingChange($event, 'open')" active-color="#0171BC" />
        </view>

        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-label">允许代接</text>
            <text class="setting-desc">开启后允许用户代替他人加入活动</text>
          </view>
          <wd-switch v-model="activityInfo.help_join" size="40rpx" @change="handleSettingChange($event, 'help_join')" active-color="#0171BC" />
        </view>

        <view class="setting-item">
          <view class="setting-info">
            <text class="setting-label">订阅通知</text>
            <text class="setting-desc">开启后将接收活动相关通知</text>
          </view>
          <wd-switch v-model="activityInfo.notice" size="40rpx" @change="handleSettingChange($event, 'notice')" active-color="#0171BC" />
        </view>
      </view>
    </view>

    <!-- Joined Members Section -->
    <view v-if="joinedMembers" class="members-section">
      <view class="section-header">
        <text class="section-title">已加入成员</text>
        <text class="member-count">({{ joinedMembers.length }}人)</text>
      </view>
      <view class="members-grid">
        <t-user-item
          v-for="member in joinedMembers"
          :key="member._id"
          :data="member.user_info"
          :type="member.type"
          :name="member.nickname"
          width="140rpx"
          @click="handleUserAction(member)"
        />
      </view>
      <t-empty v-if="joinedMembers.length === 0" top="0" data="data" text="暂无成员" />
    </view>

    <!-- Applying Members Section -->
    <view v-if="applyingMembers" class="members-section">
      <view class="section-header">
        <text class="section-title">申请成员</text>
        <text class="member-count">({{ applyingMembers.length }}人)</text>
      </view>
      <view class="members-grid">
        <t-user-item
          v-for="member in applyingMembers"
          :key="member._id"
          :data="member.user_info"
          :type="member.type"
          :name="member.nickname"
          width="140rpx"
          @click="handleUserAction(member)"
        />
      </view>
      <t-empty v-if="applyingMembers.length === 0" top="0" data="data" text="暂无申请" />
    </view>

    <!-- Dissolve Button -->
    <t-bottom>
      <view class="action-section">
        <wd-button type="error" @click="showDissolveDialog"> 解散球局 </wd-button>
      </view>
    </t-bottom>

    <!-- Member Action Popup -->
    <wd-action-sheet v-model="memberPopupVisible" :actions="actions" @close="memberPopupVisible = false" @select="actionsSelect" />

    <!-- Dissolve Reason Popup -->
    <wd-popup v-model="dissolvePopupVisible" custom-style="border-radius:24rpx">
      <view class="dissolve-content">
        <text class="dissolve-title">解散原因</text>
        <wd-checkbox-group v-model="selectedReasons" direction="vertical" inline>
          <wd-checkbox v-for="reason in reasonOptions" shape="square" :key="reason" :model-value="reason" class="reason-item">
            {{ reason }}
          </wd-checkbox>
        </wd-checkbox-group>

        <view class="dissolve-actions">
          <wd-button type="info" @click="cancelDissolve" class="dissolve-action-btn"> 取消 </wd-button>
          <wd-button type="error" @click="confirmDissolve" class="dissolve-action-btn"> 确定解散 </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script>
import tEmpty from "../../components/t-ui/t-empty.vue";
export default {
  components: { tEmpty },
  name: "ActivityManage",

  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),

      // Activity data
      activityId: null,
      activityInfo: null,

      // Members data
      joinedMembers: null,
      applyingMembers: null,

      // Popup states
      memberPopupVisible: false,
      dissolvePopupVisible: false,

      // Selected data
      selectedUser: {},
      selectedReasons: [],

      // Options
      reasonOptions: ["天气原因", "场地原因", "人数原因", "时间原因", "交通原因", "需重新编辑原因", "个人原因"],
    };
  },

  computed: {
    // 可操作事项
    actions() {
      let ary = [];
      if (this.selectedUser?.user_id !== this.userInfo._id) ary.push({ name: "用户资料", key: "info" });
      if (this.selectedUser?.state === "applying") ary.push({ name: "同意加入", key: "agree", color: "#0171BC" });
      if (this.selectedUser?.state === "applying") ary.push({ name: "拒绝申请", key: "disagree", color: "#ff3030" });
      if (this.selectedUser?.state === "joined") ary.push({ name: "踢出活动", key: "kick", color: "#ff3030" });
      return ary;
    },
    // 是否可以帮退活动
    canExitHelp() {
      if (!this.selectedUser || !this.userInfo) return false;
      return this.userInfo._id === this.selectedUser.user_id && this.selectedUser.type === "help";
    },
  },
  onLoad(options) {
    if (!options.id) {
      vk.toast("活动信息有误！", "none", true, () => {
        vk.navigateBack();
      });
      return;
    }
    this.activityId = options.id;
    this.initializeData();
  },
  methods: {
    // 操作
    actionsSelect({ item }) {
      switch (item.key) {
        case "info":
          this.viewUser();
          break;
        case "agree":
          this.approveJoinRequest();
          break;
        case "kick":
        case "disagree":
          this.kickOutMember();
          break;
      }
    },
    // 初始化数据
    initializeData() {
      this.joinedMembers = null;
      this.applyingMembers = null;
      this.memberPopupVisible = false;
      this.fetchActivityData();
      this.fetchMembersData();
    },

    // 查看用户详情
    viewUserProfile() {
      vk.navigateTo(`/pages/user/detail?id=${this.selectedUser.user_info._id}`);
      this.memberPopupVisible = false;
    },

    // 踢出成员
    async kickOutMember() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/join_state/kh/kickOut",
          title: "请求中...",
          data: {
            id: this.selectedUser._id,
          },
        });

        this.memberPopupVisible = false;
        vk.toast(data.msg, "none", true, () => {
          this.initializeData();
          uni.$emit("activity-page-refresh");
          uni.$emit("activity-refresh");
        });
      } catch (error) {
        vk.toast("操作失败");
      }
    },
    // 显示解散对话框
    showDissolveDialog() {
      this.selectedReasons = [];
      this.dissolvePopupVisible = true;
    },

    // 取消解散
    cancelDissolve() {
      this.dissolvePopupVisible = false;
      this.selectedReasons = [];
    },

    // 确认解散球局
    confirmDissolve() {
      if (this.selectedReasons.length === 0) {
        vk.toast("请选择解散原因");
        return;
      }

      uni.showModal({
        title: "解散提示",
        content: "解散球局将通过平台的消息通知已加入的成员",
        success: async (res) => {
          if (res.confirm) {
            await this.executeDissolve();
          }
        },
      });
    },

    // 执行解散操作
    async executeDissolve() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/kh/dissolve",
          title: "请求中...",
          data: {
            id: this.activityId,
            reason: this.selectedReasons.join(","),
          },
        });

        this.dissolvePopupVisible = false;
        vk.toast(data.msg, "none", true, () => {
          vk.reLaunch("/pages/index/index");
        });
      } catch (error) {
        vk.toast("解散失败");
      }
    },
    // 同意加入申请
    approveJoinRequest() {
      if (this.activityInfo.max_num && this.joinedMembers.length === this.activityInfo.max_num - 1) {
        uni.showModal({
          title: "审批提醒",
          content: "您当前审批完此人后活动人数将满员，届时将会通知申请中的人员",
          success: async (res) => {
            if (res.confirm) {
              await this.updateMemberState();
            }
          },
        });
      } else {
        this.updateMemberState();
      }
    },

    // 更新成员状态
    async updateMemberState() {
      try {
        await vk.callFunction({
          url: "client/activity/join_state/kh/update",
          title: "请求中...",
          data: {
            id: this.selectedUser._id,
            user_id: this.selectedUser.user_id,
            state: "joined",
          },
        });

        this.memberPopupVisible = false;
        vk.toast("审批成功", "success", true, () => {
          uni.$emit("activity-page-refresh");
          uni.$emit("activity-refresh");
        });
        this.initializeData();
      } catch (error) {
        vk.toast("审批失败");
        this.initializeData();
      }
    },
    // 格式化水平等级
    formatLevel(level) {
      if (level) {
        return level.toFixed(1);
      } else {
        return "无";
      }
    },

    // 处理用户操作
    handleUserAction(userData) {
      this.selectedUser = userData;
      this.memberPopupVisible = true;
    },

    // 获取成员数据
    async fetchMembersData() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/kh/member",
          title: "请求中...",
          data: {
            id: this.activityId,
          },
        });

        const joined = data.rows.filter((item) => item.state === "joined");
        const applying = data.rows.filter((item) => item.state === "applying");

        this.joinedMembers = joined;
        this.applyingMembers = applying;
      } catch (error) {
        vk.toast("获取成员数据失败");
      }
    },

    // 处理设置变更
    async handleSettingChange({ value }, key) {
      try {
        const res = await vk.callFunction({
          url: "client/activity/kh/update",
          title: "请求中...",
          data: {
            id: this.activityId,
            [key]: value,
          },
        });

        vk.toast(res.msg, "none", true, () => {
          this.fetchActivityData();
          uni.$emit("activity-page-refresh");
          uni.$emit("activity-refresh");
        });
      } catch (error) {
        vk.toast("设置更新失败");
        // 恢复原值
        this.activityInfo[key] = !value;
      }
    },

    // 获取活动数据
    async fetchActivityData() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/pub/detail",
          title: "请求中...",
          data: {
            id: this.activityId,
          },
        });

        this.activityInfo = data.data;
      } catch (error) {
        vk.toast("获取活动数据失败");
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background: #f8f8f8;
  padding: 30rpx;
  padding-bottom: env(safe-area-inset-bottom);
  box-sizing: border-box;
}

// Settings Section
.settings-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.settings-list {
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.setting-info {
  flex: 1;

  .setting-label {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    display: block;
    margin-bottom: 8rpx;
  }

  .setting-desc {
    font-size: 24rpx;
    color: #999;
    line-height: 1.4;
  }
}

// Members Section
.members-section {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}

.section-header {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;

  .member-count {
    font-size: 24rpx;
    color: #0171bc;
    margin-left: 10rpx;
    font-weight: 500;
  }
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
}

// Action Section
.action-section {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

// Member Actions Popup
.member-actions {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .action-btn {
    width: 100%;
    height: 88rpx;
    font-size: 28rpx;
    border-radius: 16rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }
  }
}

// Dissolve Content
.dissolve-content {
  width: 90vw;
  padding: 40rpx;
  box-sizing: border-box;
}
.dissolve-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  text-align: center;
  margin-bottom: 20rpx;
}
.reason-item {
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.dissolve-actions {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin-top: 40rpx;

  .dissolve-action-btn {
    flex: 1;
    height: 88rpx;
    font-size: 28rpx;
    border-radius: 16rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.98);
    }
  }
}

// Animation
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.settings-section,
.members-section {
  animation: fadeIn 0.3s ease-out;
}
</style>
