<template>
  <view class="page-container">
    <t-navbar title="平台协议"></t-navbar>
    <view class="content">
      <wd-cell-group custom-class="agreement-group">
        <wd-cell title="用户协议" is-link @click="toUrl('/pages/public/agreement?type=user')"> </wd-cell>
        <wd-cell title="隐私协议" is-link @click="toUrl('/pages/public/agreement?type=privacy')"> </wd-cell>
      </wd-cell-group>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
    };
  },
  methods: {
    toUrl(url) {
      vk.navigateTo(url);
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 30rpx;

  :deep(.agreement-group) {
    border-radius: 24rpx;
    overflow: hidden;
  }

  .cell-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 30rpx;
  }
}
</style>
