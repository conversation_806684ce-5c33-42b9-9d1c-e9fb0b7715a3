<template>
	<view class="page-content">
		<t-navbar title="选择场地"></t-navbar>
		<view class="list">
			<view class="block" v-for="item in list" :key="item._id" @click="selectSite(item)">
				<image class="cover" :src="item.images[0]" mode="aspectFill"></image>
				<view class="name">{{item.name}}</view>
				<image v-show="item._id==select" class="checked"
					src="https://cdn.cometennis.cn/icon/check-on.svg"
					mode="aspectFill"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import mixins from '@/pages_club/mixins.js'
	export default {
		mixins: [mixins],
		data() {
			return {
				total: 0,
				list: [],
				pages: 1,
				select: "",
			}
		},
		onLoad(options) {
			this.select = options.id
			this.getData()
		},
		onReachBottom() {
			if (this.list.length >= total) return
			this.pages += 1
			this.getData()
		},
		methods: {
			selectSite(item) {
				uni.$emit('select-site', item)
				vk.navigateBack();
			},
			async getData() {
				let data = await vk.callFunction({
					url: 'client/club/site/kh/list',
					title: '请求中...',
					data: {
						club_id: this.club_id,
						pageIndex: this.pages
					},
				});
				this.total = data.total
				this.list.push(...data.rows)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-content {
		background-color: #f8f8f8;
		min-height: 100vh;
		padding: 30rpx;
		box-sizing: border-box;
	}

	.list {
		display: grid;
		grid-template-columns: 48% 48%;
		gap: 20rpx;
	}

	.block {
		display: flex;
		flex-direction: column;
		align-items: center;
		position: relative;
		background-color: #fff;
		border-radius: 22rpx;
		overflow: hidden;
		height: 300rpx;

		.cover {
			width: 100%;
			height: 208rpx;
		}

		.name {
			width: 90%;
			font-size: 28rpx;
			font-weight: bold;
			margin-top: 20rpx;
			text-align: center;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
		}

		.checked {
			width: 48rpx;
			height: 48rpx;
			position: absolute;
			right: 20rpx;
			top: 20rpx;
		}
	}
</style>