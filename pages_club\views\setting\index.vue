<template>
	<view class="page-content" v-if="info">
		<t-navbar title="信息设置"></t-navbar>
		<t-image-upload :count='1' label="logo" imageType="club" v-model="info.logo"></t-image-upload>
		<t-image-upload :count='1' label="主图" imageType="club" v-model="info.cover"></t-image-upload>
		<t-image-upload :count='9' label="轮播图" imageType="club" v-model="info.banner"></t-image-upload>
		<t-input label="名称" v-model="info.name" disabled @click="vk.toast('修改名称请联系平台客服','none')"></t-input>
		<t-input label="描述" v-model="info.desc" height="200" textarea></t-input>
		<t-image-upload :count='9' label="介绍图" imageType="club" v-model="info.images"></t-image-upload>
		<t-picker label="位置选择" mode="show" v-model="info.address_name" disabled></t-picker>
		<t-input label="详细地址" v-model="info.address" :maxLength="100" height="100" textarea></t-input>

		<t-switch label="加入审批" v-model="info.apply"></t-switch>

		<t-group title="营业时间" marginTop="20">
			<t-picker marginTop="0" v-model="info.startTime" mode="time" label="开始时间"></t-picker>
			<t-picker v-model="info.endTime" mode="time" label="结束时间"></t-picker>
		</t-group>

		<t-button color="#0171BC" text-color="#fff" :customStyle="{margin:'30rpx auto'}" @click="updateInfo">更新信息</t-button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: null,
				info: null,
			}
		},
		watch: {
			"$store.state.$club.info": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.info = vk.pubfn.deepClone(newVal);
				}
			}
		},
		async onLoad() {
			this.id = vk.getVuex('$club.id')
			try {
				let info = await vk.vuex.getters('$club/getInfo');
				this.info = vk.pubfn.deepClone(info);
			} catch (err) {
				console.log(err);
				setTimeout(() => {
					uni.navigateBack()
				}, 1500)
			}
		},
		methods: {
			async updateInfo() {
				let data = await vk.callFunction({
					url: 'client/club/kh/update',
					title: '请求中...',
					data: {
						...this.info
					},
				});
				vk.setVuex('$club.info', data.data)
				vk.toast(data.msg, 'none', 'success', () => {
					vk.navigateBack();
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-content {
		padding: 30rpx;
	}
</style>