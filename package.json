{"id": "vk-cloud-router", "displayName": "【开箱即用】vk-unicloud-router - 云函数路由模式开发框架 - 已集成uni-id 框架内置了众多API、工具包，为你的业务扫平障碍。", "version": "2.19.9", "description": "这是一个unicloud快速开发框架+项目模板（已包含核心库）支持URL化，众多现成API供你使用（登录、注册、短信、微信百度服务端API等等）为你的业务扫平障碍。内置小白也能轻松上手的数据库API。", "keywords": ["vk-unicloud-router", "云函数路由、云对象路由", "vk云开发", "内置uni-id、数据库baseDao", "企业级云开发框架"], "main": "main.js", "dependencies": {"chinese-workday": "^1.10.0", "dayjs": "^1.11.10", "lodash": "^4.17.21", "wot-design-uni": "^1.12.0"}, "devDependencies": {}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "author": "VK", "license": "MIT", "repository": "https://gitee.com/vk-uni/vk-uni-cloud-router", "name": "vk-unicloud-router", "engines": {"HBuilderX": "^3.1.2", "uni-app": "^4.36", "uni-app-x": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "370725567"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "", "type": "unicloud-template-project", "darkmode": "x", "i18n": "√", "widescreen": "x"}, "uni_modules": {"platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "√", "android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "√", "jd": "√", "harmony": "√", "qq": "√", "lark": "√"}, "quickapp": {"huawei": "√", "union": "√"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}