'use strict';

var vkmail;
try {
	vkmail = require('vk-mail');
} catch (err) {
	console.error("请先添加公共模块：vk-mail");
}

exports.main = async (event, context) => {
	let res = { code: 0, msg: "" };

	let {
		email, // 前端接收邮箱
		title,
		type,
		content,
	} = event.data;

	console.log(event.data);

	let emailConfig = {
		"host": "smtp.163.com",
		"port": 465,
		"secure": true,
		"auth": {
			"user": "<EMAIL>", // 发件人邮箱账号
			"pass": "NPjLKpXFjV2azBmL", // 账号授权码
		}
	};

	// 创建邮箱服务实例
	let emailService = vkmail.createTransport({
		"host": emailConfig.host,
		"port": emailConfig.port,
		"secure": emailConfig.secure, // use SSL
		"auth": emailConfig.auth
	});

	try {
		// 发送邮件
		let params = {
			"from": emailConfig.auth.user, // 邮件的发送者
			"to": email, // 邮件的接收者
			"cc": emailConfig.auth.user, // 由于邮件可能会被当成垃圾邮件，但只要把右键抄送给自己一份，就不会被当成垃圾邮件。
			"subject": title, // 邮件的标题
		}
		if (type == 'html') {
			params.html = content
		} else {
			params.text = content
		}
		res.sendMailRes = await emailService.sendMail(params);
		// 标记发送成功
		res.code = 0;
		res.msg = "ok";
		// 发送验证码成功后，通常需要设置验证码（写入数据库）
		// await uniID.setVerifyCode({ code, email, type });
	} catch (err) {
		res.code = -1;
		res.msg = "邮件发送失败";
		res.err = err;
	}
	return res;
};