const vkPay = require("vk-uni-pay");
"use strict";
module.exports = {
	/**
	 * 退出活动
	 * @url client/activity/kh/exit 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id, activity_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let activity = await vk.baseDao.findById({
			dbName: "activity-data",
			id: activity_id,
		});
		let info = await vk.baseDao.findById({
			dbName: "activity-join-data",
			id,
		});
		if (!info) return { code: -1, msg: "用户不在活动中" }
		// 验证活动是否已结束
		if (activity.end < Date.now()) return { msg: "活动已结束", code: -1 }
		if (activity.start < Date.now()) return { msg: "活动已开始，无法退出", code: -1 }

		// 开启处理退出活动事务
		const transaction = await vk.baseDao.startTransaction();
		try {
			await vk.baseDao.deleteById({
				db: transaction,
				dbName: "activity-join-data",
				id,
			});
			// 添加记录
			await vk.baseDao.add({
				db: transaction,
				dbName: "activity-record-data",
				dataJson: {
					user_id: info.user_id,
					activity_id: info.activity_id,
					type: "exit"
				}
			});
			// 发送平台消息
			if (activity.creator_id !== uid) {
				let title = `成员退出提醒`;
				let text = `用户${userInfo.nickname}退出了你发起的活动<${activity.name}>。`;
				await vk.baseDao.add({
					db: transaction,
					dbName: "user-news-data",
					dataJson: {
						accept_user: activity.creator_id,
						title,
						text,
						read_state: false,
						type: "info",
						link: `/pages/activity/detail?id=${activity._id}`,
					},
				});
			}
			res.msg = "操作成功!";
			// 提交事务
			await transaction.commit();
		} catch (err) {
			// 事务回滚
			return await vk.baseDao.rollbackTransaction({
				db: transaction,
				err
			});
		}

		// 验证活动是否是线上收款
		if (activity.cost_type !== 'none' && activity.cost_plan === 1) {
			let isRefund = false
			let refundFee;
			let refundDesc = "活动退出退款"
			// 验证活动是否已经在退款规则时间内
			let { endTime } = vk.pubfn.getHourOffsetStartAndEnd(activity.refund_hour, new Date());
			if (endTime > activity.start && info.state === 'joined') {
				// 检验退款金额
				if (activity.refund_money > 0) {
					isRefund = true
					refundFee = activity.refund_money * 100
					refundDesc = "活动规定时间内退出退款"
				}
			} else {
				isRefund = true
			}
			// 执行退款
			if (isRefund && info.out_trade_no) {
				let order_info = await vk.baseDao.findByWhereJson({
					dbName: "vk-pay-orders",
					whereJson: {
						out_trade_no: info.out_trade_no
					},
				});
				// 计算订单金额情况
				let balance = order_info.total_fee
				if (order_info.refund_fee) balance -= order_info.refund_fee
				// 计算应退金额
				let expense;
				if (!refundFee) {
					if (activity.cost_type === 'average') {
						expense = activity.cost / activity.min_num * 100
					} else if (activity.cost_type === 'fixed') {
						expense = activity.cost * 100
					}
				} else {
					expense = refundFee
				}
				if (balance - expense < 0) {
					refundRes = { code: -1, msg: "执行退款时出现异常，请联系客服处理" }
					return
				}
				refundRes = await vkPay.refund({
					out_trade_no: order_info.out_trade_no,
					refund_desc: refundDesc,
					refund_fee: expense
				});
			}
		}
		if (refundRes.code !== 0) return refundRes


		// 发送订阅消息通知
		if (activity.notice && activity.creator_id !== uid) {
			let user = await vk.baseDao.findById({
				dbName: "uni-id-users",
				id: activity.creator_id,
			});
			let start = vk.pubfn.timeFormat(activity.start, "yyyy-MM-dd hh:mm:ss");
			await vk.openapi.weixin.subscribeMessage.send({
				touser: user.wx_openid["mp-weixin"],
				template_id: "qULr23FpxNQTuWrghCj6gA_855wO6isiP-_IM1sCO1A",
				page: "pages/activity/detail?id=" + activity._id,
				data: {
					phrase1: {
						value: "用户退出",
					},
					thing2: {
						value: activity.name,
					},
					time3: {
						value: start,
					},
					thing5: {
						value: "请在小程序查看活动详情",
					},
				},
				miniprogram_state: "formal",
			});
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	},
};