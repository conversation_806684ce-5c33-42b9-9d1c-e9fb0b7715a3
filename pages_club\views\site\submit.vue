<template>
  <view>
    <t-navbar title="新增场地"></t-navbar>
    <view class="page-content">
      <t-image-upload label="上传场地图" imageType="club" :count="9" v-model="info.images"></t-image-upload>
      <t-input label="场地名" v-model="info.name"></t-input>
      <t-checkbox v-model="info.tags" label="场地类型" :options="tags"></t-checkbox>
      <t-group marginTop="20">
        <t-picker label="位置选择" mode="show" v-model="info.address_name" @click="selectPosition"></t-picker>
        <t-input marginTop="0" label="详细地址" v-model="info.address" :maxLength="100" height="100" textarea></t-input>
      </t-group>
      <t-button color="#0171BC" text-color="#fff" :customStyle="{ margin: '30rpx auto' }" @click="newSite">新增场地</t-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      info: {
        images: [],
        name: "",
        tags: [],
        location: "",
        address: "",
        address_name: "",
      },
      tags: ["室外场", "室内场", "移动棚", "红土场", "草地场", "硬地场"],
      club_info: null,
      club_id: null,
    };
  },
  watch: {
    "$store.state.$club.info": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal, oldVal) {
        this.info = newVal;
      },
    },
    "$store.state.$club.id": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal, oldVal) {
        this.club_id = newVal;
      },
    },
  },
  async onLoad() {
    this.club_id = vk.getVuex("$club.id");
    this.club_info = await vk.vuex.getters("$club/getInfo");
    this.info.location = this.club_info.location;
    this.info.address_name = this.club_info.address_name;
    this.info.address = this.club_info.address;
  },
  methods: {
    selectPosition() {
      uni.chooseLocation({
        success: (res) => {
          this.info.location = {
            latitude: res.latitude,
            longitude: res.longitude,
          };
          this.info.address = res.address + res.name;
          this.info.address_name = res.name;
        },
        fail: (res) => {
          console.log("error", res);
        },
      });
    },
    async newSite() {
      let data = await vk.callFunction({
        url: "client/club/site/kh/add",
        title: "请求中...",
        data: {
          club_id: this.club_id,
          ...this.info,
        },
      });
      uni.$emit("refresh-site");
      vk.toast(data.msg, "none", true, () => {
        vk.navigateBack();
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-content {
  padding: 30rpx;
}
</style>
