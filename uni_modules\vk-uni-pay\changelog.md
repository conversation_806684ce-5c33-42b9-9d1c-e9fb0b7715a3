## 1.13.2（2024-09-10）
* 【修复】微信支付v3调用查询支付状态接口时，可能会报 `undefined (reading 'total')` 的问题
* 【优化】支付宝转账支持 `payer_use_alias` 参数 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/transfer.html#%E5%8F%82%E6%95%B0)
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.13.1（2024-08-21）
* 【调整】为适配uni-id，支付宝小程序支付默认调整为密钥模式（密钥模式和证书模式都能用）
* 【优化】组件 `vk-uni-qrcode` 在 Vue3 的兼容性
* 【优化】加密算法在node18上的兼容性
* 【修复】其他已知问题
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.13.0（2024-06-28）
* 【重要】新增抖音支付 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/config.html#%E6%8A%96%E9%9F%B3%E6%94%AF%E4%BB%98)
* 【调整】支付宝查询余额接口的字段返回值为驼峰命名，用以兼容抖音支付的查询余额接口（如你已使用此接口，需要调整代码）[传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/queryAccountBalance.html)
* 【调整】vk-pay-orders表新增4个字段 `notify_url` `notify_path` `mch_id` `provider_pay_method` [传送门](https://vkdoc.fsq.pub/vk-uni-pay/db/vk-pay-orders.html)
* 【修复】微信支付v2报错提示不准确的问题
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.12.3（2024-06-07）
* 【修复】修复微信支付v2在url化请求时，无法获取app支付参数的问题
* 【修复】微信支付v3在外部浏览器不会跳转的问题
* 【修复】支付宝转账时，若account为支付宝userId的情况下，再传正确的real_name也会提示姓名和账户不匹配的问题
* 【优化】支付宝小程序支付新增productCode配置，可将值设定为 `JSAPI_PAY` 即可指定支付宝小程序使用新版jsapi接口发起支付
* 【优化】ios内购验证凭据时，若未通过，则尝试使用相反的配置再次验证一次
* 【优化】云函数的node.js版本统一调整为node16
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.12.2（2024-03-13）
* 【重要】新增微信小程序虚拟支付 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/wxpay-virtual.html)
* 【新增】查询支付宝账户余额 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/queryAccountBalance.html)
* 【优化】微信自带的PC浏览器发起支付时，自动识别为PC扫码支付（原先会识别为jsapi支付）
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.12.1（2024-01-10）
* 【优化】因支付宝小程序云不主动打印日志，故回调日志主动打印原始回调数据
* 【修复】已知问题
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.12.0（2023-12-12）
* 【优化】部署时支持选择支付宝小程序云
* 【修复】已知问题
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.11.4（2023-09-25）
* 【重要】支持VksPay个人支付通道（支持个人支付，详情联系Q：*********）
* 【修复】支付宝H5支付部分情况下会提示卖家不存在的bug
* 【优化】`createPayment` 接口新增 `time_expire` 参数，支持指定支付截至时间 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/createPayment.html#data-%E5%8F%82%E6%95%B0)
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.11.3（2023-09-06）
* 【重要】支持VksPay个人支付通道（支持个人支付，详情联系Q：*********）
* 【优化】VksPay微信支付完成后支持同步跳转到自定义页面
* 【优化】弹窗组件的z-index调整为998（因为H5下uni.showModal的z-index是999）
* 【优化】下单接口返回参数新增total_fee、platform
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.11.2（2023-08-18）
* 【重要】支持VksPay个人支付通道（支持个人支付，详情联系Q：*********）
* 【修复】ios内购支付时 `pay_date` 的值是字符串的bug
* 【修复】`vk-uni-pay-qrcode-popup` 组件无法在页面一进来就弹窗的bug
* 【优化】`this.$refs.vkPay.createPayment` 新增 `create` 事件，在支付订单创建成功还未发起支付时触发，此时若 `return false` 可阻止后续逻辑执行
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.11.1（2023-08-01）
* 1、【修复】因 `1.11.0` 版本升级导致微信小程序支付报配置错误的问题
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.11.0（2023-07-31）
* 1、【重要】新增VksPay支付接口（支持个人支付，详情联系Q：*********）
* 2、【优化】新增全局错误码 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/error.html)
* 3、【优化】示例页面逻辑
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.10.1（2023-07-11）
* 1、【修复】已知问题
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.10.0（2023-06-09）
* 1、【新增】IOS内购前端示例 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/iosiap.html)
* 2、【新增】微信支付服务商模式 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/advanced/multi-merchant-service-provider.html)
* 3、【优化】支付宝服务商模式细节
* 提示：非服务商模式也能支持多商户 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/advanced/multi-merchant.html)
* 注意：本次更新完建议进行测试后再发布到线上环境。
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.9.2（2023-02-14）
* 1、【重要】简化转账支付配置 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/config.html#_1-9-0%E4%BB%A5%E4%B8%8B%E7%9A%84%E7%89%88%E6%9C%AC%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9)
* 2、【优化】其他细节
* 注意：本次更新代码目录结构有调整，更新完建议进行测试后再发布到线上环境。
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.9.1（2023-02-12）
* 1、【重要】简化转账支付配置 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/config.html#_1-9-0%E4%BB%A5%E4%B8%8B%E7%9A%84%E7%89%88%E6%9C%AC%E6%B3%A8%E6%84%8F%E4%BA%8B%E9%A1%B9)
* 2、【优化】其他细节
* 注意：本次更新代码目录结构有调整，更新完建议进行测试后再发布到线上环境。
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.9.0（2023-02-12）
* 1、【重要】简化转账支付配置 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/config.html)
* 2、【优化】其他细节
* 注意：本次更新代码目录结构有调整，更新完建议进行测试后再发布到线上环境。
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.8.4（2023-01-29）
* 1、【优化】支付云函数默认运行内存调整为512M（提升性能）
* 2、【优化】正式版阿里云兼容性
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.8.3（2022-12-03）
* 1、【优化】一些细节
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.8.2（2022-11-26）
* 1、【优化】一些细节
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.8.1（2022-11-22）
* 1、【优化】合并精简依赖
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.8.0（2022-11-17）
* 1、【优化】阿里云空间支持微信商家转账到零钱V3接口 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/transfer3.html#%E9%98%BF%E9%87%8C%E4%BA%91%E7%A9%BA%E9%97%B4)
* 2、【优化】一些细节
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.7.3（2022-11-16）
* 1、【优化】阿里云空间支持微信商家转账到零钱V3接口 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/transfer3.html#%E9%98%BF%E9%87%8C%E4%BA%91%E7%A9%BA%E9%97%B4)
* 最新文档 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.7.2（2022-11-02）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.7.1（2022-10-29）
* 1、【优化】`小程序获取openid` 可以不依赖 `uni-id`
* 2、【优化】其他细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.7.0（2022-10-25）
* 1、【优化】`createPayment` 的 `needQRcode` 支持传字符串 `image` 效果是直接在云函数内生成二维码图片(base64)，便于在nvue中展示二维码。
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.7.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
| 强制使用二维码支付（新增于1.6.10） |   支持    | 强制使用二维码支付，让顾客扫码支付，一般用于物联网，如按摩椅上的扫码支付 |
## 1.6.10（2022-10-12）
* 1、【优化】`createPayment` 新增 `needQRcode` 参数 若设置为true，则强制使用二维码支付（如App内展示二维码，让别人扫码支付）
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.6.10版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
| 强制使用二维码支付（新增于1.6.10） |   支持    | 强制使用二维码支付，让顾客扫码支付，一般用于物联网，如按摩椅上的扫码支付 |
## 1.6.9（2022-10-05）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.6.8（2022-09-22）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.6.8版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.6.7（2022-09-04）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.6.6（2022-09-03）
* 1、【新增】`vkPay.crypto.aes.encrypt` 和 `vkPay.crypto.aes.decrypt` 主要用于回调外部系统时数据加密和解密 [传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/pay-notify.html#%E7%89%B9%E5%88%AB%E6%B3%A8%E6%84%8F)
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.6.6版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.6.5（2022-08-22）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.6.4（2022-08-17）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.6.4版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.6.3（2022-08-15）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.6.2（2022-08-12）
* 1、【重要】新增 微信支付 商家转账到零钱V3版本接口（支持批量转账）（新商户目前微信已经不给申请V2的转账接口了）[传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/transfer3.html)
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.6.1版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.6.1（2022-08-11）
* 1、【重要】新增 微信支付 商家转账到零钱V3版本接口（支持批量转账）（新商户目前微信已经不给申请V2的转账接口了）[传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/transfer3.html)
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.6.1版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.6.0（2022-08-11）
* 1、【重要】新增 微信支付 商家转账到零钱V3版本接口（支持批量转账）（新商户目前微信已经不给申请V2的转账接口了）[传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/transfer3.html)
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.6.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景（秒到） |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景（秒到） |
| 微信支付商家转账到零钱（V3版本）   |   支持  | 一般用于给用户提现等业务场景（支持批量转账） |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.5.2（2022-08-06）
* 1、【优化】退款接口新整参数 `out_refund_no` 可指定退款单号，如不指定，会自动生成。[传送门](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/refund.html)
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.5.1（2022-07-22）
* 1、【优化】一些注释细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.4.5版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.5.0（2022-06-08）
* 1、【优化】兼容hbx3.4.14版本。
* 2、【修复】因hbx3.4.14新版本带来的app支付报配置错误的问题。
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.4.6（2022-05-31）
* 1、【优化】兼容hbx3.4.12版本。
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.4.5（2022-05-24）
* 1、【优化】一些注释细节
* 2、【优化】回调函数的日志打印
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.4.5版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.4.4（2022-05-21）
* 1、【优化】PC扫码支付的逻辑。
* 2、【优化】部分注释文案。
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 关于本插件任何问题来Q群：22466457 帮你解决。
## 1.4.3（2022-04-21）
* 1、【优化】一些注释细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.4.3版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.4.2（2022-03-19）
* 1、【优化】一些细节
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.4.2版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.4.1（2022-03-18）
* 1、【优化】文档和配置文件的注释部分。
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.4.0（2022-02-26）
###【重要】由于uni官方限制自定义表不可以用 `opendb-` 和 `uni-` 开头，故以下数据库表名只能进行调整

* 1、`uni-pay-orders` 改为 `vk-pay-orders`
* 2、`uni-pay-config` 改为 `vk-pay-config`

___更改表名势必会对老项目产生影响。（对新项目无影响）___

### 【重要】老项目更新注意事项：
### 【重要】老项目更新注意事项：
### 【重要】老项目更新注意事项：

* 1、老项目更新后，还需要从 `unicloud控制台` 把表名改成对应的新表名
* 2、在老项目代码种全局搜索旧表名，替换成新表名

### 关于本插件任何问题来Q群：22466457 帮你解决。

### 注意：1.4.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.3.5（2021-12-23）
* 1、【优化】使用同一个 `out_trade_no` 发起支付时（如先选择微信支付后再选择支付宝支付），支付订单表自动更新支付方式。
* 2、【优化】`queryPayment` 接口新增是否需要返回订单信息的参数 [点击查看](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/queryPayment.html)
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.3.5版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.3.4（2021-12-23）
* 1、【优化】使用同一个 `out_trade_no` 发起支付时（如先选择微信支付后再选择支付宝支付），支付订单表自动更新支付方式。
* 2、【优化】`queryPayment` 接口新增是否需要返回订单信息的参数 [点击查看](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/queryPayment.html)
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.3.4版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.3.3（2021-11-22）
* 1、【优化】支付示例页面代码逻辑
* 最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.3.3版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.3.2（2021-11-08）
* 1、【重要】自1.3.0起，支持从数据库中读取商户支付配置（支持多商户支付，非服务商模式，后面会新增服务商模式的多商户支付）。
* 2、【重要】支付配置文件参数格式有变化（使之更加直观表达，同时兼容老版本配置）[点击查看](https://vkdoc.fsq.pub/vk-uni-pay/config.html)
* 3、【优化】`vkPay.queryPayment` 新增返回参数 `notify` 用户异步通知逻辑是否全部执行完成 [点击查看](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/queryPayment.html)
* 4、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.3.1（2021-11-05）
* 1、【重要】自1.3.0起，支持从数据库中读取商户支付配置（支持多商户支付，非服务商模式，后面会新增服务商模式的多商户支付）。
* 2、【重要】支付配置文件参数格式有变化（使之更加直观表达，同时兼容老版本配置）[点击查看](https://vkdoc.fsq.pub/vk-uni-pay/config.html)
* 3、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.3.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.3.0（2021-11-05）
* 1、【重要】自1.3.0起，支持从数据库中读取商户支付配置（支持多商户支付，非服务商模式，后面会新增服务商模式的多商户支付）。
* 2、【重要】支付配置文件参数格式有变化（使之更加直观表达，同时兼容老版本配置）[点击查看](https://vkdoc.fsq.pub/vk-uni-pay/config.html)
* 3、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.3.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
| 从数据库中读取商户配置（多商户）   |   支持  | 一般用于多商户时使用（非服务商模式）|
## 1.2.2（2021-11-03）
* 1、【优化】一些细节
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.2.2版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
## 1.2.1（2021-10-26）
* 1、【优化】一些细节
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.2.0（2021-10-21）
* 1、【新增】 发起支付时 支持微信、支付宝文档上的其他选填参数 [点击查看](https://vkdoc.fsq.pub/vk-uni-pay/uniCloud/createPayment.html)
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.2.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
## 1.1.13（2021-10-16）
* 1、【优化】当请求失败时的错误提示逻辑
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.1.12（2021-10-12）
* 1、【优化】支持非uniapp项目通过url方式请求获取支付订单参数
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
### 注意：1.1.10版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |

## 1.1.11（2021-09-28）
* 1、【优化】细节优化
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.1.10（2021-09-27）
* 1、【优化】细节优化
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 注意：1.1.10版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
## 1.1.9（2021-09-27）
* 1、【优化】转账接口新增参数 `check_name` 默认为true，当为false时，不校验姓名（支付宝不校验姓名时，account参数为 支付宝的会员ID，而非支付宝账号）
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.1.8（2021-09-25）
* 1、【新增】支持微信公众号支付（至此，常规的支付方式均已全部支持）
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 注意：1.1.8版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
## 1.1.7（2021-09-23）
* 1、【新增】H5手机-微信支付 （在H5浏览器中唤起微信客户端支付）
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 注意：1.1.7版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   暂不支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |

## 1.1.6（2021-09-22）
* 1、【优化】升级`uni-pay`模块
* 2、【优化】支付回调逻辑处理。
* 3、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.1.5（2021-09-19）
* 1、【重要】修复因uni-pay更新导致的腾讯云空间支付回调处理问题。
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.1.4（2021-09-11）
* 1、【优化】一些细节问题
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.1.3（2021-09-04）
* 1、【优化】升级`uni-id`和`uni-pay`模块
* 2、最新文档 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)

### 注意：1.1.3版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 暂不支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   暂不支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
## 1.1.2（2021-08-17）
* 1、【优化】一些细节问题
* 2、文档搬家 [点击查看新文档](https://vkdoc.fsq.pub/vk-uni-pay/)
## 1.1.1（2021-08-09）
#### 【优化】一些细节问题
* 1、文档 [点击查看文档](https://gitee.com/vk-uni/vk-uni-pay-md/wikis/pages?sort_id=4248569&doc_id=1530663)

## 1.1.0（2021-08-06）
#### 同时兼容`vue2`和`vue3`（`vue3`需要`HBX3.2.0`及以上版本）
#### 回馈群内用户支持
#### 前 50名 普通授权版用户 1元 购买普通授权版。
#### 前 50名 源码授权版用户 66元 购买源码授权版。
* 1、文档 [点击查看文档](https://gitee.com/vk-uni/vk-uni-pay-md/wikis/pages?sort_id=4248569&doc_id=1530663)

### 注意：1.1.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 暂不支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   暂不支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |
## 1.0.6（2021-08-03）
#### 【修复】微信支付可能会报商品名称不合法的问题。
* 1、文档 [点击查看文档](https://gitee.com/vk-uni/vk-uni-pay-md/wikis/pages?sort_id=4248569&doc_id=1530663)

#### 关于插件使用问题几乎有问必答。Q群：22466457
## 1.0.5（2021-07-27）
#### 回馈群内用户支持
#### 前 50名 普通授权版用户 1元 购买普通授权版。
#### 前 50名 源码授权版用户 66元 购买源码授权版。
* 1、文档 [点击查看文档](https://gitee.com/vk-uni/vk-uni-pay-md/wikis/pages?sort_id=4248569&doc_id=1530663)

### 注意：1.0.5版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 暂不支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   暂不支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |

#### 关于插件使用问题几乎有问必答。Q群：22466457
## 1.0.4（2021-07-27）
#### 回馈群内用户支持
#### 前 50名 普通授权版用户 1元 购买普通授权版。
#### 前 50名 源码授权版用户 66元 购买源码授权版。
* 1、文档 [点击查看文档](https://gitee.com/vk-uni/vk-uni-pay-md/wikis/pages?sort_id=4248569&doc_id=1530663)

### 注意：1.0.4版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 暂不支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   暂不支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |

#### 关于插件使用问题几乎有问必答。Q群：22466457
## 1.0.3（2021-07-23）
#### 前50名用户1元购买普通授权版。
* 1、优化文档 [点击查看文档](https://gitee.com/vk-uni/vk-uni-pay-md/wikis/pages?sort_id=4248569&doc_id=1530663)

#### 关于插件使用问题几乎有问必答。Q群：22466457
## 1.0.2（2021-07-17）
#### 前50名用户1元购买普通授权版。
* 1、优化文档 [点击查看文档](https://gitee.com/vk-uni/vk-uni-pay-md/wikis/pages?sort_id=4248569&doc_id=1530663)
* 2、优化一些细节。


### 注意：1.0.2版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 暂不支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   暂不支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |

#### 关于插件使用问题几乎有问必答。Q群：22466457
## 1.0.1（2021-07-16）
### 暂时去除了云函数 vk-pay 的加密配置（只加密vk-uni-pay）
## 1.0.0（2021-07-16）
### 2021-07-06 vk-uni-pay 万能支付插件 正式上线 
#### 前50名用户1元购买普通授权版。
### 注意：1.0.0版本支持情况（持续增加中）

| 支付方式            | 支持情况      | 说明 | 
|------------------- |-----------|---------|
| H5手机-支付宝支付   |   支持    | 在H5浏览器中唤起支付宝客户端支付 |
| H5手机-微信支付     | 暂不支持    | 在H5浏览器中唤起微信客户端支付  | 
| PC扫码支付-支付宝支付   |   支持    |  在PC浏览器中出现支付二维码，通过支付宝扫码支付 |
| PC扫码支付-微信支付   |   支持    |  在PC浏览器中出现支付二维码，通过微信扫码支付 |
| 微信小程序支付   |   支持    | 在微信小程序中支付 |
| 支付宝小程序支付   |   支持    |  在支付宝小程序中支付 |
| APP-支付宝转H5支付   |   支持    | 可以免申请APP接口，效果等于APP支付接口，但无法自动返回APP |
| APP-支付宝支付   |   支持    | 在APP中唤起支付宝客户端支付，支付成功后自动返回到APP页面 |
| APP-微信支付   |   支持    |  在APP中唤起微信客户端支付，支付成功后自动返回到APP页面 |
| 公众号H5-微信支付   |   暂不支持    | 在微信公众号的H5页面中唤起微信客户端支付 |
| 转账到支付宝余额   |   支持    | 一般用于给用户提现等业务场景(秒到) |
| 转账到微信零钱   |   支持  | 一般用于给用户提现等业务场景(秒到) |

#### 关于插件使用问题几乎有问必答。Q群：22466457
