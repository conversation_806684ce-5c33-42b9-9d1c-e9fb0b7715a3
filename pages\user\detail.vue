<template>
  <view class="page-container">
    <t-navbar nav-style="custom" :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 200})` }" :placeholder="false">
      <view v-if="scrollTop > 200 && info" class="nav-info">
        <image class="avatar" :src="info.avatar" mode="aspectFill" />
        <view class="name">
          {{ info.nickname }}
        </view>
      </view>
    </t-navbar>
    <template v-if="info">
      <image class="background" :src="backgroundImage" mode="aspectFill" @click="previewImage"></image>
      <view class="content">
        <view class="content-info">
          <view class="user-info">
            <image class="avatar" :src="info.avatar" mode="aspectFill"></image>
            <view class="name">{{ info.nickname }}</view>
          </view>
          <view class="tags">
            <!-- <wd-tag type="primary">Lv 1</wd-tag> -->
            <wd-tag v-if="info.level" type="primary">{{ info.level.toFixed(1) }}</wd-tag>
          </view>
          <view class="follow" v-if="userInfo._id !== user_id">
            <wd-button v-if="info.follow" type="info" block :round="false" @click="followUser">取消关注</wd-button>
            <wd-button v-else type="primary" block :round="false" @click="followUser">关注</wd-button>
          </view>
          <view class="statistic">
            <view class="block">
              <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.created" :fontSize="20" color="#000"></wd-count-to>
              <view class="label">发起活动</view>
            </view>
            <view class="block">
              <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.joined" :fontSize="20" color="#000"></wd-count-to>
              <view class="label">参与活动</view>
            </view>
            <view class="block" @click="viewUserList('follow')">
              <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.follow" :fontSize="20" color="#000"></wd-count-to>
              <view class="label">关注</view>
            </view>
            <view class="block" @click="viewUserList('fans')">
              <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.fans" :fontSize="20" color="#000"></wd-count-to>
              <view class="label">粉丝</view>
            </view>
          </view>
        </view>
        <view class="content-box">
          <view class="content-block">
            <view class="title">能力分析图</view>
            <t-capacity-canvas :data="scoreData" />
          </view>
          <view class="content-block">
            <view class="title">近期比赛记录</view>
            <t-empty data="data" top="0" text="暂无比赛记录"></t-empty>
          </view>
        </view>
      </view>
    </template>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      info: null,
      scrollTop: 0,
      dataStatistic: { created: 0, joined: 0, follow: 0, fans: 0 },
      commentNum: 0,
      scoreData: null,
      user_id: null,
    };
  },
  computed: {
    backgroundImage() {
      if (!this.info) return;
      let image = this.info.back_cover || this.info.avatar;
      return image;
    },
    levelValue() {
      if (this.info && this.info.level) {
        return this.info.level.toFixed(1);
      } else {
        return "未检测";
      }
    },
  },
  onShareAppMessage() {
    return {
      title: `${this.info.nickname}的个人主页`,
      path: `/pages/user/detail?id=${this.info._id}`,
      imageUrl: this.info.avatar,
    };
  },
  async onLoad(options) {
    this.user_id = options.id;
    await this.getUserInfo();
    this.getUserStatistic();
    this.getLevelScore();
    console.log(this.userInfo._id, this.user_id);
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  methods: {
    previewImage() {
      uni.previewImage({
        urls: [this.backgroundImage],
      });
    },
    // 去查看用户列表
    viewUserList(type) {
      let user_id = this.info._id;
      vk.navigateTo(`/pages/user/list?type=${type}&user_id=${user_id}`);
    },
    // 关注用户
    async followUser() {
      try {
        let data = await vk.callFunction({
          url: "client/user/kh/follow",
          title: "请求中...",
          data: {
            target_user_id: this.user_id,
          },
        });
        this.info.follow = data.data;
        vk.toast(data.msg, () => {
          this.getUserStatistic();
        });
        vk.setVuex("$status.user_reload", true);
      } catch (error) {
        console.log(error);
      }
    },
    // 获取用户信息
    async getUserInfo() {
      let data = await vk.callFunction({
        url: "client/user/kh/get",
        title: "请求中...",
        data: {
          user_id: this.user_id,
        },
      });
      this.info = data.data;
    },
    async getUserStatistic() {
      let data = await vk.callFunction({
        url: "client/user/kh/statistic",
        title: "请求中...",
        data: {
          user_id: this.user_id,
        },
      });
      this.dataStatistic = data.data;
    },
    async getLevelScore() {
      let { comment_num, data } = await vk.callFunction({
        url: "client/user/level/kh/base",
        data: {
          user_id: this.user_id,
        },
      });
      this.commentNum = comment_num;
      this.scoreData = data;
    },
    toUrl(url) {
      vk.navigateTo(url);
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.nav-info {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .avatar {
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
    margin-right: 20rpx;
    background-color: #fff;
  }

  .name {
    font-size: 32rpx;
    color: #333;
  }
}

.background {
  width: 100%;
  height: 500rpx;
  position: fixed;
  left: 0;
  top: 0;
  opacity: 0.9;
}

.content {
  position: relative;
  padding-bottom: 30rpx;
  background-color: #f8f8f8;

  &-info {
    width: 100%;
    background-color: #fff;
    position: relative;
    padding: 0 30rpx 30rpx;
    box-sizing: border-box;
    margin-top: 500rpx;

    .user-info {
      box-sizing: border-box;

      .avatar {
        width: 140rpx;
        height: 140rpx;
        border-radius: 50%;
        border: 4rpx solid #fff;
        margin-top: -70rpx;
        background-color: #fff;
      }

      .name {
        margin-top: 20rpx;
        font-size: 40rpx;
        font-weight: bold;
        color: #000;
        @include multiline(1);
      }
    }
    .follow {
      position: absolute;
      right: 40rpx;
      top: 80rpx;
      width: 200rpx;
      :deep(.wd-button) {
        min-width: 0 !important;
      }
    }

    .tags {
      display: flex;
      align-items: center;
      margin-top: 30rpx;
    }

    .statistic {
      margin-top: 30rpx;
      display: grid;
      grid-template-columns: 25% 25% 25% 25%;
      gap: 20rpx;
      grid-template-rows: 100rpx;

      .block {
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        :deep(.value) {
          font-weight: bold;
        }

        .label {
          font-size: 24rpx;
          color: #8a8a8a;
        }
      }
    }
  }

  &-box {
    position: relative;
    margin: 0 30rpx;
  }

  &-block {
    background-color: #fff;
    padding: 30rpx;
    margin-top: 30rpx;
    border-radius: 24rpx;

    .title {
      font-size: 36rpx;
      font-weight: bold;
    }

    .footer {
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
}

// Tag styles with theme color
:deep(.wd-tag) {
  padding: 3px 6px !important;
  margin-right: 20rpx;
}
</style>
