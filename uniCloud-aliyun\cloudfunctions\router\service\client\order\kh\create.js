const vkPay = require("vk-uni-pay");
const formRules = require("../util/formRules.js");
const createOrder = require("../util/createOrder.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/order/kh/create 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam, uniIdToken } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, subject, total_fee, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let custom = {}
		switch (type) {
			case "card":
				custom = {
					card_id: data.card_id,
					club_id: data.club_id,
				}
				break;
			case "activity":
				custom = {
					activity_id: data.activity_id,
					join_type: data.join_type,
					join_name: data.join_name || "",
				}
				break;
			default:
				return { code: -1, msg: "支付参数有误" }
		}
		// 检验参数
		let formRulesRes = await formRules.check(event);
		console.log("formRules", formRulesRes);
		if (formRulesRes.code !== 0) return formRulesRes;
		// 查看是否可以重新支付
		let old_order = null
		const whereJson = {
			...custom,
			user_id: uid,
			create_date: _.gt(Date.now() - 1000 * 900).lt(Date.now()),
			status: 0,
		}
		old_order = await vk.baseDao.findByWhereJson({
			dbName: "vk-pay-orders",
			whereJson,
		});
		console.log("old_order", old_order);
		// 订单号创建
		let out_trade_no = Date.now().toString() + Math.floor(10000 + Math.random() * 90000)
		let order_params = null
		if (old_order) {
			order_params = {
				openid: old_order.openid,
				out_trade_no: old_order.out_trade_no,
				total_fee: old_order.total_fee, // 订单金额（单位分 100 = 1元）
				subject: old_order.description,
				type: old_order.type,
				user_id: old_order.user_id,
				nickname: old_order.nickname,
				custom,
				time_expire: old_order.create_date + 1000 * 900
			}
		} else {
			order_params = {
				openid: userInfo.wx_openid["mp-weixin"],
				out_trade_no,
				total_fee, // 订单金额（单位分 100 = 1元）
				subject,
				type,
				user_id: uid,
				nickname: userInfo.nickname,
				custom,
				time_expire: Date.now() + 1000 * 900
			}
			// 创建平台订单信息
			data.out_trade_no = out_trade_no
			let orderRes = await createOrder.create(data)
			console.log("orderRes", orderRes);
			if (orderRes.code !== 0) return orderRes
		}
		// 开始创建新订单
		// 创建微信订单信息
		res = await vkPay.createPayment({
			context: originalParam.context,
			provider: "wxpay",
			data: order_params,
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}