'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/news/pub/check 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		if (!uid) return res

		let num = await vk.baseDao.count({
			dbName: "user-news-data",
			whereJson: {
				accept_user: uid,
				read_state: false,
			}
		});

		res.data = num > 0 ? true : false
		res.msg = "查询成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}