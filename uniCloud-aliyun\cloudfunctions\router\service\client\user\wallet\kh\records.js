'use strict';
module.exports = {
	/**
	 * 钱包变动历史
	 * @url client/user/wallet/kh/records 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, pageIndex } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		res = await vk.baseDao.select({
			dbName: "user-wallet-record",
			getCount: false,
			pageIndex,
			pageSize: 20,
			whereJson: {
				user_id: uid
			},
			sortArr: [{ name: "_add_time", type: "desc" }],
		});



		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}