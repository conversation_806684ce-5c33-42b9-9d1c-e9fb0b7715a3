'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/kh/levelInit 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, select } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		// 请求检测题
		let { rows: options } = await vk.baseDao.select({
			dbName: "options-data",
			pageIndex: 1,
			pageSize: 9999,
			whereJson: {
				type: "level_check"
			},
			sortArr: [{ name: "sort", type: "asc" }],
		});
		const calculateScore = () => {
			let score = {
				forehand: 0,
				backhand: 0,
				serve: 0,
				return: 0,
				volley: 0,
				description: 0,
			}

			options.forEach((item, index) => {
				let value = 0
				if (select[index] || select[index] == 0) {
					value = Number(item.value[select[index]].value)
					if (item.name == 'all' && value) {
						Object.keys(score).forEach(key => {
							score[key] = value;
						});
					} else if (item.name !== 'all') {
						score[item.name] = value
					}
				}
			});

			return score;
		}

		const updateUserScore = async (score) => {
			let info = await vk.baseDao.findByWhereJson({
				dbName: "level-score-data",
				whereJson: {
					type: 'init',
					user_id: uid
				},
				fieldJson: {}
			});
			if (info) {
				await vk.baseDao.updateById({
					dbName: "level-score-data",
					id: info._id,
					dataJson: {
						data: score
					},
					getUpdateData: false
				});
			} else {
				await vk.baseDao.add({
					dbName: "level-score-data",
					dataJson: {
						type: 'init',
						user_id: uid,
						data: score
					}
				});
			}
		}

		let score = calculateScore();
		let base = Object.values(score).reduce((a, b) => a + b, 0);
		score.base = Math.floor(base / 6 * 2) / 2;

		await updateUserScore(score);

		// 更新到用户信息
		let dataJson = {}
		dataJson.uid = uid;
		dataJson.level = score.base
		await uniID.updateUser(dataJson);
		userInfo.level = score.base
		res.userInfo = userInfo
		res.needUpdateUserInfo = true
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}