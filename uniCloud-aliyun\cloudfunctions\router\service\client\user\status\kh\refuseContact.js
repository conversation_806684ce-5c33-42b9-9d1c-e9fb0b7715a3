'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/status/kh/refuseContact 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, user_id, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (!uid) return { code: -1, msg: "请先登录" }

		if (!user_id｜｜!id) return { code: -1, msg: "参数错误" }

		// 向申请用户发送拒绝消息
		await vk.baseDao.add({
			dbName: "user-news-data",
			dataJson: {
				"accept_user": user_id,
				"title": "联系方式申请成功",
				"text": `很遗憾，用户拒绝了您的联系方式获取申请`,
				"type": "contact",
				"read_state": false,
				"user_id": uid,
				"contact_status": "refuse",
			}
		});

		// 删除现有的申请消息
		await vk.baseDao.deleteById({
			dbName: "user-news-data",
			id
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}