module.exports = {
	/**
	 * 修改用户信息
	 * @url user/kh/updateUser 前端调用的url参数地址
	 * @description 设置当前登录用户的昵称等用户信息
	 * data 请求参数 说明
	 * @param {String} nickname			用户的昵称
	 * @param {String} avatar 			用户的头像
	 * @param {String} sex 				用户性别
	 * @param {String} back_cover 		背景图
	 * @param {String} level 			等级
	 * @param {String} credit 			信用
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, util, userInfo, originalParam } = event;
		let { uniID, vk } = util;
		let { uid } = data;
		let res = {};
		// 业务逻辑开始-----------------------------------------------------------
		// 检验违规内容
		if (data.nickname) {
			let msgSecCheckRes = await vk.openapi.weixin.security.msgSecCheck({
				content: data.nickname, // 文本内容，不可超过500KB
				openid: userInfo.wx_openid["mp-weixin"], // 用户的小程序openid
				scene: 2, // 场景值（建议为2或3）
				version: 2, // 接口版本号（建议为2）
			});
			if (msgSecCheckRes.code != 0) return { code: -1, msg: '昵称涉嫌违规，请重新设置' }
		}
		// 允许修改的字段列表
		let updateKeys = ["nickname", "avatar", "sex", "mobile", "setting", "back_cover", "level", "credit", "wechat", "birthday"];
		let dataJson = {};
		for (let i in updateKeys) {
			let key = updateKeys[i];
			if (vk.pubfn.isNotNull(data[key])) dataJson[key] = data[key];
		}
		dataJson.uid = uid;
		console.log('data', dataJson);
		// 检验是否是修改背景图或者头像，如果是则删除原本照片
		if (dataJson.back_cover && userInfo.back_cover && dataJson.back_cover !== userInfo.back_cover) {
			await uniCloud.deleteFile({
				fileList: [userInfo.back_cover]
			});
			await vk.baseDao.del({
				dbName: "vk-files",
				whereJson: {
					url: userInfo.back_cover
				}
			});
		}
		if (dataJson.avatar && userInfo.avatar && dataJson.avatar !== userInfo.avatar) {
			await uniCloud.deleteFile({ fileList: [userInfo.avatar] });
			await vk.baseDao.del({
				dbName: "vk-files",
				whereJson: {
					url: userInfo.avatar
				}
			});
		}
		res = await uniID.updateUser(dataJson);
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}