<template>
	<view>
		<t-navbar title="选择会员卡"></t-navbar>
		<view class="list">
			<view class="content" v-for="(item,index) in list" :key="item._id">
				<vip-card :data="item" @click="checked(index)">
					<image v-show="item.checked" class="checked"
						src="https://cdn.cometennis.cn/icon/check-on.svg"
						mode="aspectFill"></image>
				</vip-card>
			</view>
		</view>
	</view>
</template>

<script>
	import vipCard from '@/pages_club/components/vip-card.vue'
	import mixins from '@/pages_club/mixins.js'
	export default {
		mixins: [mixins],
		components: {
			vipCard
		},
		data() {
			return {
				list: [],
				ids: [],
			}
		},
		onLoad(options) {
			this.ids = options.ids.split(',')
			this.getData()
		},
		methods: {
			async getData() {
				const res = await vk.callFunction({
					url: 'client/club/card/kh/list',
					data: {
						club_id: this.club_id
					}
				})
				this.list = res.rows.map(item => {
					item.checked = this.ids.includes(item._id)
					return item
				})
			},
			checked(index) {
				this.list[index].checked = !this.list[index].checked
				this.returnResult()
			},
			returnResult() {
				let list = vk.pubfn.copyObject(this.list);
				list = list.filter(item => {
					return item.checked
				})
				uni.$emit('select-card', list)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.list {
		display: flex;
		align-items: center;
		flex-direction: column;
		padding: 30rpx;

		.content {
			margin-bottom: 30rpx;
		}
	}

	.checked {
		width: 48rpx;
		height: 48rpx;
		position: absolute;
		right: 20rpx;
		top: 20rpx;
	}
</style>