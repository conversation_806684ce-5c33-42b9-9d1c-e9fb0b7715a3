'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/addTemplate 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let {
			uid,
			name, // 接龙名称
			desc, // 接龙描述
			start, // 开始时间
			end, // 结束时间
			card, // 俱乐部活动-可使用卡片
			site, // 俱乐部场地
			coach, // 俱乐部活动-教练
			club_id, // 俱乐部活动-俱乐部id
			site_info, // 自定义场地信息
			min_num, // 最少活动人数
			max_num, // 最多活动人数
			dissolve_hour, // 自动解散时间
			auto_dissolve, // 自动解散
			cost_type, // 花费类型 0 aa 1 人均
			cost, // 接龙花费
			cost_plan, // 收费方式
			refund_hour, // 退款规则
			refund_money, // 退款规则
			level, // 水平要求
			apply = false, // 是否需要审批
			open = false, // 是否公开
			notice = true, // 订阅通知
			help_join = true, // 允许代接
			standby = true, // 人员候补
			contact,
			type = 'user'
		} = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (!name || !desc) {
			res.msg = "至少要填写标题和描述"
			res.code = -1
			return res
		}
		let num = await vk.baseDao.count({
			dbName: "activity-template-data",
			whereJson: {
				creator_id: uid,
			}
		});
		if (num >= 20) {
			res.msg = "至多只能添加20条模板数据"
			res.code = -1
			return res
		}

		let dataJson = {
			creator_id: uid,
			name, // 接龙名称
			desc, // 接龙描述
			start, // 开始时间
			end, // 结束时间
			card, // 俱乐部活动-可使用卡片
			site, // 俱乐部场地
			coach, // 俱乐部活动-教练
			club_id, // 俱乐部活动-俱乐部id
			site_info, // 自定义场地信息
			min_num, // 最少活动人数
			max_num, // 最多活动人数
			dissolve_hour, // 自动解散时间
			auto_dissolve, // 自动解散
			cost_type, // 花费类型 0 aa 1 人均
			cost, // 接龙花费
			cost_plan, // 收费方式
			refund_hour, // 退款规则
			refund_money, // 退款规则
			level, // 水平要求
			apply, // 是否需要审批
			open, // 是否公开
			notice, // 订阅通知
			help_join, // 允许代接
			standby, // 人员候补
			contact,
			type
		}
		let id = await vk.baseDao.add({
			dbName: "activity-template-data",
			dataJson: dataJson
		});
		res.data = id
		res.msg = '保存模板成功'

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}