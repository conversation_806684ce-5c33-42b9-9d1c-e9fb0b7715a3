"use strict";
module.exports = {
	/**
	 * 打卡接口
	 * @url client/public/info/kh/sign 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, id, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = "sign-info-list";
		let today = vk.pubfn.timeFormat(new Date(), "yyyy-MM-dd");
		// 查询今天是否已经签到过该场地
		let has = await vk.baseDao.findByWhereJson({
			dbName,
			getOne: true,
			whereJson: {
				data_id: id,
				user_id: uid,
				type,
				date: today,
			},
		});
		if (has) return { code: -1, msg: "今日已打卡该场地" };

		await vk.baseDao.add({
			dbName,
			dataJson: {
				data_id: id,
				user_id: uid,
				type,
				date: today,
			},
		});

		// 查询今日打卡的人数
		let today_num = await vk.baseDao.count({
			dbName,
			whereJson: {
				data_id: id,
				type,
				date: today,
			},
		});
		// 查询我打卡的次数
		let sign_num = await vk.baseDao.count({
			dbName,
			whereJson: {
				data_id: id,
				type,
				user_id: uid,
			}
		});
		// 更新场地打卡人数
		if (type == 'site') {
			let site_sign_num = await vk.baseDao.count({
				dbName,
				whereJson: {
					data_id: id,
					type: 'site',
				},
				groupJson: {
					_id: "$user_id",
					count: $.sum(1),
				},
			});
			await vk.baseDao.updateById({
				dbName: "info-site-data",
				id,
				dataJson: {
					sign_num: site_sign_num
				},
				getUpdateData: false
			});
		}

		res.msg = "打卡成功";
		res.data = {
			text: "打卡成功！",
			today_num,
			sign_num
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	},
};