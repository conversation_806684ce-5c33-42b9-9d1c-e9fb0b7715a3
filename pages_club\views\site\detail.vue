<template>
	<view v-if="info">
		<t-navbar nav-style="custom" :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 300})` }" :placeholder="false">
			<!-- <view v-if="scrollTop > 350" class="site-small-info">
				<view class="nav-name">{{ info.name }}</view>
			</view> -->
		</t-navbar>
		<swiper class="banner" circular>
			<swiper-item v-for="item in info.images" :key="item">
				<view class="swiper-item">
					<image :src="item" mode="aspectFill" />
				</view>
			</swiper-item>
		</swiper>
		<view class="site">
			<view class="site-info">
				<view class="site-name">{{ info.name }}</view>
				<view class="site-tags">
					<t-tag v-for="tag in info.tags" :key="tag" :text="tag"></t-tag>
				</view>
			</view>
			<view class="plan">
				<t-title>场地安排</t-title>
				<picker mode="date" @change="changeDate">
					<view class="plan-date" v-if="dayInfo">
						<text>{{dayInfo.date}} {{dayInfo.cn}}</text>
						<t-tag :color="tagColor.color" :textColor="tagColor.text" :text="dayInfo.holiday?'节假日':'工作日'"></t-tag>
					</view>
				</picker>
				<view class="plan-list">
					<view class="plan-block" v-for="(item,index) in plan_list" :key="item.start">
						<view class="plan-time">
							<view class="start">{{item.start}}</view>
							<view class="end" v-if="index+1==plan_list.length">{{item.end}}</view>
						</view>
						<view class="plan-content" :class="{'no-plan':item.type=='none'}">
							<view class="text" v-if="item.type!=='none'">{{item.text}}</view>
							<view class="none" v-else>场地空闲，<text>点击安排计划</text></view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 操作栏 -->
		<t-actionsheet v-model="actionVisible" :action="action" @click="actionClick"></t-actionsheet>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	import { dayInfo, planTimeLine } from '@/utils/time.js'
	export default {
		data() {
			return {
				iconUrl: vk.getVuex('$app.config.staticUrl.icon'),
				info: null, // 球场信息
				current: 0,
				scrollTop: 0,


				// 日期相关
				dayInfo: null,

				// 操作
				actionVisible: false,
				action: [{
					label: "编辑信息",
					value: "edit",
				}, {
					label: "删除场地",
					value: "delete",
					color: "#ff3030"
				}],

				// 模拟安排数据
				order_list: [{
					start: 1742950800000,
					end: 1742958000000,
					type: "site",
					text: "用户订场"
				}, {
					start: 1742961600000,
					end: 1742968800000,
					type: "site",
					text: "用户订场"
				}, {
					start: 1742976000000,
					end: 1742983200000,
					type: "activity",
					text: "俱乐部接龙活动俱乐部接龙活动俱乐部接龙活动俱乐部接龙活动俱乐部接龙活动俱乐部接龙活动俱乐部接龙活动俱乐部接龙活动俱乐部接龙活动"
				}, {
					start: 1742972400000,
					end: 1742976000000,
					type: "activity",
					text: "韦教练私教课教学"
				}],
				plan_list: [],
			}
		},
		onPageScroll(e) {
			this.scrollTop = e.scrollTop
		},
		computed: {
			tagColor() {
				if (!this.dayInfo) return
				return this.dayInfo.holiday ? {
					color: vk.getVuex('$app.color.secondary'),
					text: "#fff"
				} : null
			}
		},
		onLoad(options) {
			this.dayInfo = dayInfo()
			this.plan_list = planTimeLine(this.order_list)
			console.log(this.plan_list);
			this.getData(options.id)
		},
		methods: {
			deleteSite() {
				vk.toast('还没处理好')
			},
			actionClick(type) {
				switch (type) {
					case "new":
						break;
				}
				this.actionVisible = false
			},
			async getData(id) {
				let data = await vk.callFunction({
					url: 'client/club/site/kh/detail',
					title: '请求中...',
					data: {
						id
					},
				});
				let info = JSON.parse(JSON.stringify(data.info))
				this.info = info
			},
			changeDate(e) {
				console.log("更改日期", e);
			}
		},
	}
</script>

<style scoped lang="scss">
	.banner {
		width: 100%;
		height: 600rpx;

		.swiper-item {
			width: 100%;
			height: 100%;

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.site {
		position: relative;
		border-radius: 48rpx 48rpx 0 0;
		margin-top: -40rpx;
		z-index: 1;
		width: 100%;
		padding: 30rpx;
		box-sizing: border-box;
		background-color: #fff;

		&-info {
			display: flex;
			flex-direction: column;
			justify-content: flex-end;
			margin-bottom: 30rpx;
		}

		&-name {
			font-size: 50rpx;
			font-weight: bold;
			color: #333;
		}

		&-tags {
			display: flex;
			align-items: center;
			margin-top: 20rpx;
		}
	}

	.plan {
		&-date {
			margin-top: 20rpx;
			font-size: 32rpx;
			display: flex;
			align-items: center;

			text {
				margin-right: 20rpx;
			}
		}

		&-list {
			padding: 50rpx 0;
		}

		&-block {
			display: flex;
			margin-bottom: 10rpx;
		}

		&-time {
			font-size: 32rpx;
			color: #8a8a8a;
			width: 120rpx;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			.start {
				position: relative;
				top: -24rpx;
			}

			.end {
				position: relative;
				bottom: -24rpx;
			}
		}

		&-content {
			flex: 1;
			background-color: #f8f8f8;
			padding: 30rpx 30rpx 30rpx 50rpx;
			box-sizing: border-box;
			position: relative;
			border-radius: 12rpx;
			overflow: hidden;
			min-height: 150rpx;
			display: flex;
			align-items: center;

			&::after {
				content: "";
				position: absolute;
				top: 0;
				bottom: 0;
				left: 0;
				margin: auto 0;
				width: 20rpx;
				background-color: $primary-color;
			}
		}

		.no-plan {
			&::after {
				background-color: #ccc;
			}
		}
	}
</style>