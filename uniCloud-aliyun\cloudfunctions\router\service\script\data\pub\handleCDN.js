'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url script/data/pub/handleCDN 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		const dbName = 'uni-id-users'
		const old_val = "https://mp-4aae0bb5-3ddf-45dc-8a49-5fba7f9569cc.cdn.bspapp.com"
		const new_val = "https://cdn.cometennis.cn"
		// 查找所有数据
		res = await vk.baseDao.select({
			dbName,
			getCount: false,
			getMain: true,
			pageIndex: 1,
			pageSize: 10000,
			whereJson: {
				back_cover: new RegExp(old_val)
			},
			fieldJson: { _id: true, back_cover: true },
			sortArr: [{ name: "_id", type: "desc" }],
		});

		console.log(res.length, res);
		// 开始替换


		let list = res.map(item => {
			if (item.back_cover) item.back_cover = item.back_cover.replace(old_val, new_val)
			return item
		})

		console.log("处理后的数据", list);



		for (const item of list) {
			await vk.baseDao.updateById({
				dbName,
				id: item._id,
				dataJson: {
					back_cover: item.back_cover
				},
				getUpdateData: false
			});
		}


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}