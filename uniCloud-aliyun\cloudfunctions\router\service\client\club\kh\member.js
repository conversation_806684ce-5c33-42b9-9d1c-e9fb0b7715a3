'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/kh/member 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id, pageIndex, state } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.selects({
			dbName: "club-user-data",
			getCount: false,
			pageIndex,
			pageSize: 20,
			whereJson: {
				club_id,
				state,
			},
			foreignDB: [{
				dbName: "uni-id-users",
				localKey: "user_id",
				foreignKey: "_id",
				as: "user_info",
				limit: 1
			}],
			sortArr: [{ name: "_add_time", type: "desc" }],
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}