'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/string/pub/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, location, pageIndex, name, sort } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let whereJson = {}
		if (location) {
			location = _.geoNear({
				geometry: new db.Geo.Point(location.longitude, location.latitude),
				distanceMultiplier: 0.001,
				distanceField: "distance",
			})
		} else {
			let index = sort.findIndex(item => item.name == 'distance')
			sort.splice(index, 1)
		}
		if (location) whereJson.location = location
		if (name) whereJson.name = new RegExp(name)
		console.log(sort, "sort");
		let foreignDB = []
		if (uid) {
			foreignDB.push({
				dbName: "info-recommend-record",
				localKey: "_id",
				foreignKey: "main_id",
				as: "recommend",
				whereJson: {
					user_id: uid
				},
				limit: 1
			})
		}
		res = await vk.baseDao.selects({
			dbName: "info-string-data",
			getCount: false,
			pageIndex,
			pageSize: 20,
			// 主表where条件
			whereJson,
			// 主表字段显示规则
			fieldJson: {},
			// 主表排序规则
			sortArr: sort,
			// 副表列表
			foreignDB
		});
		// 处理字段
		res.rows = res.rows.map(item => {
			item.recommend = item.recommend ? true : false
			return item
		})


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}