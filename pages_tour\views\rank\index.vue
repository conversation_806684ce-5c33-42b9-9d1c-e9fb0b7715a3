<template>
  <view class="rank-page">
    <!-- 排名列表 -->
    <scroll-view
      class="rank-list"
      scroll-y
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @refresherrefresh="handleRefresh"
      @scrolltolower="handleLoadMore"
    >
      <view class="rank-header">
        <view class="rank-cell rank">排名</view>
        <view class="rank-cell name">选手</view>
        <view class="rank-cell points">积分</view>
      </view>

      <block v-if="rankList.length > 0">
        <view class="rank-item" v-for="(item, index) in rankList" :key="item._id" @click="goToPlayer(item)">
          <view class="rank-cell rank">
            <text :class="{ top3: index < 3 }">{{ index + 1 }}</text>
          </view>
          <view class="rank-cell name">
            <image class="avatar" :src="item.avatar || '/static/images/default-avatar.png'" mode="aspectFill"></image>
            <text>{{ item.name }}</text>
          </view>
          <view class="rank-cell points">
            <text>{{ item.points }}</text>
            <text class="points-change" :class="{ up: item.points_change > 0, down: item.points_change < 0 }">
              {{ formatPointsChange(item.points_change) }}
            </text>
          </view>
        </view>
      </block>

      <view v-else class="empty-tip">
        <image src="/static/images/empty.png" mode="aspectFit"></image>
        <text>暂无排名数据</text>
      </view>

      <uni-load-more :status="loadMoreStatus"></uni-load-more>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      rankList: [],
      page: 1,
      pageSize: 20,
      isRefreshing: false,
      loadMoreStatus: "more",
    };
  },
  onLoad() {
    this.loadRankList();
  },
  methods: {
    formatPointsChange(change) {
      if (!change) return "";
      return change > 0 ? `+${change}` : `${change}`;
    },
    async loadRankList(isRefresh = false) {
      if (isRefresh) {
        this.page = 1;
      }

      try {
        const res = await vk.callFunction({
          url: "client/tour/rank/kh/getList",
          data: {
            match_id: this.match_id,
            type: this.currentType,
          },
        });
        this.rankList = res.data;
      } catch (e) {
        vk.toast(e.message || "加载失败");
      } finally {
        this.isRefreshing = false;
      }
    },
    async handleRefresh() {
      this.isRefreshing = true;
      await this.loadRankList(true);
    },
    handleLoadMore() {
      if (this.loadMoreStatus === "more") {
        this.page++;
        this.loadRankList();
      }
    },
    goToPlayer(item) {
      uni.navigateTo({
        url: `/pages_tour/views/player/detail?id=${item.user_id}`,
      });
    },
  },
};
</script>

<style lang="scss">
.rank-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .rank-list {
    flex: 1;

    .rank-header {
      display: flex;
      align-items: center;
      padding: 20rpx 30rpx;
      background-color: #fff;
      border-bottom: 1rpx solid #eee;
      font-size: 28rpx;
      color: #666;
    }

    .rank-item {
      display: flex;
      align-items: center;
      padding: 30rpx;
      background-color: #fff;
      margin-bottom: 2rpx;

      &:active {
        background-color: #f8f8f8;
      }
    }

    .rank-cell {
      display: flex;
      align-items: center;

      &.rank {
        width: 120rpx;
        justify-content: center;
        font-size: 32rpx;
        color: #333;

        .top3 {
          width: 60rpx;
          height: 60rpx;
          line-height: 60rpx;
          text-align: center;
          background-color: #2979ff;
          color: #fff;
          border-radius: 50%;
        }
      }

      &.name {
        flex: 1;
        font-size: 28rpx;
        color: #333;

        .avatar {
          width: 80rpx;
          height: 80rpx;
          border-radius: 50%;
          margin-right: 20rpx;
          background-color: #fff;
        }
      }

      &.points {
        width: 160rpx;
        flex-direction: column;
        align-items: flex-end;

        text {
          font-size: 32rpx;
          color: #333;
          font-weight: bold;
        }

        .points-change {
          font-size: 24rpx;
          font-weight: normal;
          margin-top: 8rpx;

          &.up {
            color: #19be6b;
          }

          &.down {
            color: #fa3534;
          }
        }
      }
    }

    .empty-tip {
      padding: 100rpx 0;
      text-align: center;

      image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }

      text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}
</style>
