<template>
	<view>
		<view class="t-navbar" :style="[{ 'padding-top': statusBarHeight + 'px' }, customStyle]">
			<view v-if="backIcon" class="t-navbar__back">
				<image class="t-navbar-back" src="@/static/images/arrow-left.svg" @click="backPage" />
				<image class="t-navbar-back home" src="@/static/images/home_line.svg" @click="backHome"></image>
			</view>
			<slot v-if="navStyle === 'custom'" />
			<view v-else-if="navStyle === 'title'" class="t-navbar-title">
				{{ title }}
			</view>
		</view>
		<view v-if="placeholder" class="placeholder-element" :style="{ 'padding-top': statusBarHeight + 'px' }" />
	</view>
</template>

<script>
	export default {
		data() {
			return {
				statusBarHeight: uni.getWindowInfo().statusBarHeight,
				pageNum: getCurrentPages().length,
			}
		},
		props: {
			navStyle: {
				type: String,
				default: 'title'
			},
			title: {
				type: String,
				default: '??'
			},
			customStyle: {
				type: [Object, String],
				default: function() {
					return {
						'background-color': '#fff',
						'color': '#000'
					};
				}
			},
			backIcon: {
				type: Boolean,
				default: true
			},
			placeholder: {
				type: Boolean,
				default: true
			},
			backToast: {
				type: Boolean,
				default: false
			},
			toastText: {
				type: String,
				default: "退出后将不保存当前填写内容，是否要退出当前页面？"
			}
		},
		computed: {
			style() {},
		},
		methods: {
			backPage() {
				if (this.backToast) {
					uni.showModal({
						title: '提示',
						content: this.toastText,
						success: (res) => {
							if (!res.confirm)
								return;
							this.back();
						},
					})
				} else {
					this.back();
				}
			},
			backHome() {
				uni.reLaunch({
					url: '/pages/index/index',
				})
			},
			back() {
				const page = this.pageNum;
				if (page >= 2) {
					uni.navigateBack({
						delta: 1,
					})
				} else {
					uni.reLaunch({
						url: '/pages/index/index',
					})
				}
			}
		}
	};
</script>

<style scoped lang="scss">
	.placeholder-element {
		height: 44px;
		width: 100%;
		box-sizing: content-box;
	}

	.t-navbar {
		position: fixed;
		width: 100%;
		height: 40px;
		display: flex;
		align-items: center;
		top: 0;
		left: 0;
		padding-bottom: 4px;
		z-index: 200;
		box-sizing: content-box;

		.t-navbar-title {
			width: 350rpx;
			position: absolute;
			left: 0;
			right: 0;
			margin: 0 auto;
			white-space: nowrap;
			text-overflow: ellipsis;
			overflow: hidden;
			font-size: 17px;
			text-align: center;
		}

		&__back {
			position: absolute;
			left: 30rpx;
			bottom: 7px;
			display: flex;
			align-items: center;
			justify-content: center;
			height: 32px;
			z-index: 9;
			border-radius: 68px;
			// border: 2rpx solid transparent;
			box-sizing: border-box;
			background-color: rgba(255, 255, 255, 0.6);
			backdrop-filter: blur(4px);

			&::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				border: 2rpx solid #333;
				border-radius: 68px;
				z-index: 1;
			}

			image {
				width: 24px;
				height: 24px;
				flex-shrink: 0;
				padding: 0 20rpx;
				position: relative;
				z-index: 2;

				&:first-child {
					&::after {
						content: none;
					}
				}

				&::after {
					content: "";
					position: absolute;
					left: 0;
					top: 0;
					bottom: 0;
					margin: auto 0;
					width: 1px;
					height: 14px;
					background-color: #333;
				}
			}
		}

		.home {
			width: 24px;
			height: 24px;
		}

	}
</style>