'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/join 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id, type, name = "", out_trade_no } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// 查找是否已加入
		let activity = await vk.baseDao.findById({
			dbName: "activity-data",
			id: id
		});
		// 验证活动是否存在
		if (!activity) return { code: -1, msg: '活动信息已不存在', back: true, }
		// 验证活动是否已结束
		if (activity.end < Date.now()) return { code: -1, msg: '活动已结束' }
		// 验证支付状态并且从订单信息获取参数
		let payInfo;
		if (out_trade_no) {
			payInfo = await vk.baseDao.findByWhereJson({
				dbName: "order-pay-data",
				whereJson: {
					out_trade_no,
					status: 1
				},
			});
			// 查找不到相关订单信息，去寻找原始订单信息
			if (!payInfo) return { code: -1, msg: "订单信息异常，请联系客服或重新尝试接龙" }
			type = payInfo.join_type
			if (type === 'help') name = payInfo.join_name
		}
		let isManager = activity.creator_id == uid
		let state = activity.apply && !isManager ? 'applying' : 'joined'
		let notice_title = ''
		let notice_text = ''
		// 为自己接龙
		if (type == 'myself') {
			let info = await vk.baseDao.findByWhereJson({
				dbName: "activity-join-data",
				whereJson: {
					user_id: uid,
					type: 'myself',
					activity_id: id
				},
			});
			if (info) {
				res.code = -1
				res.msg = '请勿重复加入！'
				return res
			}
			// 通知文本和标题设置
			notice_title = `有新的${state=='applying'?'用户申请加入':'用户加入'}活动`
			notice_text = `用户${userInfo.nickname}${state=='applying'?'申请参加':'加入了'}你发起的活动<${activity.name}>。${state=='applying'?'赶快去审批他们的申请吧！':'让我们一起期待一个有趣的接龙活动！'}`
		} else if (type == 'help') {
			if (!activity.help_join && !isManager) {
				res.code = -1
				res.msg = '本活动不允许代接龙！'
				return res
			}
			notice_title = `有新的${state=='applying'?'用户申请加入':'用户加入'}活动`
			notice_text = `用户${userInfo.nickname}帮助${name}${state=='applying'?'申请加入':'加入了'}你发起的活动<${activity.name}>。${state=='applying'?'赶快去审批他们的申请吧！':'让我们一起期待一个有趣的接龙活动！'}`
		}
		let dataJson = {
			user_id: uid,
			activity_id: id,
			type,
			state
		}
		if (name) dataJson.nickname = name
		if (out_trade_no) dataJson.out_trade_no = out_trade_no
		// 开启事务
		const transaction = await vk.baseDao.startTransaction();
		try {
			let join_id = await vk.baseDao.add({
				db: transaction,
				dbName: "activity-join-data",
				dataJson
			});
			res.msg = state == 'applying' ? '申请成功' : '加入成功!'
			if (activity.creator_id !== uid) {
				await vk.baseDao.add({
					db: transaction,
					dbName: "user-news-data",
					dataJson: {
						accept_user: activity.creator_id,
						title: notice_title,
						text: notice_text,
						read_state: false,
						type: "info",
						link: `/pages/activity/detail?id=${activity._id}`,
					}
				})
			}

			// 更新订单信息
			if (payInfo) {
				await vk.baseDao.updateById({
					db: transaction,
					dbName: "order-pay-data",
					id: payInfo._id,
					dataJson: {
						status: 2,
					},
				});
			}

			await vk.baseDao.add({
				db: transaction,
				dbName: "activity-record-data",
				dataJson: {
					info_id: join_id,
					user_id: uid,
					activity_id: id,
					type: state
				}
			});

			// 提交事务
			await transaction.commit();
			console.log(`transaction succeeded`);
		} catch (err) {
			// 事务回滚
			return await vk.baseDao.rollbackTransaction({
				db: transaction,
				err
			});
		}

		// 发送订阅消息通知
		// if (activity.notice && activity.creator_id !== uid) {
		// 	let user = await vk.baseDao.findById({
		// 		dbName: "uni-id-users",
		// 		id: activity.creator_id,
		// 	});
		// 	let pharse = state == 'applying' ? '用户申请' : '用户加入'
		// 	let start = vk.pubfn.timeFormat(activity.start, "yyyy-MM-dd hh:mm:ss");
		// 	let model = await vk.openapi.weixin.subscribeMessage.send({
		// 		touser: user.wx_openid['mp-weixin'],
		// 		template_id: "qULr23FpxNQTuWrghCj6gA_855wO6isiP-_IM1sCO1A",
		// 		page: "pages/activity/detail?id=" + activity._id,
		// 		data: {
		// 			phrase1: {
		// 				value: pharse
		// 			},
		// 			thing2: {
		// 				value: activity.name
		// 			},
		// 			time3: {
		// 				value: start
		// 			},
		// 			thing5: {
		// 				value: "请在小程序查看活动详情"
		// 			}
		// 		},
		// 		miniprogram_state: "formal",
		// 	});
		// }



		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}