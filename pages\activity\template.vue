<template>
  <view class="page-container">
    <t-navbar title="选择活动模板"></t-navbar>

    <view class="template-list">
      <view class="template-card" v-for="(item, index) in list" :key="item._id" :class="{ selected: item.checked }">
        <!-- Template Header -->
        <view class="template-header">
          <view class="template-name">
            <image v-if="item.type === 'admin'" :src="`${iconUrl}theme_certificate.svg`" class="admin-icon" mode="heightFix" />
            <text class="name-text">{{ item.name }}</text>
          </view>

          <image v-show="item.checked" class="check-icon" src="/static/images/check-on.svg" mode="aspectFill" />
        </view>

        <!-- Template Description -->
        <view class="template-desc" v-if="item.desc">
          {{ item.desc }}
        </view>

        <!-- Template Info -->
        <view class="template-info">
          <view class="info-item">
            <text class="info-label">地址</text>
            <text class="info-value">{{ item.site_info?.name || "暂未设置" }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">时间</text>
            <text class="info-value">{{ timeAreaText(item) }}</text>
          </view>

          <view class="info-item">
            <text class="info-label">活动人数</text>
            <text class="info-value">{{ formatParticipantCount(item) }}</text>
          </view>
        </view>

        <!-- Action Buttons -->
        <view class="action-buttons">
          <wd-button v-if="item.type === 'user'" type="error" size="small" @click="remove(index)" class="delete-btn"> 删除 </wd-button>

          <wd-button type="primary" size="small" @click="checked(index)" class="select-btn" custom-style="background: #0171BC"> 选择 </wd-button>
        </view>
      </view>

      <!-- Empty State -->
      <view v-if="list.length === 0" class="empty-state">
        <text class="empty-text">暂无活动模板</text>
      </view>
    </view>
  </view>
</template>

<script>
import dayjs from "dayjs";

export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      list: [],
      id: null,
    };
  },

  onLoad(options) {
    if (options.id) {
      this.id = options.id;
    }
    this.getData();
  },

  methods: {
    // 处理时间展示
    timeAreaText(data) {
      if (!data.start || !data.end) {
        return "暂未设置";
      }
      return `${data.start}~${data.end}`;
    },

    // 格式化参与人数显示
    formatParticipantCount(item) {
      const minNum = item.min_num || 0;
      const maxNum = item.max_num;

      if (!maxNum) {
        return `${minNum}人~不限制人数`;
      }

      return `${minNum}人~${maxNum}人`;
    },

    // 获取模板数据
    async getData() {
      try {
        const res = await vk.callFunction({
          url: "client/activity/template/pub/list",
        });

        this.list = res.rows.map((item) => ({
          ...item,
          checked: this.id === item._id,
        }));
      } catch (error) {
        console.error("获取模板列表失败:", error);
        vk.toast("获取模板列表失败");
      }
    },

    // 选择模板
    checked(index) {
      // 清除所有选中状态
      this.list.forEach((item) => {
        item.checked = false;
      });

      // 设置当前项为选中状态
      this.list[index].checked = true;

      // 返回结果
      this.returnResult();
    },

    // 返回选择结果
    returnResult() {
      const selectedTemplate = this.list.find((item) => item.checked);

      if (selectedTemplate) {
        // 深拷贝选中的模板数据
        const templateData = JSON.parse(JSON.stringify(selectedTemplate));

        // 触发选择事件
        uni.$emit("select-activity", templateData);

        // 返回上一页
        vk.navigateBack();
      }
    },

    // 删除模板
    remove(index) {
      const template = this.list[index];

      // 只能删除用户创建的模板
      if (template.type === "admin") {
        vk.toast("系统模板不能删除");
        return;
      }

      // 只能删除自己创建的模板
      if (template.creator_id !== this.userInfo._id) {
        vk.toast("只能删除自己创建的模板");
        return;
      }

      uni.showModal({
        title: "确认删除",
        content: "是否要删除该活动模板？",
        success: async (res) => {
          if (res.confirm) {
            try {
              await vk.callFunction({
                url: "client/activity/template/kh/delete",
                title: "删除中...",
                data: {
                  id: template._id,
                },
              });

              vk.toast("删除成功", "none", () => {
                this.getData();
              });
            } catch (error) {
              console.error("删除模板失败:", error);
              vk.toast("删除失败");
            }
          }
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding: 20rpx;
  padding-bottom: env(safe-area-inset-bottom);
}

.template-list {
  padding: 10rpx 0;
}

.template-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  position: relative;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &.selected {
    border: 2rpx solid #0171bc;
    box-shadow: 0 4rpx 20rpx rgba(1, 113, 188, 0.2);
  }

  &:active {
    transform: translateY(2rpx);
  }
}

.template-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.template-name {
  display: flex;
  align-items: center;
  flex: 1;

  .admin-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 12rpx;
  }

  .name-text {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
  }
}

.check-icon {
  width: 48rpx;
  height: 48rpx;
  border-radius: 50%;
}

.template-desc {
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  text-overflow: ellipsis;
  overflow: hidden;
}

.template-info {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 12rpx;
  font-size: 28rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .info-label {
    min-width: 120rpx;
    color: #999;
    margin-right: 20rpx;
    flex-shrink: 0;
  }

  .info-value {
    color: #333;
    flex: 1;
    word-break: break-all;
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 20rpx;

  .delete-btn {
    min-width: 120rpx;
    height: 64rpx;
    font-size: 26rpx;
  }

  .select-btn {
    min-width: 120rpx;
    height: 64rpx;
    font-size: 26rpx;
  }
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;

  .empty-text {
    font-size: 28rpx;
    color: #999;
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .template-card {
    margin-bottom: 16rpx;
    padding: 24rpx;
  }

  .template-name .name-text {
    font-size: 30rpx;
  }

  .template-desc,
  .info-item {
    font-size: 26rpx;
  }

  .action-buttons {
    gap: 16rpx;

    .delete-btn,
    .select-btn {
      min-width: 100rpx;
      height: 60rpx;
      font-size: 24rpx;
    }
  }
}

// 动画效果
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.template-card {
  animation: fadeIn 0.3s ease-out;
}
</style>
