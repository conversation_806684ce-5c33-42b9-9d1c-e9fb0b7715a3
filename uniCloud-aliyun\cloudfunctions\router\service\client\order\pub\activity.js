'use strict';
module.exports = {
	/**
	 * 支付完成后加入活动或者申请加入
	 * @url client/order/pub/activity 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { out_trade_no, activity_id, type, name, user_id, token } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// 验证密钥
		if (token !== 'RvQp4mFvnYhGkaTR7b86WESoOvoSJrV1') return { code: -1, msg: "未知错误" }

		// 找到相应的订单并更新状态
		await vk.baseDao.update({
			dbName: "order-pay-data",
			whereJson: {
				out_trade_no,
			},
			dataJson: {
				status: 1
			}
		});


		// userInfo = await vk.baseDao.findById({
		// 	dbName: "uni-id-users",
		// 	id: user_id,
		// });
		// // 查询活动情况
		// const dbName = "activity-data"
		// let activity = await vk.baseDao.findById({
		// 	dbName,
		// 	id: activity_id,
		// });
		// // 查看活动是否满人
		// let num = await vk.baseDao.count({
		// 	dbName,
		// 	whereJson: {
		// 		activity_id,
		// 		state: 'joined'
		// 	}
		// });
		// if (num >= activity.max_num) return { code: -1, msg: "活动已满人" }
		// // 验证活动是否存在
		// if (!activity) return { code: -1, msg: '活动信息已不存在', back: true, }
		// // 验证活动是否已结束
		// if (activity.end < Date.now()) return { code: -1, msg: '活动已结束' }
		// // 查看活动费用方案


		// // 加入活动
		// let state = activity.apply ? 'applying' : 'joined'
		// let notice_title = ''
		// let notice_text = ''
		// // 为自己接龙
		// if (type == 'myself') {
		// 	let info = await vk.baseDao.findByWhereJson({
		// 		dbName: "activity-join-data",
		// 		whereJson: {
		// 			user_id,
		// 			type: 'myself',
		// 			activity_id
		// 		},
		// 	});
		// 	if (info) {
		// 		res.code = -1
		// 		res.msg = '请勿重复加入！'
		// 		return res
		// 	}
		// 	// 通知文本和标题设置
		// 	notice_title = `有新的${state=='applying'?'用户申请加入':'用户加入'}活动`
		// 	notice_text = `用户${userInfo.nickname}${state=='applying'?'申请参加':'加入了'}你发起的活动<${activity.name}>。${state=='applying'?'赶快去审批他们的申请吧！':'让我们一起期待一个有趣的接龙活动！'}`
		// } else if (type == 'help') {
		// 	if (!activity.help_join && !isManager) return { code: -1, msg: '本活动不允许代接龙！' }
		// 	notice_title = `有新的${state=='applying'?'用户申请加入':'用户加入'}活动`
		// 	notice_text = `用户${userInfo.nickname}帮助${name}${state=='applying'?'申请加入':'加入了'}你发起的活动<${activity.name}>。${state=='applying'?'赶快去审批他们的申请吧！':'让我们一起期待一个有趣的接龙活动！'}`
		// }
		// let dataJson = {
		// 	user_id,
		// 	activity_id,
		// 	type,
		// 	state
		// }
		// if (name) dataJson.nickname = name

		// let join_id = await vk.baseDao.add({
		// 	dbName: "activity-join-data",
		// 	dataJson
		// });

		// // 添加相对应的字段记录
		// if (out_trade_no) {
		// 	let order_info = await vk.baseDao.update({
		// 		dbName: "order-pay-data",
		// 		whereJson: {
		// 			out_trade_no
		// 		},
		// 		dataJson: {
		// 			join_id,
		// 		}
		// 	});
		// }

		// // 消息通知
		// res.msg = state == 'applying' ? '申请成功' : '加入成功!'
		// if (activity.creator_id !== user_id) {
		// 	await vk.baseDao.add({
		// 		dbName: "user-news-data",
		// 		dataJson: {
		// 			accept_user: activity.creator_id,
		// 			title: notice_title,
		// 			text: notice_text,
		// 			read_state: false,
		// 			type: "info",
		// 			link: `/pages/activity/detail?id=${activity._id}`,
		// 		}
		// 	})
		// }

		// await vk.baseDao.add({
		// 	dbName: "activity-record-data",
		// 	dataJson: {
		// 		info_id: join_id,
		// 		user_id,
		// 		activity_id,
		// 		type: state
		// 	}
		// });


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}