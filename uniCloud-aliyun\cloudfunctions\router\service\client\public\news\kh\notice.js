'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/news/kh/notice 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, pageIndex = 1 } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.select({
			dbName: "notice-data",
			getCount: false,
			pageIndex,
			pageSize: 20,
			sortArr: [{ name: "_id", type: "desc" }],
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}