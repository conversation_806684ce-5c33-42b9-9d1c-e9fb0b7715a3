'use strict';
module.exports = {
	/**
	 * 处理activity-join-data数据
	 * 删除activity-join-data不存在的数据
	 * @url script/data/pub/initActivityJoinData 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let ids = await vk.baseDao.select({
			dbName: "activity-data",
			getCount: false,
			getMain: true,
			pageIndex: 1,
			pageSize: 9999,
			fieldJson: { _id: true }
		});
		ids = ids.map(item => item._id)
		
		let num = await vk.baseDao.del({
			dbName: "activity-join-data",
			whereJson: {
				activity_id: _.nin(ids)
			}
		});

		console.log("不存在的数据数量：", num);

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}