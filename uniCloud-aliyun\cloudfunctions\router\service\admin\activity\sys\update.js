module.exports = {
	/**
	 * 修改数据
	 * @url admin/club/sys/update 前端调用的url参数地址
	 * data 请求参数 说明
	 * res 返回参数说明
	 * @param {Number} code 错误码，0表示成功
	 * @param {String} msg 详细信息
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let res = { code: 0, msg: 'ok' };
		// 业务逻辑开始-----------------------------------------------------------
		let {
			_id,
			logo,
			cover,
			banner,
			desc,
			apply,
			business_license,
			name,
			admin_uid,
			address,
		} = data;
		if (vk.pubfn.isNullOne(_id)) {
			return { code: -1, msg: '参数错误' };
		}

		// 查看现在的管理员
		let old = await vk.baseDao.findById({
			dbName: "club-data",
			id: _id,
		});
		// 判断是否变更管理员
		if (old.admin_uid != admin_uid) {
			// 删除以前的管理员所在的俱乐部成员表数据
			let old_info = await vk.baseDao.findByWhereJson({
				dbName: "club-user-data",
				whereJson: {
					user_id: old.admin_uid,
					club_id: _id,
				},
			});
			if (old_info) {
				await vk.baseDao.deleteById({
					dbName: "club-user-data",
					id: old_info._id
				});
			}
			// 新增新管理员到俱乐部成员表中
			let info = await vk.baseDao.findByWhereJson({
				dbName: "club-user-data",
				whereJson: {
					user_id: admin_uid,
					club_id: _id,
				},
			});
			console.log("info", info);
			if (!info) {
				// 把新管理员添加到俱乐部成员表中
				await vk.baseDao.add({
					dbName: "club-user-data",
					dataJson: {
						state: "admin",
						user_id: admin_uid,
						club_id: _id,
					}
				});
			} else {
				if (info.state != 'admin') {
					await vk.baseDao.updateById({
						dbName: "club-user-data",
						id: info._id,
						dataJson: {
							state: "admin"
						},
						getUpdateData: false
					});
				}
			}
		}


		let dbName = "club-data";
		await vk.baseDao.updateById({
			dbName,
			id: _id,
			dataJson: {
				logo,
				cover,
				banner,
				desc,
				apply,
				business_license,
				name,
				admin_uid,
				address,
			}
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}

}