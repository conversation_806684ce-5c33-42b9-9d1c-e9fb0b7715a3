{"bsonType": "object", "required": ["match_id", "user_id", "status"], "permission": {"read": "auth.uid != null", "create": "auth.uid != null", "update": "auth.uid != null", "delete": "auth.role == 'admin'"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "match_id": {"bsonType": "string", "description": "比赛ID"}, "user_id": {"bsonType": "string", "description": "报名用户ID"}, "partner_id": {"bsonType": "string", "description": "双打搭档ID（如果是双打比赛）"}, "status": {"bsonType": "string", "description": "报名状态", "enum": ["pending", "approved", "rejected", "cancelled"], "defaultValue": "pending"}, "payment_status": {"bsonType": "string", "description": "支付状态", "enum": ["unpaid", "paid", "refunded"], "defaultValue": "unpaid"}, "payment_amount": {"bsonType": "number", "description": "支付金额"}, "payment_time": {"bsonType": "timestamp", "description": "支付时间"}, "registration_time": {"bsonType": "timestamp", "description": "报名时间", "forceDefaultValue": {"$env": "now"}}, "remarks": {"bsonType": "string", "description": "备注信息"}}}