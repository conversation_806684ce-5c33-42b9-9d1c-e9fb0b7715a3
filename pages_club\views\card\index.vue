<template>
	<view class="page-content">
		<t-navbar title="会员卡管理" />
		<swiper :current="current" :indicator-dots="true" circular style="height: 30vh;" @change="changeSwiper">
			<swiper-item v-for="(item,index) in list" class="swiper-item" :key="item._id">
				<vip-card :data="item"></vip-card>
			</swiper-item>
		</swiper>
		<view class="form">
			<t-input label="卡名" v-model="list[current].name" :maxlength="9" disabled></t-input>
			<t-input label="价格" v-model="list[current].price" type="digit" disabled unit="元"></t-input>
			<t-group marginTop='30'>
				<t-picker label="类型" v-model="list[current].type" marginTop="0" disabled :options="typeOptions"></t-picker>
				<!-- 会员卡日期 -->
				<template v-if="list[current].type === 'date'">
					<t-picker label="开始时间" mode="date" marginTop="0" disabled v-model="list[current].value" placeholder="请选择开始时间"></t-picker>
				</template>
				<!-- 次卡类型 -->
				<template v-if="list[current].type === 'times'">
					<t-input label="次数" marginTop="0" disabled v-model="list[current].value"></t-input>
				</template>
				<!-- 储蓄卡类型 -->
				<template v-if="list[current].type === 'save'">
					<t-input label="余额" marginTop="0" disabled v-model="list[current].value"></t-input>
				</template>
				<t-input label="有效天数" marginTop="0" disabled v-model="list[current].day"></t-input>
			</t-group>
			<t-picker label="重复购买" :options="repeatOptions" marginTop="0" v-model="repeatValue" placeholder="请选择重复购买方案" @change="changeRepeat"></t-picker>

			<t-actionsheet v-if="action" v-model="actionVisible" :action="action" @click="actionClick"></t-actionsheet>
			<t-bottom>
				<view class="button-box">
					<image class="icon" src="/static/images/setting.svg" mode="heightFix" @click="actionVisible=true"></image>
					<t-button color="#0171BC" text-color="#fff" radius="68" @click="newCard()">新增会员卡</t-button>
				</view>
			</t-bottom>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	import vipCard from '@/components/card/vip-card.vue'
	export default {
		components: {
			vipCard
		},
		data() {
			return {
				id: null,
				info: null,
				typeOptions: [{
						label: '定期卡',
						value: 'date'
					},
					{
						label: '日期卡',
						value: 'day'
					},
					{
						label: '次卡',
						value: 'times'
					},
					{
						label: '储蓄卡',
						value: 'save'
					},
				],

				repeatValue: 0,
				current: 0,
				list: [],

				actionVisible: false,
			}
		},
		computed: {
			action() {
				if (this.list.length == 0) return null
				let ary = [{ label: "查看持卡成员", value: 'users' }, { label: "删除会员卡", value: 'del', color: 'red' }]
				const { enable } = this.list[this.current]
				if (enable) ary.unshift({ label: "停售", value: 'sell', color: 'red' })
				if (!enable) ary.unshift({ label: "开售", value: 'sell', color: vk.getVuex('$app.color.main') })
				return ary
			},
			repeatOptions() {
				if (this.list.length == 0) return []
				let options = [{ label: "不可重复购买", value: 0, }, { label: "重复购买不累计", value: 1 }, { label: "重复购买并累计", value: 2 }]
				const { type } = this.list[this.current]
				if (!type) return []
				switch (type) {
					case 'date':
						return [options[0], options[2]]
					default:
						return options
				}
			},
		},
		watch: {
			"$store.state.$club.info": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.info = newVal
				}
			},
			"$store.state.$club.id": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.id = newVal
					this.getData()
				}
			}
		},
		async onLoad() {
			this.id = vk.getVuex('$club.id')
			uni.$on('refresh-card', () => {
				this.getData()
			})
			this.info = await vk.vuex.getters('$club/getInfo')
			this.getData()
		},
		onUnload() {
			uni.$off('refresh-card')
		},
		methods: {
			actionClick(type) {
				switch (type) {
					case "sell":
						this.updateSell()
						break;
					case 'users':
						uni.navigateTo({
							url: '/pages/user/list'
						})
						break;
					case 'del':
						this.deleteCard()
						break;
				}
				this.actionVisible = false
			},
			async deleteCard() {},
			async updateSell() {
				const { enable, _id } = this.list[this.current]
				const result = !enable
				vk.callFunction({
					url: 'client/club/card/kh/updateCardStatus',
					title: '更新中...',
					data: {
						_id: _id,
						enable: result,
					},
				}).then((data) => {
					vk.toast(data.msg, 'none', true);
					this.$set(this.list[this.current], 'enable', result)
				});
			},
			async getData() {
				const res = await vk.callFunction({
					url: 'client/club/card/kh/list',
					data: {
						club_id: this.id
					}
				})
				this.list = res.rows
				this.current = 0
				this.repeatValue = this.getRepeatValue()
			},
			dateFormat(date) {
				return dayjs(date).format('YYYY-MM-DD')
			},
			changeSwiper(e) {
				this.current = e.detail.current
				this.repeatValue = this.getRepeatValue()
			},
			getRepeatValue() {
				const { repeat, overlay } = this.list[this.current]
				if (!repeat) return 0
				if (repeat && !overlay) return 1
				if (repeat && overlay) return 2
			},
			changeRepeat(e) {
				console.log(e);
				let value = Number(e)
				let repeat = false
				let overlay = false
				switch (value) {
					case 1:
						repeat = true
						break;
					case 2:
						repeat = true
						overlay = true
						break;
				}
				console.log(repeat, overlay);
				vk.callFunction({
					url: 'client/club/card/kh/updateCardStatus',
					title: '更新中...',
					data: {
						_id: this.list[this.current]._id,
						repeat,
						overlay,
					},
				}).then((data) => {
					vk.toast(data.msg, 'none', true);
					this.$set(this.list[this.current], 'repeat', repeat)
					this.$set(this.list[this.current], 'overlay', overlay)
					this.repeatValue = this.getRepeatValue()
				});
			},
			newCard() {
				uni.navigateTo({
					url: '/pages_club/views/card/add'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.swiper-item {
		width: 100%;
		height: 30vh;
		display: flex;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
	}

	.form {
		padding: 30rpx 50rpx;

		.color {
			display: flex;
			align-items: center;
		}

		.color-block {
			width: 60rpx;
			height: 60rpx;
			border-radius: 8rpx;
			margin-right: 20rpx;
			background-position: center;
			background-size: 100%;
		}
	}

	.button-box {
		width: 100%;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;

		.icon {
			height: 48rpx;
		}
	}
</style>