{"id": "vk-uni-pay", "displayName": "【开箱即用】vk-uni-pay 万能支付插件，支持转账到支付宝和微信，兼容任何开发框架。", "version": "1.13.2", "description": "vk-uni-pay是基于uniCloud量身定制的支付插件，兼容任何uniCloud框架。支持H5、PC、小程序、APP，微信公众号、多商户支付、个人支付，为你支付业务扫平障碍。无第三方npm依赖。", "keywords": ["vk-uni-pay、vk云开发", "多商户支付、服务商支付、个人支付", "vue3.0、安全高效、微信虚拟支付", "抖音支付", "支持微信商家转账到零钱V3接口"], "repository": "", "engines": {"HBuilderX": "^3.1.0"}, "dcloudext": {"sale": {"regular": {"price": "8.88"}, "sourcecode": {"price": "99.00"}}, "contact": {"qq": "370725567"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://vkdoc.fsq.pub/vk-uni-pay/", "type": "unicloud-template-page"}, "uni_modules": {"dependencies": ["uni-config-center"], "encrypt": ["uniCloud/cloudfunctions/common/vk-uni-pay/dao/index.js", "uniCloud/cloudfunctions/common/vk-uni-pay/dao/payConfig.js", "uniCloud/cloudfunctions/common/vk-uni-pay/dao/payOrders.js", "uniCloud/cloudfunctions/common/vk-uni-pay/dao/uniIdUsers.js", "uniCloud/cloudfunctions/common/vk-uni-pay/index.js", "uniCloud/cloudfunctions/common/vk-uni-pay/libs/certutil.js", "uniCloud/cloudfunctions/common/vk-uni-pay/libs/common.js", "uniCloud/cloudfunctions/common/vk-uni-pay/libs/crypto.js", "uniCloud/cloudfunctions/common/vk-uni-pay/libs/index.js", "uniCloud/cloudfunctions/common/vk-uni-pay/libs/qrcode.js", "uniCloud/cloudfunctions/common/vk-uni-pay/libs/uniIdToken.js", "uniCloud/cloudfunctions/common/vk-uni-pay/libs/uniPay.js", "uniCloud/cloudfunctions/common/vk-uni-pay/openapi/alipay/index.js", "uniCloud/cloudfunctions/common/vk-uni-pay/openapi/alipay/util.js", "uniCloud/cloudfunctions/common/vk-uni-pay/openapi/index.js", "uniCloud/cloudfunctions/common/vk-uni-pay/openapi/wxpay/wxpayV2.js", "uniCloud/cloudfunctions/common/vk-uni-pay/openapi/wxpay/wxpayV3.js", "uniCloud/cloudfunctions/common/vk-uni-pay/openapi/vkspay/index.js", "uniCloud/cloudfunctions/common/vk-uni-pay/openapi/vkspay/util.js"], "platforms": {"cloud": {"tcb": "y", "aliyun": "y", "alipay": "y"}, "client": {"App": {"app-vue": "y", "app-nvue": "y", "app-uvue": "n"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "n", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y"}, "快应用": {"华为": "y", "联盟": "y"}, "Vue": {"vue2": "y", "vue3": "y"}}}, "extends": {"sn": "AW9ers8Hs1g3XqQ+x1qTJ8r4epJ2wDoq7RXTmbaGq03DJSWmwiK3FgojVEPhNoyO1vJwh78AYH2fJ1kiGKHH3gUnamaXlLOWzVvwPdNhElWH4+7/UQ/wt0R9jeTGn+qeN5eu0H9bbuhBlaVF/kDN5v0KuRaoYm+w+6xG3K6XtOc9YeVB7/7ggheCbsnrSu6r9ZCOn3PGtqbpmF9F9BtmA55o0ejJtaO1uvZ5dGaHigm77usrCYBuwYWb/3c1xIzq7/25M500duSg9lyFhn+GCw+0v7tVXl4yHVPX2UMZc+XdNNfY/cbzxu8Eb7jr5rnNHi7l3cbI34wEPxgVKqlwGI7nji1xyDnZ6yo0mYT30Jl7fS3lZLDaUDJR+lLuoFAzIbkSlbaRKrNu0/41IhSe5WZeLlVJqXwUa7S+hLFAkWdmTkV0kGwlX6xLXiuX+z0O1ShXdurSSIf9z94hMYNJHSLwnNu+G3VGG1uE3oJf2pA="}}}