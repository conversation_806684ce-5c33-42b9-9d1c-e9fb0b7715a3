"use strict";
module.exports = {
  /**
   * XXXnameXXX
   * @url client/public/pub/home 前端调用的url参数地址
   * data 请求参数
   * @param {String} params1  参数1
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid, location } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    res.data = {};
    // 设置功能一览
    res.data.action = [
      {
        image: "https://cdn.cometennis.cn/images/home-page6.jpg",
        label: "网球地图",
        url: "/pages/explore/map/index",
      },
      {
        image: "https://cdn.cometennis.cn/images/home-page3.jpg",
        label: "穿线店铺",
        url: "/pages/explore/string/index",
      },
      {
        image: "https://cdn.cometennis.cn/images/home-page8.jpg",
        label: "场地信息",
        url: "/pages/explore/site/index",
      },
      {
        image: "https://cdn.cometennis.cn/images/home-page7.jpg",
        label: "附近球友",
        url: "/pages/explore/player/index",
      },
      {
        image: "https://cdn.cometennis.cn/images/home-page1.jpeg",
        label: "贡献排行",
        url: "/pages/explore/rank/index",
      },
      // {
      // 	image: "https://cdn.cometennis.cn/images/home-page5.png",
      // 	label: "附近球友",
      // 	url: "/pages/explore/player/index",
      // },
      // {
      //   image: "https://cdn.cometennis.cn/images/home-page5.png",
      //   label: "俱乐部",
      //   url: "/pages/index/club",
      // },
      // {
      //   image: "https://cdn.cometennis.cn/images/home-page5.png",
      //   label: "赛事中心",
      //   url: "/pages_tour/views/home/<USER>",
      // },
      // {
      // 	image: "https://cdn.cometennis.cn/images/home-page4.jpg",
      // 	label: "日记记录",
      // 	url: "",
      // },
    ];
    // 获取并设置轮播图
    let info = await vk.baseDao.findByWhereJson({
      dbName: "system-data",
      whereJson: {
        type: "banner",
      },
    });
    res.data.banner = info ? info.list : [];
    // 获取并设置推荐活动
    if (location) {
      location = _.geoNear({
        geometry: new db.Geo.Point(location.longitude, location.latitude),
        maxDistance: 50000,
        minDistance: 0,
        distanceMultiplier: 0.001,
        distanceField: "distance",
      });
    }
    console.log(location);
    let club_whereJson = {
      open: true,
      start: _.gte(Date.now()),
    };
    if (location) club_whereJson.location = location;
    // 获取活动信息
    let activity = await vk.baseDao.selects({
      dbName: "activity-data",
      getCount: false,
      getMain: true,
      pageIndex: 1,
      pageSize: 2,
      whereJson: club_whereJson,
      sortArr: [
        { name: "distance", type: "asc" },
        { name: "start", type: "asc" },
      ],
      foreignDB: [
        {
          dbName: "club-data",
          localKey: "club_id",
          foreignKey: "_id",
          as: "club_info",
          limit: 1,
        },
        {
          dbName: "club-site-data",
          localKey: "site",
          foreignKey: "_id",
          as: "club_site",
          limit: 1,
        },
        {
          dbName: "activity-join-data",
          localKey: "_id",
          foreignKey: "activity_id",
          as: "join_list",
          limit: 1000,
          fieldJson: { user_id: true, user_info: true },
          whereJson: { state: "joined" },
          foreignDB: [
            {
              dbName: "uni-id-users",
              localKey: "user_id",
              foreignKey: "_id",
              as: "user_info",
              limit: 1,
            },
          ],
        },
        {
          dbName: "activity-join-data",
          localKey: "_id",
          foreignKey: "activity_id",
          as: "my_join",
          limit: 1,
          fieldJson: { state: true },
          whereJson: { user_id: uid, type: "myself" },
        },
      ],
    });
    res.data.activity = activity;
    // 获取场地信息
    let site_whereJson = {};
    const site_foreignDB = [];
    if (location) site_whereJson.location = location;
    if (uid) {
      site_foreignDB.push({
        dbName: "info-recommend-record",
        localKey: "_id",
        foreignKey: "main_id",
        as: "recommend",
        whereJson: {
          user_id: uid,
        },
        fieldJson: { _id: true },
        limit: 1,
      });
    }
    let site = await vk.baseDao.selects({
      dbName: "info-site-data",
      getCount: false,
      pageIndex: 1,
      pageSize: 2,
      getMain: true,
      // 主表where条件
      whereJson: site_whereJson,
      foreignDB: site_foreignDB,
    });
    res.data.site = site;
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  },
};
