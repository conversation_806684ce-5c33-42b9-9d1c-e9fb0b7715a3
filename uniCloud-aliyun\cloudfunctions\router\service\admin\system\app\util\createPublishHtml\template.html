<!DOCTYPE html>
<html>

	<head>
		<meta charset="UTF-8">
		<meta http-equiv="pragma" content="no-cache">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no" />
		<link rel="icon" sizes="any" mask="" href="{@icon_url@}">
		<title>{@name@}</title>
		<style type="text/css">
		*{box-sizing:border-box;-webkit-box-sizing:border-box}body,html{margin:0;padding:0;background-color:#fff;font-size:14px}a{text-decoration:none}.main{padding:0}.info,.main{height:100%}img[src=""]{opacity:0}.bottom-wrap .head-content *,.list-wrap ul li,.logo-wrap *{vertical-align:middle}.downloads-content .package,.pc-info .mask{visibility:hidden}.main{text-align:center;margin:0 auto;max-width:100%;width:700px}.info{position:relative}.downloads-wrap,.logo-wrap{width:100%}.downloads-wrap{bottom:0;position:absolute}.logo-wrap{padding-top:30px}.logo-wrap>img{border-radius:5px;width:79px;height:79px}.logo-wrap h2{font-size:18px;color:#2a2a2a;font-weight:700}.desc-content h2,.screenshots-content h2{font-weight:400;font-size:15px;color:#2a2a2a}.logo-wrap p{margin-top:21px;font-size:18px;font-weight:400;color:#2a2a2a;padding:0 20px}.logo-wrap #show_qrcode{display:block;font-size:10px;color:#007aff;text-decoration:none;margin-top:28px}.logo-wrap #show_qrcode>img{width:11px;height:11px}.logo-wrap #show_qrcode>span{display:inline-block;margin-left:10px}.command-content p{display:inline-block;font-size:12px;color:#2a2a2a}.command-content .stream-token{font-size:12px;color:#2a2a2a;background-color:#f8f8f8;border-radius:3px;padding:5px}.command-content a{display:inline-block;font-size:11px;color:#007aff;width:50px}.qrcode-content{display:none;margin-top:50px;margin-bottom:20px;font-size:20px}.qrcode-content img{width:120px;height:120px}.qrcode-content span{display:block}.qrcode-content .code{display:inline-block;color:#007aff}.qrcode-content p{margin:0;padding:0;display:inline-block}.btn-primary,.btn-secondary{background-color:#007aff;color:#fff;margin-top:10px}.btn{font-size:12px;width:288px;height:40px;line-height:40px;border-radius:5px;outline:0}.btn-primary{border:1px solid #007aff}.btn-secondary{border:1px solid #fff;margin-bottom:10px}.btn.stream{background-color:#fff;color:#007aff}.more img{height:10px;margin-bottom:5px}.more.down img{transform:rotateX(0)}.more.up img{transform:rotateX(180deg)}.screenshots-content{border-top:1px solid #bfbfbf;margin:0 15px;padding:0 24px;position:relative}.screenshots-content h2{margin-top:40px;margin-bottom:21px;text-align:left}.list-wrap{overflow-x:auto;white-space:nowrap;margin-bottom:36px;margin-left:39px}.list-wrap ul{display:block;list-style:none;white-space:nowrap;-webkit-padding-start:0;-moz-padding-start:0}.bottom-wrap .tips-content .tip-desc,.desc-content pre{white-space:pre-wrap;font-family:Helvetica Neue,Helvetica,Arial,sans-serif;word-break:break-all}.list-wrap ul li{display:inline-block;margin:0 9px}.list-wrap ul li:first-child{margin:0 9px 0 0}.list-wrap ul li img{width:176px;height:311px;border:1px solid #949494}.desc-content{border-top:1px solid #bfbfbf;margin:0 15px 36px;padding:0 24px;text-align:center}.desc-content h2{margin-top:40px;margin-bottom:21px;text-align:left}.desc-content pre{width:100%;font-size:14px;color:#2a2a2a;text-align:left}.footer{text-align:center;padding:20px;border-top:1px solid #dae2e3}.show{display:block}.hide{display:none!important}.overlay{width:100%;height:100%;left:0;top:0;background:rgba(0,0,0,.6);z-index:200;position:fixed}.bottom-wrap{position:absolute;bottom:0;z-index:201;width:100%;padding-bottom:18px;background-color:#fff}.bottom-wrap .head-content{padding:12px 0;text-align:left;margin-left:21px}.bottom-wrap .head-content img{display:inline-block;width:28px;height:28px}.bottom-wrap .head-content h2{display:inline-block;font-size:15px;font-weight:700;color:#313131;margin-left:10px}.bottom-wrap .tips-content{text-align:left;margin-left:21px;margin-right:21px;border-top:1px solid #bfbfbf}.bottom-wrap .tips-content .tip-desc{font-size:13px}.bottom-wrap .tips-content .tip-title{font-size:14px;color:#313131;margin:8px 0 0;font-weight:400}.bottom-wrap .tips-content .tip-token{font-size:13px;color:#313131;margin:8px 0 0;display:inline-block;padding:3px;background-color:#96d2fa}.bottom-wrap .tips-content .tip-guide{font-size:14px;margin:8px 0 0}.bottom-wrap .tips-content .guide-launch{margin:15px 0}.bottom-wrap .tips-content ul{-webkit-padding-start:21px}.bottom-wrap .tips-content ul li{margin:0;padding:0;-webkit-padding-start:0}.bottom-wrap button{width:327px}.qrcode-wrap{position:absolute;width:320px;height:350px;background-color:#fff;top:50%;left:50%;margin-top:-164px;margin-left:-160px;border-radius:5px}.qrcode-wrap div.close{display:block;width:30px;height:30px;position:absolute;right:15px;top:10px}.qrcode-wrap div.close img{width:14px;height:14px;margin-top:8px}.qrcode-wrap .kuaima-wrap{width:211px;height:211px;display:inline-block;margin-top:28px;border:1px solid #8eceab;background-color:#fbfffe;border-radius:5px}.qrcode-wrap .kuaima-wrap .qrcode{width:161px;height:161px;margin-top:25px}.qrcode-wrap .kuaima-wrap .app-name{display:block;font-size:12px}.qrcode-wrap .channel{width:211px}.qrcode-wrap .desc{color:#c5c5c5;font-size:12px}.pc-info{display:none;position:relative;text-align:left;margin-top:80px}.pc-name h2{font-size:24px;color:#2a2a2a;font-weight:700}.pc-name p{font-size:22px;color:#2a2a2a;font-weight:400;margin-top:30px;width:520px;word-wrap:break-word;word-break:break-all;overflow:hidden}.contact-list-item .contact-title,.h5-title,.mp-title-input{font-weight:700}.btn-download{width:260px;background-color:#fff;border:1px solid #007aff;color:#007aff}.btn-download .icon{margin-right:5px;line-height:14px}@font-face{font-family:publish_iconfont;src:url(data:font/ttf;base64,AAEAAAALAIAAAwAwR1NVQrD+s+0AAAE4AAAAQk9TLzJXA0gLAAABfAAAAFZjbWFwzutqLwAAAeQAAAGcZ2x5ZgSWU8gAAAOMAAACoGhlYWQO/QtoAAAA4AAAADZoaGVhB94DhQAAALwAAAAkaG10eA/pAAAAAAHUAAAAEGxvY2EBxgD+AAADgAAAAAptYXhwARQAawAAARgAAAAgbmFtZTdoE1AAAAYsAAACzXBvc3S1lTxNAAAI/AAAADsAAQAAA4D/gABcBAAAAAAABAAAAQAAAAAAAAAAAAAAAAAAAAQAAQAAAAEAAHoF6RlfDzz1AAsEAAAAAADV7uPeAAAAANXu494AAP9rBAADRAAAAAgAAgAAAAAAAAABAAAABABfAAYAAAAAAAIAAAAKAAoAAAD/AAAAAAAAAAEAAAAKAB4ALAABREZMVAAIAAQAAAAAAAAAAQAAAAFsaWdhAAgAAAABAAAAAQAEAAQAAAABAAgAAQAGAAAAAQAAAAAAAQP6AZAABQAIAokCzAAAAI8CiQLMAAAB6wAyAQgAAAIABQMAAAAAAAAAAAAAAAAAAAAAAAAAAAAAUGZFZABAAHjmHwOA/4AAXAOAAJUAAAABAAAAAAAABAAAAAPpAAAEAAAABAAAAAAAAAUAAAADAAAALAAAAAQAAAFoAAEAAAAAAGIAAwABAAAALAADAAoAAAFoAAQANgAAAAgACAACAAAAeOYW5h///wAAAHjmFuYf//8AAAAAAAAAAQAIAAgACAAAAAEAAgADAAABBgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAA0AAAAAAAAAAMAAAB4AAAAeAAAAAEAAOYWAADmFgAAAAIAAOYfAADmHwAAAAMAAAAAAHYA/gFQAAAABQAA/+EDvAMYABMAKAAxAEQAUAAAAQYrASIOAh0BISc0LgIrARUhBRUXFA4DJyMnIQcjIi4DPQEXIgYUFjI2NCYXBgcGDwEOAR4BMyEyNicuAicBNTQ+AjsBMhYdAQEZGxpTEiUcEgOQAQoYJx6F/koCogEVHyMcDz4t/kksPxQyIBMIdwwSEhkSEowIBgUFCAICBA8OAW0XFgkFCQoG/qQFDxoVvB8pAh8BDBknGkxZDSAbEmGING4dJRcJAQGAgAETGyAOpz8RGhERGhF8GhYTEhkHEA0IGBoNIyQUAXfkCxgTDB0m4wAAAAAGAAD/awN/AvAADAAhACsANQBDAF4AACUiJj0BNDYyFh0BFAYBMScmNh8BNjIXNzYWDwEeARchPgEFMT4BNCYiBhQWBzE+ATQmIgYUFgMxIiY9ATQ2MhYdARQGBTEOAQcjFRQGIiYnNSMVDgEiJic1Iy4BNREhA18OEhIbExP+ETgGEgc6MnczOgcRBTgxQQr+AQtBASIOEhIbEhLQDhISHBIS4g0TExsSEgJQASAZKB8uHwFhAR4vHgEoGCEB/qwSDcINEhINwg0SAehHCAwHSRYWSQcMCEcbVzY2V0wBERkRERkRAQERGRERGRH+fhINwg0SEg3CDRJPGB8BhBYeHhaEhBYeHhaEAR8YAU8AAAIAAP+/A4gDRAAjAC8AAAE+ATcuASMmBgcuAScOAQcGEhceATM+ATceARc+ATc+ATcGJgM+AScOAQcOARcWNgMGBlkKLmkaOWMdHVMvP2kgPDdCHksvKz81ND4tMUUeIx8BCXCCGR0EJk0bGR8EKkwBZVVVAjghAigEAyMCAjs0bP7/WCtHBB8CAx8CA0EqMkoEAVMBsx1KKAIlHRpKJwIkAAAAAAASAN4AAQAAAAAAAAAVAAAAAQAAAAAAAQAQABUAAQAAAAAAAgAHACUAAQAAAAAAAwAQACwAAQAAAAAABAAQADwAAQAAAAAABQALAEwAAQAAAAAABgAQAFcAAQAAAAAACgArAGcAAQAAAAAACwATAJIAAwABBAkAAAAqAKUAAwABBAkAAQAgAM8AAwABBAkAAgAOAO8AAwABBAkAAwAgAP0AAwABBAkABAAgAR0AAwABBAkABQAWAT0AAwABBAkABgAgAVMAAwABBAkACgBWAXMAAwABBAkACwAmAckKQ3JlYXRlZCBieSBpY29uZm9udApwdWJsaXNoX2ljb25mb250UmVndWxhcnB1Ymxpc2hfaWNvbmZvbnRwdWJsaXNoX2ljb25mb250VmVyc2lvbiAxLjBwdWJsaXNoX2ljb25mb250R2VuZXJhdGVkIGJ5IHN2ZzJ0dGYgZnJvbSBGb250ZWxsbyBwcm9qZWN0Lmh0dHA6Ly9mb250ZWxsby5jb20ACgBDAHIAZQBhAHQAZQBkACAAYgB5ACAAaQBjAG8AbgBmAG8AbgB0AAoAcAB1AGIAbABpAHMAaABfAGkAYwBvAG4AZgBvAG4AdABSAGUAZwB1AGwAYQByAHAAdQBiAGwAaQBzAGgAXwBpAGMAbwBuAGYAbwBuAHQAcAB1AGIAbABpAHMAaABfAGkAYwBvAG4AZgBvAG4AdABWAGUAcgBzAGkAbwBuACAAMQAuADAAcAB1AGIAbABpAHMAaABfAGkAYwBvAG4AZgBvAG4AdABHAGUAbgBlAHIAYQB0AGUAZAAgAGIAeQAgAHMAdgBnADIAdAB0AGYAIABmAHIAbwBtACAARgBvAG4AdABlAGwAbABvACAAcAByAG8AagBlAGMAdAAuAGgAdAB0AHAAOgAvAC8AZgBvAG4AdABlAGwAbABvAC4AYwBvAG0AAAAAAgAAAAAAAAAKAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAQIBAwEEAQUAAXgHYW5kcm9pZANpb3MAAAA=) format("truetype")}.publish_iconfont{font-family:publish_iconfont!important;font-size:14px;font-style:normal;-webkit-font-smoothing:antialiased;-webkit-text-stroke-width:.2px;-moz-osx-font-smoothing:grayscale}#pc_download .btn-download,#pc_download_plus .btn-download{display:inline-block;text-decoration:none;text-align:center}.pc-download.plus-app{display:flex;position:relative;flex-direction:row;padding-top:50px;padding-bottom:50px}.pc-download.plus-app.center{justify-content:center}.pc-download.plus-app .qrcode-row{display:inline-block;width:200px;text-align:center}.pc-download.plus-app .qrcode-row .qrcode-img{width:200px;height:200px}.pc-download.plus-app .qrcode-row .app-tip{margin:0}.pc-download.plus-app .btn-row{display:inline-flex;flex-direction:column;margin-left:80px;justify-content:center}.pc-download.plus-app .btn-row .btn-download{margin:15px 0;width:240px}.pc-download.plus-app:after{position:absolute;right:0;bottom:0;left:0;height:1px;content:"";transform:scaleY(.5);background-color:#c8c7cc}@media only screen and (min-width:768px){.info.mobile{display:none}.pc-info{display:block}.desc-content{padding:0;margin:0 0 80px;border:none}.desc-content pre{color:#818181;font-size:14px}.desc-content h2{font-size:22px;margin-bottom:40px}.screenshots-content{border:none;margin:0;padding:0}.screenshots-content h2{font-size:22px;margin-bottom:40px}.list-wrap{margin-left:0}.tab-item{font-size:20px!important}}@media only screen and (max-width:320px){body{font-size:12px}}.command-content p{width:288px;text-align:left;position:relative}.command-content p .stream-token{width:230px;border:none}.command-content .copy-command{text-align:center;position:absolute;right:0;top:-5px;font-size:14px;line-height:14px;width:auto;background-color:#c5c5c5;color:#fff;padding:6px;border-radius:3px;text-decoration:none}.pc-logo{position:absolute;left:-126px}.pc-logo img{border-radius:5px;width:90px;height:90px}.pc-barcode{position:absolute;right:0;width:160px;height:160px;border:1px solid #8eceab;background-color:#fbfffe;border-radius:5px;text-align:center}.pc-barcode .kuaima-wrap .qrcode{width:120px;height:120px;margin-top:15px}.pc-barcode .kuaima-wrap .app-name{display:block;font-size:12px}.pc-barcode .tip{color:#296a43;margin-top:10px;font-size:14px}.bottom-wrap .head-content .loading-tip{display:inline-block;position:absolute;font-size:12px;right:21px;color:#c5c5c5}.loading-tip .spinner{margin-right:5px}.spinner{display:inline-block;width:16px;height:16px;transform-origin:50%;animation:a 1s step-end infinite}.spinner:after{display:block;width:100%;height:100%;content:"";background-image:url("data:image/svg+xml;charset=utf-8,%3Csvg viewBox='0 0 120 120' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3Cdefs%3E%3Cpath id='a' stroke='%236c6c6c' stroke-width='11' stroke-linecap='round' d='M60 7v20'/%3E%3C/defs%3E%3Cuse xlink:href='%23a' opacity='.27'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(30 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(60 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(90 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(120 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.27' transform='rotate(150 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.37' transform='rotate(180 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.46' transform='rotate(210 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.56' transform='rotate(240 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.66' transform='rotate(270 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.75' transform='rotate(300 60 60)'/%3E%3Cuse xlink:href='%23a' opacity='.85' transform='rotate(330 60 60)'/%3E%3C/svg%3E");background-repeat:no-repeat;background-position:50%;background-size:100%}#download_list a.stream,.pc-info .mask a,.tab-item{display:inline-block;text-decoration:none}@keyframes a{0%{transform:rotate(0)}8.33333333%{transform:rotate(30deg)}16.66666667%{transform:rotate(60deg)}25%{transform:rotate(90deg)}33.33333333%{transform:rotate(120deg)}41.66666667%{transform:rotate(150deg)}50%{transform:rotate(180deg)}58.33333333%{transform:rotate(210deg)}66.66666667%{transform:rotate(240deg)}75%{transform:rotate(270deg)}83.33333333%{transform:rotate(300deg)}91.66666667%{transform:rotate(330deg)}to{transform:rotate(1turn)}}.tip-button-row{text-align:right;padding-right:21px}.pc-info .mask,.tab-content.pc .tab-content-item.app,.tab-content.pc .tab-content-item.mp{text-align:center}.tip-button-row .tip-btn{border:none;height:30px;border-radius:3px;color:#fff;margin:0;padding:0;font-size:12px}.app-tip,.mp-tip,.share-tip{font-size:14px}.tip-button-row .tip-btn.tip-btn-primary{width:70px;background-color:#007aff}.tip-button-row .tip-btn.tip-btn-cancel{background-color:transparent;color:#007aff;width:40px}.pc-info .mask{position:absolute;right:0;width:162px;height:162px;background-color:rgba(0,0,0,.6);border-radius:3px;cursor:pointer}.pc-info .mask a{width:100%;color:#fff;line-height:160px}#download_list .btn.stream.disabled{background-color:#ccc;border-color:#ccc;color:#fff}.tab{border-bottom:1px solid #f7f7f7}.tab-item{padding:10px 18px;margin:0 30px;color:#007aff;font-size:16px}.h5-link,.mp-clip,.mp-title-copy,.website-url{text-decoration:underline}.tab-item.active{border-bottom:2px solid #007aff;font-weight:700}.tab-content{border-bottom:1px solid #f7f7f7;padding:50px 0}.pc-download{margin:0}.tab-content-item{display:none}.pkg-download,.tab-content-item.active,.website-url{display:block}.tab-content.pc .tab-content-item{height:350px}.app-qrcode{border:1px solid #279a36;border-radius:5px;-webkit-border-radius:5px;-moz-border-radius:5px;-ms-border-radius:5px;-o-border-radius:5px}.mp-qrcode{width:135px;height:135px;margin-top:20px}.list-media{position:relative;padding:11px 15px}.list-media-object{float:left;width:42px;height:42px;margin-right:15px}.list-media-body{height:42px;position:relative}.pc-share,.toast{position:absolute}.list-media-desc{overflow:hidden;white-space:nowrap;text-overflow:ellipsis;font-size:14px;color:#8f8f94;margin:0}.contact-list{padding:30px 0 0 50px}.contact-list-item{padding:5px}.contact-list-item .contact-text{color:#007aff}.tab-wrap{bottom:10px;width:100%}#mb_tab .tab-item{margin:0}.pkg-download{margin:0 auto;width:300px;background-color:#007aff;border:1px solid #007aff;color:#fff;padding:8px 10px;border-radius:5px}#mb_tab_content{border:none;padding:15px 0}@media only screen and (min-height:650px){#mb_tab_content{padding:30px 0}}@media only screen and (min-height:750px){#mb_tab_content{padding:50px 0}}#mb_tab_content .app-qrcode,#mb_tab_content .mp-qrcode{width:150px;height:150px}#mb_tab_content .tab-content-item{height:260px}.app-tip{margin:5px 0 10px;height:20px;color:#007aff}.website-label,.website-url{margin-left:50px}.mp-clip{color:#007aff}.mp-tip{color:#777}#mb_tab_content .tab-content-item.contact,#mb_tab_content .tab-content-item.h5{text-align:left}#mb_tab_content .tab-content-item.h5{padding-top:50px;font-size:20px}.footer-wrap,.mp-title-input,.pc-share,.share-qrcode{text-align:center}.website-url{color:#007aff}.pkg-download.hide{display:none}.pkg-download.show{display:block}.pc-share{top:0;right:10px;color:#007aff;cursor:pointer;background-color:#fff;width:200px;z-index:9}.share-qrcode{width:200px;height:200px;box-shadow:0 0 10px #666;border-radius:5px;-webkit-border-radius:5px;-moz-border-radius:5px;-ms-border-radius:5px;-o-border-radius:5px;display:flex;justify-content:center;align-items:center}.share-qrcode.hide{display:none}.share-qrcode img{width:100%;height:100%;border:none}.share-tip{color:#007aff}.top-arrow{width:15px;height:15px;border-top:2px solid #ccc;border-right:2px solid #ccc;transform:rotate(-45deg)}.download-app{margin-bottom:0}@media only screen and (min-height:600px){.tab-wrap{bottom:50px}.download-app{margin-bottom:50px}}@media only screen and (min-height:700px){.tab-wrap{bottom:100px}.download-app{margin-bottom:100px}}.download-app .qrcode{width:150px;height:150px}.download-app .app-tip{margin:0}.download-tip{color:#949494}.footer-wrap{padding:5px}.footer-wrap .power-by{font-size:12px;color:#aeaeae}.footer-wrap .power-by a{color:#007aff}.mp-title{margin-bottom:10px;display:flex;flex-direction:column}.mp-title-input{border:none;font-size:16px}.mp-title-input.input-hidden{visibility:hidden;position:absolute;top:0;z-index:-99}.mp-title-copy{color:#007aff;font-size:16px;margin:0 auto;margin-top:10px}.mp-title-tip{font-size:12px;color:#aeaeae}.toast{left:0;right:0;bottom:50px;margin:auto;width:320px}.toast.hide{display:none}.toast.show{display:block}.toast-text{-webkit-margin-before:0;-webkit-margin-after:0;background-color:#000;color:#fff;border-radius:3px;padding:3px 0;font-size:14px;opacity:.5}.mb-no-pkg{line-height:240px}.pc-no-pkg{line-height:350px}.h5-title{padding:0 0 20px 50px}.h5-content-pc .h5-title{display:inline-block;margin-top:50px}.h5-link{padding:20px 50px 0;color:#007aff;display:inline-block;word-break:break-all;font-size:15px}.h5-content-m .h5-title{display:block}@media screen and (max-width:360px) and (max-height:500px){.logo-wrap{top:5px}#mb_tab_content .tab-content-item{height:220px}.btn-primary,.btn-secondary{margin-top:5px}#mb_tab_content .app-qrcode{width:120px;height:120px}}.mp-wrap{display:flex}.mp-side{width:95px;left:0;border-right:1px solid #eaeaea;height:260px;padding-left:10px}.mp-content{display:flex;flex:1;justify-content:center}.mp-content-item{display:none}.mp-content-item.active{display:block}.mp-side-item{color:#727272;opacity:.45;line-height:40px;text-align:left}.mp-side-item.active{color:#0f0f0f;opacity:1}.mp-side-item.active .inner-item{background-color:#f3f3f3}.mp-icon{width:25px;height:25px;vertical-align:middle;display:inline-block}.pc-mp-item{display:inline-block;margin:0 20px}.tab-content.pc .tab-content-item.mp.active{display:flex;flex-wrap:wrap;justify-content:center}.mp-icon.mp_alipay{background-size:cover;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAMAAAANIilAAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABXFBMVEUAAAAYkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP8YkP////+e/RfuAAAAcnRSTlMAQsv+/crtuOtpB/uPELcnJdlJGJ+eF0rwcQV38/IGcpoVUNxBFl0su7ACXqgSlPxf5pJFTO4eoSPH9WsBRuMgxRHT0G/2+XYDi8iKPNsx1AyVBH7RMIX4dfp7ONgoCpB9NYNUvAjPkzpL3kS6K0hPnSLNskahAAAAAWJLR0RzQQk9zgAAAAd0SU1FB+UIEAYIFtK0fyQAAAH5SURBVEjH7ZbXU8JAEMYXAopBECygKGBD7NgFjBUVe+8Fu9jL/f8P7ibEcXBi7uRFZ9iH5L4Nv8txt/ddAIpRjP8RFqvEfgzJajFkbcw0bEa03ZxlzGoAfxtzSWlpybeRG8B5P3OUyQBymSMvzQM7y11a1lXuFIXdFZTxeOha4RaCvZWkq6olqcZHLb+XG66tC6CS6xtIBOtDKALhWj64sYlEc4s+b63qw0gjD9xGzWi7/qKOCMpOynWZw93Y6OmN6f+gTx1F/0AP3rpN4UGAoWH9te0jtFajVC9+gEFTOA6QyKHJMQWz4xOqSADETeFJfXhsSl3k6RlNpQAmeWBldo6x+fSClpUXbSIw1tRSKkrcstrDyqoQDAtUJmvrubFvbPLDfTKprW3n11njhVM7u6hCe+pi74cJBtcBL8wOj7ZQH5+gcZxSP9pzTpixJYIyZ+fUycVJh08IZnOzipYNXOLwHVdCMGPXN5SM3moqywN/lufqHS1Zej5Xq1zliRvjnixzM4MZz5S+Rfg2hrolsw+PeFPGkjk0luXbkqyLmjRZI5+G8PRMOQ4zQBuKqOpFd62WV5J8NoT2EabKDqWDvzBADK+ftO8NrbeKWgLWS+GmCoP3OF0FTZ8Vdtww0YOuoCOW63C3G8AFfVaAxW72QWM3ZItRjD8WH2JTtkZlpAWeAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIxLTA4LTE2VDA2OjA4OjIyKzAwOjAwoKo0bQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMS0wOC0xNlQwNjowODoyMiswMDowMNH3jNEAAAAASUVORK5CYII=)}.mp-icon.mp_baidu{background-size:cover;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAMAAAANIilAAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABp1BMVEUAAAAwbP8wbP8wbP8wbP8wbP8wbP8wbP8wbP8xbf+Ztv/j6//O3P9YiP8zbv9ejf9Iff+Nrv/////u8/8/d/9BeP/W4v/8/f+Ep//x9f+Tsv/M2//9/v9XiP/E1f9umP+vxv9kkf/T4P+rw/9RhP+/0f+5zf+8z/96oP+Ytv9vmP8ybf/o7/+EqP+nwP92nv+pwv+zyf97ov/d5/83cf9jkP9ShP9ym//C1P9Dev+Hqv/p7//7/P+6zv89df99o/+KrP9fjf+Rsf/D1P9AeP+ivf+yyP+at//L2v86c/+vx/9sl//P3f+nwf98ov+euv9Vhv/k7P80b/++0f82cP/e5//09//q8P/a5f+wx/9Eev+txf/S3/9Qg/9zm/+Or/+Us//s8v/m7f/0+P9Uhf9Cef9Jfv9djP+rxP9Ngf8ybv+jvv84cv/F1v+cuf9Kfv/K2v/2+f9Wh/9xmv9+o/9mk/+Bpv9GfP+Mrf/f6P/B0/9lkf/Y4/82cf9Ogf/Z5P9vmf+7z/+Dp/9ij//4+v+hvP9bi/+Vs/93nv9HfP/f6f/y9v/I2P9TEXL0AAAACHRSTlMACW3D8jTf9mXThGQAAAABYktHRBJ7vGwAAAAAB3RJTUUH5QgQBggW0rR/JAAAAhZJREFUSMft1vdf00AUAHDaQnlh+4AyFARqWGXJssqSFZShsldBScECynCjCDjY8Edz91gBPmken/yin0/fD7nLy3176eVyl6ioSETifwmH0xUNJhHtcjrC2Rg3hA13TBgbCxYRa6odbisr+ja7c6e1BXCaYJexkRIXn5AoK0nJKca8ywRfGec7iJiaBpDuwYxM45ibYKNNyhIYs0G5K4p7iYYrDJwjLebCfSrzbofzCRWAl0rP7fADQioUUlnExkpxSSn4yiQqhwrClWxcJVo/VKrFsaYW6qRN8HFxPfVVDI/83sfi9ElDY1Nac8vTVhZuI9xuHIAO+R86OVi7Mb7QRal8Bs6jls9E7Xl5t7dHpnop1cfAL6jlS4BX/bJSOAAQT6lBBh4aFg1HUqB5lAhWAIxRZZzzqCYmA91TAEV4FtNnM+U1e4bBG8QZnVBwNrlRvmJzbJxNw3vadcPb0Lzat8Cd24vv0ICxf8Hws1b4/ZJ4VsslK4DaKmKv9mH0Ixv7hM2kGsInxM+g48gXLv6K6IfKoLztc4zBb0wcQFyD73gFYxcTZ8nFy3MN/2DiGbH+KOs/9RDgxqZn6xdhPxP/lnN7kQbsDyUkXmbiv/LZ6n5VVRED4qhui/NZC3yx6Lfhjdg5v2a26F9uN7s7mja/t19/MBdaGzw80o8vV0Cz7cbWRmdri7W1udv7rLD3QROJSPxTcQKP12yINhIHYAAAACV0RVh0ZGF0ZTpjcmVhdGUAMjAyMS0wOC0xNlQwNjowODoyMiswMDowMKCqNG0AAAAldEVYdGRhdGU6bW9kaWZ5ADIwMjEtMDgtMTZUMDY6MDg6MjIrMDA6MDDR94zRAAAAAElFTkSuQmCC)}.mp-icon.mp_kuaishou{width:20px;height:20px;margin:0 5px;background-size:cover;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEAAAABACAYAAACqaXHeAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAQKADAAQAAAABAAAAQAAAAABGUUKwAAAGXElEQVR4Ae1aXWwUVRQ+Z5btlrQYIlASA/7GEDW+2IgPYmyJaGgLGLCJiQGJISQmJPqgYCDGfQGDFaJGffCBgBAgViNSWggPiAZIaIAYf+IDUSstErThJ/Kzpbtz/O7A3c4OM7OzszOwiTNJc2bOnHvu+b5777nnzpYouRIGEgYSBhIGEgYSBhIGEgYSBv6XDHCUqGV+w1TKF2YQSYZYhrj32q9R+o/DVyQESEemjQrm2wjwCSEq+mTm09B9TA1NH3L30NU4AFTrsxhsGEeSbRlHRw9/BNCv+rVn4h+Jjfncl/vTz+52vAtNgAW+/8h2EekMEjhIGKQMt/Kukd+C2N8qm1AEVApeg6lFEiomICz4EhIMo4V7c79r3e2UFRFQLXgN1JoJNUKCoYMqJy3wR4/sKLfmAW4Nk9EGZv/28ikk08k0D0p7/f1eNrdKH2gGFMGTvOAXGLOxgvtGPlE2Mq/uEcrLAewQTV5tosgJ0l73EJkyC7vvFDL4AhnGCWpe1c/ZrOnVr11floAw4HUHgUkIsRxk3vhZVMhvEKGZuj8tUX/8gfs13Hdth9Z5Sd8lUA141SH3XPuFxvHsqJeDzM2sAPiDbuBVv1im9+Fvu8xNfyrZrC9GzxlQLXgViL6inAnSnnlDTLNL+y4nMRu20szVS72WhCsBUYLXAUZBQqXgdd9+JNxEQBzgdSDVkBAWvO7bi4SS9REneBVIkJwAswKyeV4HrmQQ8Mzkm/WRExZT/7rNzpxQJEA6O1Ok9vkKtjp7kEHv/UjAKJ2ijNHKPVdPaX/BwPM2Sk2ZgPardTs36UZCkQC6vGtj3OB1UG4koCYYwokRh6XcgLaTtvSycgkPoLchyb3MPX9dwbb3LmqRlbq9m7xBwlr9zsoBqMieErPwvVa6SXuR4/Y+jE7nBCyOPKW4hfeMnNR+UOAsxH72Bba6lNY5ZRG8o+iRtsybIuZ7Tnv7M3P6ce67cuz6DDDNt+wvnfdxgFd96JlAadQKdvBz62ejusNRu3Lwlt++ka5yM4Ekv8qylaX31tPZ0xdQsmaUwnnFBd7Zj36W9nQzCX0L8BO0zikx8r57u7b3mwmY+peo8fmJBp07M80TPPFaXdtrp3FKmZ+ZAfD7fMET7/QrbOzxIXbMBNpo1+l7YG6kQm+TQXkaS4T6rZbM5/Rt3FIWjp+Gw9N+gJ/s1RdGfi9Nf3SJV1Xn2k5wQPK8JGXQpImnMR1K9t2ivZjrpa1uUfE5phsL/NUCanu526sLxHgIH1cX8WfHR71snHrpqHsJX6ffcerVM/zlaPLDZw3eevYyng+5GWGajEMm3hknCUXwJA+4xaB0GPkfqLGxQ39ZluXNaRx0lnjZK70F3pQt3omUDygyr09/NjZ4OYuThIDgT1Km7jnuPn/RAgbwNPRTN+LagoFZ7xZ3efCq1XXMFgFIFntQiOx0c6Z0cZAQCLwqjsiYw19fsr4uqZG3wIsssOISWekk4Qb4z71HXk1/3sR7cweUj7EE2Nj0CtbFd0rpdkVJQiDwTMOI7ln9W4ITvI4RecMiAZJtIz+GSxvekAC/nxofLP6OAcxjlyye2kDD53oB9ukxbekdGuSxKF9E2flV6ZtgTwHBn0cJ9Az3jJ5QXq1DWv+RLwHSGnm3nrDdDfvtIKqNBX7qXQt480BO+yhhykqIk+9sj2smBAbPNEeDtwLPHsQuJQM6aDcZBrzl29VZDDOhIvC9o8dd42pLfwCgr7m989O5jby2L1kCWqlklMshCvA6NpwQKyLBD7zyWbIEdCdKRrUcogRvxdU3+jpAuW5/9vgtW+Ld5FjzN9s4NY7namZC1ODtoeG43In9uQuJ8R67Xt0jIaL8NdZRb+59FFHI6d6X5xKwNwlDQpzgdWzW1njm51acIZ7Eop0C5BeR6k9Q6o59vHv4X23nJwMRoBwEJsGgZfimdwyb5Tf4wuRT3tJ5BDuHPRKeX9BRvgtMQFASggSHKVoT4FWsFREQBQm1BD4UAdWQUGvgQxMQhoRaBK9weNYB6qXfNVYnYK8tc2HfHoTJ7Nud8NzCrDgHOJ2oUxi11y/Hh5M11j8+2AzgPIcss4kaMlnuvvSP7VXN3FZNgEZiEdFR9xgJz8BnqHoSY5BSkw6rHyy0TSITBhIGEgYSBhIGEgYSBhIGEgYSBmqGgf8ATgKVYh72xWoAAAAASUVORK5CYII=)}.mp-icon.mp_lark{width:18px;height:18px;margin-left:2px;margin-right:5px;background-size:cover;background-image:url(data:image/png;base64,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)}.mp-icon.mp_qq{background-size:cover;background-image:url(data:image/png;base64,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)}.mp-icon.mp_toutiao{background-size:cover;background-image:url(data:image/png;base64,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)}.mp-icon.mp_weixin{background-size:cover;background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAMAAAANIilAAAAABGdBTUEAALGPC/xhBQAAACBjSFJNAAB6JgAAgIQAAPoAAACA6AAAdTAAAOpgAAA6mAAAF3CculE8AAABO1BMVEUAAABgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7BgW7D///9XT6ZOAAAAZ3RSTlMAABRXlb7k7wJPtftI1BGsLuE/8TfwF+blAbpd7tgJvEEihGkja9GgHCjyPJh4bLMy+ekMpqV8/BnQh1LrWV7W/nW9YfhfB3NGmdwwWkv3IJYklGTOCxv2sQUNysQnO9XHiJcYLxIVjX/ORAAAAAFiS0dEaMts9CIAAAAHdElNRQflCBAGBSMxqcVKAAAB3UlEQVRIx+2WaVPCMBCGKfd9CFq5FTlE8UAFRBFR8Va88L5FyP//BybShhKStiPjjM70/dCd2eSZbDebTXQ6TZr+gzip9AajyWwBFrPJaND3jSjBVpvdAXpy2G1W1bDTBUi5nOpgtwfQ5HGrgL0+QJfPqwiP+AFL/hEFOMBmARgNyMJjPJDT+JgMHAwBeYWCbDhMmR+JSv8kzIRjg0HHJ+BakwnM8zEWPDXAJoLd7UlGRU+KBadJNoPLajoiuNIMOEuyM+hEzGZySWjmRGeWDs+T8AKEFvMALC3DwhOd83R4hYQLEC5CW1qFSROdK3S4TIPXoC1Cuy46y3S4Io14o7opwLUtlLFtcaRCh/MYzcNagIF24Rxid/BYnQ7X8YRdNH/P0YX34fcgrwTjsO2IXT4UVj465k56LCtsnLBTlKBKt0ZQwho1STYYCcNbdQbrETXAcwHuE2OrcJEsctxFCdpLGswoElyeKEVXDdBEZVkgYUZ54oNhvoZU7Aal7eyWYNOKR/LuXjhMD4/kwinlZvD0/M2+vJIsuxlwVTyp9JZ7/2iCAVWHaIBxmQbItXhZlm/9vOn75Zv+cNcNx30yL7pPFVdsm3HFtn/9ch/yWYHUkTxoOioeNJo0/TV9ARmVgnq6P05YAAAAJXRFWHRkYXRlOmNyZWF0ZQAyMDIxLTA4LTE2VDA2OjA1OjM1KzAwOjAwXFmBzQAAACV0RVh0ZGF0ZTptb2RpZnkAMjAyMS0wOC0xNlQwNjowNTozNSswMDowMC0EOXEAAAAASUVORK5CYII=)}
		</style>
	</head>

	<body>
		<div class="main">
			<!--移动端-->
			<div class="info mobile" id="mobile_info">
				<!--logo-->
				<div class="logo-wrap">
					<img src="{@icon_url@}" />
					<h2>{@name@}</h2>
					<p>{@introduction@}</p>
				</div>
				<!-- 选项卡 -->
				{@if hasApp || hasMP || hasH5 || hasQuickApp@}
				<div class="tab-wrap">
					<div class="tab" id="mb_tab">
						{@if hasApp@}
						<a class="tab-item app" href="javascript:;" data-id="app">App</a>
						{@/if@}
						{@if hasMP@}
						<a class="tab-item mp" href="javascript:;" data-id="mp">小程序</a>
						{@/if@}
						{@if hasH5@}
						<a class="tab-item h5" href="javascript:;" data-id="h5">H5</a>
						{@/if@}
						{@if hasQuickApp@}
						<a class="tab-item quickApp" href="javascript:;" data-id="quickApp">快应用</a>
						{@/if@}
					</div>
					<div class="tab-content mb" id="mb_tab_content">
						{@if hasApp@}
						<div class="tab-content-item app">
							<img class="app-qrcode" style="width:150px;height:150px;" src="">
							<p class="app-tip">扫码获取</p>
							<a class="pkg-download hide" href="javascript:;">下载安装
								<span class="pkg-size"></span>
							</a>
							<p class="download-tip hide"><span class="os-name">Android</span>平台尚未发布，敬请期待~</p>
						</div>
						{@/if@}
						{@if hasMP@}
						<div class="tab-content-item mp">
							<div class="mp-wrap" id="mp-wrap">
								<div class="mp-side">
									{@each mpKeys@}
									<div class="mp-side-item" data-id="{@$value@}">
										<div class="inner-item">
											<div class="mp-icon {@$value@}"></div>
											{@mpNames[$value]@}
										</div>
									</div>
									{@/each@}
								</div>
								<div class="mp-content">
									{@each mpKeys@}
									<div class="mp-content-item {@$value@}">
										<div class="mp-title" style="display: none;">
											<span class="mp-title-input">{@$data[$value].name@}</span>
											<a class="mp-title-copy" data-platform="{@mpNames[$value]@}" data-name="{@$data[$value].name@}" href="javascript:;">复制</a>
											<p class="mp-title-tip">复制成功后可在{@mpNames[$value]@}中搜索小程序</p>
										</div>
										<img class="mp-qrcode" src="{@$data[$value].qrcode_url@}" />
										<p class="mp-tip hide">长按图片识别小程序</p>
									</div>
									{@/each@}
								</div>
							</div>
						</div>
						{@/if@}
						{@if hasH5@}
						<div class="tab-content-item h5" style="padding-top: 50px;">
							<div class="h5-content-m">
								<label class="h5-title">链接地址</label>
								<a class="h5-link" href="{@h5.url@}">{@h5.url@}</a>
							</div>
						</div>
						{@/if@}
						{@if hasQuickApp@}
						<div class="tab-content-item quickApp">
							<img class="mp-qrcode" style="width: 150px;height: 150px;" src="{@quickapp.qrcode_url@}">
							<p class="tip">快应用</p>
							<p class="mp-title-tip">扫描二维码或复制名称后可在手机应用市场中搜索快应用</p>
						</div>
						{@/if@}
					</div>
				</div>
				{@/if@}

				<!--应用下载-->
				<div class="downloads-wrap">
					<div class="more down" id="scroll_page">
						<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACQAAAASCAYAAAAzI3woAAAA3klEQVRIS73WsQ0CIRQG4EdorJzAbXSMV5A4gZM4gQkFY+g2TmBlQzDvAniedxwPOGggBHhfoPgR4JvW+khDpdQjzPXop3UFFTXG7K21FwDYOefuvVCEEUKcAOAtpbwi4msAedTBWnvuhZpgboj4JEcE9UQtYf5APVApzCxoS9QaZhG0BSoHkwS1ROViVkEtUBxMFqgGxcVkg0pQJRgWiIMqxbBBOagaTBEoharFFIPmUMNh36CM2cT9MfxkGXezMSYGst9LqV2MqbqhgB+hoBbTBBSej/rwheDe9Hj9BxnlByK3W3UrAAAAAElFTkSuQmCC" />
					</div>
				</div>
			</div>
			<!--pc端-->
			<div class="pc-info">
				<div class="pc-logo">
					<img src="{@icon_url@}" />
				</div>
				<div class="pc-share">
					<p class="share-tip">获取本页二维码</p>
					<div class="share-qrcode hide">
						<img class="app-qrcode" alt="二维码" src="">
					</div>
				</div>
				<div class="pc-name">
					<h2>{@name@}</h2>
					<p>{@introduction@}</p>
				</div>
				<!-- 选项卡 -->
				{@if hasApp || hasMP || hasH5 || hasQuickApp@}
				<div class="pc-tab-wrap">
					<div class="tab" id="pc_tab">
						{@if hasApp@}
						<a class="tab-item app" href="javascript:;" data-id="app">App</a>
						{@/if@}
						{@if hasMP@}
						<a class="tab-item mp" href="javascript:;" data-id="mp">小程序</a>
						{@/if@}
						{@if hasH5@}
						<a class="tab-item h5" href="javascript:;" data-id="h5">H5</a>
						{@/if@}
						{@if hasQuickApp@}
						<a class="tab-item quickApp" href="javascript:;" data-id="quickApp">快应用</a>
						{@/if@}
					</div>
					<div class="tab-content pc" id="pc_tab_content">
						{@if hasApp@}
						<div class="tab-content-item app">
							<img class="app-qrcode" style="width:256px;height:256px;" src="">
							<p class="app-tip">扫码获取</p>
							<div class="pc-download" id="pc_download">
								<a href="javascript:;" class="btn btn-download android" type="button">
									<span class="icon icon-android publish_iconfont">&#xe616;</span>
									<span>Android平台下载</span>
								</a>
								<a href="javascript:;" class="btn btn-download ios" type="button">
									<span class="icon icon-ios publish_iconfont">&#xe61f;</span>
									<span>iOS平台下载</span>
								</a>
							</div>
						</div>
						{@/if@}
						{@if hasMP@}
						<div class="tab-content-item mp">
							{@each mpKeys@}
							<div class="pc-mp-item">
								<img class="mp-qrcode" src="{@$data[$value].qrcode_url@}" />
								<div class="mp-tip">扫二维码识别小程序</div>
								<div class="mp-platform">{@mpNames[$value]@}小程序</div>
							</div>
							{@/each@}
						</div>
						{@/if@}
						{@if hasH5@}
						<div class="tab-content-item h5">
							<div class="h5-content-pc">
								<label class="h5-title">链接地址</label><br>
								<a class="h5-link" href="{@h5.url@}" target="_blank">{@h5.url@}</a>
							</div>
						</div>
						{@/if@}
						{@if hasQuickApp@}
						<div class="tab-content-item quickApp" style="text-align: center">
							<img class="mp_qrcode" style="width: 200px;height: 200px" src="{@quickapp.qrcode_url@}">
							<p class="tip">快应用</p>
							<p class="mp-title-tip">扫描二维码或复制名称后可在手机应用市场中搜索快应用</p>
						</div>
						{@/if@}
					</div>
				</div>
				{@/if@}
			</div>
			<!--应用描述-->
			{@if description && description.length@}
			<div class="desc-content">
				<h2>应用描述</h2>
				<pre>{@description@}</pre>
			</div>
			{@/if@}
			<!--应用截图-->
			{@if screenshot && screenshot.length@}
			<div class="screenshots-content">
				<h2>应用截图</h2>
			</div>
			<div class="list-wrap">
				<ul>
					{@each screenshot@}
					<li>
						<img src="{@$value@}">
					</li>
					{@/each@}
				</ul>
			</div>
			{@/if@}
			<div class="toast hide">
				<p class="toast-text">复制成功</p>
			</div>
		</div>

		<script type="text/javascript">
			window.$app = {
				appid: '{@appid@}',
				android_url: '{@android_url@}',
				ios_url: '{@ios_url@}',
				icon_url: '{@icon_url@}',
				android_size: '',
				ios_size: '',
				mpPlatforms: {
					weixin: 'mp_weixin',
					alipay: 'mp_alipay',
					toutiao: 'mp_toutiao',
					lark: 'mp_lark',
					kuaishou: 'mp_kuaishou',
					qq: 'mp_qq',
					baidu: 'mp_baidu'
				},
				mp: {@hasMP ? 1 : 0@},
				app: {@hasApp ? 1 : 0@},
				h5: {@hasH5 ? 1 : 0@},
				quickApp: {@hasQuickApp ? 1 : 0@},
				// mp_toutiao_url: 'http://t.zijieimg.com/YdVU8V/?a=b',
				mp_toutiao_url: false
			};
		</script>
	<script type="text/javascript">!function(t){var e={};function n(r){if(e[r])return e[r].exports;var i=e[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,n),i.l=!0,i.exports}n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=30)}([function(t,e){var n,r=[0,26,44,70,100,134,172,196,242,292,346,404,466,532,581,655,733,815,901,991,1085,1156,1258,1364,1474,1588,1706,1828,1921,2051,2185,2323,2465,2611,2761,2876,3034,3196,3362,3532,3706];e.getSymbolSize=function(t){if(!t)throw new Error('"version" cannot be null or undefined');if(t<1||t>40)throw new Error('"version" should be in range from 1 to 40');return 4*t+17},e.getSymbolTotalCodewords=function(t){return r[t]},e.getBCHDigit=function(t){for(var e=0;0!==t;)e++,t>>>=1;return e},e.setToSJISFunction=function(t){if("function"!=typeof t)throw new Error('"toSJISFunc" is not a valid function.');n=t},e.isKanjiModeEnabled=function(){return void 0!==n},e.toSJIS=function(t){return n(t)}},function(t,e,n){var r=n(4),i=n(5);e.NUMERIC={id:"Numeric",bit:1,ccBits:[10,12,14]},e.ALPHANUMERIC={id:"Alphanumeric",bit:2,ccBits:[9,11,13]},e.BYTE={id:"Byte",bit:4,ccBits:[8,16,16]},e.KANJI={id:"Kanji",bit:8,ccBits:[8,10,12]},e.MIXED={bit:-1},e.getCharCountIndicator=function(t,e){if(!t.ccBits)throw new Error("Invalid mode: "+t);if(!r.isValid(e))throw new Error("Invalid version: "+e);return e>=1&&e<10?t.ccBits[0]:e<27?t.ccBits[1]:t.ccBits[2]},e.getBestModeForData=function(t){return i.testNumeric(t)?e.NUMERIC:i.testAlphanumeric(t)?e.ALPHANUMERIC:i.testKanji(t)?e.KANJI:e.BYTE},e.toString=function(t){if(t&&t.id)return t.id;throw new Error("Invalid mode")},e.isValid=function(t){return t&&t.bit&&t.ccBits},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"numeric":return e.NUMERIC;case"alphanumeric":return e.ALPHANUMERIC;case"kanji":return e.KANJI;case"byte":return e.BYTE;default:throw new Error("Unknown mode: "+t)}}(t)}catch(t){return n}}},function(t,e){e.L={bit:1},e.M={bit:0},e.Q={bit:3},e.H={bit:2},e.isValid=function(t){return t&&void 0!==t.bit&&t.bit>=0&&t.bit<4},e.from=function(t,n){if(e.isValid(t))return t;try{return function(t){if("string"!=typeof t)throw new Error("Param is not a string");switch(t.toLowerCase()){case"l":case"low":return e.L;case"m":case"medium":return e.M;case"q":case"quartile":return e.Q;case"h":case"high":return e.H;default:throw new Error("Unknown EC Level: "+t)}}(t)}catch(t){return n}}},function(t,e,n){var r=n(2),i=[1,1,1,1,1,1,1,1,1,1,2,2,1,2,2,4,1,2,4,4,2,4,4,4,2,4,6,5,2,4,6,6,2,5,8,8,4,5,8,8,4,5,8,11,4,8,10,11,4,9,12,16,4,9,16,16,6,10,12,18,6,10,17,16,6,11,16,19,6,13,18,21,7,14,21,25,8,16,20,25,8,17,23,25,9,17,23,34,9,18,25,30,10,20,27,32,12,21,29,35,12,23,34,37,12,25,34,40,13,26,35,42,14,28,38,45,15,29,40,48,16,31,43,51,17,33,45,54,18,35,48,57,19,37,51,60,19,38,53,63,20,40,56,66,21,43,59,70,22,45,62,74,24,47,65,77,25,49,68,81],o=[7,10,13,17,10,16,22,28,15,26,36,44,20,36,52,64,26,48,72,88,36,64,96,112,40,72,108,130,48,88,132,156,60,110,160,192,72,130,192,224,80,150,224,264,96,176,260,308,104,198,288,352,120,216,320,384,132,240,360,432,144,280,408,480,168,308,448,532,180,338,504,588,196,364,546,650,224,416,600,700,224,442,644,750,252,476,690,816,270,504,750,900,300,560,810,960,312,588,870,1050,336,644,952,1110,360,700,1020,1200,390,728,1050,1260,420,784,1140,1350,450,812,1200,1440,480,868,1290,1530,510,924,1350,1620,540,980,1440,1710,570,1036,1530,1800,570,1064,1590,1890,600,1120,1680,1980,630,1204,1770,2100,660,1260,1860,2220,720,1316,1950,2310,750,1372,2040,2430];e.getBlocksCount=function(t,e){switch(e){case r.L:return i[4*(t-1)+0];case r.M:return i[4*(t-1)+1];case r.Q:return i[4*(t-1)+2];case r.H:return i[4*(t-1)+3];default:return}},e.getTotalCodewordsCount=function(t,e){switch(e){case r.L:return o[4*(t-1)+0];case r.M:return o[4*(t-1)+1];case r.Q:return o[4*(t-1)+2];case r.H:return o[4*(t-1)+3];default:return}}},function(t,e){e.isValid=function(t){return!isNaN(t)&&t>=1&&t<=40}},function(t,e){var n="(?:[u3000-u303F]|[u3040-u309F]|[u30A0-u30FF]|[uFF00-uFFEF]|[u4E00-u9FAF]|[u2605-u2606]|[u2190-u2195]|u203B|[u2010u2015u2018u2019u2025u2026u201Cu201Du2225u2260]|[u0391-u0451]|[u00A7u00A8u00B1u00B4u00D7u00F7])+",r="(?:(?![A-Z0-9 $%*+\\-./:]|"+(n=n.replace(/u/g,"\\u"))+")(?:.|[\r\n]))+";e.KANJI=new RegExp(n,"g"),e.BYTE_KANJI=new RegExp("[^A-Z0-9 $%*+\\-./:]+","g"),e.BYTE=new RegExp(r,"g"),e.NUMERIC=new RegExp("[0-9]+","g"),e.ALPHANUMERIC=new RegExp("[A-Z $%*+\\-./:]+","g");var i=new RegExp("^"+n+"$"),o=new RegExp("^[0-9]+$"),a=new RegExp("^[A-Z0-9 $%*+\\-./:]+$");e.testKanji=function(t){return i.test(t)},e.testNumeric=function(t){return o.test(t)},e.testAlphanumeric=function(t){return a.test(t)}},function(t,e){function n(t){if("number"==typeof t&&(t=t.toString()),"string"!=typeof t)throw new Error("Color should be defined as hex string");var e=t.slice().replace("#","").split("");if(e.length<3||5===e.length||e.length>8)throw new Error("Invalid hex color: "+t);3!==e.length&&4!==e.length||(e=Array.prototype.concat.apply([],e.map((function(t){return[t,t]})))),6===e.length&&e.push("F","F");var n=parseInt(e.join(""),16);return{r:n>>24&255,g:n>>16&255,b:n>>8&255,a:255&n,hex:"#"+e.slice(0,6).join("")}}e.getOptions=function(t){t||(t={}),t.color||(t.color={});var e=void 0===t.margin||null===t.margin||t.margin<0?4:t.margin,r=t.width&&t.width>=21?t.width:void 0,i=t.scale||4;return{width:r,scale:r?4:i,margin:e,color:{dark:n(t.color.dark||"#000000ff"),light:n(t.color.light||"#ffffffff")},type:t.type,rendererOpts:t.rendererOpts||{}}},e.getScale=function(t,e){return e.width&&e.width>=t+2*e.margin?e.width/(t+2*e.margin):e.scale},e.getImageWidth=function(t,n){var r=e.getScale(t,n);return Math.floor((t+2*n.margin)*r)},e.qrToImageData=function(t,n,r){for(var i=n.modules.size,o=n.modules.data,a=e.getScale(i,r),s=Math.floor((i+2*r.margin)*a),c=r.margin*a,u=[r.color.light,r.color.dark],l=0;l<s;l++)for(var h=0;h<s;h++){var d=4*(l*s+h),f=r.color.light;if(l>=c&&h>=c&&l<s-c&&h<s-c)f=u[o[Math.floor((l-c)/a)*i+Math.floor((h-c)/a)]?1:0];t[d++]=f.r,t[d++]=f.g,t[d++]=f.b,t[d]=f.a}}},function(t,e,n){var r=n(9),i=n(10),o=n(28),a=n(29);function s(t,e,n,o,a){var s=[].slice.call(arguments,1),c=s.length,u="function"==typeof s[c-1];if(!u&&!r())throw new Error("Callback required as last argument");if(!u){if(c<1)throw new Error("Too few arguments provided");return 1===c?(n=e,e=o=void 0):2!==c||e.getContext||(o=n,n=e,e=void 0),new Promise((function(r,a){try{var s=i.create(n,o);r(t(s,e,o))}catch(t){a(t)}}))}if(c<2)throw new Error("Too few arguments provided");2===c?(a=n,n=e,e=o=void 0):3===c&&(e.getContext&&void 0===a?(a=o,o=void 0):(a=o,o=n,n=e,e=void 0));try{var l=i.create(n,o);a(null,t(l,e,o))}catch(t){a(t)}}e.create=i.create,e.toCanvas=s.bind(null,o.render),e.toDataURL=s.bind(null,o.renderToDataURL),e.toString=s.bind(null,(function(t,e,n){return a.render(t,n)}))},function(t,e){function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}if(Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null==this)throw new TypeError('"this" is null or not defined');var e=Object(this),n=e.length>>>0;if("function"!=typeof t)throw new TypeError("predicate must be a function");for(var r=arguments[1],i=0;i<n;){var o=e[i];if(t.call(r,o,i,e))return o;i++}}}),Array.prototype.includes||Object.defineProperty(Array.prototype,"includes",{value:function(t,e){if(null==this)throw new TypeError('"this" is null or not defined');var n=Object(this),r=n.length>>>0;if(0===r)return!1;var i,o,a=0|e,s=Math.max(a>=0?a:r-Math.abs(a),0);for(;s<r;){if((i=n[s])===(o=t)||"number"==typeof i&&"number"==typeof o&&isNaN(i)&&isNaN(o))return!0;s++}return!1}}),!window.Promise){var r=function(t){if("function"!=typeof t)throw Error("Promise resolver undefined is not a function");var e=this;this.status=i,this.onFulfilledCbs=[],this.onRejectedCbs=[],this.resolve=function(t){e.status===i&&(e.status=o,e.value=t,e.onFulfilledCbs.forEach((function(t){return t()})),e.onFulfilledCbs.length=0,e.onRejectedCbs.length=0)},this.reject=function(t){e.status===i&&(e.status=a,e.value=t,e.onRejectedCbs.forEach((function(t){return t()})),e.onFulfilledCbs.length=0,e.onRejectedCbs.length=0)};try{t(this.resolve,this.reject)}catch(t){this.reject(t)}},i="PENDING",o="RESOLVE",a="REJECT",s=function t(e,r,i,o){if(e===r)return o(new TypeError("Chaining cycle detected for promise #<Promise>"));var a;if("object"===n(r)&&null!==r||"function"==typeof r)try{var s=r.then;if("function"==typeof s)s.call(r,(function(n){a||(a=!0,t(e,n,i,o))}),(function(t){a||(a=!0,o(t))}));else{if(a)return;a=!0,i(r)}}catch(t){if(a)return;a=!0,o(t)}else i(r)};r.prototype.then=function(t,e){var n=this;t="function"==typeof t?t:function(t){return t},e="function"==typeof e?e:function(t){throw t};var c=new r((function(r,u){n.status===o&&setTimeout((function(){try{var e=t(n.value);s(c,e,r,u)}catch(t){u(t)}}),0),n.status===a&&setTimeout((function(){try{var t=e(n.value);s(c,t,r,u)}catch(t){u(t)}}),0),n.status===i&&(n.onFulfilledCbs.push((function(){setTimeout((function(){try{var e=t(n.value);s(c,e,r,u)}catch(t){u(t)}}),0)})),n.onRejectedCbs.push((function(){setTimeout((function(){try{var t=e(n.value);s(c,t,r,u)}catch(t){u(t)}}),0)})))}));return c},r.prototype.finally=function(t){return this.then((function(e){return r.resolve(t(e)).then((function(){return e}))}),(function(e){return r.resolve(t(data)).then((function(){throw e}))}))},r.resolve=function(t){if(function(t){return("object"===n(t)&&null!==t||"function"==typeof t)&&"function"==typeof t.then}(t)){var e=new r((function(n,r){setTimeout((function(){try{s(e,t,n,r)}catch(t){r(t)}}),0)}));return e}return new r((function(e,n){e(t)}))},r.defer=r.deferred=function(){var t={};return t.promise=new r((function(e,n){t.resolve=e,t.reject=n})),t},r.all=function(t){return new r((function(e,n){for(var i=[],o=0,a=function(n,r){i[n]=r,++o===t.length&&e(i)},s=0;s<t.length;s++)!function(e){var n=t[e];r.resolve(n).then((function(t){a(e,t)}))}(s)}))},window.Promise=r}},function(t,e){t.exports=function(){return"function"==typeof Promise&&Promise.prototype&&Promise.prototype.then}},function(t,e,n){var r=n(0),i=n(2),o=n(11),a=n(12),s=n(13),c=n(14),u=n(15),l=n(3),h=n(16),d=n(19),f=n(20),p=n(1),g=n(21);function m(t,e,n){var r,i,o=t.size,a=f.getEncodedBits(e,n);for(r=0;r<15;r++)i=1==(a>>r&1),r<6?t.set(r,8,i,!0):r<8?t.set(r+1,8,i,!0):t.set(o-15+r,8,i,!0),r<8?t.set(8,o-r-1,i,!0):r<9?t.set(8,15-r-1+1,i,!0):t.set(8,15-r-1,i,!0);t.set(o-8,8,1,!0)}function v(t,e,n){var i=new o;n.forEach((function(e){i.put(e.mode.bit,4),i.put(e.getLength(),p.getCharCountIndicator(e.mode,t)),e.write(i)}));var a=8*(r.getSymbolTotalCodewords(t)-l.getTotalCodewordsCount(t,e));for(i.getLengthInBits()+4<=a&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(0);for(var s=(a-i.getLengthInBits())/8,c=0;c<s;c++)i.put(c%2?17:236,8);return function(t,e,n){for(var i=r.getSymbolTotalCodewords(e),o=l.getTotalCodewordsCount(e,n),a=i-o,s=l.getBlocksCount(e,n),c=s-i%s,u=Math.floor(i/s),d=Math.floor(a/s),f=d+1,p=u-d,g=new h(p),m=0,v=new Array(s),A=new Array(s),w=0,y=new Uint8Array(t.buffer),E=0;E<s;E++){var I=E<c?d:f;v[E]=y.slice(m,m+I),A[E]=g.encode(v[E]),m+=I,w=Math.max(w,I)}var b,C,N=new Uint8Array(i),M=0;for(b=0;b<w;b++)for(C=0;C<s;C++)b<v[C].length&&(N[M++]=v[C][b]);for(b=0;b<p;b++)for(C=0;C<s;C++)N[M++]=A[C][b];return N}(i,t,e)}function A(t,e,n,i){var o;if(Array.isArray(t))o=g.fromArray(t);else{if("string"!=typeof t)throw new Error("Invalid data");var l=e;if(!l){var h=g.rawSplit(t);l=d.getBestVersionForData(h,n)}o=g.fromString(t,l||40)}var f=d.getBestVersionForData(o,n);if(!f)throw new Error("The amount of data is too big to be stored in a QR Code");if(e){if(e<f)throw new Error("\nThe chosen QR Code version cannot contain this amount of data.\nMinimum version required to store current data is: "+f+".\n")}else e=f;var p=v(e,n,o),A=r.getSymbolSize(e),w=new a(A);return function(t,e){for(var n=t.size,r=c.getPositions(e),i=0;i<r.length;i++)for(var o=r[i][0],a=r[i][1],s=-1;s<=7;s++)if(!(o+s<=-1||n<=o+s))for(var u=-1;u<=7;u++)a+u<=-1||n<=a+u||(s>=0&&s<=6&&(0===u||6===u)||u>=0&&u<=6&&(0===s||6===s)||s>=2&&s<=4&&u>=2&&u<=4?t.set(o+s,a+u,!0,!0):t.set(o+s,a+u,!1,!0))}(w,e),function(t){for(var e=t.size,n=8;n<e-8;n++){var r=n%2==0;t.set(n,6,r,!0),t.set(6,n,r,!0)}}(w),function(t,e){for(var n=s.getPositions(e),r=0;r<n.length;r++)for(var i=n[r][0],o=n[r][1],a=-2;a<=2;a++)for(var c=-2;c<=2;c++)-2===a||2===a||-2===c||2===c||0===a&&0===c?t.set(i+a,o+c,!0,!0):t.set(i+a,o+c,!1,!0)}(w,e),m(w,n,0),e>=7&&function(t,e){for(var n,r,i,o=t.size,a=d.getEncodedBits(e),s=0;s<18;s++)n=Math.floor(s/3),r=s%3+o-8-3,i=1==(a>>s&1),t.set(n,r,i,!0),t.set(r,n,i,!0)}(w,e),function(t,e){for(var n=t.size,r=-1,i=n-1,o=7,a=0,s=n-1;s>0;s-=2)for(6===s&&s--;;){for(var c=0;c<2;c++)if(!t.isReserved(i,s-c)){var u=!1;a<e.length&&(u=1==(e[a]>>>o&1)),t.set(i,s-c,u),-1===--o&&(a++,o=7)}if((i+=r)<0||n<=i){i-=r,r=-r;break}}}(w,p),isNaN(i)&&(i=u.getBestMask(w,m.bind(null,w,n))),u.applyMask(i,w),m(w,n,i),{modules:w,version:e,errorCorrectionLevel:n,maskPattern:i,segments:o}}e.create=function(t,e){if(void 0===t||""===t)throw new Error("No input text");var n,o,a=i.M;return void 0!==e&&(a=i.from(e.errorCorrectionLevel,i.M),n=d.from(e.version),o=u.from(e.maskPattern),e.toSJISFunc&&r.setToSJISFunction(e.toSJISFunc)),A(t,n,a,o)}},function(t,e){function n(){this.buffer=[],this.length=0}n.prototype={get:function(t){var e=Math.floor(t/8);return 1==(this.buffer[e]>>>7-t%8&1)},put:function(t,e){for(var n=0;n<e;n++)this.putBit(1==(t>>>e-n-1&1))},getLengthInBits:function(){return this.length},putBit:function(t){var e=Math.floor(this.length/8);this.buffer.length<=e&&this.buffer.push(0),t&&(this.buffer[e]|=128>>>this.length%8),this.length++}},t.exports=n},function(t,e){function n(t){if(!t||t<1)throw new Error("BitMatrix size must be defined and greater than 0");this.size=t,this.data=new Uint8Array(t*t),this.reservedBit=new Uint8Array(t*t)}n.prototype.set=function(t,e,n,r){var i=t*this.size+e;this.data[i]=n,r&&(this.reservedBit[i]=!0)},n.prototype.get=function(t,e){return this.data[t*this.size+e]},n.prototype.xor=function(t,e,n){this.data[t*this.size+e]^=n},n.prototype.isReserved=function(t,e){return this.reservedBit[t*this.size+e]},t.exports=n},function(t,e,n){var r=n(0).getSymbolSize;e.getRowColCoords=function(t){if(1===t)return[];for(var e=Math.floor(t/7)+2,n=r(t),i=145===n?26:2*Math.ceil((n-13)/(2*e-2)),o=[n-7],a=1;a<e-1;a++)o[a]=o[a-1]-i;return o.push(6),o.reverse()},e.getPositions=function(t){for(var n=[],r=e.getRowColCoords(t),i=r.length,o=0;o<i;o++)for(var a=0;a<i;a++)0===o&&0===a||0===o&&a===i-1||o===i-1&&0===a||n.push([r[o],r[a]]);return n}},function(t,e,n){var r=n(0).getSymbolSize;e.getPositions=function(t){var e=r(t);return[[0,0],[e-7,0],[0,e-7]]}},function(t,e){e.Patterns={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7};var n=3,r=3,i=40,o=10;function a(t,n,r){switch(t){case e.Patterns.PATTERN000:return(n+r)%2==0;case e.Patterns.PATTERN001:return n%2==0;case e.Patterns.PATTERN010:return r%3==0;case e.Patterns.PATTERN011:return(n+r)%3==0;case e.Patterns.PATTERN100:return(Math.floor(n/2)+Math.floor(r/3))%2==0;case e.Patterns.PATTERN101:return n*r%2+n*r%3==0;case e.Patterns.PATTERN110:return(n*r%2+n*r%3)%2==0;case e.Patterns.PATTERN111:return(n*r%3+(n+r)%2)%2==0;default:throw new Error("bad maskPattern:"+t)}}e.isValid=function(t){return null!=t&&""!==t&&!isNaN(t)&&t>=0&&t<=7},e.from=function(t){return e.isValid(t)?parseInt(t,10):void 0},e.getPenaltyN1=function(t){for(var e=t.size,r=0,i=0,o=0,a=null,s=null,c=0;c<e;c++){i=o=0,a=s=null;for(var u=0;u<e;u++){var l=t.get(c,u);l===a?i++:(i>=5&&(r+=n+(i-5)),a=l,i=1),(l=t.get(u,c))===s?o++:(o>=5&&(r+=n+(o-5)),s=l,o=1)}i>=5&&(r+=n+(i-5)),o>=5&&(r+=n+(o-5))}return r},e.getPenaltyN2=function(t){for(var e=t.size,n=0,i=0;i<e-1;i++)for(var o=0;o<e-1;o++){var a=t.get(i,o)+t.get(i,o+1)+t.get(i+1,o)+t.get(i+1,o+1);4!==a&&0!==a||n++}return n*r},e.getPenaltyN3=function(t){for(var e=t.size,n=0,r=0,o=0,a=0;a<e;a++){r=o=0;for(var s=0;s<e;s++)r=r<<1&2047|t.get(a,s),s>=10&&(1488===r||93===r)&&n++,o=o<<1&2047|t.get(s,a),s>=10&&(1488===o||93===o)&&n++}return n*i},e.getPenaltyN4=function(t){for(var e=0,n=t.data.length,r=0;r<n;r++)e+=t.data[r];return Math.abs(Math.ceil(100*e/n/5)-10)*o},e.applyMask=function(t,e){for(var n=e.size,r=0;r<n;r++)for(var i=0;i<n;i++)e.isReserved(i,r)||e.xor(i,r,a(t,i,r))},e.getBestMask=function(t,n){for(var r=Object.keys(e.Patterns).length,i=0,o=1/0,a=0;a<r;a++){n(a),e.applyMask(a,t);var s=e.getPenaltyN1(t)+e.getPenaltyN2(t)+e.getPenaltyN3(t)+e.getPenaltyN4(t);e.applyMask(a,t),s<o&&(o=s,i=a)}return i}},function(t,e,n){var r=n(17);function i(t){this.genPoly=void 0,this.degree=t,this.degree&&this.initialize(this.degree)}i.prototype.initialize=function(t){this.degree=t,this.genPoly=r.generateECPolynomial(this.degree)},i.prototype.encode=function(t){if(!this.genPoly)throw new Error("Encoder not initialized");var e=new Uint8Array(t.length+this.degree);e.set(t);var n=r.mod(e,this.genPoly),i=this.degree-n.length;if(i>0){var o=new Uint8Array(this.degree);return o.set(n,i),o}return n},t.exports=i},function(t,e,n){var r=n(18);e.mul=function(t,e){for(var n=new Uint8Array(t.length+e.length-1),i=0;i<t.length;i++)for(var o=0;o<e.length;o++)n[i+o]^=r.mul(t[i],e[o]);return n},e.mod=function(t,e){for(var n=new Uint8Array(t);n.length-e.length>=0;){for(var i=n[0],o=0;o<e.length;o++)n[o]^=r.mul(e[o],i);for(var a=0;a<n.length&&0===n[a];)a++;n=n.slice(a)}return n},e.generateECPolynomial=function(t){for(var n=new Uint8Array([1]),i=0;i<t;i++)n=e.mul(n,new Uint8Array([1,r.exp(i)]));return n}},function(t,e){var n=new Uint8Array(512),r=new Uint8Array(256);!function(){for(var t=1,e=0;e<255;e++)n[e]=t,r[t]=e,256&(t<<=1)&&(t^=285);for(var i=255;i<512;i++)n[i]=n[i-255]}(),e.log=function(t){if(t<1)throw new Error("log("+t+")");return r[t]},e.exp=function(t){return n[t]},e.mul=function(t,e){return 0===t||0===e?0:n[r[t]+r[e]]}},function(t,e,n){var r=n(0),i=n(3),o=n(2),a=n(1),s=n(4),c=r.getBCHDigit(7973);function u(t,e){return a.getCharCountIndicator(t,e)+4}function l(t,e){var n=0;return t.forEach((function(t){var r=u(t.mode,e);n+=r+t.getBitsLength()})),n}e.from=function(t,e){return s.isValid(t)?parseInt(t,10):e},e.getCapacity=function(t,e,n){if(!s.isValid(t))throw new Error("Invalid QR Code version");void 0===n&&(n=a.BYTE);var o=8*(r.getSymbolTotalCodewords(t)-i.getTotalCodewordsCount(t,e));if(n===a.MIXED)return o;var c=o-u(n,t);switch(n){case a.NUMERIC:return Math.floor(c/10*3);case a.ALPHANUMERIC:return Math.floor(c/11*2);case a.KANJI:return Math.floor(c/13);case a.BYTE:default:return Math.floor(c/8)}},e.getBestVersionForData=function(t,n){var r,i=o.from(n,o.M);if(Array.isArray(t)){if(t.length>1)return function(t,n){for(var r=1;r<=40;r++){if(l(t,r)<=e.getCapacity(r,n,a.MIXED))return r}}(t,i);if(0===t.length)return 1;r=t[0]}else r=t;return function(t,n,r){for(var i=1;i<=40;i++)if(n<=e.getCapacity(i,r,t))return i}(r.mode,r.getLength(),i)},e.getEncodedBits=function(t){if(!s.isValid(t)||t<7)throw new Error("Invalid QR Code version");for(var e=t<<12;r.getBCHDigit(e)-c>=0;)e^=7973<<r.getBCHDigit(e)-c;return t<<12|e}},function(t,e,n){var r=n(0),i=r.getBCHDigit(1335);e.getEncodedBits=function(t,e){for(var n=t.bit<<3|e,o=n<<10;r.getBCHDigit(o)-i>=0;)o^=1335<<r.getBCHDigit(o)-i;return 21522^(n<<10|o)}},function(t,e,n){var r=n(1),i=n(22),o=n(23),a=n(24),s=n(26),c=n(5),u=n(0),l=n(27);function h(t){return unescape(encodeURIComponent(t)).length}function d(t,e,n){for(var r,i=[];null!==(r=t.exec(n));)i.push({data:r[0],index:r.index,mode:e,length:r[0].length});return i}function f(t){var e,n,i=d(c.NUMERIC,r.NUMERIC,t),o=d(c.ALPHANUMERIC,r.ALPHANUMERIC,t);return u.isKanjiModeEnabled()?(e=d(c.BYTE,r.BYTE,t),n=d(c.KANJI,r.KANJI,t)):(e=d(c.BYTE_KANJI,r.BYTE,t),n=[]),i.concat(o,e,n).sort((function(t,e){return t.index-e.index})).map((function(t){return{data:t.data,mode:t.mode,length:t.length}}))}function p(t,e){switch(e){case r.NUMERIC:return i.getBitsLength(t);case r.ALPHANUMERIC:return o.getBitsLength(t);case r.KANJI:return s.getBitsLength(t);case r.BYTE:return a.getBitsLength(t)}}function g(t,e){var n,c=r.getBestModeForData(t);if((n=r.from(e,c))!==r.BYTE&&n.bit<c.bit)throw new Error('"'+t+'" cannot be encoded with mode '+r.toString(n)+".\n Suggested mode is: "+r.toString(c));switch(n!==r.KANJI||u.isKanjiModeEnabled()||(n=r.BYTE),n){case r.NUMERIC:return new i(t);case r.ALPHANUMERIC:return new o(t);case r.KANJI:return new s(t);case r.BYTE:return new a(t)}}e.fromArray=function(t){return t.reduce((function(t,e){return"string"==typeof e?t.push(g(e,null)):e.data&&t.push(g(e.data,e.mode)),t}),[])},e.fromString=function(t,n){for(var i=function(t,e){for(var n={},i={start:{}},o=["start"],a=0;a<t.length;a++){for(var s=t[a],c=[],u=0;u<s.length;u++){var l=s[u],h=""+a+u;c.push(h),n[h]={node:l,lastCount:0},i[h]={};for(var d=0;d<o.length;d++){var f=o[d];n[f]&&n[f].node.mode===l.mode?(i[f][h]=p(n[f].lastCount+l.length,l.mode)-p(n[f].lastCount,l.mode),n[f].lastCount+=l.length):(n[f]&&(n[f].lastCount=l.length),i[f][h]=p(l.length,l.mode)+4+r.getCharCountIndicator(l.mode,e))}}o=c}for(var g=0;g<o.length;g++)i[o[g]].end=0;return{map:i,table:n}}(function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];switch(i.mode){case r.NUMERIC:e.push([i,{data:i.data,mode:r.ALPHANUMERIC,length:i.length},{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.ALPHANUMERIC:e.push([i,{data:i.data,mode:r.BYTE,length:i.length}]);break;case r.KANJI:e.push([i,{data:i.data,mode:r.BYTE,length:h(i.data)}]);break;case r.BYTE:e.push([{data:i.data,mode:r.BYTE,length:h(i.data)}])}}return e}(f(t,u.isKanjiModeEnabled())),n),o=l.find_path(i.map,"start","end"),a=[],s=1;s<o.length-1;s++)a.push(i.table[o[s]].node);return e.fromArray(function(t){return t.reduce((function(t,e){var n=t.length-1>=0?t[t.length-1]:null;return n&&n.mode===e.mode?(t[t.length-1].data+=e.data,t):(t.push(e),t)}),[])}(a))},e.rawSplit=function(t){return e.fromArray(f(t,u.isKanjiModeEnabled()))}},function(t,e,n){var r=n(1);function i(t){this.mode=r.NUMERIC,this.data=t.toString()}i.getBitsLength=function(t){return 10*Math.floor(t/3)+(t%3?t%3*3+1:0)},i.prototype.getLength=function(){return this.data.length},i.prototype.getBitsLength=function(){return i.getBitsLength(this.data.length)},i.prototype.write=function(t){var e,n,r;for(e=0;e+3<=this.data.length;e+=3)n=this.data.substr(e,3),r=parseInt(n,10),t.put(r,10);var i=this.data.length-e;i>0&&(n=this.data.substr(e),r=parseInt(n,10),t.put(r,3*i+1))},t.exports=i},function(t,e,n){var r=n(1),i=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z"," ","$","%","*","+","-",".","/",":"];function o(t){this.mode=r.ALPHANUMERIC,this.data=t}o.getBitsLength=function(t){return 11*Math.floor(t/2)+t%2*6},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){var e;for(e=0;e+2<=this.data.length;e+=2){var n=45*i.indexOf(this.data[e]);n+=i.indexOf(this.data[e+1]),t.put(n,11)}this.data.length%2&&t.put(i.indexOf(this.data[e]),6)},t.exports=o},function(t,e,n){var r=n(25),i=n(1);function o(t){this.mode=i.BYTE,this.data=new Uint8Array(r(t))}o.getBitsLength=function(t){return 8*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){for(var e=0,n=this.data.length;e<n;e++)t.put(this.data[e],8)},t.exports=o},function(t,e,n){"use strict";t.exports=function(t){for(var e=[],n=t.length,r=0;r<n;r++){var i=t.charCodeAt(r);if(i>=55296&&i<=56319&&n>r+1){var o=t.charCodeAt(r+1);o>=56320&&o<=57343&&(i=1024*(i-55296)+o-56320+65536,r+=1)}i<128?e.push(i):i<2048?(e.push(i>>6|192),e.push(63&i|128)):i<55296||i>=57344&&i<65536?(e.push(i>>12|224),e.push(i>>6&63|128),e.push(63&i|128)):i>=65536&&i<=1114111?(e.push(i>>18|240),e.push(i>>12&63|128),e.push(i>>6&63|128),e.push(63&i|128)):e.push(239,191,189)}return new Uint8Array(e).buffer}},function(t,e,n){var r=n(1),i=n(0);function o(t){this.mode=r.KANJI,this.data=t}o.getBitsLength=function(t){return 13*t},o.prototype.getLength=function(){return this.data.length},o.prototype.getBitsLength=function(){return o.getBitsLength(this.data.length)},o.prototype.write=function(t){var e;for(e=0;e<this.data.length;e++){var n=i.toSJIS(this.data[e]);if(n>=33088&&n<=40956)n-=33088;else{if(!(n>=57408&&n<=60351))throw new Error("Invalid SJIS character: "+this.data[e]+"\nMake sure your charset is UTF-8");n-=49472}n=192*(n>>>8&255)+(255&n),t.put(n,13)}},t.exports=o},function(t,e,n){"use strict";var r={single_source_shortest_paths:function(t,e,n){var i={},o={};o[e]=0;var a,s,c,u,l,h,d,f=r.PriorityQueue.make();for(f.push(e,0);!f.empty();)for(c in s=(a=f.pop()).value,u=a.cost,l=t[s]||{})l.hasOwnProperty(c)&&(h=u+l[c],d=o[c],(void 0===o[c]||d>h)&&(o[c]=h,f.push(c,h),i[c]=s));if(void 0!==n&&void 0===o[n]){var p=["Could not find a path from ",e," to ",n,"."].join("");throw new Error(p)}return i},extract_shortest_path_from_predecessor_list:function(t,e){for(var n=[],r=e;r;)n.push(r),t[r],r=t[r];return n.reverse(),n},find_path:function(t,e,n){var i=r.single_source_shortest_paths(t,e,n);return r.extract_shortest_path_from_predecessor_list(i,n)},PriorityQueue:{make:function(t){var e,n=r.PriorityQueue,i={};for(e in t=t||{},n)n.hasOwnProperty(e)&&(i[e]=n[e]);return i.queue=[],i.sorter=t.sorter||n.default_sorter,i},default_sorter:function(t,e){return t.cost-e.cost},push:function(t,e){var n={value:t,cost:e};this.queue.push(n),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};t.exports=r},function(t,e,n){var r=n(6);e.render=function(t,e,n){var i=n,o=e;void 0!==i||e&&e.getContext||(i=e,e=void 0),e||(o=function(){try{return document.createElement("canvas")}catch(t){throw new Error("You need to specify a canvas element")}}()),i=r.getOptions(i);var a=r.getImageWidth(t.modules.size,i),s=o.getContext("2d"),c=s.createImageData(a,a);return r.qrToImageData(c.data,t,i),function(t,e,n){t.clearRect(0,0,e.width,e.height),e.style||(e.style={}),e.height=n,e.width=n,e.style.height=n+"px",e.style.width=n+"px"}(s,o,a),s.putImageData(c,0,0),o},e.renderToDataURL=function(t,n,r){var i=r;void 0!==i||n&&n.getContext||(i=n,n=void 0),i||(i={});var o=e.render(t,n,i),a=i.type||"image/png",s=i.rendererOpts||{};return o.toDataURL(a,s.quality)}},function(t,e,n){var r=n(6);function i(t,e){var n=t.a/255,r=e+'="'+t.hex+'"';return n<1?r+" "+e+'-opacity="'+n.toFixed(2).slice(1)+'"':r}function o(t,e,n){var r=t+e;return void 0!==n&&(r+=" "+n),r}e.render=function(t,e,n){var a=r.getOptions(e),s=t.modules.size,c=t.modules.data,u=s+2*a.margin,l=a.color.light.a?"<path "+i(a.color.light,"fill")+' d="M0 0h'+u+"v"+u+'H0z"/>':"",h="<path "+i(a.color.dark,"stroke")+' d="'+function(t,e,n){for(var r="",i=0,a=!1,s=0,c=0;c<t.length;c++){var u=Math.floor(c%e),l=Math.floor(c/e);u||a||(a=!0),t[c]?(s++,c>0&&u>0&&t[c-1]||(r+=a?o("M",u+n,.5+l+n):o("m",i,0),i=0,a=!1),u+1<e&&t[c+1]||(r+=o("h",s),s=0)):i++}return r}(c,s,a.margin)+'"/>',d='viewBox="0 0 '+u+" "+u+'"',f='<svg xmlns="http://www.w3.org/2000/svg" '+(a.width?'width="'+a.width+'" height="'+a.width+'" ':"")+d+' shape-rendering="crispEdges">'+l+h+"</svg>\n";return"function"==typeof n&&n(null,f),f}},function(t,e,n){"use strict";n.r(e);n(8);var r={};!function(t,e,n){var r=n.createElement("style");r.innerText='.wx-share-guide-mask {\tposition: absolute;\tz-index: 900000;\twidth: 100%;\theight: 100%;\tleft: 0px;\ttop: 0px;\tbackground-color: rgba(0, 0, 0, 0.75);\tdisplay: none;\toverflow: hidden;\tbox-sizing: border-box;}.wx-share-guide-image {\tbackground-image: url("data:image/png;base64,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");\tbackground-position: center top;\tbackground-repeat: no-repeat;\tbackground-size: 100%;\tposition: absolute;\theight: 300px;\twidth: 280px;\tright: 28px;\ttop: 5px;\tbox-sizing: border-box;\tz-index: 900001;}.wx-share-guide-text {\tposition: absolute;\tcolor: #eee;\tz-index: 900002;\twidth: 260px;\tright: 10px;\ttop: 180px;\tbox-sizing: border-box;\tfont-size:15px;}.wx-share-guide-mask.active {\tdisplay: block;}',(n.head||n.body).appendChild(r),t.show=function(e){t.mask||(t.mask=n.createElement("div"),t.mask.classList.add("wx-share-guide-mask"),n.body.appendChild(t.mask),t.image=n.createElement("div"),t.image.classList.add("wx-share-guide-image"),t.mask.appendChild(t.image),t.text=n.createElement("div"),t.text.classList.add("wx-share-guide-text"),t.mask.appendChild(t.text),t.mask.addEventListener("click",(function(){t.hide()}))),e&&(t.text.innerHTML=e),t.mask.classList.add("active")},t.hide=function(){t.mask&&t.mask.classList.remove("active")}}(r,window,document);var i=n(7);var o,a=(o=n.n(i).a.toCanvas,function(){var t=Array.prototype.slice.call(arguments);return new Promise((function(e,n){t.push((function(t,r){t?n(t):e(r)})),o.apply(null,t)}))}),s=function(t){var e=t.canvas,n=t.content,r=t.width,i=void 0===r?0:r,o=t.nodeQrCodeOptions,s=void 0===o?{}:o;return s.errorCorrectionLevel=s.errorCorrectionLevel||u(n),c(n,s).then((function(t){return s.scale=0===i?void 0:i/t*4,a(e,n,s)}))},c=function(t,e){var n=document.createElement("canvas");return a(n,t,e).then((function(){return n.width}))},u=function(t){return t.length>36?"M":t.length>16?"Q":"H"},l=function(t){var e=t.canvas,n=(t.content,t.logo);if(n){var r=e.width,i=n.logoSize,o=void 0===i?.15:i,a=n.borderColor,s=n.bgColor,c=void 0===s?a||"#ffffff":s,u=n.borderSize,l=void 0===u?.05:u,d=n.crossOrigin,f=n.borderRadius,p=void 0===f?8:f,g=n.logoRadius,m=void 0===g?0:g,v="string"==typeof n?n:n.src,A=r*o,w=r*(1-o)/2,y=r*(o+l),E=r*(1-o-l)/2,I=e.getContext("2d");h(I)(E,E,y,y,p),I.fillStyle=c,I.fill();var b=new Image;(d||m)&&b.setAttribute("crossOrigin",d||"anonymous"),b.src=v;return new Promise((function(t,e){b.onload=function(){m?function(t){var e=document.createElement("canvas");e.width=w+A,e.height=w+A,e.getContext("2d").drawImage(t,w,w,A,A),h(I)(w,w,A,A,m),I.fillStyle=I.createPattern(e,"no-repeat"),I.fill()}(b):function(t){I.drawImage(t,w,w,A,A)}(b),t()}}))}},h=function(t){return function(e,n,r,i,o){var a=Math.min(r,i);return o>a/2&&(o=a/2),t.beginPath(),t.moveTo(e+o,n),t.arcTo(e+r,n,e+r,n+i,o),t.arcTo(e+r,n+i,e,n+i,o),t.arcTo(e,n+i,e,n,o),t.arcTo(e,n,e+r,n,o),t.closePath(),t}},d=function(t){return s(t).then((function(){return t})).then(l)},f=function(t,e){var n=t.src,r=document.createElement("a");r.download=e,r.href=n,document.body.appendChild(r),r.click(),document.body.removeChild(r)},p={toCanvas:d,toImage:function(t){var e=document.createElement("canvas");return t.canvas=e,t.logo&&("string"==typeof t.logo&&(t.logo={src:t.logo}),t.logo.crossOrigin="Anonymous"),d(t).then((function(){var n=t.image,r=void 0===n?new Image:n,i=t.downloadName,o=void 0===i?"qr-code":i,a=t.download;if(r.src=e.toDataURL(),!0===a||function(t){return"function"==typeof t}(a)){(a=!0===a?function(t){return t()}:a)((function(){f(r,o)}))}}))}};!function(){var t=Array.prototype.find,e=Array.prototype.includes,n=function(t){var e=[];if(t)for(var n in t)t.hasOwnProperty(n)&&e.push(encodeURIComponent(n)+"="+encodeURIComponent(t[n]));return e.length?e.join("&"):""},i=function(t,e,r,i){var o=new window.XMLHttpRequest;o.onreadystatechange=function(){if(4==o.readyState)if(o.status>=200&&o.status<300||304===o.status||0===o.status&&o.responseText){if(i){var t=!1;try{t=JSON.parse(o.responseText)}catch(e){t=!1}}i&&i(t,o)}else i&&i(!1)};var a="string"==typeof r?r:n(r);return a&&"GET"===t&&(e=function(t,e){return"string"!=typeof e&&(e=n(e)),(t+"&"+e).replace(/[&?]{1,2}/,"?")}(e,a)),o.open(t,e),"POST"===t&&"string"!=typeof r&&o.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),o.send("POST"===t&&a),o};function o(t){var e,n=t.data,r=t.success,i=t.fail,o=t.complete;if(document.execCommand){var a=document.getElementById("#clipboard");a&&a.remove();var s=document.createElement("textarea");s.id="#clipboard",s.style.position="absolute",s.style.top="-9999px",s.style.zIndex="-9999",document.body.appendChild(s),s.value=n,s.focus(),s.select(),e=document.execCommand("Copy",!1,null),s.blur()}e?r&&r():i&&i(),o&&o()}Date.now;var a=function(){return document.documentElement?document.documentElement.clientHeight:window.innerHeight},s={isVisible:function(t){return"string"==typeof t&&(t=this.querySelector(t)),"none"!==t.style.display},toggle:function(t){"string"==typeof t&&(t=this.querySelector(t));var e=t.className;~e.indexOf("show")?t.className=e.replace("show","hide"):~e.indexOf("hide")&&(t.className=e.replace("hide","show"))},querySelector:function(t){var e=null;if(document.querySelector)e=document.querySelector(t);else{for(var n=t.split(" "),r=document,i=0,o=n.length;i<o;i++)r=this.handleQuery(n[i],r);e=r}return e},handleQuery:function(t,e){var n=null;return/^#([\w-]+)$/.test(t)?n=e.getElementById(t.substring(1)):/^\.([\w-]+)$/.test(t)?n=e.getElementsByClassName(t.substring(1))[0]:/^[\w-]+$/.test(t)&&(n=e.getElementsByTagName(t)[0]),n},setFullScreen:function(t){"string"==typeof t&&(t=this.querySelector(t)),t.style.height=a()+"px"}},c={android:!1,ios:!1,mobile:!1,pc:!1,init:function(){var t=navigator.userAgent;t.match(/(Android);?[\s\/]+([\d+.]+)?/)?(this.android=!0,this.mobile=!0):t.match(/(iPhone\sOS)\s([\d_]+)/)?(this.ios=!0,this.mobile=!0):this.pc=!0}};c.init();var u={uc:!1,qihoo:!1,chrome:!1,safari:!1,weixin:!1,weibo:!1,qq:!1,meizu:!1,alipay:!1,baidu:!1,version:0,init:function(){var t=navigator.userAgent;if(c.android&&(t.indexOf("360")>0&&(this.qihoo=!0),t.indexOf("UCBrowser")>0&&(this.uc=!0),t.indexOf("MZ-")>0&&(this.meizu=!0),t.indexOf("Chrome")>0&&window.chrome)){this.chrome=!0;var e=t.match(/chrome\/(\d+)/i);e&&(this.version=e[1]-0)}~t.indexOf("MicroMessenger")&&(this.weixin=!0),~t.indexOf("Weibo")&&(this.weibo=!0),(c.ios&&/ QQ/i.test(navigator.userAgent)||c.android&&/MQQBrowser/i.test(navigator.userAgent)&&/QQ/i.test(navigator.userAgent.split("MQQBrowser")))&&(this.qq=!0),c.ios&&t.match(/safari\/[\d.]+/i)&&(this.safari=!0,this.version=t.match(/os\s+(\d+)/i)[1]-0),~t.indexOf("AlipayClient")&&(this.alipay=!0),~t.indexOf("baiduboxapp")&&(this.baidu=!0)}};u.init();var l,h={appid:"",scheme:"",tempLinkSelector:"",packageUrl:"",packageInfoService:"http://127.0.0.1:13137/?action=info",init:function(t){this.appid=t.appid,this.scheme=t.stream_scheme,this.tempLinkSelector=t.tempLinkSelector||"open_stream_temp",c.android?this.packageUrl=t.stream_android_url:c.ios&&(this.packageUrl=t.stream_ios_url)},open:function(){if(!this.scheme)return!1;c.android?this.openAndroid():c.ios&&this.openIos()},openAndroid:function(){navigator.userAgent;var t=s.querySelector(this.tempLinkSelector);if(t&&document.body.removeChild(t),u.qihoo||u.uc||u.meizu)window.location.href=this.scheme;else if(u.chrome)u.version&&u.version<42?this.openIntent(this.scheme):window.location.href=this.scheme;else{var e=document.createElement("iframe");e.id=this.tempLinkSelector,e.style.display="none",document.body.appendChild(e),e.src=this.scheme}},openIos:function(){window.location.href=this.scheme},openIntent:function(t){var e=t.split("://"),n="intent://{schemePath}#Intent;scheme={schemeName};end";n=n.replace("{schemePath}",e[1]).replace("{schemeName}",e[0]);var r=document.createElement("a");r.id=this.tempLinkSelector,r.style.width="2px",r.style.height="2px",r.href=n,document.body.appendChild(r),function(t,e){if(t.fireEvent)t.fireEvent("on"+e);else{var n=document.createEvent("Events");n.initEvent(e,!0,!1),t.dispatchEvent(n)}}(r,"click")},download:function(){window.location.href=this.packageUrl},getPackageInfo:function(t){i("GET",this.packageInfoService,{},(function(e){e?t.success&&t.success():t.error&&t.error()}))}};h.init($app),{url:"",size:0,download:function(){if(u.weixin&&!~this.url.indexOf("sj.qq.com")){var t=c.ios?"在Safari打开":"在浏览器打开";r.show("请点击右上角菜单，选择"+t)}else window.location.href=this.url},init:function(t){c.android?(this.url=t.android_url||"",this.size=t.android_size||0):c.ios&&(this.url=t.ios_url||"",this.size=t.ios_size||0)}}.init($app),l=function(){c.android&&$app.stream_scheme&&h.open(),{selector:"#mobile_info",element:null,eventType:"resize",handleEvent:function(){s.setFullScreen(this.element),this.removeEvent()},initEvent:function(){window.addEventListener(this.eventType,this.handleEvent.bind(this),!1)},removeEvent:function(){window.removeEventListener(this.eventType,this.handleEvent)},init:function(){this.element=s.querySelector(this.selector),s.setFullScreen(this.element),this.initEvent()}}.init(),["mp","app","h5","quickApp"].forEach((function(t){$app[t]||(l(document.querySelector("#mb_tab ."+t),"hide"),l(document.querySelector("#mb_tab_content ."+t),"hide"),l(document.querySelector("#pc_tab ."+t),"hide"),l(document.querySelector("#pc_tab_content ."+t),"hide"))})),{selector:"#scroll_page",element:null,eventType:"click",direction:"down",DOWN_SIGN:"down",UP_SIGN:"up",toggle:function(){var t=this.element.className;this.direction===this.DOWN_SIGN?(this.direction=this.UP_SIGN,this.element.className=t.replace(this.DOWN_SIGN,this.UP_SIGN)):(this.direction=this.DOWN_SIGN,this.element.className=t.replace(this.UP_SIGN,this.DOWN_SIGN))},initEvent:function(){this.element.addEventListener(this.eventType,this.handleEvent.bind(this),!1),window.addEventListener("scroll",this.onScroll.bind(this),!1)},handleEvent:function(){this.direction===this.DOWN_SIGN?this.down():this.direction===this.UP_SIGN&&this.up()},onScroll:function(){var t=a()-a()/2,e=window.scrollY;(e>t&&this.direction!==this.UP_SIGN||e<=t&&this.direction!==this.DOWN_SIGN)&&this.toggle()},down:function(){window.scrollTo(0,a()+1),this.toggle()},up:function(){window.scrollTo(0,0),this.toggle()},init:function(){this.element=s.querySelector(this.selector),this.initEvent()}}.init();var n=document.querySelector(".pc-share"),i=n.querySelector(".share-qrcode");function l(t,e){t&&(t.className+=" "+e)}function d(t,e){t&&(e instanceof Array?e:e.split(" ")).forEach((function(e){t.className=t.className.replace(e," ")}))}n.addEventListener("mouseover",(function(){~i.className.indexOf("hide")&&(i.className=i.className.replace("hide","show"))})),n.addEventListener("mouseleave",(function(){~i.className.indexOf("show")&&(i.className=i.className.replace("show","hide"))}));var f=function(t){this.options=t,this.container=t.container,this.itemSelector=t.itemSelector,this.activeClass="active",this.itemTagName=(t.itemTagName||"a").toUpperCase(),this.itemClassName=t.itemClassName,this.init()};f.prototype.init=function(){this.initElements()},f.prototype.initElements=function(){this.containerElem=document.querySelector(this.container),this.containerElem&&(this.activeItem=this.containerElem.querySelector(this.itemSelector+"."+this.activeClass),this.activeItem||(this.activeItem=t.call(this.containerElem.querySelectorAll(this.itemSelector),(function(t){return!e.call(t.classList,"hide")})),l(this.activeItem,"active")),this.initEvents())},f.prototype.initEvents=function(){this.containerElem.addEventListener("click",this.handleTab.bind(this),!1)},f.prototype.handleTab=function(t){for(var e=t.target;e!==document;e=e.parentNode)if(~e.className.indexOf(this.itemSelector.replace(".",""))){if(e.tagName===this.itemTagName){if(e===this.activeItem)return;this.switchTab(e)}break}},f.prototype.switchTab=function(t){this.activeItem.className=this.activeItem.className.replace(" "+this.activeClass,""),t.className=t.className+" "+this.activeClass,this.activeItem=t,this.options.callback&&this.options.callback(t.getAttribute("data-id"))};var g=function(t){this.options=t,this.container=t.container,this.itemSelector=t.itemSelector,this.activeClass="active",this.init()};g.prototype.init=function(){this.initElements()},g.prototype.initElements=function(){this.containerElem=document.querySelector(this.container),this.containerElem&&(this.activeItem=this.containerElem.querySelector(this.itemSelector+"."+this.activeClass),this.activeItem||(this.activeItem=t.call(this.containerElem.querySelectorAll(this.itemSelector),(function(t){return!e.call(t.classList,"hide")})),l(this.activeItem,"active")))},g.prototype.change=function(t){var e=this.containerElem.querySelector(this.itemSelector+"."+t);e!==this.activeItem&&(this.activeItem.className=this.activeItem.className.replace(" "+this.activeClass,""),e.className=e.className+" "+this.activeClass,this.activeItem=e)};var m=new g({container:"#pc_tab_content",itemSelector:".tab-content-item"});new f({container:"#pc_tab",itemSelector:".tab-item",callback:function(t){m.change(t)}});var v=new g({container:"#mb_tab_content",itemSelector:".tab-content-item"});new f({container:"#mb_tab",itemSelector:".tab-item",callback:function(t){v.change(t)}});var A=new g({container:".mp-content",itemSelector:".mp-content-item"});new f({container:".mp-side",itemSelector:".mp-side-item",itemTagName:"div",callback:function(t){A.change(t)}});var w=document.getElementById("mb_tab_content");if(w){var y=w.querySelector(".pkg-download"),E=w.querySelector(".download-tip");E&&(c.android&&($app.android_url?y.className=y.className.replace("hide","show"):E.querySelector(".os-name")&&(E.querySelector(".os-name").innerText="Android",E.className=E.className.replace("hide","show"))),c.ios&&($app.ios_url?y.className=y.className.replace("hide","show"):E.querySelector(".os-name")&&(E.querySelector(".os-name").innerText="iOS",E.className=E.className.replace("hide","show")))),y&&y.addEventListener("click",(function(){if(u.weixin){var t=c.ios?"在Safari打开":"在浏览器打开";r.show("请点击右上角菜单，选择"+t)}else c.android?window.location.href=$app.android_url:c.ios&&(window.location.href=$app.ios_url)}))}var I,b,C,N,M,T=document.getElementById("pc_tab_content");if(T){var S=T.querySelector(".pc-download");if(S){var B=S.querySelector("a.android");B&&($app.android_url?B.href=$app.android_url:l(B,"hide"));var R=S.querySelector("a.ios");R&&($app.ios_url?R.href=$app.ios_url:l(R,"hide"))}}$app.mp&&function(){var t=function(t,e){return t.getAttribute("data-"+e)};if(I=document.querySelectorAll(".mp-content .mp-content-item"))for(M=0;M<I.length;M++)!function(t){var e=!1;for(var n in $app.mpPlatforms)if(Object.hasOwnProperty.call($app.mpPlatforms,n)){var r=$app.mpPlatforms[n];u[n]&&(e=!0,~t.className.indexOf(r)?d(t.querySelector(".mp-tip"),"hide"):t.querySelector(".mp-title").style.display="flex")}e||(t.querySelector(".mp-title").style.display="flex")}(I[M]);if((b=document.getElementById("mp-toutiao-guide"))&&((u.chrome||u.safari)&&$app.mp_toutiao_url?b.innerHTML='<a href="'+$app.mp_toutiao_url+'">前往打开头条小程序</a>':b.innerHTML="通过Safari或Chrome打开头条体验"),C=document.querySelectorAll(".mp-title"),N=document.querySelector(".toast"),C)for(M=0;M<C.length;M++)!function(e){var n=e.querySelector(".mp-title-copy");n&&n.addEventListener("click",(function(){o({data:t(n,"name"),success:function(){N.querySelector(".toast-text").innerText="复制成功，请在%platform%搜索框里长按并粘贴搜索。".replace("%platform%",t(n,"platform")),d(N,"hide"),setTimeout((function(){l(N,"hide"),N.querySelector(".toast-text").innerText=""}),2e3)},fail:function(){N.querySelector(".toast-text").innerText="复制失败，请尝试长按小程序名称复制。",d(N,"hide"),setTimeout((function(){l(N,"hide"),N.querySelector(".toast-text").innerText=""}),2e3)},complete:function(){var t;!(t=document.activeElement)||"TEXTAREA"!==t.tagName&&"INPUT"!==t.tagName||t.blur()}})}))}(C[M])}();var U=document.createElement("img");U.style.position="absolute",U.style.left="100000px",U.style.width="0px",U.style.height="0px",document.body.appendChild(U);var q={image:U,content:location.href,width:256};$app.icon_url&&Object.assign(q,{logo:{src:$app.icon_url,logoSize:.2,radius:8}}),p.toImage(q).then((function(){Array.prototype.forEach.call(document.querySelectorAll(".app-qrcode"),(function(t){t.src=U.src}))}))},/complete|loaded|interactive/.test(document.readyState)?l():document.addEventListener("DOMContentLoaded",l,!1)}()}]);</script>
	</body>
</html>