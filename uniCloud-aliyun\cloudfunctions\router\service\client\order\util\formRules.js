'use strict';
/**
 * 表单验证
 */
class Util {
	constructor() {
		this.rules = {
			card: {
				card_id: [{ required: true, message: '卡片id不能为空', trigger: 'blur' }],
				club_id: [{ required: true, message: '俱乐部id不能为空', trigger: 'blur' }],
			},
			activity: {
				activity_id: [{ required: true, message: '活动id不能为空', trigger: 'blur' }],
			}
		}
	}
	async check(event) {
		try {
			switch (event.data.type) {
				case "card":
					return await this.card(event)
				case "activity":
					return await this.activity(event)
			}
		} catch (error) {
			return { code: -1, msg: error }
		}
	}
	async validate(event, rules) {
		let { data = {}, userInfo, util } = event;
		let { vk } = util;
		let res = vk.pubfn.formValidate({
			data: data,
			rules: rules
		});
		return res;
	}
	/**
	 * 卡片购买验证
	 */
	async card(event) {
		let res = { code: 0, msg: '' }
		let { uid, card_id, club_id } = event.data;
		// 验证字段
		let rule_res = await this.validate(event, this.rules.card);
		if (res.code !== 0) return rule_res
		// 获取卡片信息
		let info = await vk.baseDao.findById({
			dbName: "club-card-data",
			id: card_id,
		});
		// 查找验证用户是否已经拥有此卡片
		let has = await vk.baseDao.findByWhereJson({
			dbName: "club-user-card",
			whereJson: {
				user_id: uid,
				card_id,
				club_id
			},
		});
		// 拥有该会员卡，重复购买续卡
		if (has && !info.repeat) {
			res.msg = "该卡已购买，无法重复购买"
			res.code = -1
		}
		return res
	}
	/**
	 * 验证用户是否已经加入了活动
	 */
	async activity(event) {
		let res = { code: 0, msg: '' }
		let { uid, activity_id, join_type } = event.data;
		// 验证字段
		let rule_res = await this.validate(event, this.rules.activity);
		if (res.code !== 0) return rule_res
		// 获取卡片信息
		if (join_type === 'myself') {
			let info = await vk.baseDao.findByWhereJson({
				dbName: "activity-join-data",
				whereJson: {
					user_id: uid,
					activity_id: activity_id,
					type: "myself",
					state: event.util._.in(['joined', 'applying'])
				},
			});
			// 判断是否已经在活动中
			if (info) res = { code: -1, msg: "已在活动中或申请中，请勿重复提交" }
		}
		return res
	}
}
module.exports = new Util