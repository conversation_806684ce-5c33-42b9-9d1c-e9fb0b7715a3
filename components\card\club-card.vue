<template>
  <view class="club-card" v-if="clubData">
    <!-- Club Header -->
    <view class="club-header">
      <image class="club-logo" :src="clubData.logo" mode="aspectFill" @error="handleImageError" />
      <text class="club-name">{{ clubData.name }}</text>
    </view>

    <!-- Club Content -->
    <view class="club-content">
      <view class="club-intro">{{ clubIntro }}</view>

      <view class="club-address">
        <image class="location-icon" :src="`${iconUrl}theme_local-fill.svg`" mode="heightFix" />
        <text class="address-text">{{ clubData.address }}</text>
        <text class="distance-text" v-if="clubData.distance"> 距您：{{ formattedDistance }}km </text>
      </view>
    </view>

    <!-- Club Footer -->
    <view class="club-footer">
      <view class="member-avatars">
        <image
          v-for="(member, index) in displayMembers"
          :key="member.userInfo?._id || index"
          class="member-avatar"
          :src="member.userInfo?.avatar"
          mode="aspectFill"
          @error="handleAvatarError"
        />
        <view class="member-count" v-if="hasMoreMembers">
          {{ memberCountText }}
        </view>
      </view>

      <wd-button type="primary" size="small" @click="navigateToClub" class="enter-btn" custom-style="background: #0171BC"> 进入俱乐部 </wd-button>
    </view>
  </view>
</template>

<script>
export default {
  name: "ClubCard",

  props: {
    value: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },

  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      defaultLogo: "/static/images/default-club-logo.png",
      defaultAvatar: "/static/images/default-avatar.png",
    };
  },

  computed: {
    // 俱乐部数据别名
    clubData() {
      return this.value || {};
    },

    // 俱乐部简介
    clubIntro() {
      return this.clubData.intro || "暂无简介";
    },

    // 格式化距离
    formattedDistance() {
      if (typeof this.clubData.distance === "number") {
        return this.clubData.distance.toFixed(1);
      }
      return "0.0";
    },

    // 显示的成员（最多6个）
    displayMembers() {
      return this.clubData.club_user?.slice(0, 6) || [];
    },

    // 是否有更多成员
    hasMoreMembers() {
      return this.clubData.club_user?.length > 6;
    },

    // 成员数量文本
    memberCountText() {
      const totalCount = this.clubData.club_user?.length || 0;
      return totalCount > 99 ? "99+" : totalCount.toString();
    },
  },

  mounted() {},

  methods: {
    // 跳转到俱乐部
    navigateToClub() {
      if (!this.clubData._id) {
        console.error("Club ID is missing");
        vk.toast("俱乐部信息错误");
        return;
      }

      vk.navigateTo(`/pages_club/views/home/<USER>
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.warn("Club logo failed to load:", event);
      // 可以设置默认图片
      event.target.src = this.defaultLogo;
    },

    // 处理头像加载错误
    handleAvatarError(event) {
      console.warn("Member avatar failed to load:", event);
      event.target.src = this.defaultAvatar;
    },
  },
};
</script>

<style scoped lang="scss">
.club-card {
  padding: 30rpx;
  box-sizing: border-box;
  width: 100%;
  min-height: 400rpx;
  border-radius: 20rpx;
  background: #fff;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }
}

// Club Header
.club-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.club-logo {
  width: 68rpx;
  height: 68rpx;
  flex-shrink: 0;
  margin-right: 20rpx;
  border-radius: 50%;
  border: 2rpx solid #f0f0f0;
}

.club-name {
  flex: 1;
  font-size: 36rpx;
  font-family: alibaba-title, -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: bold;
  color: #333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Club Content
.club-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 20rpx 0;
}

.club-intro {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}

.club-address {
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
  background: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;

  .location-icon {
    height: 30rpx;
    flex-shrink: 0;
    margin-right: 16rpx;
  }

  .address-text {
    font-size: 26rpx;
    color: #666;
    flex: 1;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .distance-text {
    color: #0171bc;
    font-weight: bold;
    margin-left: 20rpx;
    flex-shrink: 0;
    font-size: 26rpx;
  }
}

// Club Footer
.club-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.member-avatars {
  display: flex;
  align-items: center;
  position: relative;

  .member-avatar {
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
    border: 4rpx solid #fff;
    margin-right: -24rpx;

    &:last-child {
      margin-right: 0;
    }

    &:first-child {
      z-index: 6;
    }

    &:nth-child(2) {
      z-index: 5;
    }

    &:nth-child(3) {
      z-index: 4;
    }

    &:nth-child(4) {
      z-index: 3;
    }

    &:nth-child(5) {
      z-index: 2;
    }

    &:nth-child(6) {
      z-index: 1;
    }
  }

  .member-count {
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 22rpx;
    color: #666;
    font-weight: bold;
    border: 4rpx solid #fff;
  }
}

.enter-btn {
  min-width: 140rpx;
  height: 64rpx;
  font-size: 26rpx;
}

// Responsive Design
@media screen and (max-width: 750rpx) {
  .club-card {
    padding: 24rpx;
    min-height: 360rpx;
  }

  .club-logo {
    width: 60rpx;
    height: 60rpx;
  }

  .club-name {
    font-size: 32rpx;
  }

  .club-intro {
    font-size: 24rpx;
  }

  .club-address {
    padding: 16rpx;

    .address-text,
    .distance-text {
      font-size: 24rpx;
    }
  }

  .member-avatars {
    .member-avatar,
    .member-count {
      width: 60rpx;
      height: 60rpx;
    }

    .member-count {
      font-size: 20rpx;
    }
  }

  .enter-btn {
    min-width: 120rpx;
    height: 60rpx;
    font-size: 24rpx;
  }
}
</style>
