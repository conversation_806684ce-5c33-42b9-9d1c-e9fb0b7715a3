## 插件
#### `router/service/plugs`
#### 此目录一般为插件作者开发的一些通用功能，如动态数据组件的API将在此目录，未来会新增微信小程序订阅消息发送等等。

```
.
├── plugs──────────────────────# 插件逻辑
│ └── plugs-A───────────────────# 插件A
│ ────└── admin───────────────────# 插件A admin端
│ ────└── client──────────────────# 插件A client端
│ └── plugs-B───────────────────# 插件B
│ ────└── admin───────────────────# 插件B admin端
│ ────└── client──────────────────# 插件B client端
└─────────────────────────────────
```
