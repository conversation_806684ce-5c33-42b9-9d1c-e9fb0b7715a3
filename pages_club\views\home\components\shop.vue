<template>
	<view>
		<view class="card-list">
			<view v-for="card in card_list" :key="card._id">
				<vip-card :data="card" :customStyle="{marginBottom:'20rpx'}" @click="cardDetail(card)">
				</vip-card>
			</view>
			<t-empty v-if="card_list.length==0" data="card" text="暂无会员卡"></t-empty>
		</view>
	</view>
</template>

<script>
	import VipCard from '@/components/card/vip-card'
	export default {
		components: {
			VipCard
		},
		data() {
			return {
				card_list: [],
				my_card_list: [],
				tabIndex: 0,
			}
		},
		watch: {
			"$store.state.$club.id": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.id = newVal
					this.getClubCard()
				}
			}
		},
		mounted() {
			this.id = vk.getVuex('$club.id')
			this.getClubCard()
		},
		methods: {
			async refresh() {
				this.card_list = []
				this.my_card_list = []
				await this.getClubCard()
				uni.$emit("refresh-finish")
			},
			loadMore() {
				console.log("加载更多");
			},
			async getClubCard() {
				try {
					let data = await vk.callFunction({
						url: 'client/club/card/kh/list',
						title: '获取会员卡...',
						data: {
							club_id: this.id,
							enable: true
						},
					});
					this.card_list = data.rows
				} catch (error) {
					vk.toast('获取商品失败，请重新尝试', 'none');
				}
			},
			cardDetail(data) {
				this.$emit("detail", data)
			},
		}
	}
</script>

<style scoped lang="scss">
	.content-top {
		position: sticky;
		top: 0;
		z-index: 9;
	}

	.card-list {
		padding: 30rpx;

		.title {
			font-size: 32rpx;
			font-weight: bold;
			text-align: center;
			margin-bottom: 30rpx;
		}

		.empty {
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;

			image {
				width: 350rpx;
			}

			text {
				font-size: 28rpx;
				color: #8a8a8a;
			}
		}

	}
</style>