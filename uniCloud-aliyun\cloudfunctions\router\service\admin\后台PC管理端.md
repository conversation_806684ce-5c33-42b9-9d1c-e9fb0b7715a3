## PC后台admin管理端
#### `router/service/admin` 目录为后台管理端逻辑
#### 以下的目录并非强制性，只是建议，便于统一开发规范。

```
.
├── admin──────────────────────# 后台管理端逻辑
│ └── system────────────────# 系统（用户、角色、权限、菜单、应用）
│ └── system_uni────────────# 系统插件
│ └── user──────────────────# 用户管理
│ └── order─────────────────# 订单管理
│ ── └── xxxxxxxxxxxxxxx──────# 
│ ── └── xxxxxxxxxxxxxxx──────# 
│ ── └── xxxxxxxxxxxxxxx──────# 
│ ── └── xxxxxxxxxxxxxxx──────# 
└─────────────────────────────────
```
