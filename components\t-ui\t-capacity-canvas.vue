<template>
	<div class="charts-box">
		<qiun-data-charts type="radar" :opts="opts" :chartData="chartData" />
	</div>
</template>
<script>
	const initData = {
		"forehand": 0,
		"backhand": 0,
		"return": 0,
		"volley": 0,
		"serve": 0
	}
	export default {
		props: {
			data: {
				type: Object,
			},
		},
		data() {
			return {
				chartData: {},
				opts: {
					color: ['#0171BC'],
					padding: [5, 5, 5, 5],
					dataLabel: false,
					enableScroll: false,
					legend: {
						show: false,
						position: 'right',
						lineHeight: 25,
					},
					extra: {
						radar: {
							gridType: 'radar',
							gridColor: '#ccc',
							gridCount: 3,
							opacity: 0.2,
							max: 5,
							labelShow: true,
							border: true,
						},
					},
				},
			}
		},
		watch: {
			data() {
				this.init()
			},
		},
		mounted() {
			this.init()
		},
		methods: {
			init() {
				let data = this.data || initData
				const res = {
					categories: ['正手', '反手', '发球', '接发', '网前'],
					series: [{
						name: '综合能力',
						data: [data.forehand, data.backhand, data.serve, data.return, data.volley],
					}],
				}
				this.chartData = JSON.parse(JSON.stringify(res))
			},
		}
	}
</script>

<style scoped lang="scss">
	.charts-box {
		width: 100%;
		height: 300px;
	}
</style>