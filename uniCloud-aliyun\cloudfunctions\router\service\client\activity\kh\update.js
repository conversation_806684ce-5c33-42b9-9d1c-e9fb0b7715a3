'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/update 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let updateKeys = ["open", "apply", "notice", "help_join"];
		let dataJson = {};
		for (let i in updateKeys) {
			let key = updateKeys[i];
			if (vk.pubfn.isNotNull(data[key])) dataJson[key] = data[key];
		}
		let num = await vk.baseDao.updateById({
			dbName: "activity-data",
			id,
			dataJson,
			getUpdateData: false
		});
		res.msg = '更新成功!'
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}