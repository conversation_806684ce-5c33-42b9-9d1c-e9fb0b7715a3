'use strict';
module.exports = {
	/**
	 * 获取用户信息
	 * @url client/user/kh/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, user_id, club_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// res.data = await vk.daoCenter.userDao.findById(user_id);
		// 是否联表查询我已关注
		let foreignDB = []
		if (user_id !== uid) {
			foreignDB.push({
				dbName: "user-follow-data",
				localKey: "_id",
				foreignKey: "target_user_id",
				as: "follow",
				whereJson: {
					user_id: uid
				},
				limit: 1
			})
		}

		const outField = ['nickname', 'avatar', 'mobile', 'wechat', 'level', 'back_cover']
		const fieldJson = Object.fromEntries(outField.map(field => [field, true]))
		let info = await vk.baseDao.selects({
			dbName: "uni-id-users",
			getOne: true,
			getMain: true,
			// 主表where条件
			whereJson: {
				_id: user_id
			},
			// 主表字段显示规则
			fieldJson,
			// 副表列表
			foreignDB,
		});

		// 处理数据
		console.log(info.follow);
		info.follow = info.follow ? true : false
		res.data = info
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}