<template>
  <view class="page-content">
    <t-navbar title="我的钱包"></t-navbar>

    <!-- 余额卡片区域 -->
    <view class="balance-section">
      <view class="balance-card">
        <!-- 第一行：可用余额 -->
        <view class="balance-main">
          <view class="balance-label main-label">{{ balanceData[0].label }}</view>
          <view class="balance-amount main-amount">
            <text class="currency main-currency">¥</text>
            <wd-count-to
              :startVal="0"
              :endVal="balanceData[0].value"
              :duration="1500"
              :decimals="2"
              :fontSize="42"
              color="#ffffff"
              class="amount-number main-number"
            />
          </view>
        </view>

        <!-- 分隔线 -->
        <view class="balance-divider"></view>

        <!-- 第二行：待提现和冻结余额 -->
        <view class="balance-secondary">
          <view class="balance-item" v-for="(item, index) in balanceData.slice(1)" :key="index">
            <view class="balance-label">{{ item.label }}</view>
            <view class="balance-amount">
              <text class="currency">¥</text>
              <wd-count-to :startVal="0" :endVal="item.value" :duration="1500" :decimals="2" :fontSize="28" color="#ffffff" class="amount-number" />
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 余额变动记录区域 -->
    <view class="records-section">
      <t-title>余额变动记录</t-title>

      <view class="records-list">
        <!-- 空状态 -->
        <wd-status-tip v-if="!loading && recordsList.length === 0" image="search" tip="暂无交易记录" class="empty-records" />

        <!-- 记录列表 -->
        <view v-for="(record, index) in recordsList" :key="record.id" class="record-item" :style="{ 'animation-delay': `${index * 0.1}s` }">
          <view class="record-left">
            <view class="record-type">{{ record.type_name }}</view>
            <view class="record-time">{{ formatTime(record.create_time) }}</view>
          </view>

          <view class="record-right">
            <view class="record-amount" :class="getAmountClass(record.amount)">
              {{ formatAmount(record.amount) }}
            </view>
            <view class="record-balance">余额: ¥{{ record.balance.toFixed(2) }}</view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="hasMore && recordsList.length > 0" class="load-more">
        <wd-button :loading="loadingMore" @click="loadMoreRecords" custom-class="load-more-btn" type="text">
          {{ loadingMore ? "加载中..." : "加载更多" }}
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 基础状态
      loading: false,
      loadingMore: false,
      hasMore: true,

      // 余额数据
      balanceData: [
        { label: "总余额", value: 0 },
        { label: "待提现", value: 0 },
        { label: "待到账", value: 0 },
      ],

      // 交易记录
      recordsList: [],
      pageIndex: 1,
      pageSize: 20,
    };
  },

  onLoad() {
    this.initPage();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  onReachBottom() {
    this.loadMoreRecords();
  },

  methods: {
    // 初始化页面
    async initPage() {
      this.loading = true;
      try {
        await Promise.all([this.loadBalanceData(), this.loadRecords()]);
      } catch (error) {
        console.error("初始化失败:", error);
        uni.showToast({
          title: "加载失败",
          icon: "error",
        });
      } finally {
        this.loading = false;
      }
    },

    // 刷新数据
    async refreshData() {
      try {
        this.pageIndex = 1;
        this.recordsList = [];
        await this.initPage();
        uni.showToast({
          title: "刷新成功",
          icon: "success",
        });
      } catch (error) {
        uni.showToast({
          title: "刷新失败",
          icon: "error",
        });
      } finally {
        uni.stopPullDownRefresh();
      }
    },

    // 加载余额数据
    async loadBalanceData() {
      try {
        let data = await vk.callFunction({
          url: "client/user/wallet/kh/get",
          title: "请求中...",
        });

        this.balanceData = data;
      } catch (error) {
        console.error("加载余额数据失败:", error);
      }
    },

    // 加载交易记录
    async loadRecords() {
      try {
        // 模拟接口调用
        let data = await vk.callFunction({
          url: "client/user/wallet/kh/records",
          title: "请求中...",
          data: {
            pageIndex: this.pageIndex,
          },
        });
        if (this.pageIndex === 1) {
          this.recordsList = data.rows;
        } else {
          this.recordsList.push(...data.rows);
        }

        this.hasMore = data.hasMore;
      } catch (error) {
        console.error("加载记录失败:", error);
      }
    },

    // 加载更多记录
    async loadMoreRecords() {
      if (this.loadingMore || !this.hasMore) return;

      this.loadingMore = true;
      this.pageIndex++;

      try {
        await this.loadRecords();
      } catch (error) {
        this.pageIndex--;
        uni.showToast({
          title: "加载失败",
          icon: "error",
        });
      } finally {
        this.loadingMore = false;
      }
    },

    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp);
      const now = new Date();
      const diff = now - date;

      if (diff < 24 * 60 * 60 * 1000) {
        return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
      } else {
        return `${date.getMonth() + 1}-${String(date.getDate()).padStart(2, "0")} ${String(date.getHours()).padStart(2, "0")}:${String(
          date.getMinutes()
        ).padStart(2, "0")}`;
      }
    },

    // 格式化金额
    formatAmount(amount) {
      const prefix = amount > 0 ? "+" : "";
      return `${prefix}¥${Math.abs(amount).toFixed(2)}`;
    },

    // 获取金额样式类
    getAmountClass(amount) {
      return amount > 0 ? "amount-positive" : "amount-negative";
    },
  },
};
</script>

<style lang="scss" scoped>
.page-content {
  min-height: 100vh;
  background: #f8f8f8;
}

// 余额卡片区域
.balance-section {
  padding: 30rpx;
  background: #fff;
  margin-bottom: 20rpx;
}

.balance-card {
  background: linear-gradient(135deg, #0171bc 0%, #0056a3 100%);
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(1, 113, 188, 0.2);
  display: flex;
  flex-direction: column;
}

// 第一行：可用余额
.balance-main {
  text-align: center;
  padding-bottom: 30rpx;

  .main-label {
    font-size: 28rpx;
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 15rpx;
  }

  .main-amount {
    display: flex;
    align-items: baseline;
    justify-content: center;

    .main-currency {
      font-size: 32rpx;
      color: #fff;
      margin-right: 8rpx;
    }

    .main-number {
      color: #fff;
      font-weight: 700;
    }
  }
}

// 分隔线
.balance-divider {
  height: 1rpx;
  background: rgba(255, 255, 255, 0.2);
  margin: 0 -10rpx;
}

// 第二行：待提现和冻结余额
.balance-secondary {
  display: flex;
  padding-top: 30rpx;

  .balance-item {
    flex: 1;
    text-align: center;
    position: relative;

    &:first-child::after {
      content: "";
      position: absolute;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 1rpx;
      height: 60rpx;
      background: rgba(255, 255, 255, 0.2);
    }
  }
}

.balance-label {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 10rpx;
}

.balance-amount {
  display: flex;
  align-items: baseline;
  justify-content: center;

  .currency {
    font-size: 24rpx;
    color: #fff;
    margin-right: 4rpx;
  }

  .amount-number {
    color: #fff;
    font-weight: 600;
  }
}

// 记录区域
.records-section {
  background: #fff;
  padding: 30rpx;
}

.records-list {
  margin-top: 20rpx;
}

.record-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
  opacity: 0;
  transform: translateY(20rpx);
  animation: slideInUp 0.4s ease forwards;

  &:last-child {
    border-bottom: none;
  }
}

.record-left {
  flex: 1;
}

.record-type {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 8rpx;
}

.record-time {
  font-size: 24rpx;
  color: #999;
}

.record-right {
  text-align: right;
}

.record-amount {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 8rpx;

  &.amount-positive {
    color: #52c41a;
  }

  &.amount-negative {
    color: #ff4d4f;
  }
}

.record-balance {
  font-size: 24rpx;
  color: #999;
}

// 加载更多
.load-more {
  text-align: center;
  padding: 40rpx 0;

  :deep(.load-more-btn) {
    background: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 50rpx;
    padding: 20rpx 60rpx;

    &.is-loading {
      background: rgba(1, 113, 188, 0.1);
      color: #0171bc;
    }
  }
}

// 空状态
.empty-records {
  padding: 80rpx 0;

  :deep(.wd-status-tip) {
    .wd-status-tip__image {
      width: 120rpx;
      height: 120rpx;
      opacity: 0.6;
    }

    .wd-status-tip__text {
      font-size: 28rpx;
      color: #999;
      margin-top: 20rpx;
    }
  }
}

// 动画
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .balance-card {
    padding: 30rpx 20rpx;
  }

  .balance-main {
    padding-bottom: 25rpx;

    .main-label {
      font-size: 26rpx;
    }

    .main-amount {
      .main-currency {
        font-size: 28rpx;
      }
    }
  }

  .balance-secondary {
    padding-top: 25rpx;

    .balance-item {
      &:first-child::after {
        height: 50rpx;
      }
    }
  }

  .balance-label {
    font-size: 22rpx;
  }

  .balance-amount {
    .currency {
      font-size: 18rpx;
    }
  }

  .record-item {
    padding: 24rpx 0;
  }

  .record-type {
    font-size: 28rpx;
  }

  .record-amount {
    font-size: 28rpx;
  }
}
</style>
