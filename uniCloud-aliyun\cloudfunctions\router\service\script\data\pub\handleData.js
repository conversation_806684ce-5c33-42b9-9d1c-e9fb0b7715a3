'use strict';
module.exports = {
	/**
	 * 处理表的新增字段
	 * @url script/data/pub/handleData 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		
		const dbName = 'info-site-data'
		
		let list = await vk.baseDao.select({
			dbName,
			getMain: true,
			pageIndex: 1,
			pageSize: 5000,
		});

		list = list.filter(item => {
			if (item.site_indoor && typeof item.site_indoor == 'string') return true
			if (item.site_outdoor && typeof item.site_outdoor == 'string') return true
			if (item.site_practice && typeof item.site_practice == 'string') return true
			return false
		})

		console.log("list", list.length, list);
		let ids = list.map(item => item._id)
		for (const item of list) {
			let site_indoor = item.site_indoor ? Number(item.site_indoor) : 0
			let site_outdoor = item.site_outdoor ? Number(item.site_outdoor) : 0
			let site_practice = item.site_practice ? Number(item.site_practice) : 0
			await vk.baseDao.updateById({
				dbName,
				id: item._id,
				dataJson: {
					site_indoor,
					site_outdoor,
					site_practice
				},
				getUpdateData: false
			});
		}


		// 查找并输出修改后的结果
		let updateRes = await vk.baseDao.select({
			dbName,
			getMain: true,
			pageIndex: 1,
			pageSize: 5000,
			whereJson: {
				_id: _.in(ids)
			},
		});

		console.log("处理后的结果：", updateRes);

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}