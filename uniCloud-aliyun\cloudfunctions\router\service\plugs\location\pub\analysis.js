// 引入uni-map-common公共模块
const UniMap = require('uni-map-common');
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url plugs/location/pub/analysis 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid,latitude,longitude } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		const uniMap = new UniMap({
			provider: "amap", // 指定使用哪家地图供应商
			key: "9e5ea43eecb76b2040194c005c5e32f5",
			needOriginalResult: false, // 是否需要返回原始信息
		});
		let result = await uniMap.location2address({
		  location: `${latitude},${longitude}`
		});
		console.log("result", result);
		res.data = result.result
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}
