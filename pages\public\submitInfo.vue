<template>
  <view class="page-container">
    <t-navbar title="提供信息" />

    <!-- Loading State -->
    <view v-if="!info" class="loading-container">
      <wd-loading type="ring" size="40px" color="#0171BC" />
      <text class="loading-text">加载中...</text>
    </view>

    <!-- Main Content -->
    <view v-else class="content-wrapper">
      <!-- Header Section -->
      <view class="page-header">
        <view class="header-icon">
          <wd-icon name="edit" size="48rpx" color="#fff" />
        </view>
        <view class="header-content">
          <text class="header-title">{{ pageTitle }}信息</text>
          <text class="header-subtitle">请完善以下信息，帮助更多球友寻找{{ pageTitle }}信息</text>
        </view>
      </view>

      <!-- Form Container -->
      <view class="form-container">
        <wd-form :model="info" label-width="140rpx" class="enhanced-form">
          <!-- String Type Form -->
          <template v-if="pageType == 'string'">
            <!-- Basic Information Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">基本信息</text>
              </view>

              <view class="form-card">
                <t-image-upload v-model="info.image" imageType="shop" :limit="9" label="店面图片" class="upload-field" />

                <wd-input v-model="info.name" label="店铺名称" placeholder="请输入店铺名称" />

                <wd-textarea
                  v-model="info.intro"
                  label="店铺简介"
                  placeholder="请输入店铺简介，让球友更了解您的店铺"
                  :maxlength="200"
                  show-word-count
                  custom-class="form-field"
                  custom-textarea-class="textarea-field"
                />

                <wd-input
                  v-model="info.address_name"
                  label="位置选择"
                  readonly
                  placeholder="请选择位置"
                  @click="selectPosition"
                  class="form-field location-field"
                >
                  <template #suffix>
                    <wd-icon name="chevron-right" size="22px"></wd-icon>
                  </template>
                </wd-input>

                <wd-textarea
                  v-model="info.address"
                  label="详细地址"
                  :maxlength="100"
                  show-word-count
                  placeholder="请输入详细地址"
                  custom-class="form-field"
                  custom-textarea-class="textarea-field"
                />

                <wd-input v-model="info.price" label="穿线费用" placeholder="请输入穿线费用" type="number">
                  <template #suffix>
                    <text class="price-unit">元</text>
                  </template>
                </wd-input>
              </view>
            </view>

            <!-- Contact Information Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">联系方式</text>
              </view>

              <view class="form-card">
                <view class="contact-list">
                  <view class="contact-item" v-for="(item, index) in info.contact_info" :key="index">
                    <view class="contact-header">
                      <view class="contact-number">
                        <text class="contact-title">联系方式{{ index + 1 }}</text>
                      </view>
                      <wd-button v-if="info.contact_info.length > 1" type="error" size="small" plain @click="delContact(index)">删除</wd-button>
                    </view>
                    <view class="contact-fields">
                      <wd-picker v-model="item.type" :columns="[contactOptions]" placeholder="请选择联系方式" />
                      <wd-input v-model="item.value" no-border :placeholder="'请输入' + contactWayText(item.type)" />
                    </view>
                  </view>
                </view>

                <view class="add-contact-btn">
                  <wd-button @click="addContact" type="primary" plain block class="add-btn">
                    <wd-icon name="add" size="32rpx" />
                    添加联系方式
                  </wd-button>
                </view>
              </view>
            </view>

            <!-- Business Hours Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">营业信息</text>
              </view>

              <view class="form-card">
                <wd-input v-model="info.open_time" label="营业时间" placeholder="例：周一至周日 9:00-21:00"> </wd-input>
              </view>
            </view>
          </template>

          <!-- Site Type Form -->
          <template v-else-if="pageType == 'site'">
            <!-- Basic Information Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">基本信息</text>
              </view>

              <view class="form-card">
                <t-image-upload v-model="info.image" imageType="site" :limit="9" label="场地图片" />

                <wd-input v-model="info.name" label="场地名称" placeholder="请输入场地名称" />

                <wd-textarea
                  v-model="info.intro"
                  label="场地简介"
                  placeholder="请输入场地简介，包括场地特色、设施等"
                  :maxlength="200"
                  show-word-count
                  custom-textarea-class="textarea-field"
                />

                <wd-input v-model="info.address_name" label="位置选择" readonly placeholder="请选择位置" @click="selectPosition">
                  <template #suffix>
                    <wd-icon name="chevron-right" size="22px"></wd-icon>
                  </template>
                </wd-input>

                <wd-textarea
                  v-model="info.address"
                  label="详细地址"
                  :maxlength="100"
                  show-word-count
                  placeholder="请输入详细地址"
                  custom-textarea-class="textarea-field"
                />
                <wd-cell title="场地类型" center title-width="140rpx">
                  <wd-checkbox-group v-model="info.site_type" inline shape="square" cell :min="1" @change="changeSiteType">
                    <wd-checkbox modelValue="site">常规场地</wd-checkbox>
                    <wd-checkbox modelValue="practice">学练机</wd-checkbox>
                  </wd-checkbox-group>
                </wd-cell>
              </view>
            </view>

            <!-- Court Count Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">球场数量</text>
              </view>

              <view class="form-card">
                <view class="court-count-grid">
                  <!-- 网球场 -->
                  <template v-if="info.site_type.includes('site')">
                    <view class="court-item">
                      <wd-input
                        type="number"
                        label-width="120rpx"
                        v-model="info.site_indoor"
                        label="室内场"
                        placeholder="0"
                        custom-input-class="court-input"
                      >
                        <template #suffix>
                          <view class="court-unit">个</view>
                        </template>
                      </wd-input>
                    </view>

                    <view class="court-item">
                      <wd-input
                        type="number"
                        v-model="info.site_outdoor"
                        label="室外场"
                        label-width="120rpx"
                        placeholder="0"
                        custom-input-class="court-input"
                      >
                        <template #suffix>
                          <view class="court-unit">个</view>
                        </template>
                      </wd-input>
                    </view>
                  </template>

                  <!-- 学练馆机子 -->
                  <view class="court-item" v-if="info.site_type.includes('practice')">
                    <wd-input
                      type="number"
                      v-model="info.site_practice"
                      label="学练机"
                      label-width="120rpx"
                      placeholder="0"
                      custom-input-class="court-input"
                    >
                      <template #suffix>
                        <view class="court-unit">台</view>
                      </template>
                    </wd-input>
                  </view>
                </view>
              </view>
            </view>

            <!-- Cost Information Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">订场费用参考</text>
              </view>

              <view class="form-card">
                <view class="cost-list" v-if="info.cost_info.length > 0">
                  <view class="cost-item" v-for="(item, index) in info.cost_info" :key="index">
                    <view class="cost-header">
                      <view class="cost-number">
                        <text class="cost-title">费用参考{{ index + 1 }}</text>
                      </view>
                      <view class="cost-actions">
                        <wd-button size="small" type="primary" plain @click="editCostInfo(index)"> 编辑 </wd-button>
                        <wd-button size="small" type="error" plain @click="delCostInfo(index)"> 删除 </wd-button>
                      </view>
                    </view>

                    <view class="cost-details">
                      <view class="cost-block">
                        <view class="cost-text">{{ item.type }}</view>
                        <view v-if="item.remark" class="cost-text grey">{{ item.remark }}</view>
                      </view>
                      <view class="cost-block">
                        <view class="cost-text">{{ item.day }}</view>
                        <view class="cost-text">{{ item.time }}</view>
                        <view class="cost-text theme-color">{{ item.cost }}元/小时</view>
                      </view>
                    </view>
                  </view>
                </view>

                <view class="empty-state" v-else>
                  <text class="empty-text">暂无费用信息</text>
                </view>

                <!-- 添加按钮 -->
                <view class="add-cost-btn">
                  <wd-button @click="addSiteCost" type="primary" plain block>
                    <wd-icon name="add" size="32rpx" />
                    添加订场费用参考
                  </wd-button>
                </view>
              </view>
            </view>

            <!-- Contact Information Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">联系方式</text>
              </view>

              <view class="form-card">
                <view class="contact-list">
                  <view class="contact-item" v-for="(item, index) in info.contact_info" :key="index">
                    <view class="contact-header">
                      <view class="contact-number">
                        <text class="contact-title">联系方式{{ index + 1 }}</text>
                      </view>
                      <wd-button v-if="info.contact_info.length > 1" type="error" size="small" plain @click="delContact(index)">删除</wd-button>
                    </view>

                    <view class="contact-fields">
                      <wd-picker v-model="item.type" :columns="[contactOptions]" placeholder="请选择联系方式" />
                      <wd-input v-model="item.value" no-border :placeholder="'请输入' + contactWayText(item.type)" />
                    </view>
                  </view>
                </view>

                <view class="add-contact-btn">
                  <wd-button @click="addContact" type="primary" plain block>
                    <wd-icon name="add" size="32rpx" />
                    添加联系方式
                  </wd-button>
                </view>
              </view>
            </view>

            <!-- Open Status Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">营业信息</text>
              </view>

              <view class="form-card">
                <view class="open-status-item">
                  <wd-cell title="对外开放" :value="info.open ? '是' : '否'">
                    <wd-switch v-model="info.open" size="20px" />
                  </wd-cell>
                </view>

                <view v-if="info.open" class="open-time-section">
                  <wd-input v-model="info.open_time" label="营业时间" placeholder="例：周一至周日 6:00-22:00"></wd-input>
                </view>
              </view>
            </view>

            <!-- Services Section -->
            <view class="form-section">
              <view class="section-header">
                <text class="section-title">球场服务</text>
              </view>

              <view class="form-card">
                <view class="services-grid">
                  <wd-checkbox-group v-model="info.service" inline>
                    <wd-checkbox v-for="item in siteServiceOptions" shape="square" :key="item.value" :model-value="item.value">
                      {{ item.label }}
                    </wd-checkbox>
                  </wd-checkbox-group>
                </view>
              </view>
            </view>
          </template>
        </wd-form>
      </view>

      <!-- Submit Button -->
      <t-bottom>
        <view class="submit-section">
          <wd-button v-if="info_id" type="error" block @click="feedbackInfo" custom-style="flex:1">{{ pageTitle }}不存在</wd-button>
          <wd-button type="primary" block @click="submitInfo" custom-style="flex:1">提交信息</wd-button>
        </view>
      </t-bottom>
    </view>

    <!-- Cost Information Popup -->
    <wd-popup v-model="costVisible" position="bottom" custom-style="border-radius: 24rpx 24rpx 0 0;" @touchmove.stop.prevent>
      <view class="cost-popup">
        <view class="popup-header">
          <view class="popup-title-section">
            <text class="popup-title">订场费用参考</text>
          </view>
          <wd-button type="text" @click="costVisible = false">
            <wd-icon name="close" size="32rpx" color="#999" />
          </wd-button>
        </view>

        <view class="cost-form">
          <wd-form :model="newCostInfo" label-width="160rpx" class="popup-form">
            <wd-picker v-model="newCostInfo.type" :columns="[siteTypeOptions]" label="场地类型" />

            <wd-picker v-model="newCostInfo.day" :columns="[dayOptions]" label="适用日期" />

            <view v-if="newCostInfo.day == '自定义'" class="custom-days">
              <view class="days-label">选择日期</view>
              <wd-checkbox-group v-model="newCostInfo.days" inline>
                <wd-checkbox v-for="item in weekAry" shape="square" :key="item" :model-value="item">
                  {{ item }}
                </wd-checkbox>
              </wd-checkbox-group>
            </view>

            <wd-datetime-picker type="time" v-model="newCostInfo.start" :max="newCostInfo.end" label="开始时间" placeholder="请选择开始时间" />

            <wd-datetime-picker
              v-show="newCostInfo.start"
              type="time"
              v-model="newCostInfo.end"
              :min="newCostInfo.start"
              label="结束时间"
              placeholder="请选择结束时间"
            />

            <wd-input v-model="newCostInfo.cost" label="费用" type="number" placeholder="请输入费用">
              <template #suffix>
                <text class="cost-unit">元/小时</text>
              </template>
            </wd-input>

            <wd-input v-model="newCostInfo.remark" label="备注" :maxlength="20" placeholder="会员价/非会员价/捡漏价" />
          </wd-form>
        </view>

        <view class="popup-footer">
          <view class="footer-buttons">
            <wd-button @click="costVisible = false" plain> 取消 </wd-button>
            <wd-button type="primary" @click="addSiteCostInfo"> 确定 </wd-button>
          </view>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script>
import config from "./dataConfig.js";
import _ from "lodash";

export default {
  options: {
    styleIsolation: "shared",
  },
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      pageType: "string",
      contactOptions: [
        {
          label: "电话",
          value: "phone",
        },
        {
          label: "微信号",
          value: "wechat",
        },
        {
          label: "公众号",
          value: "public",
        },
        {
          label: "小程序",
          value: "miniproject",
        },
      ],
      siteTypeOptions: [
        {
          label: "室内场",
          value: "室内场",
        },
        {
          label: "室外场",
          value: "室外场",
        },
        {
          label: "学练机",
          value: "学练机",
        },
      ],
      dayOptions: [
        {
          label: "每天",
          value: "每天",
        },
        {
          label: "工作日",
          value: "工作日",
        },
        {
          label: "节假日",
          value: "节假日",
        },
        {
          label: "自定义",
          value: "自定义",
        },
      ],
      siteServiceOptions: [],
      info_id: null,
      info: null,
      history_id: null,
      weekAry: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
      costVisible: false,
      editCostInfoIndex: null,
      newCostInfo: {
        type: "",
        day: "",
        days: [],
        start: "",
        end: "",
        cost: "",
        remark: "",
      },
    };
  },
  computed: {
    pageTitle() {
      let title = "";
      switch (this.pageType) {
        case "site":
          title = "场地";
          break;
        case "string":
          title = "穿线店铺";
          break;
      }
      return title;
    },
  },
  onShareAppMessage() {
    let userName = this.userInfo.nickname || "";
    let text = "";
    switch (this.pageType) {
      case "string":
        text = `${userName}邀请您提供穿线店铺信息`;
        break;
      case "site":
        text = `${userName}邀请您提供场地信息`;
        break;
    }
    return {
      title: text,
      path: `/pages/public/submitInfo?type=${this.pageType}`,
    };
  },
  async onLoad(options) {
    this.getSiteServiceOptions();
    this.pageType = options.type;
    if (options.id) {
      this.info_id = options.id;
      await this.getEditRecord();
      if (this.history_id) return;
      this.getData();
    } else {
      this.info = JSON.parse(JSON.stringify(config[options.type]));
    }
  },
  methods: {
    // 获取我编辑的历史
    async getEditRecord() {
      try {
        let url = `client/public/info/kh/editRecord`;
        let data = await vk.callFunction({
          url,
          title: "请求中...",
          data: { id: this.info_id, type: this.pageType },
        });
        vk.toast("已复用提交但未审核的信息");
        if (data.data) {
          this.info = { ...config[this.pageType], ...data.data };
          this.history_id = data.data._id;
        }
      } catch (err) {
        console.log(err);
      }
    },
    changeSiteType({ value }) {
      if (!value.includes("site")) {
        this.info.site_indoor = "";
        this.info.site_outdoor = "";
      } else if (!value.includes("practice")) {
        this.info.site_practice = "";
      }
    },
    uploadImage(file, formData, options) {
      console.log(file, formData, options);
    },
    // 删除花费参考信息
    delCostInfo(index) {
      vk.confirm("确定删除该费用参考信息吗").then(() => {
        this.info.cost_info.splice(index, 1);
      });
    },
    // 编辑花费参考信息
    editCostInfo(index) {
      const { type, day, time, cost, remark } = this.info.cost_info[index];

      let obj = {
        type,
        day: "",
        days: [],
        start: "",
        end: "",
        cost,
        remark,
      };

      // 处理时间字符串，转换为Date对象
      const [startTime, endTime] = time.split("~");
      obj.start = startTime;
      obj.end = endTime;

      // 处理星期信息
      if (day.startsWith("周")) {
        // 如果是自定义星期，解析星期信息
        obj.day = "自定义";
        const weekDays = day.substring(1).split("");
        obj.days = weekDays.map((item) => `周${item}`);
      } else {
        obj.day = day;
      }
      this.newCostInfo = obj;
      this.costVisible = true;
      this.editCostInfoIndex = index;
    },
    // 打开添加花费参考弹窗
    addSiteCost() {
      this.editCostInfoIndex = null;
      this.costVisible = true;
      this.newCostInfo = {
        type: "",
        day: "",
        days: [],
        start: "",
        end: "",
        cost: "",
        remark: "",
      };
    },
    // 确定添加花费
    addSiteCostInfo() {
      const { type, day, days, start, end, cost, remark } = this.newCostInfo;

      // 检查必填字段
      if (!type || !start || !end || cost === "") {
        vk.toast("请检查完善信息");
        return;
      }

      // 判断开始时间不能大于结束时间
      console.log(start, end);

      if (start >= end) {
        vk.toast("开始时间不能大于结束时间");
        return;
      }

      // 处理日期逻辑
      let dayText =
        day === "自定义" && days && days.length > 0
          ? "周" +
            this.weekAry
              .filter((item) => days.includes(item))
              .map((item) => item[1])
              .join("")
          : day;

      // 如果选择了自定义日期但未选择任何星期，则返回
      if (day === "自定义" && (!days || days.length === 0)) {
        vk.toast("请选择自定义日期的星期");
        return;
      }

      if (days.length == 7) dayText = "每天";

      // 构建成本信息对象
      const costInfo = {
        type,
        day: dayText,
        time: `${start}~${end}`,
        cost,
        remark,
      };

      console.log("消费参考", costInfo);

      // 添加消费信息
      if (this.editCostInfoIndex === null) {
        this.info.cost_info.push(costInfo);
        vk.toast("添加成功");
      } else {
        let index = this.editCostInfoIndex;
        this.info.cost_info[index] = costInfo;
        vk.toast("编辑成功");
      }
      this.costVisible = false;
      this.editCostInfoIndex = null;
    },
    // 删除联系方式
    delContact(index) {
      vk.confirm("确定删除该联系方式吗", () => {
        this.info.contact_info.splice(index, 1);
      });
    },
    // 新增联系方式
    addContact() {
      this.info.contact_info.push({
        type: "phone",
        value: "",
      });
    },
    async getData() {
      try {
        let url = `client/public/info/${this.pageType}/pub/get`;
        let data = await vk.callFunction({
          url,
          title: "请求中...",
          data: { id: this.info_id },
        });
        this.info = { ...config[this.pageType], ...data.data };
        if (this.info.contact_info.length == 0) this.addContact();
      } catch (error) {
        console.log(error);
      }
    },
    async getSiteServiceOptions() {
      try {
        let data = await vk.callFunction({
          url: "client/options/pub/get",
          data: {
            type: "site-service",
          },
        });
        this.siteServiceOptions = data.rows;
      } catch (error) {
        console.log(error);
      }
    },
    contactWayText(way) {
      switch (way) {
        case "phone":
          return "电话";
        case "wechat":
          return "微信号";
        case "public":
          return "公众号";
        case "miniproject":
          return "小程序";
      }
    },
    // 场地关闭反馈
    feedbackInfo() {
      vk.confirm(`您确认要上报该${this.pageTitle}信息不存在吗`, "反馈信息", "确定", "取消", async (res) => {
        if (res.confirm) {
          // 点击确定按钮后的回调
          let data = await vk.callFunction({
            url: "client/user/kh/feedback",
            title: "提交中...",
            data: {
              text: `用户反馈${this.pageTitle}信息<${this.info.name}(${this.info_id})>不存在`,
            },
          });
          vk.toast("感谢您的反馈", "none", true, () => {
            vk.navigateBack();
          });
        }
      });
    },
    // 提交信息
    async submitInfo() {
      let data = this.info;
      let type = this.pageType;
      let params = { type, ...data };
      // 处理无效的联系方式信息
      params.contact_info = params.contact_info.filter((item) => item.value);
      if (this.info_id) params.info_id = this.info_id;

      try {
        let res = await vk.callFunction({
          url: "client/public/info/kh/submit",
          title: "请求中...",
          data: params,
        });
        vk.toast(res.msg, "none", true, () => {
          uni.navigateBack();
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 选择位置
    selectPosition() {
      uni.chooseLocation({
        success: (res) => {
          let info = {
            location: {
              latitude: res.latitude,
              longitude: res.longitude,
            },
            address: res.address + res.name,
            name: res.name,
          };
          this.info.location = info.location;
          this.info.address = info.address;
          this.info.address_name = info.name;
        },
        fail: (res) => {
          console.log(res);
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
// Import project variables
@import "@/common/css/uni.scss";
@import "@/common/css/app.scss";

.page-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
  padding-bottom: env(safe-area-inset-bottom);
}

// Loading State
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;

  .loading-text {
    margin-top: 30rpx;
    font-size: 28rpx;
    color: #999;
  }
}

// Content Wrapper
.content-wrapper {
  padding: 0 20rpx 40rpx;
}

// Page Header
.page-header {
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #0171bc 0%, #f5c55c 100%);
  border-radius: 24rpx;
  padding: 40rpx 30rpx;
  margin: 20rpx 0 30rpx;

  .header-icon {
    width: 80rpx;
    height: 80rpx;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 30rpx;
  }

  .header-content {
    flex: 1;

    .header-title {
      display: block;
      font-size: 36rpx;
      font-weight: bold;
      color: #fff;
      margin-bottom: 10rpx;
    }

    .header-subtitle {
      display: block;
      font-size: 26rpx;
      color: rgba(255, 255, 255, 0.8);
      line-height: 1.4;
    }
  }
}

// Form Container
.form-container {
  .enhanced-form {
    background: transparent;
  }
}

// Form Sections
.form-section {
  margin-bottom: 40rpx;

  .section-header {
    display: flex;
    align-items: center;
    margin-bottom: 20rpx;
    padding: 0 10rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-left: 16rpx;
    }
  }
}

// Form Cards
.form-card {
  background: #fff;
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1rpx solid rgba(248, 202, 108, 0.1);

  .form-field {
    margin-bottom: 10rpx;

    &:last-child {
      margin-bottom: 0;
    }
  }
  :deep(.textarea-field) {
    min-height: 200rpx;
    height: 100% !important;
  }
  :deep(.cell-field) {
    flex: 1;
  }
  .location-field {
    cursor: pointer;

    &:active {
      opacity: 0.7;
    }
  }

  .price-unit,
  .court-unit,
  .cost-unit {
    font-size: 26rpx;
    color: #666;
    font-weight: 500;
  }
}

// Upload Field
.upload-field {
  .custom-upload-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 160rpx;
    height: 160rpx;
    border: 2rpx dashed #ddd;
    border-radius: 16rpx;
    background: #fafafa;
    transition: all 0.3s ease;

    &:hover {
      border-color: #0171bc;
      background: rgba(248, 202, 108, 0.05);
    }

    .upload-text {
      font-size: 24rpx;
      color: #999;
      margin-top: 10rpx;
    }
  }
}

// Contact Section
.contact-list {
  .contact-item {
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    transition: all 0.3s ease;

    &:hover {
      border-color: rgba(248, 202, 108, 0.3);
      background: rgba(248, 202, 108, 0.02);
    }

    &:last-child {
      margin-bottom: 0;
    }

    .contact-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .contact-number {
        display: flex;
        align-items: center;

        .contact-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
          margin-left: 12rpx;
        }
      }

      .delete-btn {
        border-radius: 20rpx;
      }
    }

    .contact-fields {
      display: flex;
      align-items: center;
      .form-field {
        margin-bottom: 16rpx;

        :deep(.wd-input) {
          flex: 1;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

.add-contact-btn,
.add-cost-btn {
  margin-top: 30rpx;

  .add-btn {
    border-radius: 16rpx;
    border: 2rpx dashed #0171bc;
    color: #0171bc;
    font-weight: 500;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(248, 202, 108, 0.05);
    }
  }
}

// Court Count Grid
.court-count-grid {
  .court-item {
    :deep(.court-input) {
      text-align: end;
      margin-right: 20rpx;
    }
  }
}

// Cost Information
.cost-list {
  .cost-item {
    border-bottom: 2rpx solid #f0f0f0;
    padding: 24rpx;
    margin-bottom: 20rpx;
    transition: all 0.3s ease;

    &:last-child {
      margin-bottom: 0;
    }

    .cost-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;

      .cost-number {
        display: flex;
        align-items: center;

        .cost-title {
          font-size: 28rpx;
          font-weight: 600;
          color: #333;
        }
      }

      .cost-actions {
        display: flex;
        gap: 12rpx;

        .action-btn {
          border-radius: 20rpx;
          font-size: 24rpx;
        }
      }
    }

    .cost-details {
      gap: 12rpx;
      .cost-block {
        display: flex;
        align-items: center;
        margin-bottom: 20rpx;
      }
      .cost-text {
        font-size: 28rpx;
        margin-right: 20rpx;

        &.theme-color {
          color: #1890ff;
        }
        &.grey {
          color: #8a8a8a;
        }
      }
    }
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 20rpx;

  .empty-text {
    font-size: 26rpx;
    color: #999;
    margin-top: 20rpx;
  }
}

// Open Status Section
.open-status-item {
  .open-cell {
    border-radius: 12rpx;
    background: #fafafa;
    margin-bottom: 20rpx;
  }
}

.open-time-section {
  margin-top: 20rpx;
}

// Services Section
.services-grid {
  .service-group {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20rpx;

    .service-item {
      background: #fafafa;
      border-radius: 12rpx;
      padding: 20rpx;
      border: 1rpx solid #f0f0f0;
      transition: all 0.3s ease;

      &:hover {
        border-color: rgba(248, 202, 108, 0.3);
        background: rgba(248, 202, 108, 0.05);
      }
    }
  }
}

// Submit Section
.submit-section {
  width: 100%;
  gap: 30rpx;
  display: flex;
  align-items: center;
}

// Cost Popup Styles
.cost-popup {
  .popup-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    border-bottom: 1rpx solid #f0f0f0;
    background: #fafafa;

    .popup-title-section {
      display: flex;
      align-items: center;

      .popup-title {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
        margin-left: 16rpx;
      }
    }

    .close-btn {
      padding: 0;
      min-width: auto;
    }
  }

  .cost-form {
    padding: 30rpx;
    max-height: 60vh;
    overflow-y: auto;

    .popup-form {
      .form-field {
        margin-bottom: 24rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .custom-days {
      margin: 20rpx 0;

      .days-label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        font-weight: 500;
      }

      .days-group {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 16rpx;

        .day-item {
          background: #fafafa;
          border-radius: 12rpx;
          padding: 16rpx;
          text-align: center;
          border: 1rpx solid #f0f0f0;
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(248, 202, 108, 0.3);
            background: rgba(248, 202, 108, 0.05);
          }
        }
      }
    }
  }

  .popup-footer {
    padding: 30rpx;
    border-top: 1rpx solid #f0f0f0;
    background: #fafafa;

    .footer-buttons {
      display: grid;
      grid-template-columns: 1fr 2fr;
      gap: 20rpx;

      .cancel-btn {
        border-radius: 16rpx;
        color: #666;
        border-color: #ddd;
      }

      .confirm-btn {
        border-radius: 16rpx;
        background: linear-gradient(135deg, #0171bc 0%, #f5c55c 100%);
        font-weight: 600;
      }
    }
  }
}

// Responsive Design
@media screen and (max-width: 750rpx) {
  .court-count-grid {
    grid-template-columns: 1fr;
    gap: 20rpx;
  }

  .services-grid .service-group {
    grid-template-columns: 1fr;
  }

  .cost-popup .cost-form .custom-days .days-group {
    grid-template-columns: repeat(3, 1fr);
  }
}

.location-field:focus-within {
  border-color: #0171bc;
}

// Animation Classes
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
