'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/pub/markers 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, location, type, name, filter } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		var $ = _.aggregate; // 聚合查询操作符
		let whereJson = {}
		if (!location) return res
		location = _.geoNear({
			geometry: new db.Geo.Point(location.longitude, location.latitude),
			maxDistance: 50000,
			minDistance: 0,
			distanceMultiplier: 0.001,
			distanceField: "distance",
		})
		if (location) whereJson.location = location
		if (name) whereJson.name = new RegExp(name)
		// 场地额外筛选
		if (type == 'site') {
			const { site_type, service } = filter
			if (site_type && site_type.length !== 0) {
				site_type.forEach(key => {
					whereJson[key] = _.gte(1)
				})
			}
			if (service && service.length !== 0) {
				let ary = service.map(item => _.in([item]))
				whereJson.service = _.and(ary)
			}
		}
		console.log("查询条件", whereJson);

		let ary = []
		// 查询场地
		let site = await vk.baseDao.select({
			dbName: "info-site-data",
			getCount: false,
			pageIndex: 1,
			pageSize: 100,
			getMain: true,
			// 主表where条件
			whereJson,
		});
		// 查询穿线店铺
		let string = await vk.baseDao.select({
			dbName: "info-string-data",
			getCount: false,
			pageIndex: 1,
			pageSize: 100,
			getMain: true,
			// 主表where条件
			whereJson,
		});
		console.log(site, string);

		function processItems(items, type) {
			return items.map(item => {
				let obj = {
					_id: item._id,
					image: item.image,
					name: item.name,
					recommend_num: item.recommend_num,
					latitude: item.location.coordinates[1],
					longitude: item.location.coordinates[0],
					type: type
				}
				switch (type) {
					case "site":
						obj.site_type = item.site_type
						obj.site_indoor = item.site_indoor
						obj.site_outdoor = item.site_outdoor
						obj.site_practice = item.site_practice
						obj.sign_num = item.sign_num
						break;
					case "string":
						obj.price = item.price
						break;
				}
				return obj
			});
		}

		switch (type) {
			case "site":
				ary = ary.concat(processItems(site, "site"));
				break;
			case "string":
				ary = ary.concat(processItems(string, "string"));
				break;
			default:
				ary = ary.concat(processItems(site, "site"), processItems(string, "string"));
				break;
		}
		res.data = ary
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}