<template>
  <view class="page-content">
    <t-navbar title="信息维护历史"></t-navbar>
    <view>
      <view class="edit-history" v-for="item in list" :key="item._id">
        <view>{{ item.text }}</view>
        <view class="bottom">
          <view class="date">{{ formatDate(item._add_time) }}</view>
          <view class="user-info" @click="vk.navigateTo(`/pages/user/detail?id=${item.user_info._id}`)">
            <image class="avatar" :src="item.user_info.avatar" mode="aspectFill"></image>
            <text class="nickname">{{ item.user_info.nickname }}</text>
          </view>
        </view>
      </view>
    </view>
    <wd-divider v-if="!hasMore && pageIndex > 1" top="50rpx">没有更多了</wd-divider>
  </view>
</template>

<script>
import dayjs from "dayjs";
export default {
  data() {
    return {
      id: null,
      pageIndex: 1,
      pageSize: 20,
      list: [],
      hasMore: true,
    };
  },
  onLoad(options) {
    this.id = options.id;
    this.getData();
  },
  onReachBottom() {
    if (!this.hasMore) return;
    this.pageIndex += 1;
    this.getData();
  },
  methods: {
    formatDate(time) {
      return dayjs(time).format("YYYY-MM-DD HH:mm");
    },
    async getData() {
      let data = await vk.callFunction({
        url: "client/public/info/pub/editHistory",
        title: "请求中...",
        data: {
          id: this.id,
          pageIndex: this.pageIndex,
          pageSize: this.pageSize,
        },
      });
      this.list.push(...data.rows);
      this.hasMore = data.hasMore;
    },
  },
};
</script>

<style lang="scss" scoped>
.page-content {
  padding: 30rpx;
}

.edit-history {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;

  .text {
    width: 100%;
    font-size: 28rpx;
    color: #333;
  }

  .bottom {
    margin-top: 20rpx;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;

    .date {
      font-size: 24rpx;
      color: #8a8a8a;
    }

    .user-info {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .avatar {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        flex-shrink: 0;
        background-color: #fff;
      }

      .nickname {
        font-size: 24rpx;
        margin-left: 16rpx;
        @include multiline(1);
      }
    }
  }
}
</style>
