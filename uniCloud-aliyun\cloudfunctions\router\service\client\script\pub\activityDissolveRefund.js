const vkPay = require("vk-uni-pay");
'use strict';
module.exports = {
	/**
	 * 活动解散退款脚本
	 * @url script/pub/activityDissolveRefund 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, token, activity_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (token !== 'n6Oge53r6XxyiTeH7vMHBF3WJ0ApHx6g') return { code: -1, msg: "未知错误" }

		// 查找活动相关的加入用户
		let join_list = await vk.baseDao.selects({
			dbName: "order-pay-data",
			getMain: true,
			pageIndex: 1,
			pageSize: 1000,
			whereJson: {
				activity_id,
			},
			foreignDB: [{
				dbName: "vk-pay-orders",
				localKey: "out_trade_no",
				foreignKey: "out_trade_no",
				as: "order_info",
				limit: 1
			}],
			lastWhereJson: {
				"order_info.status": 1
			}
		});


		for (const item of join_list) {
			await vkPay.refund({
				out_trade_no: item.out_trade_no,
				refund_desc: '活动解散退款',
			});
		}

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}