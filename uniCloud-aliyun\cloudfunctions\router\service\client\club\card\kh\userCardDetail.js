'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/card/kh/userCardDetail 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let info = await vk.baseDao.selects({
			dbName: "club-user-card",
			getMain: true,
			getOne: true,
			whereJson: {
				_id: id
			},
			foreignDB: [{
				dbName: "club-card-data",
				localKey: "card_id",
				foreignKey: "_id",
				as: "info",
				limit: 1,
			}],
			fieldJson: { info: false },
			addFields: {
				"type": "$info.type"
			}
		});
		res.data = info
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}