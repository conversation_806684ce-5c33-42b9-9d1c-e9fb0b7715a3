'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/join_state/kh/update 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, state, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let info = await vk.baseDao.updateById({
			dbName: "activity-join-data",
			id,
			dataJson: {
				state,
			},
			getUpdateData: true
		});

		await vk.baseDao.update({
			dbName: "activity-record-data",
			whereJson: {
				info_id: id
			},
			dataJson: {
				type: state
			}
		});

		// 查询是否是更新为加入
		if (state == 'joined') {
			let user = await vk.baseDao.findById({
				dbName: "uni-id-users",
				id: info.user_id,
			});
			let activity = await vk.baseDao.findById({
				dbName: "activity-data",
				id: info.activity_id,
			});
			let start = vk.pubfn.timeFormat(activity.start, "yyyy-MM-dd hh:mm:ss");
			let model = await vk.openapi.weixin.subscribeMessage.send({
				touser: user.wx_openid['mp-weixin'],
				template_id: "kunnNz3xL48JH06YDvtvU4dcu1iLcueEO5T5mmYlvas",
				page: "pages/activity/detail?id=" + activity._id,
				data: {
					thing1: {
						value: activity.name
					},
					time2: {
						value: start
					},
					thing6: {
						value: activity.site_info.name
					},
					thing5: {
						value: "请在小程序查看活动详情及注意事项"
					}
				},
				miniprogram_state: "formal",
			});
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}