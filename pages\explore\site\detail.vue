<template>
  <view class="page-container">
    <t-navbar nav-style="custom" :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 200})` }" :placeholder="false">
      <view v-if="scrollTop > 200" class="nav-title truncate">{{ info.name }}</view>
    </t-navbar>
    <template v-if="info">
      <swiper class="cover" :autoplay="true" :interval="5000" :duration="1000" circular>
        <swiper-item v-for="image in info.image" :key="image">
          <view class="cover-item">
            <image class="image" :src="image" mode="aspectFill" @click="previewImages"></image>
          </view>
        </swiper-item>
        <swiper-item v-if="info.image.length == 0" :key="'no-image'">
          <view class="cover-item">
            <image class="image" :src="`${iconUrl}no-image.svg`" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="content" v-if="info">
        <view class="like-button" @click="recommendInfo">
          <image class="icon" :class="[info.recommend ? '' : 'unlike']" :src="`${iconUrl}theme_thumb-up.svg`"> </image>
          <view class="text">{{ recommendNum }}</view>
        </view>
        <view class="explore-title">场地详情</view>
        <view class="name">
          <wd-tag type="primary" :bg-color="siteType.color" custom-style="margin:-4rpx 10rpx 0 0">{{ siteType.label }}</wd-tag>
          <text>{{ info.name }}</text>
        </view>
        <view class="content-text" v-if="info.intro">
          <text>{{ info.intro }}</text>
        </view>
        <view class="model">
          <view class="card-box">
            <map
              class="map"
              :style="[mapStyle]"
              :latitude="info.location.coordinates[1]"
              :longitude="info.location.coordinates[0]"
              :markers="markers"
              :enable-scroll="false"
              :enable-zoom="false"
              @markertap="navigateLocation"
              @tap="navigateLocation"
            ></map>
            <t-card
              :style="{ width: '48%', height: '330rpx' }"
              v-if="contactValue"
              :value="contactValue.value"
              :icon="contactValue.icon"
              :text="contactValue.text"
              @click="contactClick"
            >
              <template v-slot:label>
                <view class="card-label" @click="showContactPicker = true">
                  <view>{{ contactValue.name }}</view>
                  <image :src="`${iconUrl}arrow-down.svg`" mode="widthFix"> </image>
                </view>
              </template>
            </t-card>
          </view>
          <view v-if="info.address" :style="cardStyle">
            <t-title>地址详情</t-title>
            <view class="content-text" @click="navigateLocation">
              <text>{{ info.address || "暂无地址详情" }}</text>
            </view>
          </view>
          <view class="card-info">
            <t-title>营业时间</t-title>
            <text class="text">{{ info.open_time || "暂无信息" }}</text>
          </view>
          <view :style="cardStyle">
            <t-title>场地数量</t-title>
            <template v-if="siteType.type === 'all' || siteType.type === 'site'">
              <view class="card-value">
                <view>室内场</view>
                <view>
                  <text class="card-num">{{ info.site_indoor || 0 }}</text>
                  <text>个</text>
                </view>
              </view>
              <view class="card-value">
                <view>室外场</view>
                <view>
                  <text class="card-num">{{ info.site_outdoor || 0 }}</text>
                  <text>个</text>
                </view>
              </view>
            </template>
            <template v-if="siteType.type === 'all' || siteType.type === 'practice'">
              <view class="card-value">
                <view>学练机</view>
                <view>
                  <text class="card-num">{{ info.site_practice || 0 }}</text>
                  <text>台</text>
                </view>
              </view>
            </template>
          </view>
          <view :style="cardStyle">
            <t-title>订场价格参考</t-title>
            <view v-if="info.cost_info && info.cost_info.length !== 0" class="table">
              <view class="tr">
                <view>场地类型</view>
                <view>时间</view>
                <view>费用</view>
                <view>备注</view>
              </view>
              <view class="tr" v-for="(item, index) in info.cost_info" :key="index">
                <view>{{ item.type }}</view>
                <view>
                  <text>{{ item.day }}</text>
                  <text>{{ item.time }}</text>
                </view>
                <view>{{ item.cost }}/h</view>
                <view>{{ item.remark }}</view>
              </view>
            </view>
            <view class="edit-text" v-else>暂无订场价格参考信息，<text class="theme-text" @click="editInfo">点击去添加</text> </view>
          </view>
          <view :style="cardStyle">
            <t-title>场地服务</t-title>
            <view class="card-tag">
              <wd-tag v-for="item in info.service" :key="item" type="primary">{{ item }}</wd-tag>
            </view>
          </view>
          <view class="edit-text" v-if="isEdit">该信息有误/不完整？<text class="theme-text" @click="editInfo">点击编辑信息</text> </view>

          <view v-if="infoHistory.length > 0" :style="cardStyle">
            <view class="flex" style="justify-content: space-between">
              <t-title>信息维护历史</t-title>
              <wd-button type="text" @click="vk.navigateTo('/pages/explore/infoHistory?id=' + id)">查看全部</wd-button>
            </view>
            <view class="edit-history" v-for="item in infoHistory" :key="item._id">
              <view>{{ item.text }}</view>
              <view class="bottom">
                <view class="date">{{ formatDate(item._add_time) }}</view>
                <view class="user-info" @click="vk.navigateTo(`/pages/user/detail?id=${item.user_info._id}`)">
                  <image class="avatar" :src="item.user_info.avatar" mode="aspectFill"></image>
                  <text class="nickname">{{ item.user_info.nickname }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 底部操作栏 -->
        <t-bottom>
          <view class="button-box">
            <view>
              <wd-text type="primary" :text="signedNum" size="40rpx" bold custom-style="margin-right:10rpx"></wd-text>
              <wd-text size="28rpx" :text="`人已打卡该场地`"></wd-text>
            </view>
            <wd-button :type="signed ? 'info' : 'primary'" :disabled="signed" @click="signIn">{{ signed ? "今日已打卡" : "打卡场地" }}</wd-button>
          </view>
        </t-bottom>
      </view>
    </template>

    <wd-popup v-model="signSuccessVisible" custom-style="border-radius:24rpx;" position="center">
      <view class="sign-success" v-if="my_sign_info">
        <wd-icon name="check-circle-filled" size="68px" color="#4cd964"></wd-icon>
        <wd-text :text="my_sign_info.msg || '打卡成功!'"></wd-text>
        <view style="margin-top: 30rpx">
          <wd-text text="今天你是第" size="28rpx"></wd-text>
          <wd-text type="primary" :text="my_sign_info.today_num" size="40rpx" bold custom-style="margin:0 10rpx"></wd-text>
          <wd-text text="个打卡该场地的用户" size="28rpx"></wd-text>
        </view>
        <view style="margin-bottom: 30rpx">
          <wd-text text="您总计已打卡" size="28rpx"></wd-text>
          <wd-text type="primary" :text="my_sign_info.sign_num" size="40rpx" bold custom-style="margin:0 10rpx"></wd-text>
          <wd-text text="次该场地" size="28rpx"></wd-text>
        </view>
        <wd-button type="primary" @click="signSuccessVisible = false">我知道了</wd-button>
      </view>
    </wd-popup>

    <!-- 联系方式选择弹窗 -->
    <wd-action-sheet v-model="showContactPicker" :actions="contactPickerColumns" @close="showContactPicker = false" @select="confirmContactSelect" />
  </view>
</template>

<script>
import dayjs from "dayjs";
import { getLocation, calculateDistance } from "@/utils/location";

export default {
  data() {
    return {
      imgUrl: vk.getVuex("$app.config.staticUrl.image"),
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      scrollTop: 0,
      id: null,
      info: null,
      userInfo: vk.getVuex("$user.userInfo"),

      markers: [],
      cardStyle: {
        marginBottom: "20rpx",
      },
      contactValue: null,
      contactInfo: [],
      infoHistory: [],
      // 新增弹窗控制
      showContactPicker: false,
      selectedContactIndex: 0,
      // 打卡管理
      signInfo: null,
      my_sign_info: null,
      signed: false,
      signSuccessVisible: false,
    };
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onShareAppMessage(e) {
    if (!this.info) return;
    let image = this.info.image.length > 0 ? this.info.image[0] : "";
    return {
      title: `${this.userInfo.nickname}给您推荐网球场地${this.info.name}`,
      path: `/pages/explore/site/detail?id=${this.info._id}`,
      imageUrl: image,
    };
  },
  computed: {
    siteType() {
      if (!this.info) return null;
      const { site_type } = this.info;
      if (site_type.length > 1) return { label: "综合场馆", color: "#fcc800", type: "all" };
      if (site_type.includes("site")) return { label: "网球场", color: "", type: "site" };
      if (site_type.includes("practice")) return { label: "学练馆", color: "#38b48b", type: "practice" };
      return null;
    },
    mapStyle() {
      return {
        width: this.contactValue ? "48%" : "100%",
        height: "320rpx",
      };
    },
    isEdit() {
      if (!this.info) return false;
      if (!this.info.admin_id) return true;
      if (this.info.admin_id == this.userInfo._id) return true;
      return false;
    },
    recommendNum() {
      if (!this.info) return 0;
      let value = this.info.recommend_num;
      if (value > 10000) {
        value = (value / 10000).toFixed(2) + "w";
      } else if (value > 1000 && value < 10000) {
        value = (value / 1000).toFixed(2) + "k";
      } else {
        return value;
      }
    },
    signedNum() {
      if (!this.signInfo) return 0;
      let value = this.signInfo.sign_num;
      if (value > 10000) {
        value = (value / 10000).toFixed(2) + "w";
      } else if (value > 1000 && value < 10000) {
        value = (value / 1000).toFixed(2) + "k";
      } else {
        return value;
      }
    },
    // 联系方式选择器数据
    contactPickerColumns() {
      if (!this.contactInfo || this.contactInfo.length === 0) return [];

      return this.contactInfo.map((item) => {
        return {
          name: item.name,
        };
      });
    },
  },
  async onLoad(options) {
    if (!options.id) {
      vk.toast("获取信息失败!", "none", true, () => {
        vk.navigateBack();
      });
      return;
    }
    try {
      let localIcon = await uni.getImageInfo({
        src: `${this.imgUrl}marker/marker-local.png`,
      });
      this.localIcon = localIcon.path;
    } catch (err) {
      console.log(err);
    }
    this.id = options.id;
    // 获取数据
    await this.getData();
    // 获取维护历史信息
    this.getInfoHistory();

    // 获取今日打卡情况
    this.getSignInfo();
  },
  methods: {
    // 打卡
    async signIn() {
      try {
        // 获取当前位置判断是否可以打卡
        let location = await getLocation();
        let pos1 = location;
        let pos2 = {
          longitude: this.info.location.coordinates[0],
          latitude: this.info.location.coordinates[1],
        };
        let distance = calculateDistance(pos1, pos2);

        if (distance > 0.4) {
          vk.toast(`当前距离球场${distance}km，打卡失败`);
          return;
        }

        let res = await vk.callFunction({
          url: "client/public/info/kh/sign",
          title: "正在打卡...",
          data: {
            id: this.id,
            type: "site",
          },
        });
        this.my_sign_info = res.data;
        this.signed = true;
        this.signSuccessVisible = true;
        this.getSignInfo();
      } catch (err) {
        vk.toast(err.msg || "打卡失败");
      }
    },
    // 获取打卡情况
    async getSignInfo() {
      try {
        let res = await vk.callFunction({
          url: "client/public/info/pub/signCount",
          title: "请求中...",
          data: {
            id: this.id,
            type: "site",
          },
        });
        this.signInfo = res.data;
      } catch (err) {
        console.log(err);

        vk.toast(err.msg || "获取打卡信息失败");
      }
    },
    formatDate(time) {
      return dayjs(time).format("YYYY-MM-DD HH:mm");
    },
    async getInfoHistory() {
      let data = await vk.callFunction({
        url: "client/public/info/pub/editHistory",
        title: "请求中...",
        data: {
          id: this.id,
          pageSize: 3,
        },
      });
      this.infoHistory = data.rows;
    },
    handleContactInfo() {
      if (!this.info.contact_info || this.info.contact_info.length == 0) return;
      this.contactInfo = this.info.contact_info.map((item) => {
        switch (item.type) {
          case "phone":
            item.name = "电话";
            item.icon = `${this.iconUrl}call-white.svg`;
            item.text = "拨打电话";
            break;
          case "wechat":
            item.name = "微信号";
            item.icon = `${this.iconUrl}copy-white.svg`;
            item.text = "复制";
            break;
          case "public":
            item.name = "公众号";
            item.icon = `${this.iconUrl}copy-white.svg`;
            item.text = "复制";
            break;
          case "miniproject":
            item.name = "小程序";
            item.icon = `${this.iconUrl}copy-white.svg`;
            item.text = "复制";
            break;
          default:
            break;
        }
        return item;
      });
      this.contactValue = this.contactInfo[0];
    },
    // 确认选择联系方式
    confirmContactSelect({ index }) {
      this.selectedContactIndex = index;
      this.contactValue = this.contactInfo[index];
      this.showContactPicker = false;
    },
    // 预览图片
    previewImages() {
      uni.previewImage({
        urls: this.info.image,
      });
    },

    contactClick() {
      if (this.contactValue.type == "phone") {
        uni.makePhoneCall({
          phoneNumber: this.contactValue.value.toString(),
        });
      } else {
        this.copyContact();
      }
    },
    async editInfo() {
      vk.navigateTo("/pages/public/submitInfo?type=site&id=" + this.id);
    },
    async getData() {
      let data = await vk.callFunction({
        url: "client/public/info/site/pub/get",
        title: "请求中...",
        data: {
          id: this.id,
        },
      });
      this.info = data.data;
      this.signed = data.data.sign;
      console.log("场地详情数据:", this.info);
      console.log("费用信息:", this.info.cost_info);
      this.handleContactInfo();
      this.markers = [
        {
          id: 1,
          latitude: data.data.location.coordinates[1],
          longitude: data.data.location.coordinates[0],
          iconPath: this.localIcon,
          width: 30,
          height: 30,
        },
      ];
    },
    async recommendInfo() {
      let res = await vk.callFunction({
        url: "client/public/info/kh/recommend",
        title: "请求中...",
        data: {
          id: this.info._id,
          type: "site",
        },
      });
      this.info.recommend = res.data;
      this.info.recommend_num = res.num;
      uni.$emit("refresh-site");
    },
    navigateLocation() {
      let lat = this.info.location.coordinates[1];
      let lon = this.info.location.coordinates[0];
      uni.openLocation({
        name: this.info.name,
        latitude: lat,
        longitude: lon,
      });
    },
    copyContact() {
      let text = this.contactValue.value.toString();
      uni.setClipboardData({
        data: text,
        success: () => {
          vk.toast("复制成功", "success");
        },
        fail: (res) => {
          console.log("error", res);
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.nav-title {
  width: 300rpx;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
}

.cover {
  width: 100%;
  height: 600rpx;
  position: fixed;
  top: 0;

  .cover-item {
    width: 100%;
    height: 100%;
  }

  .image {
    width: 100%;
    height: 100%;
  }
}

.card-label {
  display: flex;
  align-items: center;

  image {
    width: 36rpx;
    margin-left: 20rpx;
  }
}

.table {
  display: flex;
  flex-direction: column;
  margin-top: 20rpx;

  .tr {
    display: grid;
    grid-template-columns: 18% 32% 18% 32%;

    &:nth-child(2n) {
      background-color: $primary-bg;
    }

    view {
      padding: 8rpx 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      text-align: center;
      border: 2rpx solid #ddd;
      font-size: 24rpx;
      box-sizing: border-box;
    }
  }
}

.content {
  width: 100%;
  min-height: calc(100vh - 600rpx);
  position: absolute;
  top: 560rpx;
  background-color: #fff;
  border-radius: 48rpx 0 0 0;
  padding: 30rpx 40rpx;
  box-sizing: border-box;

  .like-button {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    right: 40rpx;
    top: -50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: #fff;
    border-radius: 50%;
    z-index: 5;

    .unlike {
      filter: grayscale(1);
    }

    .icon {
      width: 48rpx;
      height: 48rpx;
      flex-shrink: 0;
    }

    .text {
      font-size: 24rpx;
      color: #ccc;
    }
  }

  .content-text {
    font-size: 28rpx;
    color: #8a8a8a;
    display: flex;
    margin-top: 10rpx;

    .label {
      flex-shrink: 0;
    }
  }

  .explore-title {
    font-family: usic;
    font-size: 32rpx;
    color: $primary-color;
    margin-bottom: 10rpx;
  }

  .name {
    font-size: 40rpx;
    font-weight: bold;
    color: #000;
    margin-bottom: 20rpx;
  }

  .model {
    .card-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 20rpx 0;
    }
    .map {
      border-radius: 12px;
      overflow: hidden;
      flex-shrink: 0;
    }

    .card-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;
      .text {
        margin-left: 20rpx;
        font-size: 28rpx;
        color: #333;
      }
    }
    .card-value {
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 2rpx solid #f8f8f8;
      padding: 20rpx 0;
    }

    .card-num {
      font-weight: bold;
      color: #000;
      margin: 0 10rpx 0 20rpx;
    }

    .card-tag {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      gap: 20rpx;
      margin-top: 20rpx;
    }
  }
}

.edit-text {
  width: 100%;
  margin: 80rpx 40rpx;
  text-align: center;
  font-size: 28rpx;
  color: #8a8a8a;

  .theme-text {
    color: $primary-color;
  }
}

.edit-history {
  width: 100%;
  background-color: #f8f8f8;
  border-radius: 24rpx;
  padding: 30rpx;
  margin-top: 20rpx;
  box-sizing: border-box;

  .text {
    width: 100%;
    font-size: 28rpx;
    color: #333;
  }

  .bottom {
    margin-top: 20rpx;
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: space-between;

    .date {
      font-size: 24rpx;
      color: #8a8a8a;
    }

    .user-info {
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .avatar {
        width: 48rpx;
        height: 48rpx;
        border-radius: 50%;
        flex-shrink: 0;
        background-color: #fff;
      }

      .nickname {
        font-size: 24rpx;
        margin-left: 16rpx;
        @include multiline(1);
      }
    }
  }
}

.button-box {
  width: 100%;
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
.sign-success {
  width: 80vw;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}

// 弹窗样式
:deep(.contact-picker-popup) {
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;
}
</style>
