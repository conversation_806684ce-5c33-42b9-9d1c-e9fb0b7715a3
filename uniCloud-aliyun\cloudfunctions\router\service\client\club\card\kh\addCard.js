const formRules = require("../../util/formRules.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/kh/addCard 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id, name, type, value, day, end, price, repeat = false, enable = false, overlay = false, style_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let num = await vk.baseDao.count({
			dbName: "club-card-data",
			whereJson: {
				club_id,
			}
		});
		if (num >= 30) {
			res.code = -1
			res.msg = "卡片数量不能超过30个"
			return res
		}
		let formRulesRes = await formRules.card_add(event);
		if (formRulesRes.code !== 0) {
			return formRulesRes;
		}
		// 如果为定期卡则不能重复购买
		if (type == 'date') repeat = false
		// 获取style样式数据
		let style = await vk.baseDao.findById({
			dbName: "card-style-data",
			id: style_id,
			fieldJson: { name: true, type: true, background: true, main_color: true, second_color: true, text_color: true }
		});
		let id = await vk.baseDao.add({
			dbName: "club-card-data",
			dataJson: {
				club_id,
				name,
				type,
				value: Number(value),
				day: Number(day),
				end,
				repeat,
				overlay,
				enable,
				price: Number(price),
				style,
			}
		});
		res.msg = "新增成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}