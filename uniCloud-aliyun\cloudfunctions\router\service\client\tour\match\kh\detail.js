'use strict';
/**
 * 获取比赛详情
 * @url client/tour/match/kh/detail 前端调用的url参数地址
 * @description 获取比赛详情
 * @param {String} match_id 比赛ID
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { match_id } = data;
    if (!match_id) return { code: -1, msg: '比赛ID不能为空' };

    const dbName = 'tour-matches';
    res = await vk.baseDao.findById({
      dbName,
      id: match_id
    });
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 