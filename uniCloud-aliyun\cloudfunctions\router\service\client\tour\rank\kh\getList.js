'use strict';
/**
 * 获取排名列表
 * @url client/tour/rank/kh/getList 前端调用的url参数地址
 * @description 获取排名列表
 * @param {String} match_id 比赛ID
 * @param {String} type 排名类型（singles/doubles）
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { match_id, type = 'singles' } = data;
    if (!match_id) return { code: -1, msg: '比赛ID不能为空' };
    if (!['singles', 'doubles'].includes(type)) {
      return { code: -1, msg: '排名类型无效' };
    }

    const dbName = 'tour-rank';
    const whereObj = {
      match_id,
      type
    };

    const result = await vk.baseDao.select({
      dbName,
      whereObj,
      sortArr: [{ "name": "rank", "type": "asc" }]
    });

    res.data = result.rows;
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 