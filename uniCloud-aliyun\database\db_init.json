{"activity-data": {"data": [{"_id": "66ac8be9337a9f3a33d39e76", "name": "云景打球活动", "desc": "", "start": 1722596400000, "end": 1722607200000, "type": "normal", "creator_id": "6684012bfe975f74405225f5", "min_num": 0, "max_num": 6, "site_info": {"location": {"latitude": 21.951628, "longitude": 108.66691}, "address": "广西壮族自治区钦州市钦南区南珠街道华成自控大厦内云景网球场", "name": "云景网球场"}, "cost_type": "fixed", "cost": 40, "level": 0, "apply": false, "open": true, "notice": true, "location": {"type": "Point", "coordinates": [108.66691, 21.951628]}, "_add_time": 1722584041604, "_add_time_str": "2024-08-02 15:34:01"}], "index": [{"IndexName": "location", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "location", "Direction": "2dsphere"}], "MgoIsUnique": false}}, {"IndexName": "订阅消息", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "notice", "Direction": "1"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "exit_time": {"description": "接龙随意退出时间"}}}}, "activity-join-data": {"data": [{"_id": "66ac8bfa862066226c490a02", "user_id": "6684012bfe975f74405225f5", "activity_id": "66ac8be9337a9f3a33d39e76", "type": "myself", "state": "joined", "_add_time": 1722584058063, "_add_time_str": "2024-08-02 15:34:18"}]}, "activity-record-data": {"data": [{"_id": "6707a4378b0da45758f6a44f", "info_id": "66ac8be9337a9f3a33d39e76", "user_id": "6684012bfe975f74405225f5", "activity_id": "66ac8be9337a9f3a33d39e76", "type": "created", "_add_time": 1722584041604, "_add_time_str": "2024-08-02 15:34:01"}]}, "activity-template-data": {"data": [{"_id": "6690a46b9755e32830fa2947", "name": "接龙双打", "desc": "1. 参与本次网球接龙活动是出于个人自愿，参与者应自行承担参与活动可能带来的风险。\n2. 组织者不对活动中发生的任何意外伤害或损失承担责任。\n3. 请确保您在身体健康的状态下参与，并已了解所有活动规则。\n4. 建议参与者自行购买相应的人身意外保险。\n5. 球场为非吸烟区，吸烟请移步；\n6. 请保管好随身物品，垃圾入篓！", "site_info": null, "max_num": 8, "min_num": 4, "cost_type": "average", "cost": "", "apply": true, "open": true, "level": 0, "creator_id": "", "type": "admin", "_add_time": 1720755307375, "_add_time_str": "2024-07-12 11:35:07"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "agreement-data": {"data": [{"_id": "6683b9853d029c65e94ac635", "title": "用户协议", "update_time": 1719846296846, "content": "<p>欢迎使用我们的来网球约球平台（以下简称“本平台”）。在注册和使用本平台之前，请您仔细阅读以下用户协议。通过注册和使用本平台，即表示您同意并接受以下所有条款和条件。如果您不同意这些条款，请不要注册或使用本平台。<br></p><p><br></p><p><strong>1. 服务内容<br></strong></p><p>本平台为用户提供一个网球约球和交流的平台，您可以在本平台上寻找网球伙伴、发布网球活动、参加他人组织的活动等。<br></p><p><br></p><p><strong>2. 注册与账户<br></strong></p><p>2.1 用户需提供真实、准确、完整的注册信息。<br>2.2 用户应妥善保管注册账户及密码，不得泄露给他人。因用户泄露账户信息导致的损失由用户自行承担。<br>2.3 严禁用户使用虚假信息或冒用他人身份注册。<br></p><p><br></p><p><strong>3. 隐私保护<br></strong></p><p>我们承诺保护用户的隐私，本平台将严格按照隐私政策收集、使用和保护用户信息。<br></p><p><br></p><p><strong>4. 用户行为规范<br></strong></p><p>4.1 用户应遵守有关法律法规及本协议的相关规定，不得利用本平台从事任何违法、违规、有悖道德的活动。<br>4.2 用户不得发布含有虚假、欺诈、诬陷、辱骂、歧视、淫秽等违法内容的信息。<br>4.3 用户在参加活动时，应尊重他人，不得有任何侵犯他人权益的行为。<br></p><p><br></p><p><strong>5. 知识产权<br></strong></p><p>本平台所提供的一切内容（包括但不限于文字、图片、音频、视频等）均受相关知识产权法律保护。未经授权，用户不得进行复制、修改、传播等行为。<br></p><p><br></p><p><strong>6. 风险与责任<br></strong></p><p>6.1 用户应自行判断并承担通过本平台进行约球、组织活动或参加他人活动的相应风险。</p><p>6.2 在约球和活动过程中，因天气、场地、装备等因素造成的意外事故及损失，由用户自行承担。</p><p>6.3 用户需确保自己的身体健康状况适合参加网球运动，如有身体不适或有医疗禁忌，请在医生的建议下参加活动。</p><p>6.4 用户在参加活动时，应尊重他人，不得有任何侵犯他人权益的行为。如因用户行为引发纠纷，用户应自行解决，并独立承担由此产生的所有责任和后果。本平台不对用户行为导致的任何损失、伤害或其他后果承担责任。<br></p><p><br></p><p><strong>7. 免责声明<br></strong></p><p>7.1 本平台对用户发布的信息进行初步审核，但不对其真实性和准确性负责。</p><p>7.2 本平台仅作为用户发布和查找网球活动信息的平台，并不对用户之间的关系及其行为进行任何保证或承担任何责任。用户在使用本平台过程中发生的任何纠纷或争议，应由纠纷双方自行解决。</p><p>7.3 对于因第三方原因或用户自身原因导致的任何损失、伤害或其他后果，本平台不承担责任。</p><p><br></p><p><strong>8. 账户终止<br></strong></p><p>8.1 用户有权随时终止使用本平台服务。<br>8.2 如用户违反本协议条款，本平台有权随时限制或终止其账户使用权。<br></p><p><br></p><p><strong>9. 修改与解释<br></strong></p><p>本平台有权根据需要不时地修改本协议的内容，并通过平台公告的方式予以公布，修改后的协议自公告之日起生效。用户有义务随时关注并阅读最新版的用户协议。若用户在本协议修改后继续使用本平台，则视为用户已阅读、理解并接受修改后的协议。<br></p><p><br></p><p><strong>10. 法律适用与争议解决<br></strong></p><p>本协议的订立、执行和解释及争议的解决均应适用中华人民共和国法律。因本协议产生的任何争议，双方应本着友好协商的原则解决；协商不成的，任何一方均可向本平台所在地的人民法院提起诉讼。<br></p><p>最后，感谢您使用我们的网球约球平台。如您对本协议有任何疑问，请及时与我们联系，邮箱：<EMAIL>。<br></p><p><strong>祝您使用愉快！</strong></p>", "type": "user"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "card-style-data": {"data": [{"_id": "65d362c299c6244dcf0dfeeb", "name": "图片样式1", "type": "image", "background": "https://cdn.cometennis.cn/cloudstorage/549f4d6b-7633-439c-b85d-072b222d3746.jpeg", "main_color": null, "second_color": null, "text_color": "#FFFFFF", "_add_time": 1708352193955, "_add_time_str": "2024-02-19 22:16:33"}]}, "club-activity-data": {"data": [{"_id": "65efbbe5337a9fefcc61cda1", "name": "3.0高手接龙局", "desc": "高手请进", "coach": [], "card": [{"id": "65efb44a213929f866b99d51", "use": "1"}], "club_id": "65e7d1e6bd022087dfd0fc39", "max_num": "8", "min_num": "4", "apply": true, "open": true, "level": 3, "creator_id": "65e68fe0e0ec199b18fcbb72", "_add_time": 1710210020662, "_add_time_str": "2024-03-12 10:20:20", "last_update": "65e68fe0e0ec199b18fcbb72", "last_update_time": 1710725907737}]}, "club-card-data": {"data": [{"_id": "6704f203b9fb2347e8d67963", "club_id": "65e7d1e6bd022087dfd0fc39", "name": "测试卡", "type": "times", "value": 1, "day": 999, "end": 1814690942787, "repeat": true, "enable": true, "price": 0.01, "style": {"_id": "65e7d9b3337a9fefcc1ab81f", "name": "图片样式2", "type": "image", "background": "https://cdn.cometennis.cn/2024/03/06/93350789-34461138-pexelspixabay209977.jpeg", "main_color": null, "second_color": null, "text_color": "#FFFFFF"}, "_add_time": 1728377346745, "_add_time_str": "2024-10-08 16:49:06"}]}, "club-data": {"data": [{"_id": "65d1c73c652341ed5e29f65b", "business_license": "https://cdn.cometennis.cn/2024/02/18/48365668-92273787-head.png", "name": "云景网球俱乐部", "address": "广西钦州市钦北区云景网球场", "_add_time": 1708246843918, "_add_time_str": "2024-02-18 17:00:43", "admin_uid": "6684012bfe975f74405225f5", "banner": ["https://cdn.cometennis.cn/club/images/44735612-44848645-ipp202308233.jpg", "https://cdn.cometennis.cn/club/images/44735614-22485833-ipp202308234.jpg", "https://cdn.cometennis.cn/club/images/44735616-79354145-ipp202308235.jpg", "https://cdn.cometennis.cn/club/images/44735618-21966288-ipp202308236.jpg"], "cover": "https://cdn.cometennis.cn/club/images/44816056-61326672-pexelspixabay209977.jpeg", "logo": "https://cdn.cometennis.cn/club/logo/14913509-33674742-logo.png", "desc": "欢迎来到云景网球俱乐部，我们是一家成立于2024年的领先网球组织，立足于提供顶级的网球训练和竞赛机会。短短时间内，我们就已经建立起口碑良好的教学课程，并且积极举办各种级别的比赛活动。在云景，我们深信每个人都有潜力发掘和提升自己的网球技艺。我们有一流的教练团队，他们专注于小型课程，以确保每位学员都能获得个性化的教学指导。除了技术训练之外，我们还重视团队合作和运动精神的培养。在云景，我们更是一个大家庭，提供一个支持、友谊和挑战自我的平台。来云景，一起分享、经历和追求网球之美吧！", "apply": false, "last_update": "", "last_update_time": "", "intro": "欢迎来到云景网球俱乐部，我们是一家成立于2024年的领先网球组织，立足于提供顶级的网球训练和竞赛机会。短短时间内，我们就已经建立起口碑良好的教学课程，并且积极举办各种级别的比赛活动。在云景，我们深信每个人都有潜力发掘和提升自己的网球技艺。我们有一流的教练团队，他们专注于小型课程，以确保每位学员都能获得个性化的教学指导。除了技术训练之外，我们还重视团队合作和运动精神的培养。在云景，我们更是一个大家庭，提供一个支持、友谊和挑战自我的平台。来云景，一起分享、经历和追求网球之美吧！"}]}, "club-site-data": {"data": [{"_id": "65f7f08221821b6d2beb466a", "images": ["https://cdn.cometennis.cn/2024/03/18/47748492-51435496-0.jpeg", "https://cdn.cometennis.cn/2024/03/18/47769159-27753267-0.png"], "name": "vip红土场", "desc": "就是一个简单的红土场地", "club_id": "65e7d1e6bd022087dfd0fc39", "location": {"type": "Point", "coordinates": [108.08728, 24.693602]}, "_add_time": 1710747778200, "_add_time_str": "2024-03-18 15:42:58", "address": "广西壮族自治区河池市金城江区百旺路10号1期12号楼丽枫酒店(河池澳门国际城店)", "book": true, "last_update": "65e68fe0e0ec199b18fcbb72", "last_update_time": 1712341661396}]}, "club-user-card": {"data": [{"_id": "6707ae299755e3b8e7df6809", "card_id": "6704f203b9fb2347e8d67963", "user_id": "6683b12899c6244dcf2d28e7", "club_id": "65e7d1e6bd022087dfd0fc39", "value": 2, "start": 1728556604000, "end": 1814870204000, "_add_time": 1728556585804, "_add_time_str": "2024-10-10 18:36:25"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "club-user-data": {"data": [{"_id": "66e3f29aeef9cba93456a7c0", "state": "admin", "user_id": "6684012bfe975f74405225f5", "club_id": "65d1c73c652341ed5e29f65b", "_add_time": 1726214809967, "_add_time_str": "2024-09-13 16:06:49"}]}, "info-edit-history": {"data": [{"_id": "669f7e3589bd273fb33edd4a", "info_id": "669a00c8c3b5c9650296a6d1", "user_id": "6683b12899c6244dcf2d28e7", "text": "维护了参考价格信息", "update_time": 1720631998759, "_add_time": 1721728565289, "_add_time_str": "2024-07-23 17:56:05"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "info-recommend-record": {"data": [{"_id": "66af09a789bd273fb3e8c4a1", "main_id": "66a8ab2c7c8de445a61e81f6", "user_id": "66ade58721821b43044251a3", "_add_time": 1722747303871, "_add_time_str": "2024-08-04 12:55:03"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "info-site-data": {"data": [{"_id": "66da7bed816a3ffb2dab5888", "image": ["https://cdn.cometennis.cn/2024/09/06/93026505-36245772-0.jpg"], "name": "悦冠网球俱乐部（跑马场）", "intro": "", "location": {"type": "Point", "coordinates": [108.3293, 22.800346]}, "address": "广西壮族自治区南宁市青秀区桃源路62号体育场", "address_name": "南宁市体育场", "site_indoor": 2, "site_outdoor": 2, "open_time": "9:00-22:00", "service": ["洗手间", "沐浴间", "饮料售卖", "车位充足"], "recommend_num": 1, "admin_id": "", "_add_time": 1725594604691, "_add_time_str": "2024-09-06 11:50:04", "contact_info": [{"type": "wechat", "value": "wyfyy564335"}], "cost_info": []}], "index": [{"IndexName": "地理信息", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "location", "Direction": "2dsphere"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "info-string-data": {"data": [{"_id": "66a8ab2c7c8de445a61e81f6", "image": ["https://cdn.cometennis.cn/2024/08/05/56691258-79497392-0.jpg"], "name": "云景网球俱乐部", "intro": "店里有线，也可自带线。手工20", "location": {"type": "Point", "coordinates": [108.66691, 21.951628]}, "address": "广西壮族自治区钦州市钦南区南珠街道华成自控大厦内云景网球场", "price": 20, "open_time": "私聊", "recommend_num": 8, "_add_time": 1722329899743, "_add_time_str": "2024-07-30 16:58:19", "address_name": "云景网球俱乐部", "admin_id": "", "contact_info": [{"type": "phone", "value": "18169625787"}]}], "index": [{"IndexName": "位置信息", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "location", "Direction": "2dsphere"}], "MgoIsUnique": false}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "level-score-data": {"data": [{"_id": "6683988eee97ef5896c896d4", "type": "init", "user_id": "666a681b652341bc2f1694d5", "data": {"forehand": 3.5, "backhand": 4, "serve": 4, "return": 4, "volley": 3.5, "description": 4, "base": 3.5}, "_add_time": 1719900302198, "_add_time_str": "2024-07-02 14:05:02"}]}, "opendb-admin-log": {"data": [{"_id": "66a072b3c3b5c965023abfa2", "user_id": "001", "user_name": "超级管理员", "title": "用户修改", "ip": "127.0.0.1", "url": "admin/system/user/sys/update", "request_param": {"_id": "6683b12899c6244dcf2d28e7", "wx_openid": {"mp-weixin": "og0oK7ZfGO8syZGxZpjYc90YMgGo"}, "my_invite_code": "ZLN22L", "register_env": {"appid": "__UNI__84F0CDF", "uni_platform": "mp-weixin", "os_name": "ios", "app_name": "tennis-client", "app_version": "1.0.0", "app_version_code": "100", "channel": "", "client_ip": "127.0.0.1"}, "dcloud_appid": ["__UNI__84F0CDF", "__UNI__570A7FB", "__UNI__01F080F"], "register_date": 1719906600468, "token": [], "credit": 100, "last_login_date": 1721730853683, "last_login_ip": "***************", "match_open": true, "sex": 1, "unLogin": false, "status": 0, "avatar": "https://cdn.cometennis.cn/2024/07/02/11210486-69677484-0.jpeg", "nickname": "Listen Hua", "level": 3.5, "back_cover": "https://cdn.cometennis.cn/2024/07/04/26611700-99451887-0.jpg", "phone": "18777708158", "role": ["operator"], "username": "listenhua", "password": "03caebb36670995fc261a275d212cad65e4bbebd", "appList": [], "pl_table_level": 0, "login_appid_type": 1, "allow_login_background": true}, "response": {"code": 0, "msg": "", "num": 1}, "request_id": "6ec7cb71-7361-469e-9c9a-443aea9b7c34", "_add_time": 1721791154745, "_add_time_str": "2024-07-24 11:19:14"}]}, "opendb-admin-menus": {"data": [{"_id": "sys-admin", "_add_time": 1596416400000, "menu_id": "sys-admin", "name": "用户角色权限", "icon": "el-icon-s-tools", "url": "", "comment": "系统内置", "sort": 100, "enable": true}]}, "opendb-app-list": {"data": [{"_id": "001", "appid": "__UNI__84F0CDF", "type": "client", "name": "用户端", "description": "此为用户端应用", "create_date": 1596416400000, "_add_time": 1596416400000, "_add_time_str": "2020-08-03 09:00:00"}]}, "opendb-app-versions": [], "opendb-open-data": [], "opendb-tempdata": [], "opendb-verify-codes": {"data": [{"_id": "679052003f1a473be28b01fc", "mobile": "18777708158", "type": "login", "code": "759657", "state": 1, "ip": "***************", "created_at": 1737511424842, "expired_at": 1737511484842}]}, "options-data": {"data": [{"_id": "66223cca6e5d2ddb5123595f", "image": "https://cdn.cometennis.cn/2024/04/19/19812819-31832271-adidas_barricade.png", "label": "Adidas Barricade", "value": "adidas_barricade", "type": "shoes", "_add_time": 1713519818025, "_add_time_str": "2024-04-19 17:43:38"}]}, "order-pay-data": {"data": [{"_id": "6704f66d1c90b6f6badce97d", "out_trade_no": "172837847635988287", "user_id": "6683b12899c6244dcf2d28e7", "club_id": "65e7d1e6bd022087dfd0fc39", "card_id": "6704f203b9fb2347e8d67963", "status": 0, "type": "new", "_add_time": 1728378476744, "_add_time_str": "2024-10-08 17:07:56"}]}, "order-pay-record": {"data": [{"_id": "6704f66e4b92477fa2d46d3e", "user_id": "6683b12899c6244dcf2d28e7", "order_id": "6704f66d1c90b6f6badce97d", "_add_time": 1728378477167, "_add_time_str": "2024-10-08 17:07:57"}]}, "pending-audit-data": {"data": [{"_id": "67d245a709664cc2d48144c0", "image": [], "name": "黄木岗网球中心", "intro": "", "location": {"type": "Point", "coordinates": [114.080282, 22.556422]}, "address": "广东省深圳市福田区笋岗西路3072号", "address_name": "黄木岗网球中心", "contact_info": [{"type": "phone", "value": ""}], "site_indoor": "", "site_outdoor": "", "cost_info": [], "open_time": "", "service": [], "open": true, "creator_id": null, "type": "site", "update": false, "_add_time": 1741833639122, "_add_time_str": "2025-03-13 10:40:39"}], "index": [{"IndexName": "地理索引", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "location", "Direction": "2dsphere"}], "MgoIsUnique": false}}]}, "player-status-data": {"data": [{"_id": "67ceac1089bd2757373c9229", "user_id": "6683b12899c6244dcf2d28e7", "location": {"type": "Point", "coordinates": [108.496251, 22.78785]}, "address_name": "丰泽·双湾紫云台", "accept_distance": 50, "tags": ["有偿陪练", "免费陪练", "可约球", "可教学"], "contact": ["wechat", "mobile"], "open": true, "_add_time": 1741597712051, "_add_time_str": "2025-03-10 17:08:32"}], "index": [{"IndexName": "地理位置", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "location", "Direction": "2dsphere"}], "MgoIsUnique": false}}]}, "system-data": {"data": [{"_id": "6683c575fe975f744048e0c2", "type": "welcome", "image": "https://cdn.cometennis.cn/images/welcome.png", "url": "", "second": 3}], "index": [{"IndexName": "类型", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "type", "Direction": "1"}], "MgoIsUnique": true}}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}, "uni-id-log": {"data": [{"_id": "65d1c2b4213929f86667318c", "type": "login", "login_type": "password", "user_id": "001", "ip": "127.0.0.1", "ua": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0", "os": "macos", "platform": "h5", "state": 1, "dcloud_appid": "__UNI__F923198", "date": {"year": 2024, "month": 2, "day": 18, "hour": 16, "minute": 41, "second": 23, "millisecond": 670, "week": 0, "quarter": 1, "date_str": "2024-02-18 16:41:23", "date_day_str": "2024-02-18", "date_month_str": "2024-02"}, "_add_time": 1708245683681, "_add_time_str": "2024-02-18 16:41:23"}]}, "uni-id-permissions": {"data": [{"_id": "sys-permission", "_add_time": 1596416400000, "permission_id": "sys-permission", "permission_name": "系统内置权限", "comment": "系统内置权限", "url": "", "sort": 103, "enable": true}]}, "uni-id-roles": {"data": [{"_id": "001", "_add_time": 1596416400000, "enable": true, "role_id": "admin", "role_name": "超级管理员", "comment": "系统内置角色 - 请勿修改", "permission": []}]}, "uni-id-users": {"data": [{"_id": "001", "username": "admin", "password": "6b4f9ff24d8bc77bf5f140b5d3f60804e5847f5d", "register_date": 1596416400000, "register_ip": "127.0.0.1", "nickname": "超级管理员", "role": ["admin"], "token": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwMDEiLCJyb2xlIjpbImFkbWluIl0sInBlcm1pc3Npb24iOltdLCJpYXQiOjE3NDEyNzIxMTksImV4cCI6MTc0MTg3NjkxOX0.gcVRNje2gDJ3oFyo6Z84I2zdj_0BfLI6yplEoCDBR0Y", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwMDEiLCJyb2xlIjpbImFkbWluIl0sInBlcm1pc3Npb24iOltdLCJpYXQiOjE3NDE0MzA2NjQsImV4cCI6MTc0MjAzNTQ2NH0.VptFrawl48EERaFU5qpkZ3aqqCb1rvkhIoJNURe1fCc", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwMDEiLCJyb2xlIjpbImFkbWluIl0sInBlcm1pc3Npb24iOltdLCJpYXQiOjE3NDE0MzA2NjQsImV4cCI6MTc0MjAzNTQ2NH0.VptFrawl48EERaFU5qpkZ3aqqCb1rvkhIoJNURe1fCc", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIwMDEiLCJyb2xlIjpbImFkbWluIl0sInBlcm1pc3Npb24iOltdLCJpYXQiOjE3NDE4MjUyNTUsImV4cCI6MTc0MjQzMDA1NX0.rDGfB5QAIPVYbZqyNCs-kkyb80W5rcmlLiyBxM61hs0"], "allow_login_background": true, "last_login_date": 1741825255203, "last_login_ip": "************", "login_ip_limit": []}]}, "user-feedback-data": [], "user-follow-data": {"data": [{"_id": "674d66c721821bdf933faa50", "target_user_id": "668651089755e328309b2549", "user_id": "6683b12899c6244dcf2d28e7", "_add_time": 1733124258885, "_add_time_str": "2024-12-02 15:24:18"}]}, "user-news-data": {"data": [{"_id": "6684f39855b3372a1f29623c", "accept_user": "6671ae736e5d2ddb517f82dd", "title": "成员退出提醒", "text": "用户Listen Hua退出了你发起的活动<测试审批接龙活动订阅模板>。", "read_state": false, "type": "info", "link": "/pages/activity/index?id=6684a8e9b9fb2360b0bb8a89", "_add_time": 1719989143957, "_add_time_str": "2024-07-03 14:45:43"}]}, "vk-components-dynamic": {"index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "data_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "data_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "title", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "title", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "type", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "type", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sort", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sort", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "show", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "show", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-files": {"data": [{"_id": "65e52944652341ed5ecb3caa", "user_id": "65d6a8948b0da4a4e405645e", "sort": 0, "status": 0, "type": "image", "url": "https://cdn.cometennis.cn/2024/03/04/17122340-97369133-0.jpg", "display_name": "17122340-97369133-0.jpg", "original_name": "17122340-97369133-0.jpg", "size": 92090, "file_id": "https://cdn.cometennis.cn/2024/03/04/17122340-97369133-0.jpg", "provider": "unicloud", "width": 1200, "height": 675, "orientation": "up", "category_id": "site-images", "_add_time": 1709517124308, "_add_time_str": "2024-03-04 09:52:04"}], "index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "user_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sort", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sort", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "status", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "status", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "type", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "type", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "display_name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "display_name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "url", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "url", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-files-categories": {"data": [{"_id": "672489e0a7c4325060c141ce", "name": "banner", "_add_time": 1730447840420, "_add_time_str": "2024-11-01 15:57:20"}], "index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "name", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "name", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "sort", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "sort", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-global-data": {"data": [{"_id": "mp-weixin-wx6046522bc39080dd", "key": "mp-weixin-wx6046522bc39080dd", "value": "90_FwKWLEJVveC9R6e449lfO8sdmnqSqEmAgcJOjJXgQ1Nau98y0qzcwgNNu2UR09lgeyOCbZoySJlgUoNCw12rNVDv1hfW3j8GZAZsFCm_1W6d08k06LJ0qpsDkhoOVPjAHACJD", "expired_at": 1741672896105, "_add_time": 1741672656105, "_add_time_str": "2025-03-11 13:57:36"}], "index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "key", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "key", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "expired_at", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "expired_at", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-pay-config": {"index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-pay-orders": {"data": [{"_id": "6704f66f7ae7081fd9039f7d", "_add_time": 1728378478438, "_add_time_str": "2024-10-08 17:07:58", "status": 0, "appId": "__UNI__84F0CDF", "provider_appid": "wx6046522bc39080dd", "mch_id": "**********", "pay_type": "wxpay_mp-weixin", "platform": "mp-weixin", "uni_platform": "mp-weixin", "type": "card", "total_fee": 1, "out_trade_no": "172837847635988287", "description": "购买会员卡<测试卡>", "create_date": 1728378477545, "client_ip": "127.0.0.1", "device_id": "172371495566975041", "user_id": "6683b12899c6244dcf2d28e7", "nickname": "Listen Hua", "openid": "og0oK7ZfGO8syZGxZpjYc90YMgGo", "card_id": "6704f203b9fb2347e8d67963", "club_id": "65e7d1e6bd022087dfd0fc39", "notify_url": "https://fc-mp-4aae0bb5-3ddf-45dc-8a49-5fba7f9569cc.next.bspapp.com/http/vk-pay", "notify_path": "/vk-pay-notify/wxpay_mp-weixin/172837847635988287", "stat_data": {"platform": "mp-weixin", "app_version": "1.0.0", "app_version_code": "100", "os": "ios", "channel": "1001", "scene": 1001}}]}}