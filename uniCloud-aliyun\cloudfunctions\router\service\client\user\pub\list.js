'use strict';
module.exports = {
	/**
	 * 获取粉丝列表
	 * @url client/user/pub/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, pageIndex, user_id, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let whereJson = {}
		let localKey = 'user_id'
		if (type == 'fans') {
			whereJson.target_user_id = user_id
			localKey = 'user_id'
		}
		if (type == 'follow') {
			whereJson.user_id = user_id
			localKey = 'target_user_id'
		}
		res = await vk.baseDao.selects({
			dbName: "user-follow-data",
			getCount: false,
			pageIndex,
			pageSize: 20,
			// 主表where条件
			whereJson,
			// 主表字段显示规则
			fieldJson: { _id: true },
			// 主表排序规则
			sortArr: [{ name: "_add_time", type: "desc" }],
			// 副表列表
			foreignDB: [{
				dbName: "uni-id-users",
				localKey,
				foreignKey: "_id",
				as: "user_info",
				fieldJson: { avatar: true, nickname: true },
				limit: 1
			}]
		});
		// 处理数据
		let user_arr = []
		res.rows.forEach(item => {
			user_arr.push(item.user_info)
		})
		res.rows = user_arr
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}