'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/card/kh/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id, user_id, enable = true } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (!club_id && !user_id) return { code: -1, msg: "请求参数异常" }
		
		res = await vk.baseDao.selects({
			dbName: "club-card-data",
			getCount: false,
			pageIndex: 1,
			pageSize: 30,
			whereJson: {
				club_id,
				enable,
			},
			foreignDB: [{
				dbName: "club-data",
				localKey: "club_id",
				foreignKey: "_id",
				as: "club_info",
				limit: 1
			}],
			// sortArr: [{ "name": "_add_time", "type": "desc" }],
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}