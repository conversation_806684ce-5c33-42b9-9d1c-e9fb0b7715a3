"use strict";
module.exports = {
	/**
	 * 解散活动
	 * @url client/activity/kh/dissolve 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id, reason } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (!reason) return { msg: "请选择解散原因", code: -1 }

		// 获取活动详情
		let activity = await vk.baseDao.findById({
			dbName: "activity-data",
			id,
		});
		// 活动已结束无法解散
		if (Date.now() > activity.end) return { msg: "活动已结束，解散失败", code: -1 }

		let member = await vk.baseDao.select({
			dbName: "activity-join-data",
			getMain: true,
			pageIndex: 1,
			pageSize: 9999,
			whereJson: {
				activity_id: id,
			},
		});
		// 删除记录
		await vk.baseDao.del({
			dbName: "activity-record-data",
			whereJson: {
				activity_id: id,
			},
		});
		// 删除相应的加入数据
		await vk.baseDao.del({
			dbName: "activity-join-data",
			whereJson: {
				activity_id: id,
			},
		});
		// 删除活动
		await vk.baseDao.deleteById({
			dbName: "activity-data",
			id,
		});
		res.msg = "解散成功";
		// 线上支付的活动
		if (activity.cost_plan === 1) {
			// 执行退款脚本
			let refundRes = await vk.callFunction({
				url: 'client/script/pub/activityDissolveRefund',
				event,
				data: {
					activity_id: id,
					token: "n6Oge53r6XxyiTeH7vMHBF3WJ0ApHx6g"
				},
			});
		}
		console.log("退款结果", refundRes);
		// 发通知
		let params = [];
		let user_ids = [];
		member.forEach((item) => {
			if (item.user_id == uid) return;
			let title = `活动解散通知`;
			let text = `您${item.state == "applying" ? "申请参加" : "参与"}的活动<${activity.name}>因${reason}已被解散`;
			params.push({
				accept_user: item.user_id,
				title,
				text,
				read_state: false,
				type: "info",
				link: ``,
			});
			user_ids.push(item.user_id);
		});
		if (params.length > 0) {
			// 发送通知
			await vk.baseDao.adds({
				dbName: "user-news-data",
				dataJson: params,
			});
		}
		// 发送订阅消息
		if (user_ids.length == 0) return res;
		let ids = [...new Set(user_ids)];
		let users = await vk.baseDao.select({
			dbName: "uni-id-users",
			getCount: false,
			pageSize: 999,
			whereJson: {
				_id: _.in(ids),
			},
			fieldJson: { wx_openid: true },
		});
		if (!users.rows) return res;
		let start = vk.pubfn.timeFormat(activity.start, "yyyy-MM-dd hh:mm:ss");
		let reason_text = reason.length > 15 ? `${reason.slice(0, 15)}...` : reason;
		users.rows.forEach((item) => {
			let model = vk.openapi.weixin.subscribeMessage.send({
				touser: item.wx_openid["mp-weixin"],
				template_id: "qULr23FpxNQTuWrghCj6gA_855wO6isiP-_IM1sCO1A",
				page: "pages/index/index",
				data: {
					thing1: {
						value: activity.name,
					},
					thing2: {
						value: start,
					},
					pharse3: {
						value: "活动已解散",
					},
					thing4: {
						value: reason_text,
					},
					thing5: {
						value: "点击前往小程序加入其它活动",
					},
				},
				miniprogram_state: "formal",
			});
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	},
};