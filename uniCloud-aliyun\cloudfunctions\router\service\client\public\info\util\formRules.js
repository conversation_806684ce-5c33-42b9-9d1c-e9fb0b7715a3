'use strict';
/**
 * 表单验证
 */
class Util {
	constructor() {
		this.rules = {
			// 添加穿线店铺信息
			string_add: {
				name: [{ required: true, message: '名称不能为空', trigger: 'blur' }],
				location: [{ required: true, message: '定位信息不能为空', trigger: 'blur' }],
				address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
			},
			// 添加俱乐部信息
			site_add: {
				name: [{ required: true, message: '场地名称不能为空', trigger: 'blur' }],
				location: [{ required: true, message: '定位信息不能为空', trigger: 'blur' }],
				address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
			},
		}
	}
	async validate(event, rules) {
		let { data = {}, userInfo, util } = event;
		let { vk } = util;
		console.log("参数", data);
		let res = vk.pubfn.formValidate({
			data: data,
			rules: rules
		});
		return res;
	}
	/**
	 * 添加穿线店铺信息
	 */
	async string_add(event) {
		return this.validate(event, this.rules.string_add);
	}
	/**
	 * 添加俱乐部信息
	 */
	async site_add(event) {
		return this.validate(event, this.rules.site_add);
	}
}
module.exports = new Util