/**
 * 请勿修改此处代码，因为插件更新后此处代码会被覆盖。
 * 作者：VK
 * 发布于：2021-07-06
 */
const alipay = require('./alipay/index');
const wxpayV2 = require('./wxpay/wxpayV2');
const wxpayV3 = require('./wxpay/wxpayV3');
const vkspay = require('./vkspay/index');
// 自动加载其他目录下的index.js
const fs = require('fs');
const modules = {};
const dirs = fs.readdirSync(__dirname);
dirs.forEach(name => {
	try {
		if (["index.js", "alipay", "vkspay", "wxpay"].indexOf(name) === -1) {
			modules[name] = require(`./${name}/index`);
		}
	} catch (error) {}
});
module.exports = {
	alipay,
	wxpayV2,
	wxpayV3,
	vkspay,
	...modules
};
