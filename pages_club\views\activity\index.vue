<template>
  <view>
    <t-navbar title="活动管理"></t-navbar>
    <t-top>
      <view class="top-box">
        <view class="base-search">
          <wd-search hide-cancel @search="searchConfirm"></wd-search>
        </view>
        <image class="add" :src="`${iconUrl}add.svg`" mode="aspectFill" @click="toAdd" />
      </view>
    </t-top>
    <view class="block" v-for="item in list" :key="item._id" @click="toEdit(item._id)">
      <view class="title">{{ item.name }}</view>
      <view class="text">
        <image class="icon" :src="`${iconUrl}coach.svg`" mode="widthFix"></image>
        <text>{{ showName(item.coach) }}</text>
      </view>
      <view class="text">
        <image class="icon" :src="`${iconUrl}vip.svg`" mode="widthFix"></image>
        <text>{{ showName(item.card_list) }}</text>
      </view>
    </view>
    <t-empty v-if="list.length == 0" data="data" text="暂无活动模板"></t-empty>
  </view>
</template>

<script>
import mixins from "@/pages_club/mixins.js";
export default {
  mixins: [mixins],
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      total: 0,
      list: [],
      pageIndex: 1,
    };
  },
  onShow() {
    uni.$off("update-activity");
  },
  onReachBottom() {
    if (list.length >= total) return;
    this.pageIndex += 1;
    this.getData();
  },
  onLoad() {
    this.getData();
  },
  methods: {
    toAdd() {
      uni.$once("update-activity", () => {
        this.list = [];
        this.pageIndex = 1;
        this.getData();
      });
      vk.navigateTo("/pages_club/views/activity/add");
    },
    toEdit(id) {
      uni.$once("update-activity", () => {
        this.list = [];
        this.pageIndex = 1;
        this.getData();
      });
      vk.navigateTo("/pages_club/views/activity/add?id=" + id);
    },
    showName(data) {
      let result = data.map((item) => {
        return item.name;
      });
      if (data.length == 0) return "无";
      return result.join(",");
    },
    async getData() {
      let data = await vk.callFunction({
        url: "client/club/activity/kh/list",
        title: "请求中...",
        data: {
          club_id: this.club_id,
        },
      });
      this.total = data.total;
      this.list.push(...data.rows);
    },
  },
};
</script>

<style lang="scss" scoped>
.top-box {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .add {
    width: 92rpx;
    height: 92rpx;
    margin-left: 30rpx;
  }
}

.block {
  padding: 30rpx;
  border-bottom: 2rpx solid #eee;

  .title {
    font-size: 32rpx;
    font-weight: bold;
  }

  .text {
    display: flex;
    align-items: center;
    margin-top: 10rpx;

    text {
      font-size: 28rpx;
      margin-left: 20rpx;
    }
  }

  .icon {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>
