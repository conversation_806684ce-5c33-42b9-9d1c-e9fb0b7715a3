<template>
  <view class="page-container">
    <t-navbar title="约球信息"></t-navbar>
    <view class="content">
      <template v-if="info">
        <view class="form-section">
          <view class="form-group">
            <wd-cell-group custom-class="location-group">
              <wd-cell title="常驻位置" :value="info.address_name || '请选择您的常驻位置'" is-link @click="selectPosition"> </wd-cell>
            </wd-cell-group>
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">约球状态</view>
          <view class="form-group">
            <view class="checkbox-section">
              <wd-checkbox-group v-model="info.tags" class="contact-group" inline>
                <wd-checkbox v-for="item in tagOptions" :key="item" :model-value="item" shape="square" checked-color="#0171BC">
                  {{ item }}
                </wd-checkbox>
              </wd-checkbox-group>
            </view>
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">联系方式</view>
          <view class="form-group">
            <view class="checkbox-section">
              <wd-checkbox-group v-model="info.contact" class="contact-group" inline>
                <wd-checkbox v-for="item in contactOptions" :key="item.value" :model-value="item.value" shape="square" checked-color="#0171BC">
                  {{ item.label }}
                </wd-checkbox>
              </wd-checkbox-group>
            </view>
          </view>
        </view>

        <view class="form-section">
          <view class="section-title">隐私设置</view>
          <view class="form-group">
            <wd-cell-group custom-class="privacy-group">
              <wd-cell title="公开展示" title-width="60%" label="是否在找球友页面公开展示您的信息">
                <template #right-icon>
                  <wd-switch v-model="info.open" size="40rpx" custom-style="--wd-switch-on-color: #0171BC"></wd-switch>
                </template>
              </wd-cell>
            </wd-cell-group>
            <wd-cell-group custom-class="privacy-group">
              <wd-cell title="申请查看" title-width="60%" label="是否需要同意才能查看您的联系方式">
                <template #right-icon>
                  <wd-switch v-model="info.apply" size="40rpx" custom-style="--wd-switch-on-color: #0171BC"></wd-switch>
                </template>
              </wd-cell>
            </wd-cell-group>
          </view>
        </view>

        <view class="button-section">
          <wd-button type="primary" @click="submitInfo" custom-style="background: #0171BC; width: 100%;">保存设置</wd-button>
        </view>
      </template>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      info: null,
      locationInfo: null,
      tagOptions: ["可约球", "可教学", "有偿陪练", "免费陪练"],
      contactOptions: [
        {
          label: "微信",
          value: "wechat",
        },
        {
          label: "手机号",
          value: "mobile",
        },
      ],
    };
  },
  watch: {
    "$store.state.$user.userInfo": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal) {
        this.userInfo = newVal;
      },
    },
  },
  onLoad() {
    this.getInfo();
    uni.getLocation({
      success: (res) => {
        this.locationInfo = res;
      },
    });
  },
  methods: {
    // 切换标签选择
    toggleTag(tag) {
      if (!this.info.tags) {
        this.info.tags = [];
      }
      const index = this.info.tags.indexOf(tag);
      if (index > -1) {
        this.info.tags.splice(index, 1);
      } else {
        this.info.tags.push(tag);
      }
    },
    async submitInfo() {
      if (!this.info.location) {
        vk.toast("请选择您的常驻位置");
        return;
      }
      if (this.info.contact.length == 0) {
        vk.toast("请选择您的联系方式");
        return;
      }
      if (this.info.contact.includes("wechat")) {
        if (!this.userInfo.wechat) {
          vk.confirm("微信号未填写，是否前往填写", "提示", "确定", "取消", (res) => {
            if (res.confirm) {
              vk.navigateTo("/pages/user/edit/base");
            }
          });
          return;
        }
      }
      if (this.info.open) {
        vk.confirm("确认要公开联系信息找球友吗？", "提示", "确定", "取消", (res) => {
          if (res.confirm) {
            this.updateInfo();
          }
        });
      } else {
        this.updateInfo();
      }
    },
    async updateInfo() {
      try {
        if (this.info._id) {
          await vk.callFunction({
            url: "client/user/status/kh/update",
            title: "请求中...",
            data: this.info,
          });
        } else {
          await vk.callFunction({
            url: "client/user/status/kh/add",
            title: "请求中...",
            data: this.info,
          });
        }
        vk.toast("保存成功", "none", true, () => {
          uni.navigateBack();
        });
      } catch (err) {
        console.log(err);
      }
    },
    async getInfo() {
      let base = {
        location: null,
        address_name: null,
        accept_distance: 50,
        tags: [],
        contact: [],
        open: false,
        apply: true,
      };
      try {
        let { info } = await vk.callFunction({
          url: "client/user/status/kh/get",
          title: "请求中...",
        });
        if (info) {
          this.info = info;
        } else {
          this.info = base;
        }
      } catch (err) {
        this.info = base;
      }
      console.log(this.info);
    },
    selectPosition() {
      let obj = {};
      if (this.locationInfo)
        obj = {
          latitude: this.locationInfo.latitude,
          longitude: this.locationInfo.longitude,
        };
      uni.chooseLocation({
        ...obj,
        success: (res) => {
          let info = {
            location: {
              latitude: res.latitude,
              longitude: res.longitude,
            },
            address: res.address + res.name,
            name: res.name,
          };
          this.info.location = info.location;
          this.info.address_name = info.name;
        },
        fail: (res) => {
          console.log(res);
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 30rpx;

  .form-section {
    margin-bottom: 30rpx;

    .section-title {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 20rpx;
      padding-left: 10rpx;
    }

    .form-group {
      background-color: #fff;
      border-radius: 24rpx;
      overflow: hidden;

      :deep(.privacy-group) {
        border-radius: 24rpx;
        overflow: hidden;
      }

      :deep(.location-group) {
        border-radius: 24rpx;
        overflow: hidden;
      }

      .checkbox-section {
        padding: 30rpx;

        .checkbox-label {
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          margin-bottom: 20rpx;
        }

        .tag-list {
          display: flex;
          flex-wrap: wrap;
          gap: 20rpx;

          .tag-item {
            background-color: #f5f5f5;
            color: #666;
            border: 2rpx solid transparent;
            cursor: pointer;
            transition: all 0.3s;

            &.tag-active {
              background-color: #0171bc;
              color: #fff;
              border-color: #0171bc;
            }
          }
        }

        .contact-group {
          display: flex;
          flex-direction: column;
          gap: 20rpx;
        }
      }
    }
  }

  .button-section {
    margin-top: 60rpx;
    padding-bottom: 80rpx;
  }
}
</style>
