const vkPay = require("vk-uni-pay");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/card/kh/addUserCard 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { user_id, card_id, club_id, out_trade_no, token } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (token !== 'RvQp4mFvnYhGkaTR7b86WESoOvoSJrV1') return { code: -1, msg: "未知错误" }
		// // 查询支付状态
		let pay_res = await vk.baseDao.findByWhereJson({
			dbName: "vk-pay-orders",
			getMain: true,
			getOne: true,
			whereJson: {
				out_trade_no
			},
		});
		let order_info = await vk.baseDao.findByWhereJson({
			dbName: "order-pay-data",
			getMain: true,
			getOne: true,
			whereJson: {
				out_trade_no
			},
		});
		if (!order_info) return { code: -1, msg: "平台订单不存在" }
		if (!pay_res) return { code: -1, msg: "微信订单不存在" }
		if (!pay_res.user_order_success && !pay_res.pay_date) {
			res.code = -1
			res.msg = "该订单已处理"
			return res
		}
		// 获取卡片信息
		let info = await vk.baseDao.findById({
			dbName: "club-card-data",
			id: card_id,
		});
		console.log("card info", info);

		// 查找验证用户是否已经拥有此卡片
		let has = await vk.baseDao.findByWhereJson({
			dbName: "club-user-card",
			whereJson: {
				card_id,
			},
		});

		let value = has ? has.value + info.value : info.value
		let start = has ? has.start : new Date().valueOf()
		let end = vk.pubfn.getOffsetTime(start, {
			day: Number(value),
			mode: "after",
		});
		// 可重复购买情况下处理值
		if (info.overlay) {
			// 更改和新增订单信息
			order_info.status = 1
			order_info.start = has.start
			order_info.value = has.value
			order_info.after_value = value
			order_info.end = has.end
			order_info.after_end = end
		} else {
			// 更改和新增订单信息
			order_info.status = 1
			order_info.start = start
			order_info.value = value
			order_info.end = end
		}
		await vk.baseDao.updateById({
			dbName: "order-pay-data",
			id: order_info._id,
			dataJson: order_info,
			getUpdateData: false
		});


		if (info.overlay) {
			// 更新用户会员卡数据
			await vk.baseDao.update({
				dbName: "club-user-card",
				whereJson: {
					_id: has._id
				},
				dataJson: {
					value: Number(value),
					start,
					end,
				}
			});
			res.msg = "续卡成功!"
		} else {
			// 添加会员卡数据
			await vk.baseDao.add({
				dbName: "club-user-card",
				dataJson: {
					card_id,
					user_id,
					club_id,
					value,
					start,
					end,
				}
			});
			res.msg = "购买成功"
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}