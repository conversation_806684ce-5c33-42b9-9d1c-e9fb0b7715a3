'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/status/kh/agreeContact 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, type, user_id, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		if (!uid) return { code: -1, msg: "请先登录" }

		if (!user_id || !type || !id) return { code: -1, msg: "参数错误" }

		// 发送申请
		let contactText = '联系方式'
		let contactValue = ''
		switch (type) {
			case 'phone':
				contactText = '手机号'
				contactValue = userInfo.mobile
				break;
			case 'wechat':
				contactText = '微信号'
				contactValue = userInfo.wechat
				break;
			default:
				contactText = '联系方式'
				break;
		}

		// 联系方式白名单
		await vk.baseDao.add({
			dbName: "contact-white-list",
			dataJson: {
				user_id,
				target_user_id: uid,
				type: type
			}
		});

		// 向申请用户发送同意消息
		await vk.baseDao.add({
			dbName: "user-news-data",
			dataJson: {
				"accept_user": user_id,
				"title": "联系方式申请成功",
				"text": `用户已同意给予他的联系方式 \n${contactText}:${contactValue}`,
				"type": "contact",
				"read_state": false,
				"user_id": uid,
				"copy_text": contactValue,
				"contact_status": "agree",
			}
		});

		// 删除现有的申请消息
		await vk.baseDao.deleteById({
			dbName: "user-news-data",
			id
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}