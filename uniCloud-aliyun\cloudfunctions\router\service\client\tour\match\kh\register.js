'use strict';
/**
 * 报名比赛
 * @url client/tour/match/kh/register 前端调用的url参数地址
 * @description 报名比赛
 * @param {String} match_id 比赛ID
 * @param {String} partner_id 搭档ID（双打时必填）
 * @param {String} remarks 备注
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { match_id, partner_id, remarks } = data;
    if (!match_id) return { code: -1, msg: '比赛ID不能为空' };

    if (!uid) return { code: -1, msg: '请先登录' };

    // 检查比赛是否存在且可报名
    const match = await vk.baseDao.findById({
      dbName: 'tour-matches',
      id: match_id
    });

    if (!match) return { code: -1, msg: '比赛不存在' };
    if (match.status !== 'registering') return { code: -1, msg: '比赛不在报名阶段' };
    if (match.current_participants >= match.max_participants) {
      return { code: -1, msg: '报名人数已满' };
    }

    // 检查是否已报名
    const existingRegistration = await vk.baseDao.count({
      dbName: 'tour-registration',
      whereObj: {
        match_id,
        user_id: uid,
        status: ['in', ['pending', 'approved']]
      }
    });

    if (existingRegistration > 0) return { code: -1, msg: '您已报名该比赛' };

    // 创建报名记录
    const registration = await vk.baseDao.add({
      dbName: 'tour-registration',
      dataObj: {
        match_id,
        user_id: uid,
        partner_id,
        remarks,
        status: 'pending',
        payment_status: 'unpaid'
      }
    });

    // 更新比赛当前参与人数
    await vk.baseDao.updateById({
      dbName: 'tour-matches',
      id: match_id,
      dataObj: {
        current_participants: vk.db.command.inc(1)
      }
    });

    res = registration;
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 