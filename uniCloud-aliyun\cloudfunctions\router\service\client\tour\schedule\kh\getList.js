'use strict';
/**
 * 获取赛程列表
 * @url client/tour/kh/schedule/getList 前端调用的url参数地址
 * @description 获取赛程列表
 * @param {String} match_id 比赛ID
 * @param {String} date 日期
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { match_id, date } = data;
    if (!match_id) return { code: -1, msg: '比赛ID不能为空' };

    const dbName = 'tour-schedule';
    const whereObj = {
      match_id
    };

    if (date) {
      whereObj.date = date;
    }

    const result = await vk.baseDao.select({
      dbName,
      whereObj,
      sortArr: [
        { "name": "date", "type": "asc" },
        { "name": "start_time", "type": "asc" }
      ]
    });

    res.data = result.rows;
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 