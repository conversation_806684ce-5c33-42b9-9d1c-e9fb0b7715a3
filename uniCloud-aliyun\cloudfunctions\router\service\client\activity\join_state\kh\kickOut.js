const vkPay = require("vk-uni-pay");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/join_state/kh/kickOut 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let info = await vk.baseDao.findById({
			dbName: "activity-join-data",
			id,
		});
		if (!info) return { code: -1, msg: "用户不在活动中" }
		let activity = await vk.baseDao.findById({
			dbName: "activity-data",
			id: info.activity_id
		});
		if (Date.now() > activity.end) return { code: -1, msg: "活动已结束，无法操作" }


		// 添加消息与通知
		let title = ''
		let text = ''
		let payText = ''
		switch (info.state) {
			case 'applying':
				title = '申请已被拒绝'
				payText = '活动的申请已被拒绝'
				text = `您${info.type==='help'?'代接龙的用户“'+info.nickname+'“':''}加入活动<${activity.name}>的申请已被拒绝。`
				break;
			default:
				title = "踢出提醒"
				payText = '加入的活动已被踢出'
				text = `您${info.type==='help'?'代接龙的用户“'+info.nickname+'“':''}已被踢出活动<${activity.name}>。`
				break;
		}

		// 开始处理踢出活动事务
		const transaction = await vk.baseDao.startTransaction();
		try {
			// 添加记录
			await vk.baseDao.add({
				db: transaction,
				dbName: "activity-record-data",
				dataJson: {
					user_id: info.user_id,
					activity_id: info.activity_id,
					type: "kick"
				}
			});

			// 执行退出
			await vk.baseDao.deleteById({
				db: transaction,
				dbName: "activity-join-data",
				id
			});

			// 发送消息
			if (activity.creator_id !== info.user_id) {
				db: transaction,
				await vk.baseDao.add({
					dbName: "user-news-data",
					dataJson: {
						accept_user: info.user_id,
						title,
						text,
						read_state: false,
						type: "info",
						link: `/pages/activity/detail?id=${activity._id}`,
					}
				})
			}
			res.msg = "操作成功!"

			// 提交事务
			await transaction.commit();
		} catch (err) {
			// 事务回滚
			return await vk.baseDao.rollbackTransaction({
				db: transaction,
				err
			});
		}


		// 查看是否是线上付款，如果是，则发起退款
		let refundRes;
		if (activity.cost_type !== 'none' && activity.cost_plan === 1) {
			let order_info = await vk.baseDao.findByWhereJson({
				dbName: "vk-pay-orders",
				whereJson: {
					out_trade_no: info.out_trade_no
				},
			});
			// 计算订单金额情况
			let balance = order_info.total_fee
			if (order_info.refund_fee) balance -= order_info.refund_fee
			// 计算应退金额
			let expense;
			if (activity.cost_type === 'average') {
				expense = activity.cost / activity.min_num
			} else if (activity.cost_type === 'fixed') {
				expense = activity.cost
			}
			if (balance - expense * 100 < 0) {
				refundRes = { code: -1, msg: "执行退款时出现异常，请联系客服处理" }
				return
			}
			refundRes = await vkPay.refund({
				out_trade_no: order_info.out_trade_no,
				refund_desc: payText,
				refund_fee: expense * 100
			});
		}
		if (refundRes.code !== 0) return refundRes

		// 发送订阅消息通知
		let user = await vk.baseDao.findById({
			dbName: "uni-id-users",
			id: info.user_id,
		});
		let start = vk.pubfn.timeFormat(activity.start, "yyyy-MM-dd hh:mm:ss");
		await vk.openapi.weixin.subscribeMessage.send({
			touser: user.wx_openid['mp-weixin'],
			template_id: "qULr23FpxNQTuWrghCj6gA_855wO6isiP-_IM1sCO1A",
			page: "pages/activity/detail?id=" + activity._id,
			data: {
				phrase1: {
					value: title
				},
				thing2: {
					value: activity.name
				},
				time3: {
					value: start
				},
				thing5: {
					value: "请在小程序查看活动详情"
				}
			},
			miniprogram_state: "formal",
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}