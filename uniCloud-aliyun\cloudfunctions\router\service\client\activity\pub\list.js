'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/pub/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, pageIndex, club_id, level, date, location, sort, name, creator_id, open } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// 是否筛选已加入的
		let queryDate;
		if (location) {
			location = _.geoNear({
				geometry: new db.Geo.Point(location.longitude, location.latitude),
				maxDistance: 50000,
				minDistance: 0,
				distanceMultiplier: 0.001,
				distanceField: "distance",
			})
		}

		let whereJson = { club_id, level }

		if (date) {
			let end = vk.pubfn.getOffsetTime(new Date(date), { day: 1, mode: "after", });
			whereJson.start = _.or([_.gte(date).lt(end), _.lte(date)])
			whereJson.end = _.gte(date)
		} else {
			let { todayStart } = vk.pubfn.getCommonTime(new Date());
			whereJson.end = _.gte(todayStart)
		}
		if (open) whereJson.open = open
		if (location) whereJson.location = location
		if (creator_id) whereJson.creator_id = creator_id
		if (name) whereJson.name = new RegExp(name)
		let sortArr = []
		if (sort) sortArr = sort
		// 开始请求数据
		res = await vk.baseDao.selects({
			dbName: "activity-data",
			getCount: false,
			pageIndex,
			pageSize: 20,
			whereJson,
			sortArr,
			foreignDB: [{
					dbName: "uni-id-users",
					localKey: "creator_id",
					foreignKey: "_id",
					as: "creator_info",
					limit: 1
				}, {
					dbName: "club-data",
					localKey: "club_id",
					foreignKey: "_id",
					as: "club_info",
					limit: 1
				},
				{
					dbName: "club-site-data",
					localKey: "site",
					foreignKey: "_id",
					as: "club_site",
					limit: 1
				},
				{
					dbName: "activity-join-data",
					localKey: "_id",
					foreignKey: "activity_id",
					as: "join_list",
					limit: 1000,
					fieldJson: { user_id: true, user_info: true, },
					whereJson: { state: 'joined' },
					foreignDB: [{
						dbName: "uni-id-users",
						localKey: "user_id",
						foreignKey: "_id",
						as: "user_info",
						fieldJson: { avatar: true, nickname: true, level: true },
						limit: 1,
					}]
				}, {
					dbName: "activity-join-data",
					localKey: "_id",
					foreignKey: "activity_id",
					as: "my_join",
					limit: 1,
					fieldJson: { state: true, },
					whereJson: { user_id: uid, type: 'myself' },
				}
			],
		});
		// 排序数据并赋予状态

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}