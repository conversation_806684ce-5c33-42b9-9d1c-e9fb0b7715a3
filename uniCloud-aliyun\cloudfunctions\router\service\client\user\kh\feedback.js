'use strict';
module.exports = {
	/**
	 * 反馈问题接口
	 * @url client/user/kh/feedback 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, text, images } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (!text) {
			res.code = -1
			res.msg = "请输入反馈内容问题"
			return res
		}
		await vk.baseDao.add({
			dbName: "user-feedback-data",
			dataJson: {
				text,
				images,
				user_id: uid
			}
		});
		res.msg = '感谢您的反馈'
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}