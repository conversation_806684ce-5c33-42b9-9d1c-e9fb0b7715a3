<!-- 首页 -->
<template>
  <view class="page">
    <t-navbar title="比赛列表" />
    <view class="content">
      <t-tabs v-model="currentStatus" :list="statusList" @change="onTabChange" />
      <view class="match-list">
        <t-empty v-if="matchList.length === 0" text="暂无比赛" />
        <view v-else class="list-content">
          <view v-for="item in matchList" :key="item._id" class="match-item" @click="goDetail(item._id)">
            <view class="match-card">
              <view class="match-info">
                <view class="match-title">{{ item.title }}</view>
                <view class="match-meta">
                  <t-tag :type="getStatusType(item.status)" :text="getStatusText(item.status)" />
                  <text class="date">{{ item.start_date }}</text>
                </view>
                <view class="match-desc">
                  <text>{{ item.venue }}</text>
                  <text>{{ item.level }}级别</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <t-loadmore v-if="matchList.length > 0" :status="loadStatus" @clickLoadMore="loadMore" />
    </view>
    <!-- 发起赛事按钮 -->
    <t-bottom>
      <!-- <t-button type="primary" @click="goCreate">发起赛事</t-button> -->
      <wd-button>主要按钮</wd-button>
    </t-bottom>
  </view>
</template>

<script>
export default {
  data() {
    return {
      currentStatus: 0,
      statusList: ["报名中", "进行中", "已结束"],
      matchList: [],
      page: 1,
      pageSize: 10,
      loadStatus: "loading",
      isLoading: false,
    };
  },
  onLoad() {
    this.getMatchList();
  },
  methods: {
    // 获取赛事列表数据
    async getMatchList(isLoadMore = false) {
      if (this.isLoading) return;
      this.isLoading = true;

      try {
        // TODO: 删除此行，使用下方的实际请求
        const res = await this.getMockData();

        // 实际请求，TODO: 取消注释使用
        // const res = await vk.callFunction({
        //   url: "client/tour/match/kh/getList",
        //   data: {
        //     status: this.getStatusValue(),
        //     page: this.page,
        //     pageSize: this.pageSize,
        //   },
        // });

        if (res.code === 0) {
          if (isLoadMore) {
            this.matchList = [...this.matchList, ...res.rows];
          } else {
            this.matchList = res.rows;
          }
          this.loadStatus = res.rows.length < this.pageSize ? "noMore" : "loadmore";
        }
      } catch (e) {
        console.error(e);
      }
      this.isLoading = false;
    },

    // 获取当前状态值
    getStatusValue() {
      const statusMap = {
        0: "registering",
        1: "ongoing",
        2: "finished",
      };
      return statusMap[this.currentStatus];
    },

    // 模拟数据 - TODO: 开发完成后删除此方法
    async getMockData() {
      const mockData = [
        {
          _id: "tour_match_001",
          title: "2024广西网球公开赛",
          status: "registering",
          start_date: "2024-08-02",
          venue: "南宁市体育中心网球场",
          level: "A",
          registration: {
            fee: 299,
            deadline: "2024-07-30",
            current: 45,
            max: 128,
          },
          prize_pool: 50000,
          format: "单打",
          organizer: "广西网球协会",
        },
        {
          _id: "tour_match_002",
          title: "2024南宁城市网球联赛",
          status: "registering",
          start_date: "2024-09-15",
          venue: "云景网球场",
          level: "B",
          registration: {
            fee: 199,
            deadline: "2024-09-01",
            current: 16,
            max: 64,
          },
          prize_pool: 20000,
          format: "单打+双打",
          organizer: "南宁市网球协会",
        },
        {
          _id: "tour_match_003",
          title: "第三届广西业余网球大师赛",
          status: "ongoing",
          start_date: "2024-07-20",
          venue: "南宁国际网球中心",
          level: "A",
          registration: {
            fee: 399,
            deadline: "2024-07-10",
            current: 96,
            max: 96,
          },
          prize_pool: 100000,
          format: "单打",
          organizer: "广西网球协会",
        },
        {
          _id: "tour_match_004",
          title: "2024广西高校网球联赛",
          status: "finished",
          start_date: "2024-06-01",
          venue: "广西大学网球场",
          level: "C",
          registration: {
            fee: 99,
            deadline: "2024-05-20",
            current: 32,
            max: 32,
          },
          prize_pool: 10000,
          format: "团体赛",
          organizer: "广西教育厅",
        },
      ];

      // 模拟数据筛选
      let filteredData = mockData;
      const currentStatusValue = this.getStatusValue();
      if (currentStatusValue) {
        filteredData = mockData.filter((item) => item.status === currentStatusValue);
      }

      // 分页处理
      const start = (this.page - 1) * this.pageSize;
      const end = start + this.pageSize;

      return {
        code: 0,
        rows: filteredData.slice(start, end),
        total: filteredData.length,
      };
    },

    onTabChange(index) {
      this.currentStatus = index;
      this.page = 1;
      this.getMatchList();
    },

    loadMore() {
      if (this.loadStatus === "noMore") return;
      this.page++;
      this.getMatchList(true);
    },

    goDetail(id) {
      uni.navigateTo({
        url: `/pages_tour/views/match/detail?id=${id}`,
      });
    },

    getStatusType(status) {
      const types = {
        registering: "primary",
        ongoing: "warning",
        finished: "info",
      };
      return types[status] || "default";
    },

    getStatusText(status) {
      const texts = {
        registering: "报名中",
        ongoing: "进行中",
        finished: "已结束",
      };
      return texts[status] || status;
    },

    goCreate() {
      uni.navigateTo({
        url: "/pages_tour/views/match/create",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content {
  padding: 20rpx;
  padding-bottom: 120rpx;
}

.match-list {
  margin-top: 20rpx;
  .match-item {
    margin-bottom: 20rpx;
    .match-card {
      background-color: #ffffff;
      border-radius: 12rpx;
      padding: 24rpx;
      box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
      .match-info {
        .match-title {
          font-size: 32rpx;
          font-weight: bold;
          margin-bottom: 16rpx;
          color: #333333;
        }
        .match-meta {
          display: flex;
          align-items: center;
          margin-bottom: 12rpx;
          .date {
            margin-left: 16rpx;
            font-size: 24rpx;
            color: #666666;
          }
        }
        .match-desc {
          font-size: 24rpx;
          color: #999999;
          text {
            margin-right: 20rpx;
            &:last-child {
              margin-right: 0;
            }
          }
        }
      }
      &:active {
        transform: scale(0.98);
        transition: transform 0.2s ease;
      }
    }
  }
}
</style>
