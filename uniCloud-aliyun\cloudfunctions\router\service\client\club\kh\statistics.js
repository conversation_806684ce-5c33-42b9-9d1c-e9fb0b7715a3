'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/kh/statistics 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let statistic = {}
		// 成员数
		statistic.user_num = await vk.baseDao.count({
			dbName: "club-user-data",
			whereJson: {
				club_id,
			},
		});
		// 场地数
		statistic.site_num = await vk.baseDao.count({
			dbName: "club-site-data",
			whereJson: {
				club_id,
			},
		});
		// 活动数
		statistic.activity_num = await vk.baseDao.count({
			dbName: "club-activity-data",
			whereJson: {
				club_id,
			},
		});
		// 卡片数
		statistic.card_num = await vk.baseDao.count({
			dbName: "club-card-data",
			whereJson: {
				club_id,
			},
		});
		res.data = statistic

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}