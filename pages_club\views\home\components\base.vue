<template>
	<view class="swiper-page" v-if="info">
		<swiper class="swiper-banner" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="800" circular>
			<swiper-item class="banner-item" v-for="image in info.banner">
				<image :src="image" mode="aspectFill"></image>
			</swiper-item>
		</swiper>
		<view style="padding: 30rpx;">
			<view class="club-info">
				<image class="logo" :src="info.logo" mode="aspectFill"></image>
				<view class="name">{{info.name}}</view>
			</view>
			<view class="desc">俱乐部简介：{{info.desc||'暂无简介'}}</view>
			<t-title :custom-style="{margin:'30rpx 0'}">俱乐部成员</t-title>
			<t-user-group :data="list"></t-user-group>
		</view>
	</view>

</template>

<script>
	export default {
		data() {
			return {
				club_id: null,
				info: null,
				list: [],
			}
		},
		watch: {
			"$store.state.$club.info": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.info = newVal
				}
			}
		},
		async mounted() {
			this.club_id = vk.getVuex('$club.id')
			this.info = await vk.vuex.getters('$club/getInfo');
			this.getUserList()
		},
		methods: {
			async getUserList() {
				let data = await vk.callFunction({
					url: 'client/club/member/kh/list',
					title: '请求中...',
					data: {
						club_id: this.club_id,
						pageIndex: 1,
						type: 1,
					},
				});
				data.rows.forEach(item => {
					if (item.user_info) this.list.push(item.user_info)
				})
			},
		}
	}
</script>

<style scoped lang="scss">
	.swiper-page {
		width: 100%;
		height: 100%;
	}
	.swiper-banner{
		width: 100vw;
		height: 100vw;
	}
	.banner-item {
		width: 100%;
		height: 100%;

		image {
			width: 100%;
			height: 100%;
		}
	}

	.club-info {
		display: flex;
		align-items: center;

		.logo {
			width: 88rpx;
			height: 88rpx;
			border-radius: 12rpx;
		}

		.name {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-left: 30rpx;
		}

	}

	.desc {
		font-size: 28rpx;
		color: #8a8a8a;
		margin-top: 20rpx;
	}
</style>