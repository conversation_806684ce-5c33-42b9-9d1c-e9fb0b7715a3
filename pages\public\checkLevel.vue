<template>
  <view class="page-content">
    <t-navbar title="水平测试"></t-navbar>
    <view class="list">
      <view v-for="(item, index) in list" :key="item._id">
        <view class="card" v-if="item.class_name" :class="[item.class_name]">
          <view class="top">
            <image v-if="index != 0" :src="`${iconUrl}left-line.svg`" mode="heightFix" @click="answerAction('last')"></image>
            <view class="title">{{ item.label }}</view>
          </view>
          <view class="options">
            <view
              class="option"
              :class="{ select: item.select == idx, flash: flashTip }"
              v-for="(items, idx) in item.value"
              :key="items.label"
              @click="selectAnswer(index, idx)"
            >
              <image class="check" :src="`/static/images/theme-check-on.svg`" mode="aspectFill"></image>
              <text class="label">{{ items.label }}</text>
            </view>
          </view>
          <view class="button-box">
            <view class="next-button" v-if="current + 1 < list.length" @click="answerAction('next')">
              <image class="right-icon" :src="`/static/images/arrow-right-white.svg`" mode="heightFix"></image>
            </view>
            <wd-button type="primary" v-else @click="submit">提交</wd-button>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      style: {
        "background-color": "#f8f8f8",
        color: "#000",
      },
      list: [],
      current: 0,
      flashTip: false,
    };
  },
  onLoad() {
    this.getData();
  },
  methods: {
    selectAnswer(index, idx) {
      this.flashTip = false;
      this.list[index].select = idx;
    },
    answerAction(type) {
      // 等待动画播放完毕
      if (this.flashTip) return;
      // 判断选项
      if (type == "next" && this.list[this.current].select == null) {
        this.flashTip = true;
        setTimeout(() => {
          this.flashTip = false;
        }, 1200);
        return;
      }
      if (type == "next") {
        let over = false;
        this.list.forEach((item) => {
          if ((item.select || item.select === 0) && item.value[item.select].action == "over") over = true;
        });
        if (over) {
          this.submit();
        } else {
          this.current += 1;
        }
      } else if (type == "last") {
        this.current -= 1;
      }
      this.list = this.list.map((item, index) => {
        item.class_name = "";
        if (type == "next") {
          if (this.current == index) {
            item.class_name = "enter-right";
          }
          if (this.current - 1 == index) {
            item.class_name = "leave-left";
          }
        } else if (type == "last") {
          if (this.current == index) {
            item.class_name = "enter-left";
          }
          if (this.current + 1 == index) {
            item.class_name = "leave-right";
          }
        }
        return item;
      });
      console.log(this.list);
    },
    async getData() {
      let data = await vk.callFunction({
        url: "client/options/pub/get",
        title: "请求中...",
        data: {
          type: "level_check",
        },
      });
      let options = data.rows.map((item, index) => {
        item.select = null;
        item.class_name = index == 0 ? "enter-right" : "";
        return item;
      });
      this.list = options;
    },
    async submit() {
      try {
        let select = this.list.map((item) => {
          return item.select;
        });
        await vk.callFunction({
          url: "client/user/level/kh/init",
          title: "请求中...",
          data: {
            select,
          },
        });
        vk.toast("水平检测成功!", "none", true, () => {
          vk.navigateBack();
          uni.$emit("level-test-over");
        });
      } catch (err) {
        console.log(err);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/static/scss/animation.scss";

.page-content {
  min-height: 100vh;
  padding: 40rpx;
  background-color: #f8f8f8;
}

.list {
  position: relative;
  width: 100%;
}

.card {
  background-color: #fff;
  width: 100%;
  border-radius: 28rpx;
  padding: 30rpx;
  box-sizing: border-box;
  opacity: 0;

  .top {
    display: flex;
    align-items: center;
    margin-bottom: 30rpx;

    image {
      width: 40rpx;
      height: 40rpx;
      margin-right: 20rpx;
    }

    .title {
      font-size: 32rpx;
      font-weight: bold;
    }
  }

  .options {
    .option {
      background-color: #f8f8f8;
      display: flex;
      align-items: center;
      padding: 30rpx;
      box-sizing: border-box;
      margin-top: 20rpx;
      border-radius: 12rpx;
      transition: background-color 0.3s;

      .check {
        width: 48rpx;
        height: 48rpx;
        flex-shrink: 0;
        margin-right: 20rpx;
        border-radius: 50%;
        transition: opacity 0.3s;
        filter: grayscale(1);
        opacity: 0.4;
      }

      .text {
        font-size: 28rpx;
      }
    }

    .select {
      background-color: rgba(1, 113, 188, 0.2);

      .check {
        filter: grayscale(0);
        opacity: 1;
      }
    }
  }

  .button-box {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    margin-top: 30rpx;
    .next-button {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 88rpx;
      height: 88rpx;
      border-radius: 24rpx;
      background-color: $primary-color;
    }
    .right-icon {
      height: 40rpx;
      width: 40rpx;
    }
  }
}

.flash {
  animation: flash 0.2s 4 alternate;
}

@keyframes flash {
  0% {
    background-color: #f8f8f8;
  }

  100% {
    background-color: rgba(248, 202, 108, 0.2);
  }
}

/* 动画 */
.enter-left {
  -webkit-animation: fade-in-left 0.4s cubic-bezier(0.39, 0.575, 0.565, 1) both 0.2s;
  animation: fade-in-left 0.4s cubic-bezier(0.39, 0.575, 0.565, 1) both 0.2s;
  display: block;
}

.enter-right {
  -webkit-animation: fade-in-right 0.4s cubic-bezier(0.39, 0.575, 0.565, 1) both 0.2s;
  animation: fade-in-right 0.4s cubic-bezier(0.39, 0.575, 0.565, 1) both 0.2s;
  display: block;
}

.leave-left {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  -webkit-animation: fade-out-left 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  animation: fade-out-left 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  display: none;
}

.leave-right {
  position: absolute;
  left: 0;
  top: 0;
  z-index: -1;
  -webkit-animation: fade-out-right 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  animation: fade-out-right 0.6s cubic-bezier(0.39, 0.575, 0.565, 1) both;
  display: none;
}
</style>
