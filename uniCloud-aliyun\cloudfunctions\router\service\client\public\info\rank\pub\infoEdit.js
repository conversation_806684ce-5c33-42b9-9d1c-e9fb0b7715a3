'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/rank/pub/infoEdit 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.selects({
			dbName: "info-edit-history", // 表名
			pageIndex: 1,
			pageSize: 30,
			whereJson: { // 条件
			},
			groupJson: {
				_id: '$user_id',
				count: { '$sum': 1 }
			},
			sortArr: [{ name: "count", type: "desc" }], // 对分组后的结果进行排序
			foreignDB: [{
				dbName: "uni-id-users",
				localKey: "_id",
				foreignKey: "_id",
				as: "user_info",
				fieldJson: { avatar: true, nickname: true },
				limit: 1
			}]
		});


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}