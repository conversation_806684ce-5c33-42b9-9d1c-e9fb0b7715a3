'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/member/kh/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 * @param {String} type  0:全部 1:非自增成员
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id, pageIndex, state, type = 0 } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		const outField = ['nickname', 'avatar', 'mobile', 'wechat', 'level']
		const fieldJson = Object.fromEntries(outField.map(field => [field, true]))
		const whereJson = { club_id }
		if (!state && type == 1) whereJson.state = _.neq('custom')
		console.log(whereJson,'查询条件');
		res = await vk.baseDao.selects({
			dbName: "club-user-data",
			getCount: false,
			pageIndex,
			pageSize: 20,
			whereJson,
			foreignDB: [{
				dbName: "uni-id-users",
				localKey: "user_id",
				foreignKey: "_id",
				as: "user_info",
				limit: 1
			}],
			sortArr: [{ name: "_add_time", type: "desc" }],
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}