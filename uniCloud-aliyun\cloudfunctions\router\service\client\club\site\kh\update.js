const formRules = require("../../util/formRules.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/site/kh/update 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, _id, images, name, desc, location, address, book = false } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (vk.pubfn.isNullOne(_id)) {
			return {
				code: -1,
				msg: '_id不能为空'
			};
		}
		let formRulesRes = await formRules.site_update(event);
		if (formRulesRes.code !== 0) {
			return formRulesRes;
		}
		// 执行数据库API请求
		res.data = await vk.baseDao.updateById({
			dbName: "club-site-data",
			id: _id,
			getUpdateData: true,
			dataJson: {
				images,
				name,
				desc,
				location: new db.Geo.Point(location.longitude, location.latitude),
				address,
				book,
				last_update: uid,
				last_update_time: new Date().valueOf()
			}
		});
		res.msg = "编辑成功!";
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
};