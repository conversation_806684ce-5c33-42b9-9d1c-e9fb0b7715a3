[{"_id": "688ad69af2949c1a83cf370f", "request_id": "ac1cc31d1753929370026-4929", "err": {"message": "查询参数对象值不能均为undefined", "stack": "Error: 查询参数对象值不能均为undefined\n    at where (/tmp/function/@dcloudio/serverless/lib/aliyun/uni-cloud.js:1:42079)\n    at baseDao.count (/tmp/function/@common_modules/vk-unicloud/index.js:1:29438)\n    at main (/tmp/function/service/client/public/info/rank/pub/oneself.js:23:30)\n    at serviceRun (/tmp/function/@common_modules/vk-unicloud/index.js:1:13144)\n    at main (/tmp/function/@common_modules/vk-unicloud/index.js:1:22102)\n    at exports.main (/tmp/function/__index.js:6:9)\n    at $e (/tmp/function/index.js:1:85303)\n    at Ve (/tmp/function/index.js:1:86420)\n    at runUserFunction (/code/index.js:93:265116)\n    at (/code/index.js:93:259283)"}, "md5": "35240dcca4f6c9ae9948465140cd503e", "url": "client/public/info/rank/pub/oneself", "data": {}, "status": 0, "_add_time": 1753929370091, "_add_time_str": "2025-07-31 10:36:10"}]