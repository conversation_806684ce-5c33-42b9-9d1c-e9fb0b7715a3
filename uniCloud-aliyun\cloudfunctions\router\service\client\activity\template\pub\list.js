'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/template 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, pageIndex } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let whereJson = _.or({ type: "user", creator_id: uid }, { type: "admin" })
		res = await vk.baseDao.selects({
			dbName: "activity-template-data",
			getCount: false,
			pageIndex,
			pageSize: 999,
			whereJson,
			// 主表排序规则
			sortArr: [{ name: "type", type: "desc" }, { name: "_add_time", type: "asc" }],
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}