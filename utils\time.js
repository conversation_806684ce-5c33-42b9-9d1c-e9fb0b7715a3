import dayjs from "dayjs";
import cnWork from 'chinese-workday'

/* 
 * 时间线分割方法
 * start和end要求传入"HH:mm"格式
 * isPop 是否去除最后一项
 * type 可选'half'和'hour'来分割半小时还是一小时
 */
export const splitTime = (start, end, isPop, type = 'half') => {
	const date = dayjs().format('YYYY-MM-DD')
	// 确保传入的时间格式正确
	if (!dayjs(`${date} ${start}`).isValid() || !dayjs(`${date} ${end}`).isValid()) {
		throw new Error('Invalid time format. Please use HH:mm format.');
	}

	// 解析开始和结束时间
	let zero = dayjs(`${date} 00:00`).valueOf()
	let startTime = dayjs(`${date} ${start}`)
	const endTime = dayjs(`${date} ${end}`)

	// 初始化时间数组
	const timeArray = [];

	// 按照半小时或一小时分割时间
	let currentTime = startTime;
	while (startTime <= endTime) {
		timeArray.push({
			timestamp: startTime.valueOf() - zero,
			time: startTime.format('HH:mm')
		})
		// 根据类型增加时间
		if (type === 'half') {
			startTime = startTime.add(30, 'minute')
		} else if (type === 'hour') {
			startTime = startTime.add(1, 'hour')
		} else {
			throw new Error('Invalid type. Type should be "half" or "hour".');
		}
	}
	if (isPop) timeArray.pop();
	return timeArray;
}

export const dayInfo = (val) => {
	const datetime = val ? dayjs().valueOf() : val
	const date = dayjs(datetime).date()
	const day = dayjs(datetime).day()
	const week_cn = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]

	const num = dayjs(datetime).date(date).format('DD')
	const en = dayjs(datetime).day(day).format('ddd')
	const dayIndex = dayjs(datetime).day(day).format('d')
	const theDate = dayjs(datetime).date(date).format('YYYY-MM-DD')
	return {
		value: dayjs(datetime).date(date).valueOf(),
		date: theDate,
		num,
		en: en.toUpperCase(),
		cn: week_cn[dayIndex],
		day: Number(dayIndex),
		holiday: cnWork.isHoliday(theDate),
		festival: cnWork.getFestival(theDate)
	}
}

export const planTimeLine = (list, start, end) => {
	start = start ? dayjs(start).valueOf() : dayjs().startOf('day').valueOf();
	end = end ? dayjs(end).valueOf() : dayjs().add(1, 'day').startOf('day').valueOf();

	list.sort((a, b) => a.start - b.start);

	const gaps = [];

	// 遍历排序后的列表，计算空余时间
	for (let i = 0; i < list.length - 1; i++) {
		const currentEnd = list[i].end;
		const nextStart = list[i + 1].start;

		if (nextStart - currentEnd > 0) {
			gaps.push({ start: currentEnd, end: nextStart, type: 'none' });
		}
	}

	// 检查是否需要添加开始和结束的空余时间
	let result = [...list, ...gaps].sort((a, b) => a.start - b.start);;
	if (result[0].start - start > 0) {
		result = [{ start, end: result[0].start, type: 'none' }, ...result];
	}
	if (end - result[result.length - 1].end > 0) {
		result = [...result, { start: result[result.length - 1].end, end, type: 'none' }];
	}

	// 格式化时间
	const formatTime = (timestamp) => dayjs(timestamp).format('HH:mm');
	result = result.map(item => ({
		...item,
		start: formatTime(item.start),
		end: formatTime(item.end)
	}));

	return result
}