<!-- 比赛详情 -->
<template>
  <view class="page">
    <t-navbar title="比赛详情" />
    <view class="content">
      <view class="card" v-if="matchInfo">
        <view class="match-header">
          <view class="title">{{matchInfo.title}}</view>
          <t-tag :type="getStatusType(matchInfo.status)">{{getStatusText(matchInfo.status)}}</t-tag>
        </view>
        
        <view class="info-list">
          <view class="info-item">
            <text class="label">比赛时间</text>
            <text class="value">{{matchInfo.start_date}}</text>
          </view>
          <view class="info-item">
            <text class="label">比赛地点</text>
            <text class="value">{{matchInfo.venue}}</text>
          </view>
          <view class="info-item">
            <text class="label">比赛级别</text>
            <text class="value">{{matchInfo.level}}级别</text>
          </view>
          <view class="info-item">
            <text class="label">报名费用</text>
            <text class="value">¥{{matchInfo.entry_fee}}</text>
          </view>
          <view class="info-item">
            <text class="label">报名人数</text>
            <text class="value">{{matchInfo.current_participants}}/{{matchInfo.max_participants}}</text>
          </view>
        </view>

        <view class="match-desc">
          <text class="section-title">比赛说明</text>
          <rich-text :nodes="matchInfo.description"></rich-text>
        </view>

        <!-- 奖金设置 -->
        <view class="prize-section" v-if="matchInfo.prize_pool">
          <text class="section-title">奖金设置</text>
          <view class="prize-total">总奖金：¥{{matchInfo.prize_pool.total}}</view>
          <view class="prize-list">
            <view class="prize-item" v-for="(prize, index) in matchInfo.prize_pool.distribution" :key="index">
              <text class="rank">{{prize.rank}}</text>
              <text class="amount">¥{{prize.amount}}</text>
            </view>
          </view>
        </view>

        <!-- 比赛规则 -->
        <view class="rules-section">
          <text class="section-title">比赛规则</text>
          <view class="rules-list">
            <view class="rule-item" v-for="(rule, index) in matchInfo.rules" :key="index">
              <text class="rule-title">{{rule.title}}</text>
              <text class="rule-content">{{rule.content}}</text>
            </view>
          </view>
        </view>
      </view>

      <view class="action-bar">
        <t-button 
          v-if="matchInfo && matchInfo.status === 'registering'"
          type="primary" 
          block 
          :loading="isRegistering"
          :disabled="isRegistered || matchInfo.current_participants >= matchInfo.max_participants"
          @click="handleRegister"
        >
          {{getRegisterButtonText}}
        </t-button>
      </view>
    </view>

    <!-- 报名确认弹窗 -->
    <t-popup v-model="showRegisterPopup" position="bottom">
      <view class="register-popup">
        <view class="popup-header">
          <text class="title">报名确认</text>
          <text class="close" @click="showRegisterPopup = false">×</text>
        </view>
        <view class="popup-content">
          <view class="input-item" v-if="matchInfo && matchInfo.type === 'doubles'">
            <text class="label">搭档选择</text>
            <t-input v-model="registerForm.partner_id" placeholder="请输入搭档ID" />
          </view>
          <view class="input-item">
            <text class="label">备注信息</text>
            <t-input v-model="registerForm.remarks" type="textarea" placeholder="请输入备注信息" />
          </view>
        </view>
        <view class="popup-footer">
          <t-button type="primary" block @click="confirmRegister">确认报名</t-button>
        </view>
      </view>
    </t-popup>
  </view>
</template>

<script>
export default {
  data() {
    return {
      id: '',
      matchInfo: {
        title: '2024年春季网球精英赛',
        status: 'registering',
        start_date: '2024-04-15',
        end_date: '2024-04-20',
        venue: '阳光网球中心',
        level: 'A',
        entry_fee: 299,
        current_participants: 28,
        max_participants: 32,
        type: 'singles',
        description: `
          <div style="color: #333; line-height: 1.6;">
            <p>春季网球精英赛是面向业余网球爱好者的高水平赛事，旨在为网球爱好者提供一个展示技术、以球会友的平台。</p>
            <p>比赛采用最新ITF规则，全程裁判监场，让参赛选手享受专业级的比赛体验。</p>
          </div>
        `,
        prize_pool: {
          total: 10000,
          distribution: [
            { rank: '冠军', amount: 5000 },
            { rank: '亚军', amount: 3000 },
            { rank: '季军', amount: 2000 }
          ]
        },
        rules: [
          {
            title: '比赛制度',
            content: '小组循环+淘汰赛制，每场比赛采用3盘2胜制，决胜盘为抢10。'
          },
          {
            title: '分组方式',
            content: '根据选手等级评定，采用蛇形分组，确保每个小组实力均衡。'
          },
          {
            title: '赛制安排',
            content: '首日进行小组赛，次日进行淘汰赛，决赛日进行半决赛和决赛。'
          },
          {
            title: '参赛要求',
            content: '参赛选手需持有效身份证件，并确保身体状况良好，适合参加剧烈运动。'
          }
        ]
      },
      isRegistered: false,
      registration: null,
      isRegistering: false,
      showRegisterPopup: false,
      registerForm: {
        partner_id: '',
        remarks: ''
      }
    }
  },
  computed: {
    getRegisterButtonText() {
      if (this.isRegistered) return '已报名'
      if (this.matchInfo && this.matchInfo.current_participants >= this.matchInfo.max_participants) {
        return '报名已满'
      }
      return '立即报名'
    }
  },
  onLoad(options) {
    if (options.id) {
      this.id = options.id
      // 暂时注释掉实际接口调用，使用示例数据
      // this.getMatchDetail()
      // this.checkRegistration()
    }
  },
  methods: {
    async getMatchDetail() {
      try {
        const res = await vk.callFunction({
          url: 'client/tour/match/kh/detail',
          data: {
            match_id: this.id
          }
        })
        if (res.code === 0) {
          this.matchInfo = res.data
        }
      } catch (e) {
        console.error(e)
      }
    },
    async checkRegistration() {
      try {
        const res = await vk.callFunction({
          url: 'client/tour/match/kh/checkRegistration',
          data: {
            match_id: this.id
          }
        })
        if (res.code === 0) {
          this.isRegistered = res.data.isRegistered
          this.registration = res.data.registration
        }
      } catch (e) {
        console.error(e)
      }
    },
    handleRegister() {
      if (!this.matchInfo) return
      this.showRegisterPopup = true
    },
    async confirmRegister() {
      if (this.isRegistering) return
      this.isRegistering = true
      try {
        const res = await vk.callFunction({
          url: 'client/tour/match/kh/register',
          data: {
            match_id: this.matchInfo._id,
            ...this.registerForm
          }
        })
        if (res.code === 0) {
          uni.showToast({
            title: '报名成功',
            icon: 'success'
          })
          this.showRegisterPopup = false
          this.getMatchDetail()
          this.checkRegistration()
          // 如果需要支付，跳转到支付页面
          if (this.matchInfo.entry_fee > 0) {
            uni.navigateTo({
              url: `/pages_tour/views/match/submit?match_id=${this.matchInfo._id}&registration_id=${res.data._id}`
            })
          }
        }
      } catch (e) {
        uni.showToast({
          title: e.message || '报名失败',
          icon: 'none'
        })
      }
      this.isRegistering = false
    },
    getStatusType(status) {
      const types = {
        registering: 'primary',
        ongoing: 'warning',
        finished: 'info'
      }
      return types[status] || 'default'
    },
    getStatusText(status) {
      const texts = {
        registering: '报名中',
        ongoing: '进行中',
        finished: '已结束'
      }
      return texts[status] || status
    }
  }
}
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content {
  padding: 20rpx;
}
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
}
.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  .title {
    font-size: 36rpx;
    font-weight: bold;
  }
}
.info-list {
  margin-bottom: 30rpx;
  .info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20rpx;
    &:last-child {
      margin-bottom: 0;
    }
    .label {
      color: #666;
      font-size: 28rpx;
    }
    .value {
      font-size: 28rpx;
      color: #333;
    }
  }
}
.match-desc {
  margin-bottom: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }
}
.prize-section {
  margin-bottom: 30rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }
  .prize-total {
    font-size: 28rpx;
    color: #ff6b00;
    margin-bottom: 20rpx;
  }
  .prize-list {
    .prize-item {
      display: flex;
      justify-content: space-between;
      padding: 16rpx 0;
      font-size: 28rpx;
      border-bottom: 1rpx solid #eee;
      &:last-child {
        border-bottom: none;
      }
      .rank {
        color: #333;
      }
      .amount {
        color: #ff6b00;
        font-weight: bold;
      }
    }
  }
}
.rules-section {
  padding-top: 30rpx;
  border-top: 1rpx solid #eee;
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
    display: block;
  }
  .rules-list {
    .rule-item {
      margin-bottom: 20rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .rule-title {
        font-size: 28rpx;
        font-weight: bold;
        color: #333;
        margin-bottom: 10rpx;
        display: block;
      }
      .rule-content {
        font-size: 28rpx;
        color: #666;
        line-height: 1.6;
      }
    }
  }
}
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0,0,0,0.05);
}
.register-popup {
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #eee;
    .title {
      font-size: 32rpx;
      font-weight: bold;
    }
    .close {
      font-size: 40rpx;
      color: #999;
      padding: 0 20rpx;
    }
  }
  .popup-content {
    padding: 30rpx;
    .input-item {
      margin-bottom: 30rpx;
      &:last-child {
        margin-bottom: 0;
      }
      .label {
        font-size: 28rpx;
        color: #333;
        margin-bottom: 16rpx;
        display: block;
      }
    }
  }
  .popup-footer {
    padding: 30rpx;
  }
}
</style> 