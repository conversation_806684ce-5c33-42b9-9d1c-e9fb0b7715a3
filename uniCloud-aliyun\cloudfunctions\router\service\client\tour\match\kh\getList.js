'use strict';
/**
 * 获取比赛列表
 * @url client/tour/match/kh/getList 前端调用的url参数地址
 * @description 获取比赛列表
 * @param {String} type 比赛类型
 * @param {String} level 比赛级别
 * @param {String} status 比赛状态
 * @param {Number} page 页码
 * @param {Number} pageSize 每页数量
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { type, level, status, page = 1, pageSize = 10 } = data;
    const dbName = 'tour-matches';
    const whereObj = {};
    
    if (type) whereObj.type = type;
    if (level) whereObj.level = level;
    if (status) whereObj.status = status;

    res = await vk.baseDao.select({
      dbName,
      pageIndex: page,
      pageSize,
      whereObj,
      sortArr: [{ "name": "create_date", "type": "desc" }]
    });
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 