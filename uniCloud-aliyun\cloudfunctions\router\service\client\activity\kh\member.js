'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/member 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.selects({
			dbName: "activity-join-data",
			getCount: false,
			pageIndex: 1,
			pageSize: 9999,
			whereJson: {
				activity_id: id
			},
			sortArr: [{ name: "_add_time", type: "asc" }],
			foreignDB: [{
				dbName: "uni-id-users",
				localKey: "user_id",
				foreignKey: "_id",
				as: "user_info",
				fieldJson: { avatar: true, nickname: true, level: true, tennis: true },
				limit: 1,
			}]
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}