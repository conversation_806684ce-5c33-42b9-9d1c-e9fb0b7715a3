<template>
  <view class="page-container">
    <t-navbar title="使用反馈"></t-navbar>
    <view class="content">
      <view class="form-group">
        <view class="form-title">反馈内容</view>
        <wd-textarea
          v-model="form.text"
          placeholder="请输入你遇到的问题或者你想要提的建议"
          :maxlength="500"
          show-word-limit
          no-border
          custom-class="feedback-textarea"
        ></wd-textarea>
      </view>

      <view class="form-group">
        <t-image-upload v-model="form.images" label="图片附件" imageType="feedback" :count="5"></t-image-upload>
      </view>

      <view class="submit-section">
        <wd-button type="primary" @click="submit" custom-style="background: #0171BC; width: 100%;">提交反馈</wd-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      form: {
        text: "",
        images: [],
      },
    };
  },
  methods: {
    async submit() {
      if (!this.form.text) return vk.toast("请输入反馈内容", "none");
      try {
        let data = await vk.callFunction({
          url: "client/user/kh/feedback",
          title: "提交中...",
          data: this.form,
        });
        vk.toast(data.msg, "none", true, () => {
          vk.navigateBack();
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 30rpx;

  .form-group {
    background-color: #fff;
    border-radius: 24rpx;
    padding: 30rpx;
    margin-bottom: 30rpx;

    .form-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      margin-bottom: 20rpx;
    }

    :deep(.feedback-textarea) {
      min-height: 200rpx;
    }
  }

  .submit-section {
    margin-top: 60rpx;
  }
}
</style>
