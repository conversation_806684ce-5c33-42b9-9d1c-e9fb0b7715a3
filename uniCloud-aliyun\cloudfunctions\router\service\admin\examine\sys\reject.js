'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/examine/sys/reject 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, params, text } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let typeName = ""

		switch (params.type) {
			case "site":
				typeName = '场地'
				break;
			case "string":
				typeName = '店铺'
				break;
		}
		let notice_title = `信息审核未通过`
		let notice_text = `很遗憾，您提交的${typeName}信息“${params.name}”未能通过，未通过原因为：${text||'无'}`
		await vk.baseDao.add({
			dbName: "user-news-data",
			dataJson: {
				accept_user: params.creator_id,
				title: notice_title,
				text: notice_text,
				read_state: false,
				type: "info",
				link: `/pages/public/submitInfo?type=${params.type}&id=${params.info_id||''}`,
			}
		})
		// 删除审核信息
		await vk.baseDao.deleteById({
			dbName: "pending-audit-data",
			id: params._id
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}