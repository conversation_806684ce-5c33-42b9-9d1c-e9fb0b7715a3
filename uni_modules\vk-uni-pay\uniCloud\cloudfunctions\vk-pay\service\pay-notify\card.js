'use strict';
/**
 * 此处建议只改下订单状态，保证能及时返回给第三方支付服务器成功状态
 * 且where条件可以增加判断服务器推送过来的金额和订单表中订单需支付金额是否一致
 * 将消息发送、返佣、业绩结算等业务逻辑异步处理(写入异步任务队列表)
 * 如开启定时器每隔5秒触发一次，处理订单
 */
module.exports = async (obj, originalParam) => {
	let user_order_success = true;
	let { data = {}, verifyResult } = obj;
	let { db, _ } = originalParam;
	let {
		out_trade_no,
		total_fee,
		user_id,
		card_id,
		club_id,
	} = data;
	// 此处写你自己的支付成功逻辑开始-----------------------------------------------------------
	// 设置订单为已付款
	// 有三种方式
	// 方式一：直接写数据库操作（原生数据库语句）
	// 方式二：使用 await uniCloud.callFunction 调用其他云函数
	let res = await uniCloud.callFunction({
		name: "router",
		data: {
			$url: "client/order/pub/card",
			data: {
				out_trade_no,
				user_id,
				card_id,
				club_id,
				token: "RvQp4mFvnYhGkaTR7b86WESoOvoSJrV1"
			}
		}
	})
	// 方式三：使用 await uniCloud.httpclient.request 调用http接口地址

	// 此处写你自己的支付成功逻辑结束-----------------------------------------------------------
	user_order_success = res.result.code == 0
	return user_order_success;
};