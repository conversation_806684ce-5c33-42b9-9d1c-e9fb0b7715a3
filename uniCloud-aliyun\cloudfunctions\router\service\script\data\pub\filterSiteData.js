'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url script/data/pub/filterSiteData 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// list.map(item=>{
		// 	item.location = new db.Geo.Point(Number(item.location.longitude), Number(item.location.latitude))
		// })
		// await vk.baseDao.adds({
		// 	dbName: "info-site-data",
		// 	dataJson: list
		// });

		// let dbName = 'info-site-data'
		// res = await vk.baseDao.select({
		// 	dbName,
		// 	getCount: false,
		// 	pageIndex: 1,
		// 	pageSize: 1000,
		// 	whereJson: {
		// 		address: new RegExp('广西壮族自治区南宁市')
		// 	}
		// });
		// console.log(res);
		// let num = await vk.baseDao.del({
		// 	dbName,
		// 	whereJson: {
		// 		address: new RegExp('广西壮族自治区南宁市')
		// 	}
		// });
		// console.log(num);

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}