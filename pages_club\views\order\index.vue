<template>
	<view class="page-content">
		<t-navbar title="订单列表"></t-navbar>
		<view class="tabs">
			<t-tabs v-model="current" :list="['全部','已付款','未付款','已退款']" @change="changeTabs"></t-tabs>
		</view>
		<view class="order">
			<view class="order-block" v-for="item in list">
				<view class="top">
					<view class="order-num">{{item.out_trade_no}}</view>
					<view class="order-status">{{item.status}}</view>
				</view>
				<view class="content">
					<view class="cell">购买<{{item.card_info.name}}>卡片</view>
					<view class="cell">
						<view class="label">用户:</view>
						<image class="user-avatar" :src="item.user_info.avatar" mode="aspectFill"></image>
						<view class="user-name">{{item.user_info.nickname}}</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import VipCard from '@/components/card/vip-card.vue'
	export default {
		components: { VipCard },
		data() {
			return {
				club_id: null,
				system: uni.getSystemInfoSync(),
				current: 0,
				page: 1,
				list: [],
			}
		},
		computed: {
			cardStyle() {
				return {
					transform: 'scale(0.3)',
					transformOrigin: 'left'
				}
			}
		},
		onLoad(options) {
			this.club_id = options.id
			this.getInfo()
		},
		methods: {
			changeTabs(index) {
				switch (index) {
					case 0:
						this.status = null
						break;
					case 1:
						this.status = 1
						break;
					case 2:
						this.status = 0
						break;
					case 3:
						this.status = 2
						break;
				}
				this.list = []
				this.page = 1
				this.getInfo()
			},
			async getInfo() {
				try {
					let params = {
						pageIndex: this.page,
						club_id: this.club_id
					}
					if (this.status && this.status !== 0) params.status = this.status
					let data = await vk.callFunction({
						url: 'client/order/kh/list',
						title: '请求中...',
						data: params,
					});

					data.rows.forEach(item => {
						const { status, _add_time } = item;
						const isExpired = _add_time + 900000 < Date.now();

						switch (status) {
							case 0:
								item.status = isExpired ? "已过期" : "未付款";
								break;
							case 1:
								item.status = "已付款";
								break;
							default:
								item.status = "已退款";
						}

						this.list.push(item);
					});
				} catch (error) {
					console.log(error);
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.tabs {
		background-color: #fff;
		display: flex;
		align-items: center;
		width: 100%;
		height: 44px;
		padding: 0 30rpx;
	}

	.order {
		padding: 30rpx;

		&-block {
			width: 100%;
			background-color: #fff;
			border-radius: 12px;
			box-sizing: border-box;
			padding: 30rpx;
			margin-bottom: 20rpx;
		}

		.top {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding-bottom: 20rpx;
			border-bottom: 2rpx solid #f8f8f8;
		}

		.content {
			.cell {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				padding: 10rpx;
			}

			.label {
				font-weight: bold;
			}

			.user-avatar {
				width: 68rpx;
				height: 68rpx;
				border-radius: 50%;
			}

			.user-name {
				font-size: 28rpx;
			}
		}
	}
</style>