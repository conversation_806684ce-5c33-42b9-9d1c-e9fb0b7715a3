{"id": "vk-unicloud", "displayName": "vk-unicloud-router开发框架核心库 - 已集成uni-id 框架内置了众多API。", "version": "2.19.9", "description": "此为vk-unicloud-router框架核心库（新手建议下载完整框架项目）已集成uni-id支持云函数url化。众多现成API，内置小白也能轻松上手的数据库API。使你项目刚起步进度就是百分之50", "keywords": ["vk-unicloud-router", "云函数路由、云对象路由", "vk云开发", "内置uni-id、数据库baseDao", "内置众多API、工具包"], "author": "VK", "repository": "https://gitee.com/vk-uni/vk-uni-cloud-router", "engines": {"HBuilderX": "^3.1.0", "uni-app": "^4.36", "uni-app-x": ""}, "dcloudext": {"sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": "370725567"}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": "https://vkdoc.fsq.pub/", "type": "unicloud-template-function", "darkmode": "x", "i18n": "x", "widescreen": "x"}, "uni_modules": {"dependencies": ["uni-config-center"], "encrypt": [], "platforms": {"cloud": {"tcb": "√", "aliyun": "√", "alipay": "√"}, "client": {"uni-app": {"vue": {"vue2": "√", "vue3": "√"}, "web": {"safari": "√", "chrome": "√"}, "app": {"vue": "√", "nvue": "x", "android": "√", "ios": "√", "harmony": "√"}, "mp": {"weixin": "√", "alipay": "√", "toutiao": "√", "baidu": "√", "kuaishou": "-", "jd": "-", "harmony": "√", "qq": "√", "lark": "-"}, "quickapp": {"huawei": "√", "union": "√"}}, "uni-app-x": {"web": {"safari": "-", "chrome": "-"}, "app": {"android": "-", "ios": "-", "harmony": "-"}, "mp": {"weixin": "-"}}}}}}