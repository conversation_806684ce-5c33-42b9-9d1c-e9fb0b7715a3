{"name": "vk-pay", "version": "1.0.0", "description": "vk-uni-pay是基于uniCloud量身定制的支付插件，兼容任何uniCloud框架。支持H5、PC、小程序、APP，微信公众号、多商户支付，为你支付业务扫平障碍。无第三方npm依赖。", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"uni-config-center": "file:../../../../uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "vk-uni-pay": "file:../common/vk-uni-pay"}, "private": true, "cloudfunction-config": {"concurrency": 1, "memorySize": 512, "path": "/http/vk-pay", "timeout": 60, "triggers": [], "runtime": "Nodejs16", "keepRunningAfterReturn": false}, "extensions": {}, "origin-plugin-dev-name": "vk-uni-pay", "origin-plugin-version": "1.13.2", "plugin-dev-name": "vk-uni-pay", "plugin-version": "1.13.2"}