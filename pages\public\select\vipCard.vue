<template>
  <view>
    <t-navbar title="会员卡选择"></t-navbar>
    <view class="card-list">
      <view v-for="(item, index) in list" :key="item._id" class="card-block" @click="select(index)">
        <vip-card :data="item"></vip-card>
        <image v-if="selected.includes(index)" class="select-icon" src="/static/images/i-mingcute-check-circle-fill.svg" mode="aspectFill" />
      </view>
    </view>
    <t-bottom>
      <t-bottom>
        <view style="width: 100%">
          <wd-button type="primary" @click="confirmSelect">确认选择</wd-button>
        </view>
      </t-bottom>
    </t-bottom>
  </view>
</template>

<script>
import VipCard from "@/components/card/vip-card.vue";
export default {
  components: { VipCard },
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      list: [],
      club_id: "",
      // 请求类型
      type: "",
      // 模式，单选还是多选
      mode: "single",
      // 场景
      scene: "",
      // 选中的会员卡的索引
      selected: [],
    };
  },
  onLoad(options) {
    this.type = options.type;
    this.club_id = options.club_id;
    if (options.mode) this.mode = options.mode;
    if (options.scene) this.scene = options.scene;
    if (options.data) this.params = options.data;
    this.getData();
  },
  methods: {
    // 确认选择
    confirmSelect() {
      if (this.mode == "single") {
        if (this.selected.length == 0) {
          vk.toast("请选择会员卡");
          return;
        }
        uni.$emit("vip-card-select", this.list[this.selected[0]]);
      } else if (this.mode == "multi") {
        if (this.selected.length == 0) {
          vk.toast("请选择会员卡");
          return;
        }
        uni.$emit(
          "vip-card-select",
          this.selected.map((item) => this.list[item])
        );
      }
    },
    // 获取数据
    async getData() {
      let params = {
        club_id: this.club_id,
      };
      if (this.type == "user") params.user_id = this.userInfo._id;

      let data = await vk.callFunction({
        url: "client/club/card/kh/list",
        title: "请求中...",
        data: params,
      });

      this.list = data.rows;
      console.log(data);
    },
    // 选择会员卡
    select(index) {
      if (this.mode == "single") {
        this.selected = [index];
      } else {
        if (this.selected.includes(index)) {
          this.selected.splice(this.selected.indexOf(index), 1);
        } else {
          this.selected.push(index);
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.card-list {
  width: 680rpx;
  margin: 0 auto;
  .card-block {
    margin-bottom: 20rpx;
    position: relative;
  }
}
.selected {
  border: 2rpx solid #409eff;
}
.select-icon {
  width: 60rpx;
  height: 60rpx;
  position: absolute;
  right: 20rpx;
  top: 20rpx;
  z-index: 1;
}
</style>
