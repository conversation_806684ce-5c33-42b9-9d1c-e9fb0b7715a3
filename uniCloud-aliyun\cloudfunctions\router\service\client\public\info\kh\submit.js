const formRules = require("../util/formRules.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/kh/submit 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let keysToDelete = ['_id', '_add_time', '_add_time_str'];
		const deleteKey = (obj) => {
			Object.keys(obj).forEach(key => {
				if (keysToDelete.includes(key)) delete obj[key];
			});
			return obj
		}
		let creator_id = uid
		let params = vk.pubfn.deepClone(data);
		let formRulesRes = { code: 0, msg: "" }
		switch (type) {
			case "string":
				formRulesRes = await formRules.string_add(event);
				break;
			case "site":
				formRulesRes = await formRules.site_add(event);
				break;
		}
		if (formRulesRes.code !== 0) return formRulesRes
		// 是否为编辑状态
		params.update = params.info_id ? true : false
		if (!params.location || params.location.type !== 'Point') {
			params.location = new db.Geo.Point(params.location.longitude, params.location.latitude)
		}
		if (params.info_id) {
			// 查询我是否编辑过
			let info = await vk.baseDao.findByWhereJson({
				dbName: "pending-audit-data",
				whereJson: {
					info_id: params.info_id
				},
				fieldJson: { _id: true }
			});
			console.log('info', info);
			params = deleteKey(params)
			console.log("参数", params);
			if (info) {
				await vk.baseDao.updateById({
					dbName: "pending-audit-data",
					id: info._id,
					dataJson: params,
					getUpdateData: false
				});
			} else {
				await vk.baseDao.add({
					dbName: "pending-audit-data",
					dataJson: {
						creator_id: uid,
						...params
					}
				});
			}
		} else {

			await vk.baseDao.add({
				dbName: "pending-audit-data",
				dataJson: {
					creator_id: uid,
					...params
				}
			});
		}
		vk.callFunction({
			name: "router",
			url: 'public/pub/sendEmail',
			event,
			data: {
				email: '<EMAIL>',
				title: `有新的审核信息，${type} ${params.name}`,
				content: `用户<${userInfo.nickname}>提交了"${type}"（${params.name}）审核信息，请火速审核`
			},
			success: (data) => {
				console.log("发送成功", data);
			},
			fail: (err) => {
				console.log("发送失败", err);
			},
			complete: (res) => {
				console.log("执行结束", res);
			}
		});
		res.msg = "提交成功，感谢您的支持"
		res.back = true
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}