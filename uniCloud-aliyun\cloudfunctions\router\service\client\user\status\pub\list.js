'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/status/pub/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, location, pageIndex = 1, tags, level, sex, name } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = 'player-status-data'
		let whereJson = {
			open: true
		}
		let lastWhereJson = {}
		let foreignDB = [{
			dbName: "uni-id-users",
			localKey: "user_id",
			foreignKey: "_id",
			as: "user_info",
			limit: 1,
			fieldJson: { avatar: true, nickname: true, sex: true, level: true, mobile: true, wechat: true },
		}]
		if (location) {
			location = _.geoNear({
				geometry: new db.Geo.Point(location.longitude, location.latitude),
				maxDistance: 50000,
				minDistance: 0,
				distanceMultiplier: 0.001,
				distanceField: "distance",
			})
		}
		if (location) whereJson.location = location
		if (tags && tags.length != 0) whereJson.tags = _.in(tags)
		if (uid) {
			whereJson.user_id = _.neq(uid)
			foreignDB.push({
				dbName: "contact-white-list",
				localKey: "user_id",
				foreignKey: "target_user_id",
				as: "contact_white",
				whereJson: { user_id: uid },
				limit: 10,
			})
		}
		if (level) lastWhereJson.level = _.gte(Number(level))
		if (sex) lastWhereJson["user_info.sex"] = sex
		if (name) lastWhereJson["user_info.nickname"] = new RegExp(name);

		res = await vk.baseDao.selects({
			dbName,
			getCount: false,
			pageIndex,
			pageSize: 20,
			// 主表where条件
			whereJson,
			// 主表排序规则
			sortArr: [{ name: "_id", type: "desc" }],
			// 副表列表
			foreignDB,
			lastWhereJson,
		});

		// 数据处理
		// 1. 把contact_white处理成数组
		res.rows = res.rows.map(item => {
			if (item.contact_white) item.contact_white = item.contact_white.map(items => items.type)
			return item
		})

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}