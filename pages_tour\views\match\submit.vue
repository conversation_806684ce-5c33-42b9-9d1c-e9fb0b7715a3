<!-- 比赛报名支付页 -->
<template>
  <view class="page">
    <t-navbar title="报名支付" />
    <view class="content">
      <t-card v-if="matchInfo">
        <view class="match-info">
          <view class="title">{{ matchInfo.title }}</view>
          <view class="info-list">
            <view class="info-item">
              <text class="label">比赛时间</text>
              <text class="value">{{ matchInfo.start_date }}</text>
            </view>
            <view class="info-item">
              <text class="label">比赛地点</text>
              <text class="value">{{ matchInfo.venue }}</text>
            </view>
            <view class="info-item">
              <text class="label">报名费用</text>
              <text class="value price">¥{{ matchInfo.entry_fee }}</text>
            </view>
          </view>
        </view>
      </t-card>

      <t-card title="支付方式" class="payment-methods">
        <view class="payment-item">
          <text class="iconfont icon-wechat"></text>
          <text class="payment-name">微信支付</text>
        </view>
      </t-card>

      <view class="action-bar">
        <view class="price-info">
          <text>实付金额</text>
          <text class="price">¥{{ matchInfo ? matchInfo.entry_fee : 0 }}</text>
        </view>
        <t-button type="primary" :loading="isPaying" @click="handlePay"> 立即支付 </t-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      match_id: "",
      registration_id: "",
      matchInfo: null,
      isPaying: false,
    };
  },
  onLoad(options) {
    if (options.match_id && options.registration_id) {
      this.match_id = options.match_id;
      this.registration_id = options.registration_id;
      this.getMatchDetail();
    }
  },
  methods: {
    async getMatchDetail() {
      try {
        const res = await vk.callFunction({
          url: "client/tour/match/kh/detail",
          data: {
            match_id: this.match_id,
          },
        });
        if (res.code === 0) {
          this.matchInfo = res.data;
        }
      } catch (e) {
        console.error(e);
      }
    },
    async handlePay() {
      if (this.isPaying) return;
      this.isPaying = true;
      try {
        const res = await vk.callFunction({
          url: "client/tour/match/kh/pay",
          data: {
            registration_id: this.registration_id,
          },
        });
        if (res.code === 0) {
          // 调用微信支付
          uni.requestPayment({
            provider: "wxpay",
            ...res.data,
            success: () => {
              uni.showToast({
                title: "支付成功",
                icon: "success",
              });
              setTimeout(() => {
                uni.redirectTo({
                  url: `/pages_tour/views/match/detail?id=${this.match_id}`,
                });
              }, 1500);
            },
            fail: (err) => {
              uni.showToast({
                title: "支付失败",
                icon: "none",
              });
            },
          });
        }
      } catch (e) {
        uni.showToast({
          title: e.message || "支付失败",
          icon: "none",
        });
      }
      this.isPaying = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content {
  padding: 20rpx;
}
.match-info {
  .title {
    font-size: 32rpx;
    font-weight: bold;
    margin-bottom: 20rpx;
  }
  .info-list {
    .info-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16rpx;
      font-size: 28rpx;
      .label {
        color: #666;
      }
      .value {
        &.price {
          color: #ff6b00;
          font-weight: bold;
        }
      }
    }
  }
}
.payment-methods {
  margin-top: 20rpx;
  .payment-item {
    display: flex;
    align-items: center;
    padding: 30rpx 0;

    .iconfont {
      font-size: 48rpx;
      color: #07c160;
      margin-right: 20rpx;
    }

    .payment-name {
      font-size: 28rpx;
      color: #333;
    }
  }
}
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: space-between;
  .price-info {
    font-size: 28rpx;
    .price {
      color: #ff6b00;
      font-size: 36rpx;
      font-weight: bold;
      margin-left: 20rpx;
    }
  }
}
</style>
