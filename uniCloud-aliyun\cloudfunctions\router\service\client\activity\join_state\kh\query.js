'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/joinState 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let info = await vk.baseDao.select({
			dbName: "activity-join-data",
			getOne: true,
			getMain: true,
			whereJson: {
				user_id: uid,
				type,
				activity_id: id
			}
		});
		console.log(info, "state");
		res.data = info
		res.state = info ? info.state : 'none'

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}