'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/kh/join 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// 查看是否已存在数据
		let has = await vk.baseDao.count({
			dbName: "club-user-data",
			whereJson: {
				user_id: uid,
				club_id,
			},
		});
		if (has > 0) {
			res.code = -1
			res.msg = "请勿重复加入"
			return res
		}
		// 查看俱乐部是否需要审批
		let info = await vk.baseDao.findById({
			dbName: "club-data",
			id: club_id,
		});
		let state = 'applying'
		if (!info.apply) state = 'member'

		let id = await vk.baseDao.add({
			dbName: "club-user-data",
			dataJson: {
				state,
				user_id: uid,
				club_id,
			}
		});
		res.msg = state == 'applying' ? "申请成功" : "加入成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}