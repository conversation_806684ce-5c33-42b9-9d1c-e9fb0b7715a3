/**
 * vuex 系统状态管理模块
 */
import config from '@/app.config.js'
import dayjs from 'dayjs'
import cnWork from 'chinese-workday'

let lifeData = uni.getStorageSync('lifeData') || {};

let $club = lifeData.$club || {};

export default {
	// 通过添加 namespaced: true 的方式使其成为带命名空间的模块
	namespaced: true,
	/**
	 * vuex的基本数据，用来存储变量
	 */
	state: {
		/**
		 * 是否已经初始化
		 * js调用示例
		 * vk.getVuex('$club.inited');
		 * 页面上直接使用示例
		 * {{ vk.getVuex('$club.inited') }}
		 * js更新示例
		 * vk.setVuex('$club.inited', true);
		 */
		dayList: []
	},
	/**
	 * 从基本数据(state)派生的数据，相当于state的计算属性
	 */
	getters: {

	},
	/**
	 * 提交更新数据的方法，必须是同步的(如果需要异步使用action)。
	 * 每个 mutation 都有一个字符串的 事件类型 (type) 和 一个 回调函数 (handler)。
	 * 回调函数就是我们实际进行状态更改的地方，并且它会接受 state 作为第一个参数，提交载荷作为第二个参数。
	 */
	mutations: {
		SET_DAYLIST(state, list) {
			state.dayList = list
		}
	},
	/**
	 * 和mutation的功能大致相同，不同之处在于 ==》
	 * 1. Action 提交的是 mutation，而不是直接变更状态。
	 * 2. Action 可以包含任意异步操作。
	 */
	actions: {
		getDays({ commit }, { start, num: numDays }) {
			const list = []
			const date = dayjs(start).date()
			const day = dayjs(start).day()
			const week_cn = ["周日", "周一", "周二", "周三", "周四", "周五", "周六"]
			for (let i = 0; i < numDays; i++) {
				const num = dayjs(start).date(date + i).format('DD')
				const en = dayjs(start).day(day + i).format('ddd')
				const dayIndex = dayjs(start).day(day + i).format('d')
				const theDate = dayjs(start).date(date + i).format('YYYY-MM-DD')
				list.push({
					value: dayjs(start).date(date + i).valueOf(),
					date: theDate,
					num,
					en: en.toUpperCase(),
					cn: week_cn[dayIndex],
					day: Number(dayIndex),
					holiday: cnWork.isHoliday(theDate),
					festival: cnWork.getFestival(theDate)
				})
			}
			commit('SET_DAYLIST', list)
		}
	}
}