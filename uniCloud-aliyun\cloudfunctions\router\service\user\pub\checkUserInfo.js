'use strict';
module.exports = {
	/**
	 * 验证用户是否已经注册
	 * @url user/pub/checkUserInfo 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, encryptedKey } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		// 解密获得openid和unionid
		let decryptedRes = vk.crypto.aes.decrypt({
			data: encryptedKey, // 待解密的原文
		});
		let wx_openid = {};
		let wx_unionid = decryptedRes.unionid;
		wx_openid["mp-weixin"] = decryptedRes.openid;
		// 查询openid或unionid是否已经存在,如果存在,则不绑定微信.
		let queryUserInfo = await vk.daoCenter.userDao.findByUserInfo({
			"wx_openid.mp-weixin": wx_openid["mp-weixin"],
			"wx_unionid": wx_unionid
		});
		let unRegister = false;
		// 用户信息不为空且已认证手机号
		if (vk.pubfn.isNull(queryUserInfo) || !queryUserInfo.mobile_confirmed) unRegister = true

		res.data = unRegister

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}