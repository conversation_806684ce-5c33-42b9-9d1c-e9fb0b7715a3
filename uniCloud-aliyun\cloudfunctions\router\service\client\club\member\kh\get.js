'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/member/kh/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, user_id, club_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let info = await vk.baseDao.findByWhereJson({
			dbName: "club-user-data",
			whereJson: {
				user_id,
				club_id
			},
		});
		// 查用户信息
		const outField = ['nickname', 'avatar', 'mobile', 'wechat', 'level']
		const fieldJson = Object.fromEntries(outField.map(field => [field, true]))
		let user_info = await vk.baseDao.findById({
			dbName: "uni-id-users",
			id: user_id,
			fieldJson
		});
		// 查会员卡信息
		let card = await vk.baseDao.selects({
			dbName: "club-user-card",
			pageIndex: 1,
			pageSize: 99,
			whereJson: {
				user_id,
				club_id
			},
			// 副表列表
			foreignDB: [{
				dbName: "club-card-data",
				localKey: "card_id",
				foreignKey: "_id",
				as: "card_info",
				limit: 1,
				foreignDB: [{
					dbName: "club-data",
					localKey: "club_id",
					foreignKey: "_id",
					as: "club_info",
					limit: 1
				}],
			}],
		});
		// 查备注信息
		let remark = await vk.baseDao.findByWhereJson({
			dbName: "club-user-remark",
			whereJson: {
				user_id,
				club_id
			},
		});

		if (!info) return { code: -1, msg: '找不到该成员', back: true }
		if (user_info) info = { ...info, ...user_info }
		if (remark) info.remark = remark
		info.card = card.total > 0 ? card.rows : []
		res.data = info
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}