'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/card/kh/cardDetail 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let ary = await vk.baseDao.selects({
			dbName: "club-card-data",
			getCount: false,
			pageIndex: 1,
			pageSize: 1,
			// 主表where条件
			whereJson: {
				_id: id
			},
			// 副表列表
			foreignDB: [{
				dbName: "club-data",
				localKey: "club_id",
				foreignKey: "_id",
				as: "club_info",
				limit: 1
			}, {
				dbName: "card-style-data",
				localKey: "style_id",
				foreignKey: "_id",
				as: "card_info",
				limit: 1
			}],
		});
		let info = await vk.baseDao.findById({
			dbName: "club-card-data",
			id,
		});
		if (ary.rows.length == 0) {
			res.code = -1
			res.msg = '未找到会员卡'
		} else {
			if (!ary.rows[0].enable) {
				res.code = -1
				res.msg = '该卡未开售'
			} else {
				res.data = ary.rows[0]
			}
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}