<template>
  <view class="t-empty" :style="{ paddingTop: top }">
    <image :src="image" mode="widthFix" />
    <text v-if="text">{{ text }}</text>
    <slot></slot>
  </view>
</template>

<script>
export default {
  name: "t-empty",
  props: {
    text: {
      type: String,
    },
    top: {
      type: String,
      default: "200rpx",
    },
    data: {
      type: String,
      default: "data",
    },
  },
  computed: {
    image() {
      return `${vk.getVuex("$app.config.staticUrl.image")}empty/${this.data}.svg`;
    },
  },
};
</script>

<style scoped lang="scss">
.t-empty {
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  image {
    width: 50%;
  }

  text {
    font-size: 28rpx;
    color: #8a8a8a;
    margin-top: 20rpx;
  }
}
</style>
