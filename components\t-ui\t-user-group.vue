<template>
	<view class="t-user-group">
		<image v-for="(item,index) in data" :key="index" v-show="index<=max" :src="item.avatar" mode="aspectFill" :style="[imageStyle]">
		</image>
		<view class="more" v-if="data.length>max" :style="[imageStyle]">{{data.length>99?'99+':data.length}}</view>
	</view>
</template>

<script>
	export default {
		props: {
			data: {
				type: Array,
				default: () => []
			},
			max: {
				type: [Number, String],
				default: 6,
			},
			size: {
				type: [Number, String],
				default: 88,
			}
		},
		computed: {
			maxNum() {
				return Number(this.max)
			},
			imageStyle() {
				return {
					width: this.size + 'rpx',
					height: this.size + 'rpx'
				}
			},
			moreStyle() {
				return {
					width: this.size + 'rpx',
					height: this.size + 'rpx',
					fontSize: this.size * 0.3 + 'rpx'
				}
			}
		},
	}
</script>

<style lang="scss" scoped>
	.t-user-group {
		display: flex;
		align-items: center;
		position: relative;

		image {
			border-radius: 50%;
			margin-right: -30rpx;
			background-color: #fff;

			&:last-child {
				margin-right: 0;
			}
		}

		.more {
			border-radius: 50%;
			margin-right: 0;
			background-color: #ddd;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}
	}
</style>