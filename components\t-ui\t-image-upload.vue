<template>
  <view class="t-image-upload" :class="[arrangeClass]" :style="[style]">
    <view v-if="label" class="label" :style="[labelStyle]">{{ label }}</view>
    <wd-upload
      :file-list="fileList"
      :limit="count"
      :reupload="reupload"
      :upload-method="handleUploadMethod"
      :before-choose="handleBeforeChoose"
      image-mode="aspectFill"
      :auto-upload="!crop"
      @update:file-list="handleFileListUpdate"
      @success="handleUploadSuccess"
      @fail="handleUploadFail"
    >
    </wd-upload>

    <!-- Image Cropper Modal -->
    <wd-popup v-model="showCropper" position="center" :close-on-click-modal="false" custom-style="width: 100vw; height: 100vh; background: #000;">
      <wd-img-cropper
        v-model="showCropper"
        :img-src="currentCropImage"
        :aspect-ratio="cropAspectRatio"
        @confirm="handleCropConfirm"
        @cancel="handleCropCancel"
      />
    </wd-popup>
  </view>
</template>

<script>
export default {
  emits: ["update:modelValue", "input"],
  props: {
    modelValue: {
      type: [Array, String],
      default: () => [],
    },
    // 保持向下兼容
    value: {
      type: [Array, String],
      default: () => [],
    },
    label: {
      type: String,
      default: "上传图片",
    },
    count: {
      type: Number,
      default: 9,
    },
    labelWidth: {
      type: Number,
      default: 140,
    },
    marginTop: {
      type: [String, Number],
      default: 20,
    },
    imageType: {
      type: String,
      default: "images",
    },
    // New prop for enabling cropping functionality
    crop: {
      type: Boolean,
      default: false,
    },
    // Crop aspect ratio (width:height format)
    cropAspectRatio: {
      type: String,
      default: "1:1",
    },
    // 覆盖上传
    reupload: {
      type: Boolean,
      default: false,
    },
  },
  name: "t-image-upload",
  data() {
    return {
      fileList: [],
      showCropper: false,
      currentCropImage: "",
      pendingCropFile: null,
    };
  },
  computed: {
    style() {
      return {
        marginTop: `${this.marginTop}rpx`,
      };
    },
    arrangeClass() {
      return this.count > 1 ? "multiple" : "single";
    },
    labelStyle() {
      return this.count > 1 ? {} : { width: `${this.labelWidth}rpx` };
    },
  },
  computed: {
    // 获取当前值，优先使用 modelValue (Vue 3)，回退到 value (Vue 2)
    currentValue() {
      return this.modelValue !== undefined ? this.modelValue : this.value;
    },
  },
  watch: {
    modelValue: {
      handler(newVal) {
        this.updateFileList(newVal);
      },
      immediate: true,
      deep: true,
    },
    value: {
      handler(newVal) {
        if (this.modelValue === undefined) {
          this.updateFileList(newVal);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    this.updateFileList(this.currentValue);
  },
  methods: {
    // Update file list based on value prop
    updateFileList(value) {
      if (!value) {
        this.fileList = [];
        return;
      }

      const urls = Array.isArray(value) ? value : [value];
      this.fileList = urls.map((url, index) => ({
        uid: Date.now() + index,
        url: url,
        status: "success",
        name: `image_${index + 1}`,
      }));
    },

    // Handle file list updates from wd-upload
    handleFileListUpdate(newFileList) {
      this.fileList = newFileList;
      const urls = newFileList.filter((file) => file.status == "success" && file.url).map((file) => file.url);

      const newValue = this.count > 1 ? urls : urls[0] || "";

      // Vue 3 双向绑定
      this.$emit("update:modelValue", newValue);
      // Vue 2 兼容
      this.$emit("input", newValue);
    },

    // Handle before choose - intercept for cropping
    handleBeforeChoose({ resolve }) {
      if (this.crop) {
        // For cropping, we'll handle file selection manually
        this.chooseImageForCrop();
        resolve(false); // Prevent default file selection
      } else {
        resolve(true); // Allow default file selection
      }
    },

    // Choose image for cropping
    chooseImageForCrop() {
      uni.chooseImage({
        count: 1, // Only allow one image at a time for cropping
        sizeType: ["compressed"],
        success: (res) => {
          this.currentCropImage = res.tempFilePaths[0];
          this.showCropper = true;
        },
        fail: (err) => {
          console.error("Choose image failed:", err);
        },
      });
    },
    // Handle crop confirmation
    handleCropConfirm(result) {
      this.showCropper = false;
      this.currentCropImage = "";

      // Upload the cropped image
      this.uploadCroppedImage(result.tempFilePath);
    },

    // Handle crop cancellation
    handleCropCancel() {
      this.showCropper = false;
      this.currentCropImage = "";
    },

    // Upload cropped image
    async uploadCroppedImage(filePath) {
      try {
        vk.showLoading("上传中...");
        const url = await this.uploadImage(filePath);

        let newValue;
        if (this.count > 1) {
          const currentUrls = Array.isArray(this.currentValue) ? this.currentValue : [];
          newValue = [...currentUrls, url];
        } else {
          newValue = url;
        }

        // Vue 3 双向绑定
        this.$emit("update:modelValue", newValue);
        // Vue 2 兼容
        this.$emit("input", newValue);
        vk.hideLoading();
      } catch (err) {
        vk.hideLoading();
        console.error("Upload failed:", err);
        uni.showToast({
          title: "上传失败",
          icon: "none",
        });
      }
    },

    // Custom upload method for wd-upload
    async handleUploadMethod(uploadFile, formData, options) {
      const { onSuccess, onError } = options;

      try {
        const url = await this.uploadImage(uploadFile.url);
        // Update the file with the uploaded URL
        uploadFile.url = url;
        onSuccess({ data: { url } }, uploadFile, formData);
      } catch (error) {
        onError(error, uploadFile, formData);
      }
    },

    // Core upload method using vk.uploadFile
    uploadImage(filePath) {
      return new Promise((resolve, reject) => {
        vk.uploadFile({
          filePath,
          needSave: true,
          cloudDirectory: `photo/${this.imageType}`,
          category_id: this.imageType,
          success: (res) => {
            resolve(res.url);
          },
          fail: (err) => {
            reject(err);
          },
        });
      });
    },

    // Handle upload success
    handleUploadSuccess({ fileList }) {
      // Update internal file list and emit value change
      this.handleFileListUpdate(fileList);
    },

    // Handle upload failure
    handleUploadFail({ error }) {
      console.error("Upload failed:", error);
      uni.showToast({
        title: "上传失败",
        icon: "none",
      });
    },

    // Legacy methods for backward compatibility
    previewImage() {
      const urls = Array.isArray(this.currentValue) ? this.currentValue : [this.currentValue].filter(Boolean);
      uni.previewImage({
        urls: urls,
      });
    },

    deleteImage(index) {
      let ary = Array.isArray(this.currentValue) ? [...this.currentValue] : [];
      ary.splice(index, 1);
      // Vue 3 双向绑定
      this.$emit("update:modelValue", ary);
      // Vue 2 兼容
      this.$emit("input", ary);
    },
  },
};
</script>

<style scoped lang="scss">
.t-image-upload {
  width: 100%;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 14rpx;
  box-sizing: border-box;

  .label {
    font-size: 28rpx;
    font-weight: bold;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    margin-bottom: 20rpx;
  }

  .list {
    width: 100%;
    display: grid;
    grid-template-columns: 30% 30% 30%;
    gap: 10rpx 5%;
  }

  .image {
    width: 100%;
    aspect-ratio: 1 / 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    border-radius: 12rpx;
    overflow: hidden;
    margin-bottom: 10rpx;
    position: relative;

    image {
      width: 100%;
      height: 100%;
    }
  }

  .add {
    border: 4rpx dashed #333;

    image {
      width: 88rpx;
      height: 88rpx;
    }
  }
}

.single {
  display: flex;
  align-items: center;

  .image {
    width: 108rpx;
    height: 108rpx;
    margin-bottom: 0;
  }

  .add image {
    width: 48rpx;
    height: 48rpx;
  }
}

.delete {
  position: absolute;
  width: 48rpx;
  height: 48rpx;
  border-radius: 10rpx;
  top: 0;
  right: 0;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);

  &::after {
    content: "";
    width: 20rpx;
    height: 4rpx;
    transform: rotate(-45deg);
    background-color: #ff3030;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }

  &::before {
    content: "";
    width: 20rpx;
    height: 4rpx;
    transform: rotate(45deg);
    background-color: #ff3030;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    margin: auto;
  }
}

.multiple {
  .label {
    margin-bottom: 20rpx;
  }
}
</style>
