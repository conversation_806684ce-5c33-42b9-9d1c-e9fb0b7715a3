<template>
  <view class="page-container">
    <t-navbar title="发起活动" backToast></t-navbar>

    <!-- Main Form -->
    <view class="form-wrapper" v-if="info">
      <wd-form :model="info" class="activity-form">
        <view class="form-section">
          <view class="section-header">
            <t-title>活动信息</t-title>
          </view>
          <view class="form-group">
            <wd-cell-group>
              <wd-cell title="活动模板" :value="info.activity?.name || '点击选择活动模板'" is-link @click="selectActivity"> </wd-cell>

              <wd-input v-model="info.name" label="活动名称" required marker-side="after" placeholder="请输入活动名称" />

              <wd-textarea
                v-model="info.desc"
                label="活动说明"
                required
                marker-side="after"
                placeholder="请输入活动说明"
                auto-height
                show-word-count
              />
            </wd-cell-group>

            <!-- Location Section -->
            <wd-cell-group>
              <wd-cell title="位置选择" required marker-side="after" :value="info.site_info.name || '请选择位置'" is-link @click="selectPosition" />

              <template v-if="info.site_info.location">
                <wd-input v-model="info.site_info.address_name" label="场地名称" required marker-side="after" placeholder="请输入场地名称" />

                <wd-textarea
                  v-model="info.site_info.address"
                  required
                  marker-side="after"
                  label="详细地址"
                  placeholder="请输入详细地址"
                  :maxlength="100"
                  show-word-count
                  auto-height
                />
              </template>
            </wd-cell-group>

            <!-- Time Section -->
            <wd-cell-group>
              <wd-datetime-picker
                v-model="selectDate"
                required
                marker-side="after"
                type="date"
                label="活动日期"
                :min-date="todayValue"
                placeholder="请选择活动日期"
                @confirm="validatorTime"
              />

              <wd-datetime-picker
                v-model="info.start"
                required
                marker-side="after"
                type="time"
                label="开始时间"
                placeholder="请选择开始时间"
                @confirm="validatorTime"
              />

              <wd-datetime-picker
                v-if="info.start"
                required
                marker-side="after"
                v-model="info.end"
                type="time"
                label="结束时间"
                placeholder="请选择结束时间"
                @confirm="validatorTime"
              />
            </wd-cell-group>
            <wd-cell-group>
              <wd-picker v-model="info.level" :columns="[levelOptions]" label="水平要求" placeholder="请选择活动水平要求" />

              <wd-input v-model="info.min_num" type="number" label="开场人数" placeholder="活动至少人数(不填为不限制)">
                <template #suffix>
                  <text class="unit-text">人</text>
                </template>
              </wd-input>

              <wd-input v-model="info.max_num" type="number" label="满场人数" placeholder="活动满场人数(不填为不限制)">
                <template #suffix>
                  <text class="unit-text">人</text>
                </template>
              </wd-input>
            </wd-cell-group>

            <!-- Contact Section -->
            <wd-cell-group>
              <wd-picker v-model="info.contact" :columns="[contactOptions]" label="联系方式" placeholder="请选择您的联系方式" />
            </wd-cell-group>
          </view>
        </view>

        <!-- Settings Section -->
        <view class="form-section">
          <view class="section-header">
            <t-title>费用方案</t-title>
          </view>
          <view class="form-group">
            <!-- Cost Section -->
            <wd-cell-group>
              <wd-picker
                v-model="info.cost_type"
                required
                marker-side="after"
                :columns="[costOptions]"
                label="费用方案"
                placeholder="请选择费用方案"
              />

              <wd-cell v-if="info.cost_type !== 'none'" title="收款方式" required marker-side="after" center title-width="140rpx">
                <wd-radio-group cell v-model="info.cost_plan" inline shape="dot">
                  <wd-radio :value="1">线上收款</wd-radio>
                  <wd-radio :value="2">线下收款</wd-radio>
                </wd-radio-group>
              </wd-cell>

              <template v-if="info.cost_type !== 'none'">
                <wd-input required marker-side="after" v-model="info.cost" type="number" :label="costLabel" :placeholder="costPlaceholder">
                  <template #suffix>
                    <text class="unit-text">元</text>
                  </template>
                </wd-input>
                <view v-if="info.cost_plan === 1 && info.cost_type == 'average' && peoplePay" class="cost-tip">
                  根据"开场人数"计算，每人需先预缴{{ peoplePay }}元，活动开始后根据人数判断退回相应费用
                </view>
              </template>

              <wd-cell title="退出退款" required marker-side="after" title-width="140rpx" v-if="info.cost_plan === 1">
                <view style="display: flex; align-items: center">
                  <view style="margin-left: 0">活动不足</view>
                  <wd-input
                    type="number"
                    no-border
                    custom-style="flex:1;text-align:center;background:#f8f8f8;border-radius:12rpx;margin:0 10rpx;"
                    placeholder="24"
                    v-model="info.refund_hour"
                  />
                  <view>小时退出仅退</view>
                  <wd-input
                    type="number"
                    no-border
                    custom-style="flex:1;text-align:center;background:#f8f8f8;border-radius:12rpx;margin:0 10rpx;"
                    placeholder="0"
                    v-model="info.refund_money"
                  />
                  <view>元</view>
                </view>
              </wd-cell>
            </wd-cell-group>
          </view>
        </view>

        <!-- 活动设置 -->
        <view class="form-section">
          <view class="section-header">
            <t-title>活动设置</t-title>
          </view>
          <view class="form-group">
            <wd-cell-group>
              <wd-cell title="自动解散" class="switch-cell">
                <wd-switch v-model="info.auto_dissolve" :disabled="!info.min_num" active-color="#0171BC" size="40rpx" />
              </wd-cell>
              <wd-cell title="解散规则" title-width="140rpx" v-if="info.auto_dissolve">
                <view style="display: flex; align-items: center">
                  <view style="margin-left: 0">活动开始前</view>
                  <wd-input
                    type="number"
                    no-border
                    custom-style="flex:1;text-align:center;background:#f8f8f8;border-radius:12rpx;margin:0 10rpx;"
                    placeholder="0"
                    v-model="info.dissolve_hour"
                  />
                  <view>小时，人数不足则解散</view>
                </view>
              </wd-cell>

              <wd-cell title="是否审批" class="switch-cell">
                <wd-switch v-model="info.apply" active-color="#0171BC" size="40rpx" />
              </wd-cell>

              <wd-cell title="是否公开" class="switch-cell">
                <wd-switch v-model="info.open" active-color="#0171BC" size="40rpx" />
              </wd-cell>

              <wd-cell title="允许代接" class="switch-cell">
                <wd-switch v-model="info.help_join" active-color="#0171BC" size="40rpx" />
              </wd-cell>

              <wd-cell title="人员候补" class="switch-cell">
                <wd-switch v-model="info.standby" active-color="#0171BC" size="40rpx" />
              </wd-cell>

              <wd-cell title="订阅消息" class="switch-cell">
                <wd-switch v-model="info.notice" active-color="#0171BC" size="40rpx" />
              </wd-cell>
            </wd-cell-group>
          </view>
        </view>
      </wd-form>
    </view>

    <!-- Agreement Section -->
    <view class="agreement-section">
      <view class="agreement-content">
        <wd-checkbox v-model="agreement" shape="square" checked-color="#0171BC" class="agreement-checkbox" />
        <text class="agreement-text" @click="agreement = !agreement">我已阅读并同意来网球</text>
        <text class="agreement-link" @click.stop="vk.navigateTo('/pages/public/agreement?type=disclaimer')"> 《活动免责声明》 </text>
      </view>
    </view>

    <!-- Action Buttons -->
    <t-bottom>
      <view class="action-section">
        <wd-button type="info" @click="saveActivity" class="save-btn"> 存为模板 </wd-button>
        <wd-button type="primary" @click="startActivity" class="start-btn"> 发起活动 </wd-button>
      </view>
    </t-bottom>
  </view>
</template>

<script>
import _ from "lodash";
import dayjs from "dayjs";
import { baseActivityInfo } from "./config";

export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      info: null,
      agreement: false,
      selectDate: dayjs().valueOf(),
      today: dayjs().format("YYYY-MM-DD"),
      contactOptions: [
        {
          label: "手机号",
          value: "mobile",
        },
        {
          label: "微信号",
          value: "wechat",
        },
        {
          label: "无需联系",
          value: "none",
        },
      ],
      levelOptions: [{ label: "不限", value: "" }],
      costOptions: [
        { label: "无需费用", value: "none" },
        { label: "平摊场地费", value: "average" },
        { label: "固定费用", value: "fixed" },
      ],
      timeLimit: {
        minHour: 0,
        maxHour: 23,
        minMinute: 0,
        maxMinute: 59,
      },
    };
  },
  onShareAppMessage(e) {
    let userName = this.userInfo.nickname || "";
    return {
      title: `${userName}邀请您组织网球活动`,
      path: `/pages/activity/start`,
    };
  },
  onLoad() {
    this.info = _.cloneDeep(baseActivityInfo);
    this.getOptions();
  },

  computed: {
    // 今日时间时间戳
    todayValue() {
      return dayjs().valueOf();
    },
    // 活动费用文字
    costLabel() {
      if (!this.info.cost_type) return "活动费用";
      switch (this.info.cost_type) {
        case "none":
          return "活动费用";
        case "average":
          return "平摊费用";
        case "fixed":
          return "固定费用";
        default:
          return "活动费用";
      }
    },
    costPlaceholder() {
      if (!this.info.cost_type) return "请输入活动费用";
      switch (this.info.cost_type) {
        case "none":
          return "请输入活动费用";
        case "average":
          return "请输入活动总费用";
        case "fixed":
          return "请输入固定费用";
        default:
          return "请输入活动费用";
      }
    },
    // 计算每人需先预缴费用
    peoplePay() {
      if (!this.info.cost) return null;
      let minNum = this.info.min_num || 1;
      return (this.info.cost / minNum).toFixed(2);
    },
  },
  watch: {
    "$store.state.$user.userInfo": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal, oldVal) {
        this.userInfo = newVal;
      },
    },
    info: {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal, oldVal) {
        if (!newVal.min_num) this.info.auto_dissolve = false;
      },
    },
  },
  methods: {
    // 验证时间
    validatorTime() {
      const { start, end } = this.info;
      console.log(start);
      let date = dayjs(this.selectDate).format("YYYY-MM-DD");
      // 验证开始时间
      let startTime = dayjs(`${date} ${start}`).valueOf();
      let now = dayjs().valueOf();
      console.log("开始时间", `${date} ${start}`);
      console.log("时间戳", startTime, now);
      if (startTime < now) {
        vk.toast("开始时间不能小于当前时间!");
        this.$nextTick(() => {
          this.info.start = "";
        });
        return;
      }
      if (start && end && start >= end) {
        vk.toast("开始时间不能大于等于结束时间!");
        this.$nextTick(() => {
          this.info.end = "";
        });
        return;
      }
    },
    async saveActivity() {
      const { activity } = this.info;
      try {
        // 判断是否是更新
        if (activity && activity._id && activity.type == "user") {
          uni.showModal({
            title: "更新提示",
            content: `希望把当前信息存为新模板还是更新当前‘${activity.name}’这个模板信息`,
            cancelText: "新增",
            confirmText: "更新",
            success: async (res) => {
              if (res.confirm) {
                this.info.id = activity._id;
                let data = await vk.callFunction({
                  url: "client/activity/template/kh/update",
                  title: "更新中...",
                  data: this.info,
                });
                vk.toast(data.msg, "success");
              } else {
                let data = await vk.callFunction({
                  url: "client/activity/template/kh/add",
                  title: "新增中...",
                  data: this.info,
                });
                vk.toast(data.msg, "success");
              }
            },
          });
        } else {
          let data = await vk.callFunction({
            url: "client/activity/template/kh/add",
            title: "保存中...",
            data: this.info,
          });
          this.info.activity = { _id: data.data, type: "user", ...this.info };
          vk.toast(data.msg, "success");
        }
      } catch (error) {
        console.log(error, "error");
      }
    },
    async startActivity() {
      const { start, end } = this.info;
      if (!this.agreement) {
        vk.toast("请先阅读并同意《活动免责声明》");
        return;
      }
      // 判断开始时间和结束时间
      if (!start || !end) {
        vk.toast("请完善活动时间");
        return;
      } else if (start > end) {
        vk.toast("开始时间不能大于结束时间");
        return;
      }
      if (this.info.contact == "wechat") {
        if (!this.userInfo.wechat) {
          vk.confirm("微信号未填写，是否前往填写", "提示", "确定", "取消", (res) => {
            if (res.confirm) {
              vk.navigateTo("/pages/user/edit/base");
            }
          });
          return;
        }
      }
      const params = _.cloneDeep(this.info);
      const date = dayjs(this.selectDate).format("YYYY-MM-DD");
      params.start = dayjs(`${date} ${params.start}`).valueOf();
      params.end = dayjs(`${date} ${params.end}`).valueOf();

      let data = await vk.callFunction({
        url: "client/activity/kh/start",
        title: "正在发起活动...",
        data: params,
      });
      uni.requestSubscribeMessage({
        tmplIds: ["qULr23FpxNQTuWrghCj6gA_855wO6isiP-_IM1sCO1A"],
        complete: () => {
          vk.toast(data.msg, "none", true, () => {
            vk.navigateBack();
            uni.$emit("activity-refresh");
          });
        },
      });
    },
    // 处理从模板加载的时间数据
    datetimeHandle(start, end) {
      const date = dayjs(this.selectDate).format("YYYY-MM-DD");
      start = dayjs(`${this.selectDate} ${start}`).valueOf();
      end = dayjs(`${this.selectDate} ${end}`).valueOf();
      const now = dayjs();

      let startTimeStr = null;
      let endTimeStr = null;
      let startTimestamp = null;
      let endTimestamp = null;

      // 处理开始时间
      if (start) {
        startTimeStr = dayjs(start).format("HH:mm");
        startTimestamp = dayjs(`${date} ${startTimeStr}`).valueOf();
      }

      // 处理结束时间
      if (end) {
        endTimeStr = dayjs(end).format("HH:mm");
        endTimestamp = dayjs(`${date} ${endTimeStr}`).valueOf();
      }

      // 如果选择的是今天，需要考虑当前时间
      if (dayjs(this.selectDate).isSame(now, "day")) {
        // 如果开始时间已过，但结束时间未过，设置开始时间为当前时间
        if (startTimestamp && endTimestamp && now.valueOf() > startTimestamp && endTimestamp > now.valueOf()) {
          this.info.start = now.format("HH:mm");
          this.info.end = endTimeStr;
        }
        // 如果开始时间和结束时间都已过，清空时间
        else if (startTimestamp && endTimestamp && now.valueOf() > startTimestamp && now.valueOf() > endTimestamp) {
          this.info.start = "";
          this.info.end = "";
        }
        // 其他情况直接设置
        else {
          this.info.start = startTimeStr || "";
          this.info.end = endTimeStr || "";
        }
      } else {
        // 非今天的日期，直接设置时间戳
        this.info.start = startTimeStr || "";
        this.info.end = endTimeStr || "";
      }
    },
    selectActivity() {
      uni.$on("select-activity", (data) => {
        const fields = [
          "name",
          "desc",
          "site_info",
          "min_num",
          "max_num",
          "dissolve_hour",
          "auto_dissolve",
          "level",
          "apply",
          "start",
          "end",
          "open",
          "cost_type",
          "cost",
          "cost_plan",
          "refund_hour",
          "refund_money",
          "help_join",
          "contact",
          "notice",
          "standby",
        ];
        this.info.activity = data;
        fields.forEach((field) => {
          if (data[field]) this.info[field] = data[field];
        });
        // 单独处理时间
        this.datetimeHandle(data.start, data.end);
      });
      let id = this.info.activity ? this.info.activity._id : "";
      vk.navigateTo("/pages/activity/template?id=" + id);
    },
    selectPosition() {
      uni.chooseLocation({
        success: (res) => {
          let site_info = {
            location: {
              latitude: res.latitude,
              longitude: res.longitude,
            },
            address: res.address + res.name,
            address_name: res.name,
            name: res.name,
          };
          this.info.site_info = site_info;
        },
        fail: (res) => {
          console.log(res);
        },
      });
    },
    async getOptions() {
      try {
        let level = await vk.callFunction({
          url: "client/options/pub/level",
        });
        if (level.data && level.data.length > 0) {
          this.levelOptions = level.data;
        }
      } catch (error) {
        console.error("获取选项失败:", error);
      }
    },
    selectSite() {
      uni.$on("select-site", (data) => {
        this.info.site = data;
      });
      vk.navigateTo("/pages_club/views/select/site?id=" + this.info.site._id);
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: env(safe-area-inset-bottom);
}

.form-wrapper {
  padding: 20rpx;
}

.form-section {
  margin-bottom: 40rpx;

  .section-header {
    margin-bottom: 20rpx;
    padding: 0 10rpx;

    .section-title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }
  }
}

.form-group {
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  padding: 20rpx 10rpx;
}

.form-field {
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.switch-cell {
  padding: 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 28rpx;
  border-bottom: 1rpx solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }
}

.unit-text {
  font-size: 26rpx;
  color: #666;
}

.agreement-section {
  padding: 0 20rpx 40rpx;

  .agreement-content {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 16rpx;

    .agreement-checkbox {
      margin-right: 16rpx;
    }

    .agreement-text {
      font-size: 26rpx;
      color: #999;
      margin-right: 8rpx;
    }

    .agreement-link {
      font-size: 26rpx;
      color: #0171bc;
      font-weight: 500;
      text-decoration: underline;
    }
  }
}
.cost-tip {
  font-size: 24rpx;
  color: #999;
  padding: 0 var(--wot-input-padding, 15px);
}
.action-section {
  padding: 20rpx;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20rpx;
}
</style>
