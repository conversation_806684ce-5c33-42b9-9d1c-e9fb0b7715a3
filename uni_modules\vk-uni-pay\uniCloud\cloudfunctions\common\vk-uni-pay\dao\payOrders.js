/**
 * 请勿修改此处代码，因为插件更新后此处代码会被覆盖。
 * 作者：VK
 * 发布于：2021-07-06
 */
const dbName = {
	payOrders: "vk-pay-orders", // 数据库表名 - 第三方支付订单表
};

const common = require('../libs/common');

const db = uniCloud.database();
const _ = db.command;
var dao = {};

/**
 * 添加 - 第三方支付订单数据
await dao.payOrders.add({
	status: 0,
	pay_type : payType,
	out_trade_no : outTradeNo,
	openid : openid,
	totalFee : totalFee,
	appid : appId,
	original_data : event.body,
	wxpay_info : wxpay_info,
	alipay_info : alipay_info,
	user_order_success: userOrderSuccess
});
 */
dao.add = async (dataJson = {}) => {
	// 数据库操作开始-----------------------------------------------------------
	let date = new Date();
	let _add_time = date.getTime();
	let _add_time_str = common.timeFormat(date, "yyyy-MM-dd hh:mm:ss");
	let res = await db.collection(dbName.payOrders).add({
		_add_time,
		_add_time_str,
		...dataJson
	});
	// 数据库操作结束-----------------------------------------------------------
	return res.id ? res.id : null;
};


/**
 * 获取 - 第三方支付订单数据（根据where条件）
let payOrderInfo = await dao.payOrders.find({
	out_trade_no,
	transaction_id
});
 */
dao.find = async (where) => {
	let res = await db.collection(dbName.payOrders)
		.where(where)
		.limit(1)
		.get();
	if (res.data && res.data.length > 0) {
		return res.data[0];
	} else {
		return null;
	}
	return res;
};

/**
 * 获取 - 第三方支付订单数据（根据商户单号）
await dao.payOrders.findByOutTradeNo(outTradeNo);
 */
dao.findByOutTradeNo = async (out_trade_no = "___") => {
	let res = await db.collection(dbName.payOrders)
		.where({
			out_trade_no
		})
		.limit(1)
		.get();
	if (res.data && res.data.length > 0) {
		return res.data[0];
	} else {
		return null;
	}
	return res;
};

/**
 * 获取 - 第三方支付订单数据（根据支付单号）
await dao.payOrders.findByTransactionId(transaction_id);
 */
dao.findByTransactionId = async (transaction_id = "___") => {
	let res = await db.collection(dbName.payOrders)
		.where({
			transaction_id
		})
		.limit(1)
		.get();
	if (res.data && res.data.length > 0) {
		return res.data[0];
	} else {
		return null;
	}
	return res;
};

/**
 * 修改 - 第三方支付订单数据
await dao.payOrders.updateById(id, {

});
 */
dao.updateById = async (id = "___", dataJson) => {
	// 数据库操作开始-----------------------------------------------------------
	let res = await db.collection(dbName.payOrders).doc(id).update(dataJson);
	// 数据库操作结束-----------------------------------------------------------
	return res ? res.updated : 0;
};
/**
 * 修改 - 第三方支付订单数据
await dao.payOrders.update({
  whereJson:{
    status:0,
    out_trade_no
  },
  dataJson:{

  }
});
 */
dao.update = async (obj) => {
	let { whereJson, dataJson } = obj;
	// 数据库操作开始-----------------------------------------------------------
	let res = await db.collection(dbName.payOrders).where(whereJson).update(dataJson);
	// 数据库操作结束-----------------------------------------------------------
	return res ? res.updated : 0;
};
/**
 * 修改 - 第三方支付订单数据
await dao.payOrders.updateAndReturn({
	whereJson:{

	},
	dataJson:{

	}
});
 */
dao.updateAndReturn = async (obj) => {
	let { whereJson, dataJson } = obj;
	// 数据库操作开始-----------------------------------------------------------
	let res = await db.collection(dbName.payOrders).where(whereJson).updateAndReturn(dataJson);
	// 数据库操作结束-----------------------------------------------------------
	return res.doc ? res.doc : null;
};
/**
 * 删除超过3天还未支付款的订单
await dao.payOrders.deleteExpPayOrders({
	day: 3
});
 */
dao.deleteExpPayOrders = async (obj = {}) => {
	let {
		day
	} = obj;
	if (day > 0) {
		let time = Date.now() - 1000 * 3600 * 24 * day;
		let res = await db.collection(dbName.payOrders)
			.where({
				status: _.in([-1, 0]),
				_add_time: _.lt(time)
			})
			.remove();
		return res ? res.updated : 0;
	} else {
		return 0;
	}
};

module.exports = dao;
