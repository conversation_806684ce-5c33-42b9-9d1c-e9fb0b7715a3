<template>
  <view class="page-container">
    <t-navbar title="编辑信息"></t-navbar>
    <view class="content" v-if="userInfo">
      <wd-form :model="userInfo" label-width="160rpx" class="user-form">
        <!-- 基本信息表单 -->
        <view class="form-section">
          <view class="form-group">
            <t-image-upload v-model="userInfo.avatar" crop reupload :count="1" label="上传头像" imageType="avatar"></t-image-upload>

            <wd-input v-model="userInfo.nickname" label="昵称" placeholder="请输入昵称" :maxlength="20" class="form-field" />

            <wd-input v-model="userInfo.wechat" label="微信号" placeholder="请输入微信号" :maxlength="50" class="form-field" />

            <wd-input v-model="userInfo.mobile" label="手机号" placeholder="绑定手机号" disabled class="form-field" />

            <wd-picker v-model="userInfo.sex" :columns="sexColumns" label="性别" placeholder="请选择性别" class="form-field" />

            <wd-datetime-picker v-model="userInfo.birthday" type="date" label="出生日期" placeholder="请选择出生日期" class="form-field" />
          </view>
        </view>
      </wd-form>

      <view class="button-section">
        <wd-button type="primary" @click="editInfo" custom-style="background: #0171BC; width: 100%;">保存信息</wd-button>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      encryptedKey: null,
      userInfo: null,
      sexOptions: [
        { label: "保密", value: 0 },
        { label: "男", value: 1 },
        { label: "女", value: 2 },
      ],
    };
  },
  computed: {
    sexColumns() {
      return [this.sexOptions];
    },
  },
  onLoad() {
    this.userInfo = vk.getVuex("$user.userInfo");
    if (!this.userInfo.sex) this.userInfo.sex = 0;
  },
  methods: {
    getPhoneNumber(e) {
      let { encryptedData, iv } = e.detail;
      if (!encryptedData || !iv) {
        return false;
      }
      vk.userCenter.code2SessionWeixin({
        data: {
          needCache: true,
        },
        success: (data) => {
          let encryptedKey = data.encryptedKey;
          vk.userCenter.getPhoneNumber({
            data: {
              encryptedData,
              iv,
              encryptedKey,
            },
            success: async (data) => {
              try {
                await vk.callFunction({
                  url: "user/kh/updateUser",
                  title: "请求中...",
                  data: {
                    phone: data.phone,
                  },
                });
                vk.toast("手机号修改成功", "success", true, () => {
                  this.userInfo = vk.getVuex("$user.userInfo");
                });
              } catch (e) {
                vk.toast("修改失败", "none");
              }
            },
          });
        },
      });
    },
    async editInfo() {
      try {
        await vk.callFunction({
          url: "user/kh/updateUser",
          title: "请求中...",
          data: {
            avatar: this.userInfo.avatar,
            nickname: this.userInfo.nickname,
            sex: this.userInfo.sex,
            wechat: this.userInfo.wechat,
            birthday: this.userInfo.birthday,
          },
        });
        vk.toast("编辑成功", "success", true, () => {
          vk.navigateBack();
        });
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 30rpx;

  .form-section {
    margin-bottom: 30rpx;

    .form-group {
      background-color: #fff;
      border-radius: 24rpx;
      overflow: hidden;
      padding: 10rpx;

      .form-field {
        margin-bottom: 30rpx;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .button-section {
    margin-top: 60rpx;
    padding-bottom: 80rpx;
  }
}

// 表单样式定制
:deep(.wd-form) {
  .wd-form-item {
    margin-bottom: 0;

    .wd-form-item__label {
      color: #333;
      font-weight: 500;
    }

    .wd-form-item__content {
      .wd-input,
      .wd-picker,
      .wd-datetime-picker {
        border: 2rpx solid #e5e5e5;
        border-radius: 12rpx;
        background-color: #fafafa;
        transition: all 0.3s;

        &:focus,
        &.is-focus {
          border-color: #0171bc;
          background-color: #fff;
        }
      }

      .wd-input {
        --wd-input-padding-vertical: 24rpx;
        --wd-input-padding-horizontal: 20rpx;
        --wd-input-font-size: 28rpx;
        --wd-input-color: #333;
        --wd-input-placeholder-color: #999;
      }

      .wd-picker,
      .wd-datetime-picker {
        --wd-picker-padding-vertical: 24rpx;
        --wd-picker-padding-horizontal: 20rpx;
        --wd-picker-font-size: 28rpx;
        --wd-picker-color: #333;
        --wd-picker-placeholder-color: #999;
      }
    }
  }
}

// 头像上传组件样式
:deep(.t-image-upload) {
  margin-bottom: 0;
}
</style>
