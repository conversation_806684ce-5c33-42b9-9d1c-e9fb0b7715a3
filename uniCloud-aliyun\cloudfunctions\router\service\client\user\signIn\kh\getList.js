'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/signIn/kh/getList 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, pageIndex, pageSize } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let list = await vk.baseDao.selects({
			dbName: "sign-info-list",
			getCount: false,
			getMain: true,
			pageIndex,
			pageSize,
			whereJson: {
				user_id: uid
			},
			sortArr: [{ name: "_add_time", type: "desc" }],
			foreignDB: [{
				dbName: "info-site-data",
				localKey: "data_id",
				foreignKey: "_id",
				as: "site_info",
				limit: 1
			}, {
				dbName: "info-site-close",
				localKey: "data_id",
				foreignKey: "data_id",
				as: "site_close",
				limit: 1
			}]
		});

		res.data = list.map(item => {
			let site = item.site_info || item.site_close
			return {
				"_id": item._id,
				"site_id": site.data_id ? '' : site._id,
				"site_name": site.name,
				"create_date": item._add_time,
			}
		})

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}