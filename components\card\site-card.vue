<template>
  <view class="site-card" :class="[animationClass]" :style="customStyle" v-if="siteData">
    <!-- Site Content -->
    <view class="site-content" @click="navigateToDetail">
      <image class="site-cover" :src="coverImage" mode="aspectFill" @error="handleImageError" />

      <view class="site-info">
        <view class="site-name">
          <wd-tag type="primary" :bg-color="siteType.color" custom-style="margin-right:10rpx;">{{ siteType.label }}</wd-tag>
          <view class="text">{{ siteData.name }}</view>
        </view>
        <view class="site-address">地址：{{ siteData.address }}</view>
        <view class="site-hours">营业时间：{{ businessHours }}</view>
        <view class="site-contact">联系方式：{{ contactInfo }}</view>
      </view>
    </view>

    <!-- Site Footer -->
    <view class="site-footer">
      <view class="distance-info" v-if="hasDistance">
        <text>距您 {{ formattedDistance }}km</text>
      </view>

      <view class="count-text">
        <view class="text">{{ signText }}</view>
        <text class="text">{{ recommendText }}</text>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: "SiteCard",

  props: {
    value: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    animateIndex: {
      type: Number,
      default: 0,
    },
    animateNumber: {
      type: Number,
      default: 20,
    },
    customStyle: {
      type: Object,
      default: () => ({}),
    },
  },

  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      defaultImage: "/static/images/default-site.png",
    };
  },

  computed: {
    siteType() {
      if (!this.value) return null;
      const { site_type } = this.value;
      if (site_type.length > 1) return { label: "综合场馆", color: "#fcc800", type: "all" };
      if (site_type.includes("site")) return { label: "网球场", color: "", type: "site" };
      if (site_type.includes("practice")) return { label: "学练馆", color: "#38b48b", type: "practice" };
      return null;
    },
    // 场地数据别名
    siteData() {
      return this.value || {};
    },

    // 动画类名
    animationClass() {
      const index = this.animateIndex + 1;
      const number = this.animateNumber;
      const result = index % number;

      if (result === 0) {
        return `animation${number}`;
      } else {
        return `animation${result}`;
      }
    },

    // 封面图片
    coverImage() {
      if (this.siteData.image?.length > 0) {
        return this.siteData.image[0];
      }
      return `${this.iconUrl}no-image.svg`;
    },

    // 营业时间
    businessHours() {
      return this.siteData.open_time || "暂无信息";
    },

    // 联系方式
    contactInfo() {
      if (!this.siteData.contact_info?.length) {
        return "暂无信息";
      }

      const contact = this.siteData.contact_info[0];
      if (!contact) return "暂无信息";

      switch (contact.type) {
        case "phone":
          return `电话 ${contact.value}`;
        case "wechat":
          return `微信号 ${contact.value}`;
        case "public":
          return `公众号 ${contact.value}`;
        case "miniproject":
          return `小程序 ${contact.value}`;
        default:
          return "暂无信息";
      }
    },

    // 距离信息
    hasDistance() {
      return this.siteData.distance !== undefined && this.siteData.distance !== null;
    },

    formattedDistance() {
      return this.siteData.distance?.toFixed(2) || "0.00";
    },

    // 推荐数量
    recommendCount() {
      const value = this.siteData.recommend_num || 0;

      if (value > 10000) {
        return (value / 10000).toFixed(1) + "w";
      } else if (value > 1000) {
        return (value / 1000).toFixed(1) + "k";
      } else {
        return value.toString();
      }
    },

    // 推荐文本
    recommendText() {
      return `${this.recommendCount}人推荐`;
    },

    // 打卡数量
    signCount() {
      const value = this.siteData.sign_num || 0;

      if (value > 10000) {
        return (value / 10000).toFixed(1) + "w";
      } else if (value > 1000) {
        return (value / 1000).toFixed(1) + "k";
      } else {
        return value.toString();
      }
    },

    // 打卡文本
    signText() {
      return `${this.signCount}人已打卡`;
    },
  },

  methods: {
    // 跳转到详情页
    navigateToDetail() {
      if (!this.siteData._id) {
        console.error("Site ID is missing");
        vk.toast("场地信息错误");
        return;
      }

      vk.navigateTo(`/pages/explore/site/detail?id=${this.siteData._id}`);
    },

    // 处理推荐
    async handleRecommend() {
      try {
        const res = await vk.callFunction({
          url: "client/public/info/kh/recommend",
          title: "请求中...",
          data: {
            id: this.siteData._id,
            type: "site",
          },
        });

        // 更新推荐状态
        this.siteData.recommend = res.data;
        this.siteData.recommend_num = res.num;

        const action = res.data ? "推荐" : "取消推荐";
        vk.toast(`${action}成功`);
      } catch (error) {
        console.error("Failed to handle recommend:", error);
        vk.toast("操作失败");
      }
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.warn("Site image failed to load:", event);
      event.target.src = this.defaultImage;
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/static/scss/animation.scss";

.site-card {
  opacity: 0;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 28rpx;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }
}

// Site Content
.site-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  min-height: 180rpx;
}

.site-cover {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  background: #f8f8f8;
  flex-shrink: 0;
}

.site-info {
  flex: 1;
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 180rpx;

  .site-name {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;
    .text {
      flex: 1;
      font-size: 28rpx;
      font-weight: bold;
      color: #333;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      line-clamp: 1;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }

  .site-address {
    font-size: 26rpx;
    color: #666;
    margin-bottom: 12rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .site-type {
    display: flex;
    align-items: center;
  }

  .site-hours,
  .site-contact {
    font-size: 24rpx;
    color: #999;
    margin-bottom: 8rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Site Footer
.site-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.distance-info {
  font-size: 28rpx;
  font-weight: bold;
  color: #000;
}

.count-text {
  display: flex;
  align-items: center;
  gap: 30rpx;
  .text {
    font-size: 24rpx;
    color: #999;
  }
}

// Animation Classes
@for $i from 1 to 99 {
  .animation#{$i} {
    animation: fade-in-right 0.8s forwards #{$i * 0.2s};
  }
}
</style>
