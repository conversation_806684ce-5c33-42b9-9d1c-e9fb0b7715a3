'use strict';
module.exports = {
	/**
	 * 取消申请加入俱乐部
	 * @url client/club/kh/backout 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, club_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		const dbName = "club-user-data"
		let has = await vk.baseDao.findByWhereJson({
			dbName,
			whereJson: {
				user_id: uid,
				club_id
			},
		});
		console.log(has);
		if (has && has.state !== 'applying') {
			return {
				code: -1,
				msg: "您已是该俱乐部成员"
			}
		} else if (!has) {
			return {
				code: -1,
				msg: "未查询到申请记录"
			}
		}

		await vk.baseDao.deleteById({
			dbName,
			id: has._id
		});

		res.msg = "取消申请成功"

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}