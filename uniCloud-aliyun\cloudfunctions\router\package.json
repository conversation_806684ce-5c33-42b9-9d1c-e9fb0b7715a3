{"name": "router", "version": "1.0.0", "description": "【开箱即用】vk-uniCloud-router - 云函数路由模式 - uniCloud开发框架 - 已集成uni-id", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"uni-config-center": "file:../../../uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center", "uni-id": "file:../../../uni_modules/uni-id/uniCloud/cloudfunctions/common/uni-id", "uni-map-common": "file:../../../uni_modules/uni-map-common/uniCloud/cloudfunctions/common/uni-map-common", "vk-mail": "file:../../../uni_modules/vk-mail/uniCloud/cloudfunctions/common/vk-mail", "vk-uni-pay": "file:../../../uni_modules/vk-uni-pay/uniCloud/cloudfunctions/common/vk-uni-pay", "vk-unicloud": "file:../../../uni_modules/vk-unicloud/uniCloud/cloudfunctions/common/vk-unicloud"}, "private": true, "cloudfunction-config": {"concurrency": 1, "memorySize": 512, "path": "/api", "timeout": 60, "triggers": [], "runtime": "Nodejs12", "keepRunningAfterReturn": false}, "extensions": {"uni-cloud-ext-storage": {}}}