<template>
  <view class="page-content">
    <view class="header" :style="{ paddingTop: `${system.statusBarHeight}px` }">
      <t-tabs v-model="tabIndex" :list="tabs" :customStyle="{ width: `${system.screenWidth - 115}px`, height: '40px' }"></t-tabs>
      <template v-if="tabIndex == 0">
        <scroll-view class="date" enable-flex scroll-x :show-scrollbar="false">
          <view class="date-fixed" v-if="calendar">
            <view class="date-day" :class="{ active: selectDate === 'all' }" @click="selectDate = 'all'">
              <text class="all">全部</text>
            </view>
            <view
              v-for="(days, index) in calendar"
              :key="index"
              class="date-day"
              :class="{ active: selectDate === index }"
              @click="selectDate = index"
            >
              <text>{{ days.num }}</text>
              <text>{{ days.cn }}</text>
            </view>
            <view class="selected" :style="[selectBlockStyle]" />
          </view>
        </scroll-view>
        <view class="filter">
          <view class="filter-block" @click="changeLocation">
            <image class="icon" :src="`${iconUrl}location-fill.svg`"></image>
            <text class="text truncate">{{ locationName }}</text>
          </view>
          <view class="filter-block" @click="sortList">
            <image class="icon" :src="`${iconUrl}transfer-2-fill.svg`"></image>
            <text class="text">{{ filters[filterIndex].title }}排序</text>
          </view>
          <view class="base-search">
            <wd-search placeholder="请输入活动标题" hide-cancel @confirm="confirmSearch"></wd-search>
          </view>
        </view>
      </template>
    </view>
    <swiper class="swiper-page" :current="tabIndex" :style="{ top: swiperTop, height: swiperHeight }" @change="changTabIndex">
      <!-- 全部 -->
      <swiper-item>
        <scroll-view
          scroll-y
          :show-scrollbar="false"
          :refresher-enabled="pullEnable"
          :refresher-triggered="triggered"
          :refresher-threshold="100"
          @refresherrefresh="onRefresh"
          :style="{ height: swiperHeight }"
          @scrolltolower="loadMore"
        >
          <t-empty v-if="all.total == 0 && all.nodata == true" data="data">
            <view class="empty-text">附近暂无约球信息,<text class="theme" @click="vk.navigateTo('/pages/activity/start')">去发起</text></view>
          </t-empty>
          <template v-else>
            <view v-for="(item, index) in all.list" :key="item._id">
              <activity-card :animateIndex="index" :value="item"></activity-card>
            </view>
            <wd-divider v-if="all.page !== 1 && all.nodata == true">没有更多了</wd-divider>
          </template>
        </scroll-view>
      </swiper-item>
      <!-- 已发起 -->
      <swiper-item>
        <scroll-view
          scroll-y
          :show-scrollbar="false"
          :refresher-enabled="pullEnable"
          :refresher-triggered="triggered"
          :refresher-threshold="100"
          @refresherrefresh="onRefresh"
          :style="{ height: swiperHeight }"
          @scrolltolower="loadMore"
        >
          <t-empty v-if="created.total == 0 && created.nodata == true" data="data" text="暂无记录"></t-empty>
          <template v-else>
            <view v-for="(item, index) in created.list" :key="item._id">
              <activity-card :animateIndex="index" :value="item"></activity-card>
            </view>
            <wd-divider v-if="created.page !== 1 && created.nodata == true">没有更多了</wd-divider>
          </template>
        </scroll-view>
      </swiper-item>
      <!-- 已加入 -->
      <swiper-item>
        <scroll-view
          scroll-y
          :show-scrollbar="false"
          :refresher-enabled="pullEnable"
          :refresher-triggered="triggered"
          :refresher-threshold="100"
          @refresherrefresh="onRefresh"
          :style="{ height: swiperHeight }"
          @scrolltolower="loadMore"
        >
          <t-empty v-if="joined.total == 0 && joined.nodata == true" data="data" text="暂无记录"></t-empty>
          <template v-else>
            <view v-for="(item, index) in joined.list" :key="item._id">
              <activity-card :animateIndex="index" :value="item"></activity-card>
            </view>
            <wd-divider v-if="joined.page !== 1 && joined.nodata == true">没有更多了</wd-divider>
          </template>
        </scroll-view>
      </swiper-item>
      <!-- 已申请 -->
      <swiper-item>
        <scroll-view
          scroll-y
          :show-scrollbar="false"
          :refresher-enabled="pullEnable"
          :refresher-triggered="triggered"
          :refresher-threshold="100"
          @refresherrefresh="onRefresh"
          :style="{ height: swiperHeight }"
          @scrolltolower="loadMore"
        >
          <t-empty v-if="applying.total == 0 && applying.nodata == true" data="data" text="暂无记录"></t-empty>
          <template v-else>
            <view v-for="(item, index) in applying.list" :key="item._id">
              <activity-card :animateIndex="index" :value="item"></activity-card>
            </view>
            <wd-divider v-if="applying.page !== 1 && applying.nodata == true">没有更多了</wd-divider>
          </template>
        </scroll-view>
      </swiper-item>
    </swiper>

    <t-tabbar></t-tabbar>
  </view>
</template>
<script>
import activityCard from "@/components/card/activity-card.vue";
import dayjs from "dayjs";
export default {
  components: {
    activityCard,
  },
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      tabIndex: 0,
      tabList: [
        { label: "全部", value: "all" },
        { label: "已发起", value: "created" },
        { label: "已加入", value: "joined" },
        { label: "申请中", value: "applying" },
      ],
      pullEnable: false,
      triggered: false,
      userInfo: vk.getVuex("$user.userInfo"),
      list: [],
      pages: 1,
      total: 0,
      filters: [
        {
          type: "distance",
          title: "距离",
        },
        {
          type: "start",
          title: "时间",
        },
      ],
      filterIndex: 0,
      queryParams: {
        level: null,
        club_id: null,
      },
      searchValue: "",
      locationName: "南宁市",
      locationInfo: null,
      system: uni.getSystemInfoSync(),
      paddingTop: 0,
      calendar: null,
      selectDate: "all",

      all: {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      },
      created: {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      },
      joined: {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      },
      applying: {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      },
    };
  },
  onShareAppMessage() {
    let userName = this.userInfo.nickname || "";
    return {
      title: `${userName}邀请您使用来网球`,
      imageUrl: `https://cdn.cometennis.cn/images/share/share.jpeg`,
    };
  },
  watch: {
    selectDate() {
      this.all.list = [];
      this.all.page = 1;
      this.all.nodata = false;
      this.all.total = 0;
      this.getActivity();
    },
  },
  computed: {
    selectBlockStyle() {
      let index = this.selectDate == "all" ? 0 : this.selectDate + 1;
      console.log("样式", { left: `${index * 50}px` });
      return { left: `${index * 50}px` };
    },
    swiperTop() {
      const { statusBarHeight } = this.system;
      let base = 55;
      if (this.tabIndex == 0) base += 104;
      return `${statusBarHeight + base}px`;
    },
    swiperHeight() {
      const { statusBarHeight, screenHeight } = this.system;
      let base = 55;
      if (this.tabIndex == 0) base += 104;
      return `${screenHeight - statusBarHeight - base - 78}px`;
    },
    tabType() {
      return this.tabList[this.tabIndex].value;
    },
    tabs() {
      return this.tabList.map((item) => item.label);
    },
  },
  async onLoad(options = {}) {
    // 初始化日历
    vk.vuex.dispatch("$date/getDays", {
      start: dayjs().format("YYYY-MM-DD"),
      num: 30,
    });
    this.calendar = vk.getVuex("$date.dayList");
    // 初始化定位并获取数据
    await this.getAnalysisLocal();
    this.paddingTop = `${this.system.statusBarHeight - 10}px`;

    // 监听数据变化
    uni.$on("activity-refresh", (value) => {
      this.allRefresh();
    });
    this.pullEnable = true;
  },
  // 函数
  methods: {
    // 加载更多
    loadMore() {
      if (this[this.tabType].nodata) return;
      this[this.tabType].page += 1;
      this.getData();
    },
    getData() {
      if (this.tabIndex == 0) {
        this.getActivity();
      } else {
        this.getRecord(this.tabType);
      }
    },
    allRefresh() {
      let init = {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      };
      this.tabList.forEach((item) => {
        this[item.value] = JSON.parse(JSON.stringify(init));
      });
      this.getData();
    },
    onRefresh() {
      if (this.triggered) return;
      this.triggered = true;
      if (vk.getVuex("$setting.vibrate")) uni.vibrateShort();
      let init = {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      };

      this[this.tabType] = init;
      this.getData();
    },
    changTabIndex(e) {
      let index = e.detail.current;
      this.tabIndex = index;
      if (this[this.tabType].total === 0 && this[this.tabType].nodata === false) this.getData();
    },
    confirmSearch({ value }) {
      let init = {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      };
      this[this.tabType] = init;
      this.searchValue = value;
      this.getData();
    },
    sortList() {
      if (this.filters.length == this.filterIndex + 1) {
        this.filterIndex = 0;
      } else {
        this.filterIndex += 1;
      }
      let init = {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      };
      this[this.tabType] = init;
      this.getData();
    },
    async getAnalysisLocal(info) {
      if (info) {
        let data = await vk.callFunction({
          url: "plugs/location/pub/analysis",
          title: "请求中...",
          data: {
            latitude: info.latitude,
            longitude: info.longitude,
          },
        });
        this.locationName = data.data.city;
        this.allRefresh();
      } else {
        uni.getLocation({
          type: "gcj02",
          success: (res) => {
            this.locationInfo = res;
            vk.setVuex("$app.locationInfo", res);
            this.getAnalysisLocal(res);
          },
          fail: () => {
            vk.toast("位置授权失败", "none");
            this.getActivity();
          },
        });
      }
    },
    changeLocation() {
      let latitude = "";
      let longitude = "";
      if (this.locationInfo) {
        latitude = this.locationInfo.latitude;
        longitude = this.locationInfo.longitude;
      }
      uni.chooseLocation({
        latitude,
        longitude,
        success: (res) => {
          this.locationInfo = {
            latitude: res.latitude,
            longitude: res.longitude,
          };
          this.getAnalysisLocal(this.locationInfo);
        },
      });
    },
    async getActivity() {
      let index = this.selectDate;
      let params = {
        pageIndex: this.all.page,
        location: this.locationInfo,
        sort: [{ name: this.filters[this.filterIndex].type, type: "asc" }],
        name: this.searchValue,
        open: true,
      };
      if (index !== "all") params.date = this.calendar[index].value;
      let data = await vk.callFunction({
        url: "client/activity/pub/list",
        title: "请求中...",
        data: params,
      });
      this.all.total = data.total;
      data.rows.forEach((item) => {
        this.all.list.push(item);
      });
      this.all.nodata = !data.hasMore;
      this.triggered = false;
    },
    async getRecord(type) {
      try {
        let data = await vk.callFunction({
          url: "client/activity/kh/record",
          title: "请求中...",
          data: {
            type,
            location: this.locationInfo,
            pageIndex: this[type].page,
          },
        });
        data.rows.forEach((item) => {
          this[type].list.push(item);
        });
        this[type].total = data.total;
        this[type].nodata = !data.hasMore;
        this.triggered = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.page-content {
  background-color: #f8f8f8;
}

.header {
  width: 100%;
  z-index: 9;
  left: 0;
  padding: 0 30rpx 10px;
  box-sizing: border-box;
  position: fixed;
  background-color: #fff;
}

.date {
  width: 100%;
  height: 54px;
  position: relative;
  display: flex;
  margin-bottom: 5px;

  &-fixed {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    box-sizing: border-box;
  }

  &-day {
    width: 50px;
    height: 54px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    z-index: 1;
    flex: 1;
    text-align: center;

    &.active {
      text {
        color: #fff !important;
      }
    }

    text {
      transition: color 0.5s;
      &:first-child {
        font-size: 18px;
        color: #000;
        font-weight: bold;
      }

      &:nth-child(2) {
        font-size: 12px;
        color: #333;
      }
    }

    .all {
      font-size: 16px !important;
    }
  }

  .selected {
    position: absolute;
    width: 50px;
    height: 54px;
    background-color: #0171bc;
    z-index: 0;
    top: 0;
    border-radius: 14rpx;
    transition: left 0.3s;
  }
}

.filter {
  flex: 1;
  display: flex;
  align-items: center;

  &-block {
    display: flex;
    align-items: center;
    width: 170rpx;
    height: 60rpx;
    margin-right: 20rpx;

    .icon {
      width: 30rpx;
      height: 30rpx;
      margin-right: 10rpx;
      flex-shrink: 0;
    }

    .text {
      font-size: 28rpx;
      font-weight: bold;
      white-space: nowrap;
    }
  }
}

.swiper-page {
  width: 100%;
  position: relative;
  left: 0;
  background-color: #f8f8f8;
  padding: 0 30rpx;
  box-sizing: border-box;
}
</style>
