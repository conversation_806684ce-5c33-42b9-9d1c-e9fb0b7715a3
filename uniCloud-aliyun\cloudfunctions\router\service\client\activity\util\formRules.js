'use strict';
/**
 * 表单验证
 */
class Util {
	constructor() {
		this.rules = {
			add: {
				name: [
					{ required: true, message: '活动名称不能为空', trigger: 'blur' },
					{ min: 2, max: 20, message: '活动名称长度在2到20个字符之间', trigger: 'blur' }
				],
				start: [
					{ required: true, message: '开始时间不能为空', trigger: 'blur' }
				],
				end: [
					{ required: true, message: '结束时间不能为空', trigger: 'blur' }
				],
				level: [
					{ required: true, message: '水平要求不能为空', trigger: 'blur' }
				],
				apply: [
					{ required: true, message: '审批状态为必填项', trigger: 'blur' }
				],
				open: [
					{ required: true, message: '公开状态为必填项', trigger: 'blur' }
				],
				standby: [
					{ required: true, message: '人员候补为必填项', trigger: 'blur' }
				]
			},
		}
	}
	async validate(event, rules) {
		let { data = {}, userInfo, util } = event;
		let { vk } = util;
		let res = vk.pubfn.formValidate({
			data: data,
			rules: rules
		});
		return res;
	}
	/**
	 * 添加活动
	 */
	async add(event) {
		let res = { code: 0, msg: "" };
		const { site_info, cost, cost_type, cost_plan, refund_money, refund_hour, min_num, max_num, start, end, club_id } = event.data
		// 判断场地信息
		if (!site_info.location || !site_info.name) return { code: -1, msg: "请选择场地信息" }
		// 判断开场人数
		if (max_num && min_num > max_num) return { code: -1, msg: "开场人数不能大于满场人数" }
		// 判断开始时间
		if (start < Date.now()) return { code: -1, msg: "开始时间不能小于当前时间" }
		// 判断时间段
		if (start >= end) return { code: -1, msg: "结束时间要大于开场时间" }
		// 验证费用方案
		if (!club_id && !cost_type) return { code: -1, msg: '请设置费用方案' }
		// 验证退款规则
		if (cost_type !== 'none' && !cost) {
			if (cost_plan === 1 && (!refund_money || !refund_hour)) return { code: -1, msg: "请完善退款规则" }
			return { code: -1, msg: '请填写活动费用' }
		}
		return this.validate(event, this.rules.add);
	}

}
module.exports = new Util