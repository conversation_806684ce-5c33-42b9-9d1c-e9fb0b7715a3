'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/kh/recommend 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = "info-recommend-record"
		let has = await vk.baseDao.findByWhereJson({
			dbName,
			getOne: true,
			whereJson: {
				main_id: id,
				user_id: uid
			},
		});
		if (has) {
			await vk.baseDao.deleteById({
				dbName,
				id: has._id
			});
			res.msg = "取消推荐"
			res.data = false
		} else {
			await vk.baseDao.add({
				dbName,
				dataJson: {
					main_id: id,
					user_id: uid
				}
			});
			res.msg = "推荐成功"
			res.data = true
		}
		// 查询点赞数量
		let num = await vk.baseDao.count({
			dbName,
			whereJson: {
				main_id: id,
			}
		});
		// 更新信息表数量
		let updateRes = await vk.baseDao.updateById({
			dbName: `info-${type}-data`,
			id,
			dataJson: {
				recommend_num: num
			},
			getUpdateData: true
		});
		res.num = num
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}