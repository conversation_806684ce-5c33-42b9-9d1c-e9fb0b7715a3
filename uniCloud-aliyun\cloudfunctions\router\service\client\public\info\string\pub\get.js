'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/string/pub/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let foreignDB = []
		if (uid) {
			foreignDB.push({
				dbName: "info-recommend-record",
				localKey: "_id",
				foreignKey: "main_id",
				as: "recommend",
				whereJson: {
					user_id: uid
				},
				limit: 1
			})
		}
		res.data = await vk.baseDao.selects({
			dbName: "info-string-data",
			getCount: false,
			getOne: true,
			getMain: true,
			// 主表where条件
			whereJson: {
				_id: id
			},
			// 副表列表
			foreignDB
		});


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}