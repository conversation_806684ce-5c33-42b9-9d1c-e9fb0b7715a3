<template>
  <view>
    <view class="t-bottom" :style="[style]">
      <slot></slot>
    </view>
    <view class="placeholder" :style="[style]"></view>
  </view>
</template>

<script>
export default {
  name: "t-bottom",
  props: {
    height: {
      type: [String, Number],
      default: 120,
    },
  },
  computed: {
    style() {
      const safeArea = uni.getSystemInfoSync().safeAreaInsets.bottom;
      return {
        height: this.height + safeArea + "rpx",
        paddingBottom: safeArea + "rpx",
      };
    },
  },
  data() {
    return {};
  },
};
</script>

<style scoped lang="scss">
.t-bottom {
  position: fixed;
  z-index: 9;
  left: 0;
  bottom: 0;
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  box-shadow: 0 0 12rpx rgba(0, 0, 0, 0.08);
}

.placeholder {
  box-sizing: border-box;
}
</style>
