'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/order/kh/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id, pageIndex, status } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let whereJson = {}
		let foreignWhereJson = {}
		let foreignInfo = [{
			dbName: "club-card-data",
			localKey: "card_id",
			foreignKey: "_id",
			as: "card_info",
			limit: 1
		}]
		if (club_id) {
			whereJson.club_id = club_id
			foreignInfo.push({
				dbName: "uni-id-users",
				localKey: "user_id",
				foreignKey: "_id",
				as: "user_info",
				fieldJson: { nickname: true, avatar: true },
				limit: 1
			})
		} else {
			whereJson.user_id = uid
		}
		if (status) foreignWhereJson.status = status
		res = await vk.baseDao.selects({
			dbName: "order-pay-record",
			getCount: false,
			pageIndex,
			pageSize: 20,
			// 主表where条件
			whereJson,
			// 主表字段显示规则
			fieldJson: { order_id: true },
			// 主表排序规则
			sortArr: [{ name: "_add_time", type: "desc" }],
			// 副表列表
			foreignDB: [{
				dbName: "order-pay-data",
				localKey: "order_id",
				foreignKey: "_id",
				as: "order_info",
				whereJson: foreignWhereJson,
				limit: 1,
				foreignDB: foreignInfo
			}]
		});
		res.rows = res.rows.map(item => item.order_info)
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}