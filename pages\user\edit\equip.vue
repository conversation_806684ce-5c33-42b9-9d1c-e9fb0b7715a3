<template>
	<view class="page-content">
		<t-navbar :title="title"></t-navbar>
		<view class="list">
			<view class="block" v-for="item in list" :key="item._id">
				<image class="image" :src="item.image" mode="aspectFit"></image>
				<view class="name">{{item.label}}</view>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				title: '',
				type: '',
				list: [],
			}
		},
		onLoad(options) {
			this.type = options.type
			switch (options.type) {
				case "racket":
					this.title = '常用球拍'
					break;
				case "shoes":
					this.title = '常用球鞋'
					break;
			}
			this.getData()
		},
		methods: {
			async getData() {
				let { rows } = await vk.callFunction({
					url: 'client/options/pub/get',
					title: '请求中...',
					data: {
						type: this.type
					},
				});
				this.list = rows
			},
		}
	}
</script>

<style scoped lang="scss">
	.page-content {
		padding: 30rpx;
	}

	.list {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;

		.block {
			border-radius: 22rpx;
			flex: 0.48;
			background-color: #fff;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 30rpx;

			.image {
				width: 200rpx;
				height: 200rpx;
			}

			.name {
				font-size: 28rpx;
				font-weight: bold;
			}
		}
	}
</style>