<template>
	<view class="page-content">
		<t-navbar title="新增会员卡" />
		<vip-card :data="new_card">
			<view class="watermark">卡片预览</view>
		</vip-card>
		<view class="form">
			<view class="color">
				<view class="color-block" v-for="item in card_style" @click="selectColor(item)" :style="[itemStyle(item)]">
				</view>
			</view>
			<t-input label="卡名" v-model="new_card.name" placeholder="请输入会员卡名称"></t-input>
			<t-input label="价格" v-model="new_card.price" type="digit" unit="元" placeholder="请输入会员卡价格"></t-input>
			<t-group marginTop='30'>
				<t-picker label="类型" v-model="new_card.type" marginTop="0" :options="typeOptions"></t-picker>
				<!-- 日期卡日期 -->
				<template v-if="new_card.type === 'date'">
					<t-picker label="开始时间" mode="date" marginTop="0" :end="new_card.end" v-model="new_card.value" placeholder="请选择开始时间">
					</t-picker>
				</template>
				<!-- 次卡类型 -->
				<template v-if="new_card.type === 'times'">
					<t-input label="次数" marginTop="0" type="number" v-model="new_card.value"></t-input>
				</template>
				<!-- 储蓄卡类型 -->
				<template v-if="new_card.type === 'save'">
					<t-input label="余额" marginTop="0" type="digit" v-model="new_card.value"></t-input>
				</template>
				<t-input label="有效天数" marginTop="0" type="number" :max="99999" v-model="new_card.day"></t-input>
			</t-group>
			<t-bottom>
				<view style="width: 100%;">
					<t-button width="100%" color="#0171BC" text-color="#fff" :customStyle="{margin:'30rpx auto'}" @click="addCard">新增会员卡
					</t-button>
				</view>
			</t-bottom>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	import vipCard from '@/components/card/vip-card.vue'
	export default {
		components: {
			vipCard
		},
		data() {
			return {
				id: null,
				info: null,
				card_style: [],

				typeOptions: [{
						label: '定期卡',
						value: 'date'
					},
					{
						label: '日期卡',
						value: 'day'
					},
					{
						label: '次卡',
						value: 'times'
					},
					{
						label: '储蓄卡',
						value: 'save'
					},
				],
				new_card: {
					name: "",
					type: "times",
					value: "",
					day: "",
					end: "",
					price: "",
					style: {},
					repeat: false,
					overlay: false,
				}
			}
		},
		watch: {
			'new_card.type'() {
				this.new_card.value = this.new_card.type == 'day' ? this.new_card.day : ''
			},
			'new_card.day'() {
				if (this.new_card.type == 'date') {
					this.new_card.end = dayjs(this.new_card.value).add(this.new_card.day, 'day').valueOf()
				} else {
					this.new_card.end = dayjs().add(this.new_card.day, 'day').valueOf()
				}
				if (this.new_card.type == 'day') this.new_card.value = this.new_card.day
			},
			'new_card.value'() {
				if (this.new_card.type == 'date' && this.new_card.day) {
					this.new_card.end = dayjs(this.new_card.value).add(this.new_card.day, 'day').valueOf()
				}
			},
			"$store.state.$club.info": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.info = newVal
				}
			},
			"$store.state.$club.id": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.id = newVal
					this.getData()
				}
			}
		},
		async onLoad() {
			this.id = vk.getVuex('$club.id')
			this.info = await vk.vuex.getters('$club/getInfo')
			this.new_card.club_info = this.info
			this.getData()
		},
		methods: {
			async getData() {
				const style = await vk.callFunction({
					url: 'client/club/card/kh/cardStyle',
				});
				this.card_style = style.rows
				this.new_card.style = style.rows[0]
			},
			selectColor(item) {
				this.new_card.style = item
			},
			dateFormat(date) {
				return dayjs(date).format('YYYY-MM-DD')
			},
			itemStyle(item) {
				const obj = {
					color: item.text_color
				}
				if (item.type === 'image') {
					obj.backgroundImage = `url(${item.background})`;
				} else if (item.type === 'color') {
					obj.background = `linear-gradient(120deg, ${item.main_color} 50%, ${item.second_color} 100%)`
				}
				return obj
			},

			async addCard() {
				let params = vk.pubfn.copyObject(this.new_card);
				params.style_id = params.style._id
				delete params.style
				try {
					const res = await vk.callFunction({
						url: 'client/club/card/kh/addCard',
						title: '请求中...',
						data: {
							...params,
							club_id: this.id
						},
					})
					vk.toast(res.msg, 'success', true, () => {
						uni.$emit('refresh-card')
						uni.navigateBack()
					});
				} catch (error) {
					console.log(error);
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-content {
		padding: 30rpx;
		box-sizing: border-box;
	}

	.watermark {
		position: absolute;
		top: 100rpx;
		left: 150rpx;
		transform: rotate(30deg);
		font-size: 100rpx;
		font-weight: bold;
		color: #fff;
		opacity: 0.2;
		z-index: 1;
	}

	.form {
		margin-top: 50rpx;

		.color {
			display: flex;
			align-items: center;
		}

		.color-block {
			width: 60rpx;
			height: 60rpx;
			border-radius: 8rpx;
			margin-right: 20rpx;
			background-position: center;
			background-size: 100%;
		}
	}
</style>