'use strict';
module.exports = {
	/**
	 * 处理activity-record-data数据
	 * @url script/data/pub/initActivityRecordData 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// 删除全部数据
		await vk.baseDao.del({
			dbName: "activity-record-data",
			whereJson: {
				type: _.in(["created", "joined", "applying"])
			}
		});
		console.log("已删除全部数据");
		// 新建创建人的数据
		let created = await vk.baseDao.select({
			dbName: "activity-data",
			getCount: false,
			getMain: true,
			pageIndex: 1,
			pageSize: 9999,
		});
		let created_params = []
		created.forEach(item => {
			created_params.push({
				info_id: item._id,
				user_id: item.creator_id,
				activity_id: item._id,
				type: "created",
				_add_time: item._add_time,
				_add_time_str: item._add_time_str
			})
		})
		await vk.baseDao.adds({
			dbName: "activity-record-data",
			dataJson: created_params
		});
		console.log("创建人记录已初始化");
		// 新建参与人数据
		let join = await vk.baseDao.select({
			dbName: "activity-join-data",
			getCount: false,
			getMain: true,
			pageIndex: 1,
			pageSize: 9999,
		});
		let join_params = []
		join.forEach(item => {
			join_params.push({
				info_id: item._id,
				user_id: item.user_id,
				activity_id: item.activity_id,
				type: item.state,
				_add_time: item._add_time,
				_add_time_str: item._add_time_str
			})
		})
		await vk.baseDao.adds({
			dbName: "activity-record-data",
			dataJson: join_params
		});
		console.log("加入与申请记录已初始化");
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}