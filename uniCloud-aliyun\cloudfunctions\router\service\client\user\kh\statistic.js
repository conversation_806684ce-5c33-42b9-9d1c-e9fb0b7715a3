'use strict';
module.exports = {
	/**
	 * 个人中心以及主页统计数据
	 * @url client/user/kh/statistic 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, user_id } = data;
		let res = { code: 0, msg: "" };
		user_id = user_id ? user_id : uid
		// 业务逻辑开始-----------------------------------------------------------

		// 发起的活动
		let num1 = await vk.baseDao.count({
			dbName: "activity-data",
			whereJson: {
				creator_id: user_id
			}
		});
		// 加入的活动
		let num2 = await vk.baseDao.count({
			dbName: "activity-join-data",
			whereJson: {
				state: "joined",
				user_id,
			}
		});
		// 关注的数量
		let num3 = await vk.baseDao.count({
			dbName: "user-follow-data",
			whereJson: {
				user_id,
			}
		});
		// 粉丝的数量
		let num4 = await vk.baseDao.count({
			dbName: "user-follow-data",
			whereJson: {
				target_user_id: user_id,
			}
		});

		res.data = {
			created: num1,
			joined: num2,
			follow: num3,
			fans: num4

		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}