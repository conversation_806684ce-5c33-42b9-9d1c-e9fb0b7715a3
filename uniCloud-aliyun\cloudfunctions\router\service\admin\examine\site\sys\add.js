'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/examine/string/sys/add 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let keys = ["image", "name", "site_type", "intro", "location", "address", "address_name", "site_indoor",
			"site_outdoor", "site_practice", "open_time", "cost_info", "contact_info", "service"
		]
		let num_keys = ["site_indoor", "site_outdoor", "site_practice"]
		let dataJson = {}
		for (let key of keys) {
			let val = data[key];
			if (num_keys.includes(key)) val = val ? Number(val) : 0
			dataJson[key] = val
		}
		// 添加recommend_num
		dataJson.recommend_num = 0
		dataJson.admin_id = ""
		let id = await vk.baseDao.add({
			dbName: "info-site-data",
			dataJson
		});
		// 添加历史更新记录
		if (data.creator_id) {
			await vk.baseDao.add({
				dbName: "info-edit-history",
				dataJson: {
					info_id: id,
					user_id: data.creator_id,
					text: "创建了信息",
					update_time: data._add_time
				}
			});
		}

		let notice_title = `新增场地信息已审核通过`
		let notice_text = `您提交的场地信息“${data.name}”已审核通过，十分感谢您对本平台的支持`
		await vk.baseDao.add({
			dbName: "user-news-data",
			dataJson: {
				accept_user: data.creator_id,
				title: notice_title,
				text: notice_text,
				read_state: false,
				type: "info",
				link: `/pages/explore/site/detail?id=${id}`,
			}
		})


		// 添加完后删除相关信息
		await vk.baseDao.deleteById({
			dbName: "pending-audit-data",
			id: data._id
		});
		res.msg = "更新成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}