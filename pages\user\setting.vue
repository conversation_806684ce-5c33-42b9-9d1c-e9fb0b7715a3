<template>
  <view class="page-container">
    <t-navbar title="系统设置"></t-navbar>
    <view class="content" v-if="setting">
      <view class="setting-group">
        <view class="group-title">基础设置</view>
        <wd-cell-group custom-class="setting-cell-group">
          <wd-cell title="震动反馈">
            <template #right-icon>
              <wd-switch
                v-model="setting.vibrate"
                size="40rpx"
                @change="change($event, 'vibrate')"
                custom-style="--wd-switch-on-color: #0171BC"
              ></wd-switch>
            </template>
          </wd-cell>
        </wd-cell-group>
      </view>

      <view class="setting-group" v-if="false">
        <view class="group-title">隐私设置</view>
        <wd-cell-group custom-class="setting-cell-group">
          <wd-cell title="公开记录">
            <template #right-icon>
              <wd-switch
                v-model="setting.match_open"
                size="40rpx"
                @change="change($event, 'match_open')"
                custom-style="--wd-switch-on-color: #0171BC"
              ></wd-switch>
            </template>
          </wd-cell>
          <wd-cell title="展示活动">
            <template #right-icon>
              <wd-switch
                v-model="setting.show_activity"
                size="40rpx"
                @change="change($event, 'show_activity')"
                custom-style="--wd-switch-on-color: #0171BC"
              ></wd-switch>
            </template>
          </wd-cell>
          <wd-cell title="展示活动次数">
            <template #right-icon>
              <wd-switch
                v-model="setting.show_activity_times"
                size="40rpx"
                @change="change($event, 'show_activity_times')"
                custom-style="--wd-switch-on-color: #0171BC"
              ></wd-switch>
            </template>
          </wd-cell>
        </wd-cell-group>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      setting: null,
    };
  },
  onLoad() {
    this.setting = vk.getVuex("$user.userInfo.setting");
  },
  methods: {
    change(value, type) {
      this.setting[type] = value;
      vk.userCenter.updateUser({
        data: {
          setting: this.setting,
        },
        success: () => {
          vk.toast("设置成功");
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 30rpx;

  .setting-group {
    margin-bottom: 30rpx;

    .group-title {
      font-size: 28rpx;
      color: #666;
      margin-bottom: 20rpx;
      padding-left: 10rpx;
    }

    :deep(.setting-cell-group) {
      border-radius: 24rpx;
      overflow: hidden;
    }
  }
}
</style>
