<template>
	<view>
		<t-navbar title="裁剪图片"></t-navbar>
		<!-- 图片裁剪 -->
		<qf-image-cropper ref="qfImageCropper" :width="500" :height="500" :radius="24"
			@crop="handleCrop"></qf-image-cropper>
	</view>
</template>

<script>
	export default {
		data() {
			return {

			}
		},
		onLoad() {
			this.$refs.qfImageCropper.chooseImage({ sourceType: ['album'] });
		},
		onUnload() {
			uni.$off('crop-image')
		},
		methods: {
			handleCrop(e) {
				console.log("确定返回", e);
				uni.$emit('crop-image', e.tempFilePath)
				uni.navigateBack()
			},
		}
	}
</script>

<style>

</style>