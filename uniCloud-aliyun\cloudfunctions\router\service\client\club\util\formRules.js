'use strict';
/**
 * 表单验证
 */
class Util {
	constructor() {
		this.rules = {
			// 更新俱乐部信息
			club_update: {
				_id: [{ required: true, message: 'ID不能为空', trigger: 'blur' }],
				name: [
					{ required: true, message: '名称不能为空', trigger: 'blur' },
					{ min: 4, max: 20, message: '名称长度在4到20个字符之间', trigger: 'blur' }
				],
				address_name: [{ required: true, message: '地址不能为空', trigger: 'blur' }],
				location: [{ required: true, message: '经纬度不能为空', trigger: 'blur' }],
				address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
				logo: [{ required: true, message: 'Logo不能为空', trigger: 'blur' }],
				banner: [{ required: true, message: '轮播图不能为空', trigger: 'blur' }],
				cover: [{ required: true, message: '主图不能为空', trigger: 'blur' }],
				desc: [
					{ required: true, message: '俱乐部描述不能为空', trigger: 'blur' }
				],
			},
			// 添加活动模板
			activity_add: {
				uid: [
					{ required: true, message: '用户ID不能为空', trigger: 'blur' }
				],
				name: [
					{ required: true, message: '活动名字不能为空', trigger: 'blur' },
					{ min: 4, max: 20, message: '活动名字长度在4到20个字符之间', trigger: 'blur' }
				],
				club_id: [
					{ required: true, message: '俱乐部ID不能为空', trigger: 'blur' }
				],
			},
			// 更新活动模板
			activity_update: {
				uid: [
					{ required: true, message: '用户ID不能为空', trigger: 'blur' }
				],
				name: [
					{ required: true, message: '活动名字不能为空', trigger: 'blur' },
					{ min: 4, max: 20, message: '活动名字长度在4到20个字符之间', trigger: 'blur' }
				],
			},
			// 添加场地
			site_add: {
				images: [{
					required: true,
					message: '图片为必填字段',
					trigger: 'blur'
				}],
				name: [
					{ required: true, message: '名称为必填字段', trigger: 'blur' },
					{ min: 2, max: 20, message: '名称长度在 2 到 20 个字符', trigger: 'blur' }
				],
				address_name: [{
					required: true,
					message: '请选择位置信息',
					trigger: 'blur'
				}],
				location: [{
					required: true,
					message: '请选择位置信息',
					trigger: 'blur'
				}],
				address: [{ required: true, message: '详细地址不能为空', trigger: 'blur' }],
				club_id: [
					{ required: true, message: '俱乐部ID为必填字段', trigger: 'blur' }
				],
			},
			// 更新场地
			site_update: {
				images: [{
					required: true,
					message: '图片为必填字段',
					trigger: 'blur'
				}],
				name: [
					{ required: true, message: '名称为必填字段', trigger: 'blur' },
					{ min: 2, max: 20, message: '名称长度在 2 到 20 个字符', trigger: 'blur' }
				],
				desc: [{
					required: true,
					message: '描述为必填字段',
					trigger: 'blur'
				}],
				location: [{
					required: true,
					message: '请选择位置信息',
					trigger: 'blur'
				}],
				address: [{
					required: true,
					message: '请选择位置信息',
					trigger: 'blur'
				}],
				book: [{
					required: true,
					message: '请选择位置信息',
					trigger: 'blur'
				}],
			},
			// 添加会员卡
			card_add: {
				uid: [
					{ required: true, message: '用户ID不能为空', trigger: 'blur' }
				],
				club_id: [
					{ required: true, message: '俱乐部ID不能为空', trigger: 'blur' }
				],
				name: [
					{ required: true, message: '名称不能为空', trigger: 'blur' },
					{ min: 2, max: 16, message: '名称长度在2到16个字符之间', trigger: 'blur' }
				],
				type: [{
					required: true,
					message: '类型为必填字段',
					trigger: 'blur'
				}],
				value: [
					{ required: true, message: '值为必填字段', trigger: 'blur' }
				],
				day: [
					{ required: true, message: '请输入有效日期', trigger: 'blur' }
				],
				end: [
					{ required: true, message: '结束日期为必填字段', trigger: 'blur' }
				],
				price: [
					{ required: true, message: '价格为必填字段', trigger: 'blur' }
				],
				style_id: [
					{ required: true, message: '样式ID为必填字段', trigger: 'blur' }
				]
			}
		}
	}
	async validate(event, rules) {
		let { data = {}, userInfo, util } = event;
		let { vk } = util;
		let res = vk.pubfn.formValidate({
			data: data,
			rules: rules
		});
		return res;
	}
	/**
	 * 更新俱乐部信息
	 */
	async club_update(event) {
		return this.validate(event, this.rules.club_update);
	}
	/**
	 * 添加活动模板
	 */
	async activity_add(event) {
		return this.validate(event, this.rules.activity_add);
	}
	/**
	 * 更新活动模板
	 */
	async activity_update(event) {
		return this.validate(event, this.rules.activity_update);
	}
	/**
	 * 添加场地
	 */
	async site_add(event) {
		return this.validate(event, this.rules.site_add);
	}
	/**
	 * 更新场地
	 */
	async site_update(event) {
		return this.validate(event, this.rules.site_update);
	}
	/**
	 * 添加会员卡
	 */
	async card_add(event) {
		return this.validate(event, this.rules.card_add);
	}
}
module.exports = new Util