<template>
  <view class="page-content">
    <t-navbar title="俱乐部列表"></t-navbar>
    <t-top>
      <view class="base-search">
        <wd-search hide-cancel placeholder="请输入要搜索的俱乐部名称" v-model="club_name" @search="searchClub"></wd-search>
      </view>
    </t-top>
    <view class="club-list">
      <club-card v-for="item in list" :key="item._id" :value="item"></club-card>
    </view>
    <!-- <t-tabbar></t-tabbar> -->
  </view>
</template>

<script>
import clubCard from "@/components/card/club-card.vue";
export default {
  components: { clubCard },
  data() {
    // 页面数据变量
    return {
      pageCurrent: 0,
      club_name: "",
      list: [],
      pages: 1,
      total: 0,
      loading: false,
    };
  },
  onReachBottom() {
    if (this.list.length >= this.total) return;
    this.pages += 1;
    this.getList();
  },
  onLoad() {
    this.getList();
  },
  // 函数
  methods: {
    async searchClub({ value }) {
      this.club_name = value;
      this.total = 0;
      this.pages = 1;
      this.list = [];
      this.getList();
    },
    async getList() {
      if (this.loading) return;
      this.loading = true;
      try {
        let data = await vk.callFunction({
          url: "client/club/pub/list",
          title: "请求中...",
          data: {
            pageIndex: this.pages,
            name: this.club_name,
          },
        });
        this.total = data.total;
        this.list.push(...data.rows);
      } catch (error) {
        console.log(error);
      } finally {
        this.loading = false;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.page-content {
  min-height: 100vh;
  width: 100%;
  background-color: #f8f8f8;
  box-sizing: border-box;
}

.club-list {
  padding: 30rpx;
}
</style>
