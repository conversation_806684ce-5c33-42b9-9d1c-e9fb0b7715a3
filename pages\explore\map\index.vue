<template>
  <view class="page-content">
    <t-navbar navStyle="custom" :customStyle="{ backgroundColor: 'transparent' }"></t-navbar>
    <view class="header" :style="[headerStyle]">
      <view class="flex">
        <picker class="header__box" mode="selector" range-key="title" :range="filters" @change="changeType">
          <view class="header__block">
            <text class="text">{{ filters[filterIndex].title }}</text>
            <image class="mini-icon" :src="`${iconUrl}down-line.svg`"></image>
          </view>
        </picker>
        <view class="header__box" @click="reloadPosition">
          <view class="header__block">
            <image class="icon" :src="`${iconUrl}location-line.svg`"></image>
          </view>
        </view>
        <view v-if="filters[filterIndex].type == 'site'" class="header__box" @click="filterVisible = true">
          <view class="header__block">
            <image class="icon" :src="`${iconUrl}filter-line.svg`"></image>
          </view>
        </view>
      </view>
      <view class="flex" style="padding-right: 30rpx">
        <view class="header__box theme" @click="confirmPop = true">
          <view class="header__block">
            <wd-icon name="add" size="14px" color="#fff"></wd-icon>
            <text class="text">提供信息</text>
          </view>
        </view>
      </view>
    </view>
    <image class="local-center" :src="`${imgUrl}marker/marker-local.png`" :style="[localStyle]"></image>
    <map
      class="map"
      id="map"
      ref="map"
      show-location
      :scale="15"
      :controls="controls"
      :longitude="initLocation.longitude"
      :latitude="initLocation.latitude"
      :style="[mapStyle]"
      @regionchange="mapMove"
      @markertap="markerClick"
      @callouttap="markerClick"
    ></map>
    <!-- 浮动面板 -->
    <wd-floating-panel
      v-model:height="currentPanelHeight"
      :anchors="floatingPanelHeight"
      :content-draggable="false"
      custom-style="z-index:9"
      @touchmove.stop.prevent
    >
      <view class="panel-content">
        <!-- 搜索框 -->
        <view class="search-container base-search">
          <wd-search v-model="searchVal" hide-cancel placeholder="请输入要搜索的内容" @search="searchData" @clear="clearSearch"></wd-search>
        </view>

        <!-- 内容区域 -->
        <scroll-view class="info-list" scroll-y :style="{ height: panelScrollHeight + 'px' }">
          <view v-if="nodata" class="empty-container">
            <t-empty data="search" :text="searchVal ? '暂无搜索数据' : '标记点附近暂无数据'" class="map-empty-tip" />
            <view class="edit">
              <text class="theme-text" @click="confirmPop = true">点击新增</text>
            </view>
          </view>

          <view class="block" v-for="item in markers" :key="item._id">
            <image class="cover" :src="item.image[0] || '/static/images/no-image.svg'" mode="aspectFill" @click="moveToLocation(item)"></image>
            <view class="info">
              <view class="top" @click="moveToLocation(item)">
                <wd-tag v-if="item.site_type_info" type="primary" :bg-color="item.site_type_info.color" custom-style="margin-right:10rpx;">
                  {{ item.site_type_info.label }}
                </wd-tag>
                <view class="name">{{ item.name }}</view>
              </view>
              <view class="distance" @click="moveToLocation(item)">
                <text v-if="myLocation">距您 {{ item.distance_now || 0 }}km</text>
                <text v-if="mapLocation">距标记点 {{ item.distance_local || 0 }}km</text>
              </view>
              <view class="msg" @click="moveToLocation(item)">
                <text>{{ item.recommend_num || 0 }}人推荐</text>
                <text v-if="item.price">参考价格 {{ item.price }}元</text>
                <text v-if="item.sign_num">{{ item.sign_num }}人已打卡</text>
                <text v-if="item.site_indoor">室内场 {{ item.site_indoor }}片</text>
                <text v-if="item.site_outdoor">室外场 {{ item.site_outdoor }}片</text>
                <text v-if="item.site_practice">学练机 {{ item.site_practice }}台</text>
              </view>
              <view class="action">
                <wd-button type="primary" size="small" @click="viewDetail(item)"> 查看详情 </wd-button>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
    </wd-floating-panel>
    <!-- 选择弹窗 -->
    <wd-action-sheet v-model="confirmPop" :actions="infoActions" @close="confirmPop = false" @select="toSubmitInfo" />

    <!-- 筛选弹窗 -->
    <wd-popup v-model="filterVisible" custom-style="border-radius:32rpx;" @close="filterVisible = false">
      <view class="filter-popup">
        <t-title :custom-style="{ 'text-align': 'center' }">筛选场地</t-title>
        <view class="filter-content">
          <view class="filter-label">场地类型</view>
          <wd-checkbox-group v-model="filterInfo.siteType" shape="square" inline>
            <wd-checkbox v-for="item in siteColumns" :key="item.type" :modelValue="item.type">{{ item.label }}</wd-checkbox>
          </wd-checkbox-group>
          <view class="filter-label">场地服务</view>
          <wd-checkbox-group v-model="filterInfo.siteService" shape="square" inline>
            <wd-checkbox v-for="item in serviceColumns" :key="item.value" :model-value="item.value">{{ item.label }}</wd-checkbox>
          </wd-checkbox-group>
        </view>
        <view class="button-box">
          <wd-button type="info" @click="reset" class="reset-btn"> 重置 </wd-button>
          <wd-button type="primary" @click="filterConfirm" class="confirm-btn"> 确定筛选 </wd-button>
        </view>
      </view>
    </wd-popup>
    <!-- safeArea -->
    <view :style="[safeAreaStyle]"></view>
  </view>
</template>

<script>
import { getLocation } from "@/utils/location";
export default {
  options: {
    styleIsolation: "shared",
  },
  data() {
    return {
      // 系统信息
      windowInfo: uni.getWindowInfo(),
      userInfo: vk.getVuex("$user.userInfo"),
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      imgUrl: vk.getVuex("$app.config.staticUrl.image"),
      // 地图相关
      mapContext: null,
      initLocation: {
        longitude: 108.332073,
        latitude: 22.810267,
      },
      myLocation: null, // 我的定位信息
      mapLocation: null, //标记点的定位信息
      changeBeforeLocation: null, //标记点上次的定位信息
      markers: [],
      controls: [],
      moving: false,
      // 页面信息
      filters: [
        {
          type: "site",
          title: "场地",
        },
        {
          type: "string",
          title: "穿线",
        },
      ],
      infoActions: [
        {
          name: "网球场地",
          type: "site",
        },
        {
          name: "穿线店铺",
          type: "string",
        },
      ],
      filterInfo: {
        siteType: [],
        siteService: [],
      },
      siteColumns: [
        {
          label: "室外场",
          type: "site_outdoor",
        },
        {
          label: "室内场",
          type: "site_indoor",
        },
        {
          label: "学练机",
          type: "site_practice",
        },
      ],
      serviceColumns: [],
      filterIndex: 0,
      confirmPop: false,
      searchVal: "",
      nodata: false,
      filterVisible: false,
      // 浮动面板相关
      floatingPanelVisible: true,
      currentPanelHeight: 0, // 当前面板高度
    };
  },
  onShareAppMessage() {
    return {
      title: `提升球技第一步：找到最近的网球场！`,
      path: `/pages/explore/map/index`,
      imageUrl: `${this.imgUrl}share/map.png`,
    };
  },
  watch: {
    changeBeforeLocation(val) {
      console.log("changeBeforeLocation", val);
    },
  },
  computed: {
    headerStyle() {
      return {
        top: `${this.windowInfo.statusBarHeight + 44 + 10}px`,
      };
    },
    mapStyle() {
      return {
        height: `${this.windowInfo.screenHeight - 50}px`,
      };
    },
    localStyle() {
      if (this.moving) {
        return {
          transform: "translateY(-64rpx)",
        };
      } else {
        return {};
      }
    },
    // 面板滚动区域高度
    panelScrollHeight() {
      const { safeAreaInsets } = this.windowInfo;
      return this.currentPanelHeight - 80 + safeAreaInsets.bottom; // 减去搜索框和指示器的高度
    },
    floatingPanelHeight() {
      const { windowHeight, safeAreaInsets } = this.windowInfo;
      return [90 + safeAreaInsets.bottom, Math.round(0.5 * windowHeight), Math.round(0.8 * windowHeight)];
    },
    safeAreaStyle() {
      return {
        position: "fixed",
        bottom: 0,
        left: 0,
        width: "100%",
        height: this.windowInfo.safeAreaInsets.bottom + "px",
        zIndex: 11,
        backgroundColor: "#fff",
      };
    },
  },
  async onLoad() {
    this.currentPanelHeight = this.floatingPanelHeight[0];
    // 加载图标图片
    try {
      let localIcon = await uni.getImageInfo({
        src: `${this.imgUrl}marker/marker-local.png`,
      });
      let siteIcon = await uni.getImageInfo({
        src: `${this.imgUrl}marker/marker-site.png`,
      });
      let shopIcon = await uni.getImageInfo({
        src: `${this.imgUrl}marker/marker-shop.png`,
      });
      console.log(localIcon);
      this.localIcon = localIcon.path;
      this.siteIcon = siteIcon.path;
      this.shopIcon = shopIcon.path;
    } catch (error) {
      console.log(error);
    } finally {
      this.getAnalysisLocal();
      await this.getSiteServiceColumns();
    }
  },
  methods: {
    async getSiteServiceColumns() {
      try {
        let data = await vk.callFunction({
          url: "client/options/pub/get",
          data: {
            type: "site-service",
          },
        });
        this.serviceColumns = data.rows;
      } catch (error) {
        console.log(error);
      }
    },
    // 搜索内容
    searchData({ value }) {
      this.searchVal = value || this.searchVal;
      this.initMap();
      // 搜索时展开面板到最大高度
      this.$nextTick(() => {
        this.currentPanelHeight = this.floatingPanelHeight[2];
      });
    },
    // 清除搜索
    clearSearch() {
      this.searchVal = "";
      this.initMap();
    },
    // 计算与标记点和我的距离
    async calcMarkersDistance() {
      let mapContext = uni.createMapContext("map", this);
      let center = await mapContext.getCenterLocation();
      let myLocation = this.myLocation;
      this.markers = this.markers.map((item) => {
        if (this.myLocation) item.distance_now = this.calculateDistance(item.latitude, item.longitude, myLocation.latitude, myLocation.longitude);

        item.distance_local = this.calculateDistance(item.latitude, item.longitude, center.latitude, center.longitude);
        return item;
      });
    },
    toSubmitInfo({ item }) {
      vk.navigateTo(`/pages/public/submitInfo?type=${item.type}`);
      this.confirmPop = false;
    },
    reloadPosition() {
      let mapContext = uni.createMapContext("map", this);
      mapContext.moveToLocation();
    },
    moveToLocation(data) {
      let lat = data.latitude;
      let lon = data.longitude;
      if (lat == this.initLocation.latitude && lon == this.initLocation.longitude) {
        lat += 0.0000001;
        lon += 0.0000001;
      }
      this.initLocation = {
        latitude: lat,
        longitude: lon,
      };
    },
    async getAnalysisLocal() {
      try {
        let res = await getLocation();
        this.locationInfo = res;
        vk.setVuex("$app.locationInfo", res);

        this.myLocation = res;
        this.initLocation = res;
        this.mapLocation = res;
        this.changeBeforeLocation = res;
        this.initMap();
      } catch (err) {
        vk.toast("位置授权失败，请点击右上角···授权定位", "none");
        this.mapLocation = this.initLocation;
        this.changeBeforeLocation = this.initLocation;
      } finally {
        this.initMap();
      }
    },
    changeType(e) {
      let index = e.detail.value;
      this.filterIndex = index;
      this.searchVal = "";
      this.initMap();
    },
    markerClick(e) {
      let index = e.detail.markerId;
      let info = this.markers[index];
      if (!info) return;
      let url = null;
      switch (info.type) {
        case "site":
          url = "/pages/explore/site/detail";
          break;
        case "string":
          url = "/pages/explore/string/detail";
          break;
      }
      if (url) vk.navigateTo(`${url}?id=${info._id}`);
    },
    viewDetail(info) {
      if (!info) return;
      let url = null;
      switch (info.type) {
        case "site":
          url = "/pages/explore/site/detail";
          break;
        case "string":
          url = "/pages/explore/string/detail";
          break;
      }
      if (url) vk.navigateTo(`${url}?id=${info._id}`);
    },
    async initMap() {
      if (this.loading) return;
      this.loading = true;
      this.nodata = false;
      try {
        let ary = [];
        this.markers = [];
        // 初始化定位点位置
        let list = await this.getMarkers();
        this.nodata = list.length == 0;
        list.forEach((item, index) => {
          let icon = "localIcon";
          switch (item.type) {
            case "site":
              icon = "siteIcon";
              break;
            case "string":
              icon = "shopIcon";
              break;
            case "club":
              icon = "clubIcon";
              break;
            default:
              icon = "localIcon";
              break;
          }
          let obj = this.getMarkerObj(item.longitude, item.latitude, index, icon, item);
          if (item.site_type) {
            const { site_type } = item;
            if (site_type.length > 1) item.site_type_info = { label: "综合场馆", color: "#fcc800", type: "all" };
            else if (site_type.includes("site")) item.site_type_info = { label: "网球场", color: "", type: "site" };
            else if (site_type.includes("practice")) item.site_type_info = { label: "学练馆", color: "#38b48b", type: "practice" };
          }
          ary.push(obj);
          this.markers.push(item);
          this.calcMarkersDistance();
        });
        // 删除地图点
        let ids = this.markers.map((_, index) => index);
        let mapContext = uni.createMapContext("map", this);
        mapContext.removeMarkers({
          markerIds: ids,
        });
        mapContext.addMarkers({
          markers: ary,
          clear: true,
        });
      } catch (error) {
        console.log(error);
        this.nodata = true;
      } finally {
        this.loading = false;
      }
    },
    reset() {
      this.filterInfo.siteService = [];
      this.filterInfo.siteType = [];
    },
    async filterConfirm() {
      try {
        this.initMap();
      } catch (err) {
        vk.toast(err.msg || "筛选失败");
        console.log(err);
      } finally {
        this.filterVisible = false;
      }
    },
    async getMarkers() {
      try {
        let data = await vk.callFunction({
          url: "client/public/info/pub/markers",
          title: "请求中...",
          data: {
            type: this.filters[this.filterIndex].type,
            location: this.mapLocation,
            filter: {
              site_type: this.filterInfo.siteType,
              service: this.filterInfo.siteService,
            },
            name: this.searchVal,
          },
        });
        return data.data;
      } catch (error) {
        vk.toast("获取数据失败", "none");
      }
    },
    // 地图视图移动
    async mapMove(e) {
      console.log(e);
      try {
        let state = e.detail.type;
        if (state == "begin") {
          this.moving = true;
          clearTimeout(this.moveTimeout);
        } else if (state === "end") {
          this.moveTimeout = setTimeout(async () => {
            this.moving = false;
            let location = e.detail.centerLocation;
            // 判断距离处理请求
            let distance = this.calculateDistance(
              this.changeBeforeLocation.latitude,
              this.changeBeforeLocation.longitude,
              location.latitude,
              location.longitude
            );
            this.mapLocation = location;
            this.calcMarkersDistance();
            if (distance < 5) return;
            this.changeBeforeLocation = location;
            await this.initMap();
          }, 500);
        }
      } catch (error) {
        console.log(error);
      }
    },
    getMarkerObj(lon, lat, id, icon, callout) {
      let obj = {
        id,
        latitude: lat,
        longitude: lon,
      };
      if (callout) obj.callout = this.calloutData(callout);
      if (this[icon]) {
        obj = {
          ...obj,
          iconPath: this[icon],
          width: 20,
          height: 20,
        };
      }
      return obj;
    },
    calloutData(data) {
      return {
        content: data.name,
        fontSize: 14,
        borderRadius: 6,
        bgColor: vk.getVuex("$app.color.primary"),
        color: "#fff",
        padding: 8,
        display: "ALWAYS",
      };
    },
    // 通过经纬度计算距离
    calculateDistance(lat1, lon1, lat2, lon2) {
      const R = 6371; // 地球半径，单位千米
      const dLat = ((lat2 - lat1) * Math.PI) / 180;
      const dLon = ((lon2 - lon1) * Math.PI) / 180;
      const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos((lat1 * Math.PI) / 180) * Math.cos((lat2 * Math.PI) / 180) * Math.sin(dLon / 2) * Math.sin(dLon / 2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
      const distance = R * c; // 距离，单位千米

      return Number(distance.toFixed(2));
    },
  },
};
</script>

<style scoped lang="scss">
.header {
  position: fixed;
  width: 100%;
  z-index: 9;
  left: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  flex-shrink: 0;

  .flex {
    display: flex;
    align-items: center;
  }

  .theme {
    background-color: $primary-color;
    color: #fff;
  }

  &__box {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(4px);
    padding: 14rpx;
    border-radius: 34px;
    margin-left: 30rpx;
  }

  &__block {
    display: flex;
    align-items: center;

    .mini-icon {
      width: 15px;
      height: 15px;
      flex-shrink: 0;
    }

    .icon {
      width: 20px;
      height: 20px;
      flex-shrink: 0;
    }

    .text {
      margin: 0 10rpx;
      font-size: 28rpx;
      font-weight: bold;
      white-space: nowrap;
      text-align: center;
    }

    .multiline {
      @include multiline(1);
    }
  }
}

.map {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  margin: 0 auto;
  width: 100%;
}

.local-center {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  transform: translateY(-60rpx);
  right: 0;
  z-index: 9;
  margin: auto;
  width: 40rpx;
  height: 40rpx;
}

.panel-content {
  padding: 0 30rpx 30rpx;
  box-sizing: border-box;

  .search-container {
    margin-bottom: 20rpx;
  }

  .empty-container {
    padding: 40rpx 0;
    text-align: center;
  }
}

.info-list {
  flex: 1;
  overflow: hidden;

  .edit {
    font-size: 24rpx;
    margin-top: 20rpx;
    color: #8a8a8a;
    text-align: center;

    .theme-text {
      color: #0171bc;
      font-weight: 500;

      &:active {
        opacity: 0.7;
      }
    }
  }

  .block {
    display: flex;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 2rpx solid #f0f0f0;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(1, 113, 188, 0.02);
    }

    &:last-child {
      border-bottom: none;
    }

    .cover {
      width: 180rpx;
      height: 180rpx;
      border-radius: 12rpx;
      flex-shrink: 0;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      &:active {
        transform: scale(0.98);
      }
    }

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      flex: 1;
      margin-left: 30rpx;
      min-height: 180rpx;

      .msg {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        text {
          display: block;
          margin-left: 10rpx;
          padding-left: 10rpx;
          font-size: 24rpx;
          color: #8a8a8a;
          position: relative;

          &:first-child {
            margin-left: 0;
            padding-left: 0;

            &::after {
              content: none;
            }
          }

          &::after {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            margin: auto 0;
            width: 2rpx;
            height: 20rpx;
            background-color: #8a8a8a;
          }
        }
      }

      .distance {
        display: flex;
        align-items: center;
        flex-wrap: wrap;

        text {
          display: block;
          margin-left: 10rpx;
          padding-left: 10rpx;
          font-size: 24rpx;
          position: relative;

          &:first-child {
            margin-left: 0;
            padding-left: 0;

            &::after {
              content: none;
            }
          }

          &::after {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            margin: auto 0;
            width: 2rpx;
            height: 20rpx;
            background-color: #8a8a8a;
          }
        }
      }

      .top {
        display: flex;
        align-items: center;

        .name {
          flex: 1;
          font-size: 28rpx;
          font-weight: bold;
          color: #333;
          transition: color 0.3s ease;
          @include multiline(1);

          &:active {
            color: #0171bc;
          }
        }
      }

      .action {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: 20rpx;
      }
    }
  }
}

// 信息类型选择弹窗样式
.info-type-popup {
  :deep(.wd-popup) {
    .wd-popup__content {
      border-radius: 24rpx;
      overflow: hidden;
    }
  }
}

.select-box {
  padding: 60rpx 40rpx 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;

  .popup-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 40rpx;
    text-align: center;
  }

  .button-group {
    display: flex;
    flex-direction: column;
    gap: 24rpx;
    width: 100%;
  }
}

.filter-popup {
  width: 80vw;
  min-height: 300rpx;
  max-height: 50vh;
  padding: 30rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  .filter-content {
    flex: 1;
    overflow: scroll;
  }
  .filter-label {
    font-size: 28rpx;
    color: #000;
    margin-bottom: 10rpx;
    margin-top: 20rpx;
  }
  .button-box {
    padding-top: 30rpx;
    margin-top: 30rpx;
    display: flex;
    justify-content: space-around;
    gap: 20rpx;
    border-top: 1rpx solid #f0f0f0;
  }
}
</style>
