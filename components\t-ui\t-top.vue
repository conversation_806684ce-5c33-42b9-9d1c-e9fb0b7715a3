<template>
	<view class="filter-top" :style="{ top: `${system.statusBarHeight + 44}px` }">
		<slot></slot>
	</view>
</template>

<script>
	export default {
		name: "t-top",
		data() {
			return {
				system: uni.getSystemInfoSync()
			};
		}
	}
</script>

<style>
	.filter-top {
		position: sticky;
		width: 100%;
		background-color: #fff;
		padding: 10rpx 30rpx;
		box-sizing: border-box;
		z-index: 9;
	}
</style>