<template>
  <view class="string-card" :class="[animationClass]" v-if="stringData">
    <!-- String Content -->
    <view class="string-content" @click="navigateToDetail">
      <image class="string-cover" :src="coverImage" mode="aspectFill" @error="handleImageError" />

      <view class="string-info">
        <view class="string-name">{{ stringData.name }}</view>
        <view class="string-price">参考价格：{{ priceInfo }}</view>
        <view class="string-hours">营业时间：{{ businessHours }}</view>
        <view class="string-contact">联系方式：{{ contactInfo }}</view>
      </view>
    </view>

    <!-- String Footer -->
    <view class="string-footer">
      <view class="distance-info" v-if="hasDistance">
        <text>距您 {{ formattedDistance }}km</text>
      </view>

      <text class="recommend-text">{{ recommendText }}</text>
    </view>
  </view>
</template>

<script>
export default {
  name: "StringCard",

  props: {
    value: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    animateIndex: {
      type: Number,
      default: 0,
    },
    animateNumber: {
      type: Number,
      default: 20,
    },
  },

  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      defaultImage: "/static/images/default-string.png",
    };
  },

  computed: {
    // 穿线数据别名
    stringData() {
      return this.value || {};
    },

    // 动画类名
    animationClass() {
      const index = this.animateIndex + 1;
      const number = this.animateNumber;
      const result = index % number;

      if (result === 0) {
        return `animation${number}`;
      } else {
        return `animation${result}`;
      }
    },

    // 封面图片
    coverImage() {
      if (this.stringData.image?.length > 0) {
        return this.stringData.image[0];
      }
      return `${this.iconUrl}no-image.svg`;
    },

    // 价格信息
    priceInfo() {
      return this.stringData.price || "暂无信息";
    },

    // 营业时间
    businessHours() {
      return this.stringData.open_time || "暂无信息";
    },

    // 联系方式
    contactInfo() {
      if (!this.stringData.contact_info?.length) {
        return "暂无信息";
      }

      const contact = this.stringData.contact_info[0];
      if (!contact) return "暂无信息";

      switch (contact.type) {
        case "phone":
          return `电话 ${contact.value}`;
        case "wechat":
          return `微信号 ${contact.value}`;
        case "public":
          return `公众号 ${contact.value}`;
        case "miniproject":
          return `小程序 ${contact.value}`;
        default:
          return "暂无信息";
      }
    },

    // 距离信息
    hasDistance() {
      return this.stringData.distance !== undefined && this.stringData.distance !== null;
    },

    formattedDistance() {
      return this.stringData.distance?.toFixed(2) || "0.00";
    },

    // 推荐数量
    recommendCount() {
      const value = this.stringData.recommend_num || 0;

      if (value > 10000) {
        return (value / 10000).toFixed(1) + "w";
      } else if (value > 1000) {
        return (value / 1000).toFixed(1) + "k";
      } else {
        return value.toString();
      }
    },

    // 推荐文本
    recommendText() {
      return `${this.recommendCount}人推荐`;
    },
  },

  methods: {
    // 跳转到详情页
    navigateToDetail() {
      if (!this.stringData._id) {
        console.error("String ID is missing");
        vk.toast("穿线信息错误");
        return;
      }

      vk.navigateTo(`/pages/explore/string/detail?id=${this.stringData._id}`);
    },

    // 处理推荐
    async handleRecommend() {
      try {
        const res = await vk.callFunction({
          url: "client/public/info/kh/recommend",
          title: "请求中...",
          data: {
            id: this.stringData._id,
            type: "string",
          },
        });

        // 更新推荐状态
        this.stringData.recommend = res.data;
        this.stringData.recommend_num = res.num;

        const action = res.data ? "推荐" : "取消推荐";
        vk.toast(`${action}成功`);
      } catch (error) {
        console.error("Failed to handle recommend:", error);
        vk.toast("操作失败");
      }
    },

    // 处理图片加载错误
    handleImageError(event) {
      console.warn("String image failed to load:", event);
      event.target.src = this.defaultImage;
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/static/scss/animation.scss";

.string-card {
  opacity: 0;
  background: #fff;
  padding: 30rpx;
  margin-bottom: 20rpx;
  border-radius: 28rpx;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }
}

// String Content
.string-content {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  min-height: 180rpx;
}

.string-cover {
  width: 180rpx;
  height: 180rpx;
  border-radius: 16rpx;
  background: #f8f8f8;
  flex-shrink: 0;
}

.string-info {
  flex: 1;
  margin-left: 30rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 180rpx;

  .string-name {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 16rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .string-price,
  .string-hours,
  .string-contact {
    font-size: 24rpx;
    color: #666;
    margin-bottom: 12rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .string-price {
    color: #0171bc;
    font-weight: 500;
  }
}

// String Footer
.string-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.distance-info {
  font-size: 28rpx;
  font-weight: bold;
  color: #000;
}

.recommend-text {
  font-size: 24rpx;
  color: #999;
}

// Animation Classes
@for $i from 1 to 99 {
  .animation#{$i} {
    animation: fade-in-right 0.8s forwards #{$i * 0.2s};
  }
}

// Custom animations
@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
