'use strict';
/**
 * 检查报名状态
 * @url client/tour/match/kh/checkRegistration 前端调用的url参数地址
 * @description 检查报名状态
 * @param {String} match_id 比赛ID
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { match_id } = data;
    if (!match_id) return { code: -1, msg: '比赛ID不能为空' };
    if (!uid) return { code: -1, msg: '请先登录' };

    // 检查是否已报名
    const registration = await vk.baseDao.findByWhereJson({
      dbName: 'tour-registration',
      whereJson: {
        match_id,
        user_id: uid,
        status: ['in', ['pending', 'approved']]
      }
    });

    res.data = {
      isRegistered: !!registration,
      registration: registration || null
    };
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 