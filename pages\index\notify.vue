<template>
  <view class="page-container">
    <view class="header" :style="{ paddingTop: `${system.statusBarHeight}px` }">
      <t-tabs v-model="tabIndex" :list="tabList" :customStyle="{ height: '40px' }"></t-tabs>
      <view v-if="tabIndex == 0" class="notify-action">
        <image class="icon" :src="`${iconUrl}clean.svg`" @click="readAll"></image>
        <image class="icon" :src="`${iconUrl}delete.svg`" @click="removeRead"></image>
      </view>
    </view>
    <swiper class="swiper-page" :current="tabIndex" :style="{ top: swiperTop, height: swiperHeight }" @change="changTabIndex">
      <swiper-item>
        <scroll-view
          scroll-y
          :show-scrollbar="false"
          :refresher-enabled="pullEnable"
          :refresher-triggered="triggered"
          :refresher-threshold="100"
          @refresherrefresh="onRefresh"
          :style="{ height: swiperHeight }"
          @scrolltolower="loadMore"
        >
          <t-empty v-if="notify.total == 0 && notify.nodata == true" data="news" text="暂无消息"></t-empty>
          <view class="list" v-else>
            <view
              :class="getCardClass(item)"
              v-for="(item, index) in notify.list"
              :key="item._id"
              :style="getCardAnimationStyle(item, index)"
              @click="showDetail(item, index)"
            >
              <view class="top">
                <view class="unread-dot" v-if="!item.read_state"></view>
                <view class="title">{{ item.title }}</view>
              </view>
              <view class="text">{{ item.text }}</view>
              <view class="time">{{ relativeTime(item._add_time) }}</view>
            </view>
            <wd-divider v-if="notify.page > 1 && notify.nodata == true">没有更多了</wd-divider>
          </view>
        </scroll-view>
      </swiper-item>
      <swiper-item>
        <scroll-view
          scroll-y
          :show-scrollbar="false"
          :refresher-enabled="pullEnable"
          :refresher-triggered="triggered"
          :refresher-threshold="100"
          @refresherrefresh="onRefresh"
          :style="{ height: swiperHeight }"
          @scrolltolower="loadMore"
        >
          <t-empty v-if="notice.total == 0 && notice.nodata == true" data="news" text="暂无公告"></t-empty>
          <view class="list" v-else>
            <view
              :class="getCardClass(item, true)"
              v-for="(item, index) in notice.list"
              :key="item._id"
              :style="getCardAnimationStyle(item, index)"
              @click="noticeDetail(item)"
            >
              <view class="top">
                <view class="title">{{ item.title }}</view>
              </view>
              <view class="time">{{ relativeTime(item._add_time) }}</view>
            </view>
            <wd-divider v-if="notice.page > 1 && notice.nodata == true">没有更多了</wd-divider>
          </view>
        </scroll-view>
      </swiper-item>
    </swiper>

    <!-- Message Detail Popup -->
    <wd-popup v-if="detailInfo" v-model="detailPop" position="center" custom-style="border-radius:24rpx">
      <view class="detail-popup">
        <template v-if="detailInfo.type === 'contact'">
          <view class="detail">
            <view class="title" style="margin-bottom: 40rpx;">{{ detailInfo.title || "联系方式申请" }}</view>
            <t-user-item :data="detailInfo.user_info" @click="viewUser(detailInfo.user_info._id)"></t-user-item>
            <view class="text">{{ detailInfo.text || "用户申请添加您的联系方式，是否同意给予" }}</view>
          </view>
          <view class="button-box">
            <template v-if="!detailInfo.contact_status">
              <wd-button block type="primary" @click="agreeContact" custom-style="flex:1">同意给予</wd-button>
              <wd-button block type="error" @click="refuseContact" custom-style="flex:1">拒绝</wd-button>
            </template>
            <template v-else>
              <wd-button
                v-if="detailInfo.contact_status == 'agree'"
                block
                type="primary"
                @click="copyText(detailInfo.copy_text)"
                custom-style="flex:1"
                >复制</wd-button
              >
              <wd-button v-else block type="info" @click="detailPop = false" custom-style="flex:1">关闭</wd-button>
            </template>
          </view>
        </template>
        <template v-else>
          <view class="detail">
            <view class="title">{{ detailInfo.title }}</view>
            <view class="text" v-html="detailInfo.text"></view>
          </view>
          <view class="button-box">
            <wd-button v-if="detailInfo.link" block type="primary" @click="toLink" custom-style="flex:1">前往查看</wd-button>
            <wd-button v-if="detailType != 'notice'" block type="error" @click="removeNews" custom-style="flex:1">删除</wd-button>
            <wd-button v-else block type="info" @click="detailPop = false" custom-style="flex:1">关闭</wd-button>
          </view>
        </template>
      </view>
    </wd-popup>

    <!-- Confirmation Popups -->
    <wd-popup v-model="readAllPopupVisible" position="center" custom-class="confirm-popup">
      <view class="confirm-content">
        <view class="confirm-title">提示</view>
        <view class="confirm-message">是否要已读全部消息？</view>
        <view class="confirm-actions">
          <wd-button type="info" @click="cancelReadAll" class="confirm-btn">取消</wd-button>
          <wd-button type="primary" @click="confirmReadAll" class="confirm-btn">确定</wd-button>
        </view>
      </view>
    </wd-popup>

    <wd-popup v-model="removeReadPopupVisible" position="center" custom-class="confirm-popup">
      <view class="confirm-content">
        <view class="confirm-title">提示</view>
        <view class="confirm-message">是否要删除已读消息？</view>
        <view class="confirm-actions">
          <wd-button type="info" @click="cancelRemoveRead" class="confirm-btn">取消</wd-button>
          <wd-button type="error" @click="confirmRemoveRead" class="confirm-btn">确定</wd-button>
        </view>
      </view>
    </wd-popup>

    <wd-popup v-model="deleteNewsPopupVisible" position="center" custom-class="confirm-popup">
      <view class="confirm-content">
        <view class="confirm-title">删除提示</view>
        <view class="confirm-message">确定要删除该条通知吗？</view>
        <view class="confirm-actions">
          <wd-button type="info" @click="cancelDeleteNews" class="confirm-btn">取消</wd-button>
          <wd-button type="error" @click="confirmDeleteNews" class="confirm-btn">确定</wd-button>
        </view>
      </view>
    </wd-popup>

    <t-tabbar></t-tabbar>
  </view>
</template>

<script>
import dayjs from "dayjs";
export default {
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      pullEnable: false,
      triggered: false,
      userInfo: vk.getVuex("$user.userInfo"),
      locationInfo: vk.getVuex("$app.locationInfo"),
      warningColor: vk.getVuex("$app.color.warning"),
      system: uni.getSystemInfoSync(),
      tabIndex: 0,
      tabList: ["消息", "公告"],
      notify: {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
      },
      notice: {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
        refresh: true,
      },
      detailInfo: null,
      detailIndex: 0,
      detailType: null,
      detailPop: false,
      // 新增的弹窗状态
      readAllPopupVisible: false,
      removeReadPopupVisible: false,
      deleteNewsPopupVisible: false,
    };
  },
  watch: {
    "$store.state.$user.userInfo": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal) {
        this.userInfo = newVal;
      },
    },
    "$store.state.$app.locationInfo": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal) {
        this.locationInfo = newVal;
      },
    },
  },
  computed: {
    swiperTop() {
      const { statusBarHeight } = this.system;
      let base = 60;
      if (this.tabIndex == 0) base += 35;
      return `${statusBarHeight + base}px`;
    },
    swiperHeight() {
      const { statusBarHeight, screenHeight } = this.system;
      let base = 60;
      if (this.tabIndex == 0) base += 35;
      return `${screenHeight - statusBarHeight - base - 78}px`;
    },
  },
  async onShow() {
    await this.getCount();
    this.pullEnable = true;
  },
  onLoad() {
    this.getNotice();
  },
  methods: {
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          vk.toast("复制成功！");
        },
      });
    },
    // 查看用户详情
    viewUser(id) {
      vk.navigateTo(`/pages/user/detail?id=${id}`);
    },
    // 联系方式
    async agreeContact() {
      const { contact_type, user_id } = this.detailInfo;
      try {
        let data = await vk.callFunction({
          url: "client/user/status/kh/agreeContact",
          title: "请求中...",
          data: {
            id: this.detailInfo._id,
            user_id,
            type: contact_type,
          },
        });
        vk.toast(data.msg, "none", () => {
          this.init("notify");
        });
      } catch (err) {
        console.log(err);
      }
    },
    async refuseContact() {
      const { user_id } = this.detailInfo;
      try {
        let data = await vk.callFunction({
          url: "client/user/status/kh/refuseContact",
          title: "请求中...",
          data: {
            id: this.detailInfo._id,
            user_id,
          },
        });
        vk.toast(data.msg, "none", () => {
          this.init("notify");
        });
      } catch (err) {
        console.log(err);
      }
    },
    // 跳转链接
    toLink() {
      this.detailPop = false;
      vk.navigateTo(this.detailInfo.link);
    },
    // 相对时间展示
    relativeTime(time) {
      // 完整参数
      let newStr = vk.pubfn.dateDiff(time);
      if (Date.now() - time > 1000 * 3600 * 24 * 7) {
        newStr = dayjs(time).format("YYYY-MM-DD HH:mm:ss");
      }
      return newStr;
    },
    // 获取卡片动画类名
    getCardClass(item, isNotice = false) {
      const baseClasses = ["card"];
      if (isNotice) {
        baseClasses.push("notice");
      }
      if (item.isNewCard) {
        baseClasses.push("new-card");
      }
      return baseClasses;
    },

    // 获取卡片动画样式
    getCardAnimationStyle(item, index) {
      const isNewCard = item.isNewCard;
      const animationDelay = isNewCard ? `${(index % 5) * 0.1}s` : `${index * 0.08}s`;
      const animationDuration = isNewCard ? "0.5s" : "0.4s";

      return {
        "animation-delay": animationDelay,
        "animation-duration": animationDuration,
      };
    },

    // 加载更多
    async loadMore() {
      switch (this.tabIndex) {
        case 0:
          if (this.notify.nodata) return;
          this.notify.page += 1;
          await this.getNotify();
          break;
        case 1:
          if (this.notice.nodata) return;
          this.notice.page += 1;
          await this.getNotice();
          break;
      }
    },
    // 下拉刷新
    async onRefresh() {
      if (this.triggered) return;
      this.triggered = true;
      if (vk.getVuex("$setting.vibrate")) uni.vibrateShort();
      let init = {
        list: [],
        total: 0,
        nodata: false,
        page: 1,
        refresh: true,
      };
      switch (this.tabIndex) {
        case 0:
          this.notify = init;
          await this.getNotify();
          console.log("获取消息");
          break;
        case 1:
          this.notice = init;
          await this.getNotice();
          break;
      }
      this.triggered = false;
    },
    // 初始化数据
    init(type) {
      this[type].page = 1;
      this[type].list = [];
      this[type].nodata = false;
      this[type].refresh = false;
      switch (type) {
        case "notify":
          this.getNotify();
          break;
        case "notice":
          this.getNotice();
          break;
      }
    },
    // 全部已读
    readAll() {
      this.readAllPopupVisible = true;
    },
    // 取消全部已读
    cancelReadAll() {
      this.readAllPopupVisible = false;
    },
    // 确认全部已读
    async confirmReadAll() {
      this.readAllPopupVisible = false;
      try {
        let data = await vk.callFunction({
          url: "client/public/news/kh/readAll",
          title: "请求中...",
        });
        vk.toast(data.msg, "none", () => {
          this.init("notify");
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 删除已读
    removeRead() {
      this.removeReadPopupVisible = true;
    },
    // 取消删除已读
    cancelRemoveRead() {
      this.removeReadPopupVisible = false;
    },
    // 确认删除已读
    async confirmRemoveRead() {
      this.removeReadPopupVisible = false;
      try {
        let data = await vk.callFunction({
          url: "client/public/news/kh/deleteRead",
          title: "请求中...",
        });
        vk.toast(data.msg, "none", () => {
          this.init("notify");
        });
      } catch (error) {
        console.log(error);
      }
    },
    // 切换标签页
    changTabIndex(e) {
      this.tabIndex = e.detail.current;
    },
    // 删除通知
    removeNews() {
      this.detailPop = false;
      this.deleteNewsPopupVisible = true;
    },
    // 取消删除通知
    cancelDeleteNews() {
      this.deleteNewsPopupVisible = false;
    },
    // 确认删除通知
    async confirmDeleteNews() {
      this.deleteNewsPopupVisible = false;
      try {
        let result = await vk.callFunction({
          url: "client/public/news/kh/delete",
          title: "删除中...",
          data: {
            id: this.detailInfo._id,
          },
        });
        vk.toast(result.msg, "none");
        this.notify.list.splice(this.detailIndex, 1);
        this.notify.total -= 1;
        if (this.notify.list.length == 0) this.notify.nodata = true;
      } catch (error) {
        console.log(error);
      }
    },
    // 显示消息详情
    async showDetail(data, index) {
      this.detailIndex = index;
      this.detailInfo = data;
      this.detailType = "news";
      this.detailPop = true;
      if (!this.notify.list[index].read_state) {
        await this.updateReadState(data._id);
        this.notify.list[index].read_state = true;
      }
    },
    // 跳转至公告详情
    noticeDetail(data) {
      this.detailInfo = data;
      this.detailType = "notice";
      this.detailPop = true;
    },

    // 更新为已读
    async updateReadState(id) {
      try {
        await vk.callFunction({
          url: "client/public/news/kh/updateReadState",
          data: { id },
        });
        getApp().pollingCheckNotify();
      } catch (error) {
        console.log(error);
      }
    },
    // 获取消息数量
    async getCount() {
      try {
        let data = await vk.callFunction({
          url: "client/public/news/kh/get",
          data: {
            accept_user: this.userInfo._id,
            pageSize: 1,
          },
        });
        if (this.notify.total !== data.total) {
          this.notify.page = 1;
          this.notify.list = [];
          this.notify.nodata = false;
          await this.getNotify();
        } else if (data.total === 0) {
          this.notify.nodata = true;
        }
      } catch (error) {
        console.log(error);
      }
    },
    // 获取消息列表
    async getNotify() {
      try {
        let data = await vk.callFunction({
          url: "client/public/news/kh/get",
          title: "请求中...",
          data: {
            accept_user: this.userInfo._id,
            pageIndex: this.notify.page,
            pageSize: 20,
          },
        });
        if (this.notify.page === 1) {
          this.notify.list = data.rows.map((item, index) => ({
            ...item,
            animationIndex: index,
          }));
        } else {
          const startIndex = this.notify.list.length;
          const newItems = data.rows.map((item, index) => ({
            ...item,
            animationIndex: startIndex + index,
            isNewCard: true,
          }));
          this.notify.list.push(...newItems);

          // 延迟移除新卡片标识，确保动画完成
          setTimeout(() => {
            newItems.forEach((item) => {
              item.isNewCard = false;
            });
          }, 500);
        }
        getApp().pollingCheckNotify();
        this.notify.total = data.total;
        this.notify.nodata = this.notify.list.length >= data.total;
        this.notify.refresh = false;
      } catch (error) {
        console.log(error);
      }
    },
    // 获取公告列表
    async getNotice() {
      try {
        let data = await vk.callFunction({
          url: "client/public/news/kh/notice",
          title: this.notice.refresh ? "请求中..." : "",
          data: {
            pageIndex: this.notice.page,
            pageSize: 20,
            sort: [{ name: "_add_time", type: "desc" }],
          },
        });
        if (this.notice.page === 1) {
          this.notice.list = data.rows.map((item, index) => ({
            ...item,
            animationIndex: index,
          }));
        } else {
          const startIndex = this.notice.list.length;
          const newItems = data.rows.map((item, index) => ({
            ...item,
            animationIndex: startIndex + index,
            isNewCard: true,
          }));
          this.notice.list.push(...newItems);

          // 延迟移除新卡片标识，确保动画完成
          setTimeout(() => {
            newItems.forEach((item) => {
              item.isNewCard = false;
            });
          }, 500);
        }
        this.notice.total = data.total;
        this.notice.nodata = this.notice.list.length >= data.total;
        this.notice.refresh = false;
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/static/scss/animation.scss";

.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.swiper-page {
  width: 100%;
  position: relative;
  left: 0;
  background-color: #f8f8f8;
}

.header {
  width: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 9;
  background-color: #fff;
  padding: 10px 30rpx;
  box-sizing: border-box;
}

.record-action {
  padding-top: 20rpx;
}

.notify-action {
  height: 36px;
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  line-height: 1;

  .icon {
    width: 48rpx;
    height: 48rpx;
    margin-left: 40rpx;
  }
}

.list {
  padding: 30rpx;

  .notice {
    max-height: 200rpx;
    overflow: hidden;
    position: relative;
  }

  .card {
    width: 100%;
    overflow: hidden;
    padding: 30rpx;
    background-color: #fff;
    box-sizing: border-box;
    border-radius: 12rpx;
    margin-bottom: 20rpx;
    opacity: 0;
    transform: translateY(30rpx);
    animation: card-slide-in 0.4s ease-out forwards;

    .top {
      display: flex;
      align-items: center;

      .unread-dot {
        width: 14rpx;
        height: 14rpx;
        border-radius: 50%;
        background-color: $warning-color;
        margin-right: 20rpx;
        flex-shrink: 0;
      }

      .title {
        font-size: 32rpx;
        font-weight: bold;
        color: #000;
        @include multiline(1);
      }
    }

    .text {
      margin-top: 20rpx;
      @include multiline(3);
      font-size: 24rpx;
      color: #8a8a8a;
    }
    .time {
      margin-top: 20rpx;
      font-size: 24rpx;
      color: #8a8a8a;
      text-align: end;
    }
  }
}

// Detail Popup Styles
.detail-popup {
  width: 80vw;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  padding: 30rpx;
  box-sizing: border-box;

  .detail {
    overflow-y: scroll;
    flex: 1;
    width: 100%;
    min-height: 300rpx;
    max-height: 50vh;
    box-sizing: border-box;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #000;
    }
    .text {
      font-size: 28rpx;
      color: #333;
      margin-top: 20rpx;
      word-break: break-all;
    }
  }

  .button-box {
    background-color: #fff;
    width: 100%;
    padding: 0 20rpx;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    gap: 30rpx;
    margin-top: 30rpx;
  }
}

// Confirmation Popup Styles
:deep(.confirm-popup) {
  border-radius: 24rpx;
}

.confirm-content {
  padding: 60rpx 40rpx 40rpx;
  text-align: center;

  .confirm-title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 30rpx;
  }

  .confirm-message {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 60rpx;
    line-height: 1.5;
  }

  .confirm-actions {
    display: flex;
    justify-content: space-between;
    gap: 30rpx;

    .confirm-btn {
      flex: 1;
    }
  }
}

.content {
  padding: 30rpx 30rpx 100rpx;
}

// 卡片依次加载动画
@keyframes card-slide-in {
  0% {
    opacity: 0;
    transform: translateY(30rpx) scale(0.95);
  }
  60% {
    opacity: 0.8;
    transform: translateY(-5rpx) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 为新加载的卡片添加特殊动画类
.card.new-card {
  animation: card-slide-in-new 0.5s ease-out forwards;
}

@keyframes card-slide-in-new {
  0% {
    opacity: 0;
    transform: translateY(40rpx) scale(0.9);
  }
  70% {
    opacity: 0.9;
    transform: translateY(-8rpx) scale(1.05);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// 优化动画性能
.card {
  will-change: transform, opacity;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}
</style>
