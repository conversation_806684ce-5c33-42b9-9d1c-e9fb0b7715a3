'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/status/kh/add 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, location, address_name, accept_distance, tags, contact, open } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = 'player-status-data'
		if (location || location.type !== 'Point') {
			location = new db.Geo.Point(location.longitude, location.latitude)
		}
		let id = await vk.baseDao.add({
			dbName,
			dataJson: {
				user_id: uid,
				location,
				address_name,
				accept_distance,
				tags,
				contact,
				open
			}
		});
		res.msg = "保存成功！"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}