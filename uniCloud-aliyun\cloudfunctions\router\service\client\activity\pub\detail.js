'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/detail 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (!id) {
			res.msg = '无活动信息'
			res.code = -1
			return res
		}
		res.data = await vk.baseDao.selects({
			dbName: "activity-data",
			getOne: true,
			getMain: true,
			// 主表where条件
			whereJson: {
				_id: id
			},
			// 主表排序规则
			sortArr: [{ name: "_id", type: "desc" }],
			// 副表列表
			foreignDB: [{
				dbName: "club-data",
				localKey: "club_id",
				foreignKey: "_id",
				as: "club_info",
				limit: 1
			}, {
				dbName: "club-site-data",
				localKey: "site",
				foreignKey: "_id",
				as: "club_site",
				limit: 1
			}, {
				dbName: "club-card-data",
				localKey: "card.id",
				localKeyType: "array",
				foreignKey: "_id",
				as: "card_list",
				limit: 1000
			}, {
				dbName: "activity-join-data",
				localKey: "_id",
				foreignKey: "activity_id",
				as: "join_list",
				limit: 1000,
				whereJson: {
					state: 'joined'
				},
				foreignDB: [{
					dbName: "uni-id-users",
					localKey: "user_id",
					foreignKey: "_id",
					as: "user_info",
					fieldJson: { avatar: true, nickname: true, level: true },
					limit: 1,
				}]
			}, {
				dbName: "uni-id-users",
				localKey: "creator_id",
				foreignKey: "_id",
				as: "creator_info",
				fieldJson: { avatar: true, nickname: true },
				limit: 1,
			}]
		});
		if (!res.data) {
			res.msg = '活动已解散或不存在'
			res.code = -1
			res.back = true
			return res
		}

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}