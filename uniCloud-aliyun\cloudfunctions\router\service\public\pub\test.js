'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url public/test 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		
		let result = await vk.callFunction({
			name: "router",
			url: 'public/pub/sendEmail',
			event,
			data: {
				email:'<EMAIL>',
				title:"审核提醒",
				content:`用户<李华>提交了"site"（网球场）审核信息，请火速审核`
			},
		});
		console.log(result);

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}
