{"bsonType": "object", "required": ["user_id", "match_id", "schedule_id", "points", "type"], "permission": {"read": true, "create": "auth.role == 'admin' || auth.role == 'referee'", "update": false, "delete": "auth.role == 'admin'"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "user_id": {"bsonType": "string", "description": "用户ID"}, "match_id": {"bsonType": "string", "description": "比赛ID"}, "schedule_id": {"bsonType": "string", "description": "赛程ID"}, "points": {"bsonType": "int", "description": "获得积分"}, "type": {"bsonType": "string", "description": "积分类型", "enum": ["win", "lose"]}, "create_date": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}}}