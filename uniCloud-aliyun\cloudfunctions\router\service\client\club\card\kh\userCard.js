'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/card/kh/userCard 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id, pageIndex } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.selects({
			dbName: "club-user-card",
			getCount: false,
			pageIndex,
			pageSize: 30,
			whereJson: {
				user_id: uid,
				club_id,
			},
			foreignDB: [{
				dbName: "club-card-data",
				localKey: "card_id",
				foreignKey: "_id",
				as: "info",
				limit: 1,
				foreignDB: [{
					dbName: "club-data",
					localKey: "club_id",
					foreignKey: "_id",
					as: "club_info",
					limit: 1
				}],
			}],
		});
		// 处理数据结构
		res.rows = res.rows.map(item => {
			item.info.value = item.value
			item.info.start = item.start
			item.info.end = item.end
			return item.info
		})


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}