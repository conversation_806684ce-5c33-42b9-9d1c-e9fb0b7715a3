<template>
  <view>
    <t-navbar title="管理成员" />
    <t-top>
      <view class="filter-top">
        <view class="base-search">
          <wd-search placeholder="请输入成员名称" @search="confirmSearch" style="flex: 1"></wd-search>
        </view>
        <button class="button" @click="actionVisible = true">
          <t-button width="200">新增成员</t-button>
        </button>
      </view>
    </t-top>
    <view class="user-list">
      <view v-for="item in user_list" :key="item._id" class="user-block" @click="userDetail(item)">
        <image class="user-avatar" :src="item.user_info.avatar || item.avatar" mode="aspectFill" />
        <view class="user-info">
          <view class="user-name">{{ item.user_info.nickname || item.nickname }}</view>
          <view class="user-type">
            <text>{{ userRole(item.state) }}</text>
          </view>
        </view>
      </view>
      <l-empty v-if="user_list.length === 0" text="暂无数据" />
    </view>
    <t-modal :show="codeVisible" @close="codeVisible = false" :showCancel="false" confirmText="保存图片" @confirm="saveQRcode">
      <image class="qrcode" :src="codeUrl" mode="widthFix"></image>
    </t-modal>
    <t-actionsheet v-model="actionVisible" :action="action" @click="actionClick"></t-actionsheet>
  </view>
</template>
<script>
import dayjs from "dayjs";
export default {
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      userInfo: vk.getVuex("$user.userInfo"),
      defaultAvatar: "",
      user_list: [],
      hasMore: true,
      searchValue: "",

      codeVisible: false,
      codeUrl: "",

      typeOptions: [
        { label: "全部", value: "全部" },
        { label: "期限卡", value: "date" },
        { label: "次卡", value: "times" },
        { label: "储蓄卡", value: "save" },
      ],
      actionVisible: false,
      action: [
        {
          label: "邀请用户",
          value: "invite",
          share: true,
        },
        {
          label: "新增用户",
          value: "new",
        },
      ],
    };
  },

  onShareAppMessage(e) {
    if (e.from == "button") {
      return {
        title: `${this.userInfo.nickname}邀请您加入${this.club_info.name}`,
        imageUrl: this.club_info.cover,
      };
    }
  },
  onReachBottom() {
    if (!this.hasMore) return;
    this.pages += 1;
    this.getData();
  },
  async onLoad() {
    this.club_id = vk.getVuex("$club.id");
    this.club_info = await vk.vuex.getters("$club/getInfo");
    this.init();
  },
  methods: {
    actionClick(type) {
      switch (type) {
        case "new":
          uni.navigateTo({
            url: "/pages_club/views/member/add",
          });
          break;
      }
      this.actionVisible = false;
    },
    userRole(state) {
      switch (state) {
        case "applying":
          return "审核中";
        case "member":
          return "普通成员";
        case "admin":
          return "主理人";
        case "manage":
          return "管理员";
        case "coach":
          return "教练";
        default:
          return "普通成员";
      }
    },
    init() {
      this.pages = 1;
      this.hasMore = true;
      this.getData();
    },
    async getData() {
      let data = await vk.callFunction({
        url: "client/club/member/kh/list",
        title: "请求中...",
        data: {
          pageIndex: this.pages,
          club_id: this.club_id,
          name: this.searchValue,
        },
      });
      this.hasMore = data.hasMore;
      this.user_list.push(...data.rows);
    },
    confirmSearch({ value }) {
      this.searchValue = value;
      this.init();
    },
    userDetail(data) {
      uni.navigateTo({
        url: `/pages_club/views/member/detail?id=${data.user_id}`,
      });
    },
  },
};
</script>
<style scoped lang="scss">
.qrcode {
  width: 100%;
  height: 100%;
}

.filter-top {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .button {
    margin: 0 0 0 20rpx !important;
    flex-shrink: 0;
    padding: 0;
    border: none;
    background-color: #fff;

    &::after {
      content: none;
    }

    image {
      width: 92rpx;
      height: 92rpx;
    }
  }
}

.user {
  &-list {
    padding: 0 30rpx;
  }

  &-block {
    display: flex;
    padding: 20rpx 0;
    box-sizing: border-box;
    border-bottom: 2rpx solid #f8f8f8;
    position: relative;
  }

  &-avatar {
    width: 128rpx;
    height: 128rpx;
    border-radius: 14rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
    background-color: #eee;
  }

  &-name {
    font-size: 36rpx;
    color: #000;
    font-weight: bold;
  }

  &-type {
    font-size: 28rpx;
    color: #333;
  }

  &-remark {
    font-size: 24rpx;
    color: #ccc;
  }

  &-level {
    position: absolute;
    right: 20rpx;
    top: 30rpx;
    font-size: 28rpx;
    color: #ff3030;
  }
}
</style>
