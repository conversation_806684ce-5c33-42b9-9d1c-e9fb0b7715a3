'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/status/kh/applyContact 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, user_id, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		if (!uid) return { code: -1, msg: "请先登录" }

		if (!user_id || !type) return { code: -1, msg: "参数错误" }
		// 查找是否已经申请过
		let info = await vk.baseDao.findByWhereJson({
			dbName: "user-news-data",
			whereJson: {
				"accept_user": user_id,
				"user_id": uid,
				"type": "contact",
				"contact_type": type,
			},
		});
		if (info) return { code: -1, msg: '已申请，请等待回应' }

		// 查看是否已经是成功申请的
		let white = await vk.baseDao.findByWhereJson({
			dbName: "contact-white-list",
			whereJson: {
				user_id: uid,
				target_user_id: user_id,
				type
			},
		});
		
		if (white) return { code: 0, msg: '', copy: true }

		// 发送申请
		let contactText = '联系方式'
		switch (type) {
			case 'phone':
				contactText = '手机号'
				break;
			case 'wechat':
				contactText = '微信号'
				break;
			default:
				contactText = '联系方式'
				break;
		}

		await vk.baseDao.add({
			dbName: "user-news-data",
			dataJson: {
				"accept_user": user_id,
				"title": "联系方式申请",
				"text": `用户申请获取您的${contactText}，是否同意给予`,
				"contact_type": type,
				"read_state": false,
				"type": "contact",
				"user_id": uid
			}
		});
		res.msg = '申请已发送'

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}