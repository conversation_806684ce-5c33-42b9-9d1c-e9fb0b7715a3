'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/pub/signCount 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, id, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let dbName = 'sign-info-list'
		let today = vk.pubfn.timeFormat(new Date(), "yyyy-MM-dd");

		let sign_num = await vk.baseDao.count({
			dbName,
			whereJson: {
				data_id: id,
				type,
			},
			groupJson: {
				_id: "$user_id",
				count: $.sum(1),
			},
		});

		let today_num = await vk.baseDao.count({
			dbName,
			whereJson: {
				data_id: id,
				type,
				date: today
			},
			groupJson: {
				_id: "$_id",
				count: $.sum(1),
			},
		});

		res.msg = '获取成功'
		res.data = {
			sign_num,
			today_num
		}

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}