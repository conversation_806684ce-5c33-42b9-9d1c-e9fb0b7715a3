'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/news/kh/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, pageIndex = 1, pageSize = 20 } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.selects({
			dbName: "user-news-data",
			getCount: true,
			pageIndex,
			pageSize,
			whereJson: {
				accept_user: uid
			},
			fieldJson: {},
			sortArr: [{ name: "_id", type: "desc" }],
			foreignDB: [{
				dbName: "uni-id-users",
				localKey: "user_id",
				foreignKey: "_id",
				as: "user_info",
				limit: 1,
				fieldJson: { avatar: true, nickname: true, level: true },
			}]
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}