const vkPay = require("vk-uni-pay");
'use strict';
module.exports = {
	/**
	 * 重新支付订单
	 * @url client/order/kh/repay 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, out_trade_no } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let info = await vk.baseDao.findByWhereJson({
			dbName: "vk-pay-orders",
			whereJson: {
				out_trade_no
			},
		});
		if (!info || Date.now() > info.create_date + 1000 * 900) {
			res.code = -1
			res.msg = '订单不存在或已过期'
			return res
		}
		let custom = {}
		switch (info.type) {
			case "card":
				custom = {
					card_id: info.card_id,
					club_id: info.club_id
				}
				break;
		}
		res = await vkPay.createPayment({
			context: originalParam.context,
			provider: "wxpay",
			data: {
				openid: info.openid,
				out_trade_no: info.out_trade_no,
				total_fee: info.total_fee, // 订单金额（单位分 100 = 1元）
				subject: info.description,
				type: info.type,
				user_id: info.user_id,
				nickname: info.nickname,
				custom,
				time_expire: info.create_date + 1000 * 900
			},
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}