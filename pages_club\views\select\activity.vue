<template>
	<view>
		<t-navbar title="选择活动"></t-navbar>
		<view class="list">
			<view class="content" v-for="(item,index) in list" :key="item._id" @click="checked(index)">
				<view class="name">{{item.name}}</view>
				<image v-show="item.checked" class="checked"
					src="https://cdn.cometennis.cn/icon/check-on.svg"
					mode="aspectFill"></image>
			</view>
		</view>
	</view>
</template>

<script>
	import mixins from '@/pages_club/mixins.js'
	export default {
		mixins: [mixins],
		data() {
			return {
				list: [],
				ids: [],
			}
		},
		onLoad(options) {
			if (options.id) this.id = options.id
			this.getData()
		},
		methods: {
			async getData() {
				const res = await vk.callFunction({
					url: 'client/club/activity/kh/list',
					data: {
						club_id: this.club_id
					}
				})
				this.list = res.rows.map(item => {
					item.checked = this.id == item._id
					return item
				})
			},
			checked(index) {
				this.list.forEach(item => {
					item.checked = false
				})
				this.list[index].checked = !this.list[index].checked
				this.returnResult()
			},
			returnResult() {
				let list = vk.pubfn.copyObject(this.list);
				let info = list.find(item => {
					return item.checked
				})
				uni.$emit('select-activity', info)
				vk.navigateBack();
			},
		}
	}
</script>

<style scoped lang="scss">
	.content {
		width: 100%;
		height: 128rpx;
		padding: 30rpx;
		border-bottom: 2rpx solid #f8f8f8;
		display: flex;
		align-items: center;
		justify-content: space-between;
		box-sizing: border-box;
	}

	.checked {
		width: 48rpx;
		height: 48rpx;
		border-radius: 50%;
	}
</style>