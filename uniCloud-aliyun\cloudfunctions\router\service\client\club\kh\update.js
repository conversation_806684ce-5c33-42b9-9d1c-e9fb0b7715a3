const formRules = require("../util/formRules.js");
const Log = require("../../utils/createLog.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/kh/update 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, _id, name, location, address, address_name, logo, banner, cover, desc, images, apply, startTime, endTime } = data;
		let res = {
			code: 0,
			msg: ""
		}; // 业务逻辑开始-----------------------------------------------------------
		if (vk.pubfn.isNullOne(_id)) {
			return { code: -1, msg: '_id不能为空' };
		}
		let formRulesRes = await formRules.club_update(event);
		if (formRulesRes.code !== 0) {
			return formRulesRes;
		}

		let info = await vk.baseDao.findById({
			dbName: "club-data",
			id: _id,
		});
		if (!location.type || location.type !== 'Point') location = new db.Geo.Point(location.longitude, location.latitude)
		// 执行数据库API请求
		res.data = await vk.baseDao.updateById({
			dbName: "club-data",
			id: _id,
			getUpdateData: true,
			dataJson: {
				name,
				location,
				address,
				logo,
				banner,
				cover,
				desc,
				images,
				apply,
				startTime,
				endTime
			}
		});
		Log.update_club(event, info)
		res.msg = "编辑成功!"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}