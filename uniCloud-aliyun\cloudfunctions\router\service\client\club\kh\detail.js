'use strict';
module.exports = {
	/**
	 * 查询俱乐部详情数据
	 * @url client/club/kh/detail 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		// 数据结构
		let keys = [
			{ "key": "name", "default": "" },
			{ "key": "logo", "default": "" },
			{ "key": "cover", "default": "" },
			{ "key": "banner", "default": [] },
			{ "key": "desc", "default": "" },
			{ "key": "location", "default": "" },
			{ "key": "images", "default": [] },
			{ "key": "address_name", "default": "" },
			{ "key": "address", "default": "" },
			{ "key": "apply", "default": true },
			{ "key": "startTime", "default": "" },
			{ "key": "endTime", "default": "" }
		]

		let info = await vk.baseDao.findById({
			dbName: "club-data",
			id,
		});
		keys.forEach(item => {
			if (!JSON.stringify(info[item.key])) info[item.key] = item.default
		})
		res.data = info

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}