'use strict';
module.exports = {
	/**
	 * 结算活动的费用至活动发起人
	 * @url client/timerTask/pub/settlementActivity 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, token, now } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (token !== 'du6AVGV61YTCoqbvMUzF7QgY5Z1uExOd') return { code: -1, msg: "未知错误" }
		// 查找需要结算并且已结束的活动
		let list = await vk.baseDao.select({
			dbName: "activity-data",
			getMain: true,
			pageIndex: 1,
			pageSize: 20,
			whereJson: {
				settlement: 1,
				end: _.lte(now)
			},
			sortArr: [{ name: "_add_time", type: "asc" }],
		});
		console.log("list", list);
		if (!list || list.length == 0) return { code: 0, msg: "没有要处理的数据" }
		let ids = list.map(item => item._id)
		// 查找活动相关的支付订单
		let orders = await vk.baseDao.select({
			dbName: "vk-pay-orders",
			getMain: true,
			pageIndex: 1,
			pageSize: 20,
			whereJson: {
				activity_id: _.in(ids),
				type: "activity",
				status: _.in([1, 2])
			},
		});

		// 开启事务
		const transaction = await vk.baseDao.startTransaction();
		// 轮询结算相应活动
		for (const activity of list) {
			let order_list = orders.filter(item => item.activity_id === activity._id)
			let total = 0
			order_list.forEach(item => {
				let fee = item.total_fee
				if (item.refund_fee) fee = fee - item.refund_fee
				total += fee
			})

			// 查询发起人的钱包
			let wallet = await vk.baseDao.findByWhereJson({
				dbName: "user-wallet",
				whereJson: {
					user_id: activity.creator_id
				},
			});

			// 开启结算记录变更事务
			const transaction = await vk.baseDao.startTransaction();
			try {
				// 如果没有可结算的款项就不执行钱包操作
				if (total !== 0) {
					// 获得锁的实例
					const lockManage = vk.getReentrantLockManage({
						id: `$walletBy${activity.creator_id}`, // 锁的id
						timeout: 600, // 锁的超时时间，超时后强制归还锁，单位秒，0代表永不超时，默认5秒
					});

					// 尝试获取锁
					let lock = await lockManage.lock();
					if (!lock) continue
					// 没有钱包就创建钱包信息并且添加数据
					if (!wallet) {
						await vk.baseDao.add({
							db: transaction,
							dbName: "user-wallet",
							dataJson: {
								user_id: activity.creator_id,
								withdrawn: 0,
								balance: total,
								update_time: now
							}
						});
					} else {
						let wallet_total = wallet.balance + total
						await vk.baseDao.updateById({
							db: transaction,
							dbName: "user-wallet",
							id: wallet._id,
							dataJson: {
								balance: wallet_total,
								update_time: now
							},
							getUpdateData: false
						});
					}
					// 添加钱包记录
					let wallet_balance = wallet ? wallet.balance : 0
					await vk.baseDao.add({
						db: transaction,
						dbName: "user-wallet-record",
						dataJson: {
							user_id: activity.creator_id,
							description: "活动线上收款结算",
							type: "activity",
							activity_id: activity._id,
							status: 1,
							balance: total,
							before: wallet_balance,
							after: wallet_balance + total,
						}
					});
				}

				// 更新活动的结算状态
				await vk.baseDao.updateById({
					db: transaction,
					dbName: "activity-data",
					id: activity._id,
					dataJson: {
						settlement: 2
					},
					getUpdateData: false
				});
				// 提交事务
				await transaction.commit();
				console.log(`transaction succeeded`);
			} catch (err) {
				// 事务回滚
				return await vk.baseDao.rollbackTransaction({
					db: transaction,
					err
				});
			} finally {
				await lock.unlock();
			}
		}
		res.data = ids
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}