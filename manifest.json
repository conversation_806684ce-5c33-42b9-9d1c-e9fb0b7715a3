{"name": "tennis-client", "appid": "__UNI__84F0CDF", "description": "", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": true, "waiting": true, "autoclose": true, "delay": 0}, "modules": {}, "distribute": {"android": {"permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>", "<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"]}, "ios": {}, "sdkConfigs": {"oauth": {}, "maps": {}}}}, "quickapp": {}, "mp-weixin": {"appid": "wx6046522bc39080dd", "requiredPrivateInfos": ["getLocation", "onLocationChange", "startLocationUpdateBackground", "<PERSON><PERSON><PERSON><PERSON>", "chooseLocation"], "setting": {"urlCheck": false, "es6": false, "postcss": true, "minified": true, "checkSiteMap": false}, "usingComponents": true, "lazyCodeLoading": "requiredComponents", "permission": {"scope.userLocation": {"desc": "你的位置信息将用于获取附近的活动信息或场地信息"}}, "__usePrivacyCheck__": true, "libVersion": "latest"}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true, "setting": {"urlCheck": false}}, "mp-toutiao": {"usingComponents": true, "setting": {"urlCheck": false, "minified": true, "postcss": true, "es6": false}}, "uniStatistics": {"enable": false}, "h5": {"title": "", "template": "template.h5.html", "devServer": {"disableHostCheck": true}}, "vueVersion": "3", "mp-qq": {"setting": {"urlCheck": false, "minified": true, "postcss": true, "es6": false}}, "networkTimeout": {"request": 60000, "connectSocket": 60000, "uploadFile": 60000, "downloadFile": 60000}}