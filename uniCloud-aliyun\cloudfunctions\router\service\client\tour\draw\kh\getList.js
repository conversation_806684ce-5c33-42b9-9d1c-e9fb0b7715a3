'use strict';
/**
 * 获取签表列表
 * @url client/tour/draw/kh/getList 前端调用的url参数地址
 * @description 获取签表列表
 * @param {String} match_id 比赛ID
 * @param {String} round 轮次
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { match_id, round } = data;
    if (!match_id) return { code: -1, msg: '比赛ID不能为空' };
    if (!round) return { code: -1, msg: '轮次不能为空' };

    const dbName = 'tour-draw';
    const whereObj = {
      match_id,
      round
    };

    const result = await vk.baseDao.select({
      dbName,
      whereObj,
      sortArr: [{ "name": "position", "type": "asc" }]
    });

    res.data = result.rows;
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 