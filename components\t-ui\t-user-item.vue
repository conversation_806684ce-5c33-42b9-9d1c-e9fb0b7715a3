<template>
  <view class="t-user-item" @click="$emit('click')" :style="[customStyle, { width }]">
    <view class="t-user-item__avatar">
      <image :src="data.avatar" mode="aspectFill"></image>
      <view class="t-user-item__level">{{ levelText }}</view>
    </view>
    <view v-if="typeText" class="t-user-item__type">{{ typeText }}</view>
    <view class="t-user-item__name">{{ nickname }}</view>
  </view>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    type: {
      type: String,
    },
    name: {
      type: String,
    },
    width: {
      type: String,
      default: "160rpx",
    },
    customStyle: {
      type: Object,
	  default: () => {},
    },
  },
  computed: {
    typeText() {
      let text = "";
      switch (this.type) {
        case "help":
          text = "代接";
          break;
        case "myself":
          text = "";
          break;
        default:
          text = this.type;
          break;
      }
      return text;
    },
    levelText() {
      let level = this.data.level;
      if (level) {
        return level.toFixed(1);
      } else {
        return "无";
      }
    },
    nickname() {
      return this.name ? this.name : this.data.nickname;
    },
  },
  data() {
    return {};
  },
};
</script>

<style scoped lang="scss">
.t-user-item {
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  position: relative;

  &__type {
    font-size: 20upx;
    color: #fff;
    position: absolute;
    top: 0;
    right: 0;
    background-color: $primary-color;
    border-radius: 10rpx 10rpx 10rpx 0;
    padding: 4rpx 10rpx;
  }

  &__avatar {
    width: 100rpx;
    height: 100rpx;
    position: relative;
    border-radius: 12rpx;

    image {
      width: 100%;
      height: 100%;
      border-radius: 50%;
    }
  }

  &__level {
    position: absolute;
    right: 20rpx;
    bottom: -15rpx;
    width: 60rpx;
    text-align: center;
    border-radius: 68rpx;
    font-size: 24rpx;
    font-weight: bold;
    color: #fff;
    background-color: $primary-color;
  }

  &__name {
    width: 100%;
    font-size: 28rpx;
    margin-top: 20rpx;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    line-height: 2;
  }
}
</style>
