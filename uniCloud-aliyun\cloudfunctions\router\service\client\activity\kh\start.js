const formRules = require("../util/formRules.js");

'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/activity/kh/start 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let {
			uid,
			name, // 接龙名称
			desc, // 接龙描述
			start, // 开始时间
			end, // 结束时间
			club_id, // 俱乐部活动-俱乐部id
			site_info, // 自定义场地信息
			min_num, // 最少活动人数
			max_num, // 最多活动人数
			dissolve_hour, // 自动解散时间
			auto_dissolve = false, // 自动解散
			cost_type, // 花费类型 0 aa 1 人均
			cost, // 接龙花费
			cost_plan, // 收费方式
			refund_hour, // 退款规则
			refund_money, // 退款规则
			level, // 水平要求
			apply = false, // 是否需要审批
			open = false, // 是否公开
			notice = true, // 订阅通知
			help_join = true, // 允许代接
			standby = true, // 人员候补
			contact,
		} = data;
		let res = {
			code: 0,
			msg: ""
		};
		let formRulesRes = await formRules.add(event);
		if (formRulesRes.code !== 0) { return formRulesRes; }
		// 检验违规内容
		if (name) {
			let msgSecCheckRes = await vk.openapi.weixin.security.msgSecCheck({
				content: name, // 文本内容，不可超过500KB
				openid: userInfo.wx_openid["mp-weixin"], // 用户的小程序openid
				scene: 2, // 场景值（建议为2或3）
				version: 2, // 接口版本号（建议为2）
			});
			if (msgSecCheckRes.code != 0) return { code: -1, msg: '活动名称涉嫌违规，请重新设置' }
		}
		if (desc) {
			let msgSecCheckRes = await vk.openapi.weixin.security.msgSecCheck({
				content: desc, // 文本内容，不可超过500KB
				openid: userInfo.wx_openid["mp-weixin"], // 用户的小程序openid
				scene: 2, // 场景值（建议为2或3）
				version: 2, // 接口版本号（建议为2）
			});
			if (msgSecCheckRes.code != 0) return { code: -1, msg: '活动描述涉嫌违规，请重新设置' }
		}
		// 业务逻辑开始-----------------------------------------------------------
		let type = 'normal' // 判断是否是俱乐部开启的接龙
		if (club_id) type = 'club'
		// 如果为0设置为空
		if (max_num == 0) max_num = ""
		let dataJson = {
			name,
			desc,
			start,
			end,
			type,
			club_id,
			creator_id: uid,
			dissolve_hour, // 自动解散时间
			auto_dissolve, // 自动解散
			min_num,
			max_num,
			site_info,
			cost_type,
			cost,
			cost_plan,
			refund_hour,
			refund_money,
			level,
			apply,
			open,
			notice,
			help_join,
			standby,
			contact,
			location: new db.Geo.Point(site_info.location.longitude, site_info.location.latitude),
			settlement: -1,
		}
		// 字段转数值
		dataJson = pubFun.fieldsToNumber(dataJson, ["min_num", "max_num", "cost", "refund_hour", "refund_money", "level"])
		// 判断是否是线上收款，如果是的话则设置结算状态
		if (dataJson.cost_plan === 1) dataJson.settlement = 0
		let id = await vk.baseDao.add({
			dbName: "activity-data",
			dataJson,
		});
		await vk.baseDao.add({
			dbName: "activity-record-data",
			dataJson: {
				info_id: id,
				user_id: uid,
				activity_id: id,
				type: "created"
			}
		});
		res.msg = "新增成功"
		// 判断如果是线上收款的话添加待处理任务

		// 活动公开的话发布订阅消息
		// if (open) {
		// 	let users = await vk.baseDao.selects({
		// 		dbName: "user-follow-data",
		// 		getCount: false,
		// 		pageIndex: 1,
		// 		pageSize: 1000,
		// 		whereJson: {
		// 			target_user_id: uid
		// 		},
		// 		foreignDB: [{
		// 			dbName: "uni-id-users",
		// 			localKey: "user_id",
		// 			foreignKey: "_id",
		// 			as: "user_info",
		// 			fieldJson: { wx_openid: true },
		// 			limit: 1
		// 		}],
		// 	});
		// 	console.log("关注的人", users);
		// 	// 发布订阅消息
		// 	let start_time = vk.pubfn.timeFormat(start, "yyyy-MM-dd hh:mm:ss");
		// 	let requests = []
		// 	users.rows.forEach(item => {
		// 		if (!item.user_info.wx_openid['mp-weixin']) return
		// 		requests.push(vk.openapi.weixin.subscribeMessage.send({
		// 			touser: item.user_info.wx_openid['mp-weixin'],
		// 			template_id: "_Cvh4LoC_6ZJcaADaoB0-k4y86H02dLxIzoIZUF55BI",
		// 			page: `pages/activity/detail?id=${id}`,
		// 			data: {
		// 				thing4: {
		// 					value: userInfo.nickname
		// 				},
		// 				thing3: {
		// 					value: name
		// 				},
		// 				time5: {
		// 					value: start_time
		// 				},
		// 				thing7: {
		// 					value: site_info.name
		// 				},
		// 			},
		// 			miniprogram_state: "formal",
		// 		}))
		// 	})
		// 	Promise.all(requests).then(res => {
		// 		console.log("发送完毕", res);
		// 	}).catch(error => {
		// 		console.log("发送失败", error);
		// 	})
		// }

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}