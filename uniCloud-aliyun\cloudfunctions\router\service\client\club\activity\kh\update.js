const formRules = require("../../util/formRules.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/activity/kh/update 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let {
			_id,
			uid,
			name,
			desc,
			coach,
			card,
			site,
			max_num,
			min_num,
			apply,
			open,
			level,
		} = data;
		let res = { code: 0, msg: "" };
		console.log(data);
		// 业务逻辑开始-----------------------------------------------------------
		if (vk.pubfn.isNullOne(_id)) {
			return { code: -1, msg: '_id不能为空' };
		}
		let formRulesRes = await formRules.activity_update(event);
		if (formRulesRes.code !== 0) {
			return formRulesRes;
		}
		// 执行数据库API请求
		await vk.baseDao.updateById({
			dbName: "club-activity-data",
			id: _id,
			dataJson: {
				name,
				desc,
				coach,
				card,
				site,
				max_num,
				min_num,
				apply,
				open,
				level,
				last_update: uid,
				last_update_time: new Date().valueOf()
			}
		});
		res.msg = "编辑成功!"
		return res;
	}
}