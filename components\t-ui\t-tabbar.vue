<template>
  <view>
    <view class="t-tabbar">
      <view class="t-tabbar__block" v-for="(item, index) in list" :key="index">
        <view class="t-tabbar__notify_dot" v-if="item.label == '通知' && hasNotify"></view>
        <image
          class="t-tabbar-icon"
          :class="{ main: !item.isTab, active: item.url.includes(currentPage) || !item.isTab }"
          :src="item.icon"
          mode="heightFix"
          @click="toPage(item)"
        />
        <text class="t-tabbar-text">{{ item.label }}</text>
      </view>
    </view>
    <view class="placeholder"></view>
  </view>
</template>

<script>
export default {
  name: "t-tabbar",
  data() {
    return {
      hasNotify: vk.getVuex("$user.hasNotify"),
    };
  },
  computed: {
    currentPage() {
      const pages = getCurrentPages();
      return pages && pages[0] && pages[0].route;
    },
    list() {
      return [
        { icon: `/static/images/tabbar-home.svg`, label: "首页", url: "/pages/index/index", isTab: true, needLogin: false },
        { icon: `/static/images/tabbar-activity.svg`, label: "约球", url: "/pages/activity/index", isTab: true, needLogin: false },
        { icon: `/static/images/tabbar-add.svg`, url: "/pages/activity/start", isTab: false, needLogin: true },
        { icon: `/static/images/tabbar-notify.svg`, label: "通知", url: "/pages/index/notify", isTab: true, needLogin: true },
        { icon: `/static/images/tabbar-user.svg`, label: "我的", url: "/pages/user/index", isTab: true, needLogin: true },
      ];
    },
  },
  watch: {
    "$store.state.$user.hasNotify": {
      deep: true,
      handler: function (newVal) {
        this.hasNotify = newVal;
      },
    },
  },
  mounted() {
    uni.hideTabBar();
  },
  methods: {
    toPage(info) {
      if (info.isTab) {
        vk.switchTab(info.url);
      } else {
        vk.navigateTo(info.url);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.t-tabbar {
  width: 100%;
  height: 68px;
  border-radius: 12rpx 12rpx 0 0;
  padding-bottom: 10px;
  box-shadow: 0 0 12rpx rgba(0, 0, 0, 0.08);
  background-color: #fff;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-around;

  &__block {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    position: relative;
  }

  &-icon {
    width: 48rpx;
    height: 48rpx;
    opacity: 0.3;
  }

  &-text {
    font-size: 24rpx;
    opacity: 0.3;
  }

  .active {
    opacity: 1;
  }
  &__notify_dot {
    position: absolute;
    top: -6rpx;
    right: -6rpx;
    width: 16rpx;
    height: 16rpx;
    border-radius: 50%;
    background-color: #ff3030;
  }
  .main {
    width: 80rpx;
    height: 80rpx;
  }
}

.placeholder {
  height: 128rpx;
  width: 100%;
}
</style>
