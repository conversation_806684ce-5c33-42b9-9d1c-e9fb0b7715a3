'use strict';
exports.main = async (event, context) => {
	let res = { code: 0, msg: "" };

	/**
	 * 平摊费用的活动退款
	 */
	const now = Date.now()
	await uniCloud.callFunction({
		name: "router",
		data: {
			// 需要执行的云函数路径
			$url: "client/timerTask/pub/settlementRefund",
			// 请求参数，这里加个key可以有效防止云函数被直接访问，云函数中加判断条件，如果key不是666666，则不运行。
			data: {
				token: "du6AVGV61YTCoqbvMUzF7QgY5Z1uExOd",
				now,
			}
		}
	});

	/**
	 * 结算活动费用至用户
	 */
	await uniCloud.callFunction({
		name: "router",
		data: {
			// 需要执行的云函数路径
			$url: "client/timerTask/pub/settlementActivity",
			// 请求参数，这里加个key可以有效防止云函数被直接访问，云函数中加判断条件，如果key不是666666，则不运行。
			data: {
				token: "du6AVGV61YTCoqbvMUzF7QgY5Z1uExOd",
				now
			}
		}
	});
	return res;
};