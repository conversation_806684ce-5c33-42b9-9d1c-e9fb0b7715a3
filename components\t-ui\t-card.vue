<template>
  <view class="t-card" :style="[customStyle]" @click="click">
    <slot v-if="custom" />
    <template v-else>
      <view v-if="label && !$slots.label" class="t-card__label">
        <view class="text">{{ label }}</view>
        <slot name="label_right"></slot>
      </view>
      <slot name="label"></slot>
      <view v-if="value" class="t-card__value" :style="[valueStyle]">{{ value }}</view>
      <slot name="value"></slot>
      <view class="t-card__button" v-if="icon || text" @click.stop="buttonClick">
        <image class="t-card__icon" :src="icon"></image>
        <text class="t-card__text" v-if="text">{{ text }}</text>
      </view>
    </template>
  </view>
</template>

<script>
export default {
  props: {
    custom: {
      type: Boolean,
      default: false,
    },
    label: {
      type: String,
    },
    value: {
      type: String,
    },
    text: {
      type: String,
    },
    icon: {
      type: String,
    },
    customStyle: {
      type: Object,
    },
    valueStyle: {
      type: Object,
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {
    click() {
      this.$emit("click-card");
    },
    buttonClick() {
      this.$emit("click");
    },
  },
};
</script>

<style scoped lang="scss">
.t-card {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  overflow: hidden;
  box-sizing: border-box;
  padding: 30rpx;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background-color: #f8f8f8;

  &__label {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-shrink: 0;

    .text {
      font-size: 36rpx;
      color: #000;
      font-family: usic;
      flex-shrink: 0;
    }
  }

  &__value {
    font-size: 28rpx;
    font-weight: bold;
    color: #000;
    word-break: break-all;
  }

  &__button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 20rpx;
    border-radius: 12px;
    background-color: $primary-color;
    align-self: flex-end;
  }

  &__icon {
    width: 40rpx;
    height: 40rpx;
  }

  &__text {
    color: #fff;
    font-size: 28rpx;
    margin-left: 20rpx;
  }
}
</style>
