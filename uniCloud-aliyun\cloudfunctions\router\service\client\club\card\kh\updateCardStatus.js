'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/kh/updateCardStatus 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, _id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		const fieldsToKeep = ['enable', 'repeat', 'overlay'];

		const dataJson = Object.entries(data)
			.filter(([key]) => fieldsToKeep.includes(key))
			.reduce((obj, [key, value]) => {
				obj[key] = value;
				return obj;
			}, {});
		res.num = await vk.baseDao.update({
			dbName: "club-card-data",
			whereJson: {
				_id
			},
			dataJson
		});
		res.msg = "更新成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}