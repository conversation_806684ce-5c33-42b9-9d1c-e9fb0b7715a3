'use strict';
module.exports = {
	/**
	 * 我的钱包余额
	 * @url client/user/wallet/kh/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let info = await vk.baseDao.findByWhereJson({
			dbName: "user-wallet",
			whereJson: {
				user_id: uid
			},
			dataJson: {
				withdrawn: true,
				balance: true,
				update_time: true
			}
		});
		if (!info) {
			info = {
				withdrawn: 0,
				balance: 0,
				update_time: Date.now()
			}
		}
		console.log("wallet", info);
		res.data = info


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}