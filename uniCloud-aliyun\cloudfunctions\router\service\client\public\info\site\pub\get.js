'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/site/pub/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id, name } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let foreignDB = []
		if (uid) {
			foreignDB.push({
				dbName: "info-recommend-record",
				localKey: "_id",
				foreignKey: "main_id",
				as: "recommend",
				whereJson: {
					user_id: uid
				},
				limit: 1
			})
			foreignDB.push({
				dbName: "sign-info-list",
				localKey: "_id",
				foreignKey: "data_id",
				as: "sign",
				whereJson: {
					user_id: uid,
					date: vk.pubfn.timeFormat(new Date(), "yyyy-MM-dd")
				},
				limit: 1
			})
		}

		let info = await vk.baseDao.selects({
			dbName: "info-site-data",
			getCount: false,
			getOne: true,
			getMain: true,
			// 主表where条件
			whereJson: { _id: id },
			// 副表列表
			foreignDB
		});


		// 处理recommend和sign的值
		info.recommend = !!info.recommend
		info.sign = !!info.sign

		res.data = info

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}