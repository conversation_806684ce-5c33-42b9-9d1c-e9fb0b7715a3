<template>
  <view class="page-container">
    <t-navbar nav-style="custom" :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 200})` }">
      <view v-if="scrollTop > 200" class="nav-title">{{ activityInfo.name }}</view>
    </t-navbar>

    <view class="background-decoration"></view>

    <view class="content-wrapper" v-if="activityInfo">
      <!-- Main Content -->
      <view class="main-content">
        <t-title size="54rpx" :custom-style="{ marginBottom: '30rpx' }">{{ activityInfo.name }}</t-title>

        <template v-if="activityInfo.desc">
          <t-title>活动说明</t-title>
          <view class="activity-description">{{ activityInfo.desc }}</view>
        </template>

        <view v-if="onlinePay" class="cost-warning">{{ costTips }}</view>

        <!-- Activity Details -->
        <view class="details-section">
          <!-- Location -->
          <view class="detail-item">
            <view class="detail-icon">
              <image :src="`${iconUrl}location-line.svg`" mode="widthFix" />
            </view>
            <view class="detail-value">{{ siteName }}</view>
            <view class="action-button" @click="openLocation">
              <image src="/static/images/navigate-white.svg" mode="widthFix" />
            </view>
          </view>

          <!-- Time -->
          <view class="detail-item">
            <view class="detail-icon">
              <image :src="`${iconUrl}time-line.svg`" mode="widthFix" />
            </view>
            <view class="detail-value">{{ formattedDateTime }}</view>
            <wd-tag v-if="formattedDateEasy" :type="formattedDateEasy.type" custom-style="margin-left:30rpx;flex-shrink:0">
              {{ formattedDateEasy.text }}
            </wd-tag>
          </view>

          <!-- Participants Count -->
          <view class="detail-item">
            <view class="detail-icon">
              <image :src="`${iconUrl}group-3-line.svg`" mode="widthFix" />
            </view>
            <view class="detail-value">
              <text :class="{ insufficient: isParticipantInsufficient }">{{ activityInfo.join_list.length }}</text>
              <text v-if="activityInfo.min_num > activityInfo.join_list.length">/{{ activityInfo.min_num }}</text>
              <text>/{{ activityInfo.max_num || "无限制" }}</text>
              <text v-if="isParticipantInsufficient" class="insufficient-text">(活动人数不足)</text>
              <text v-if="activityInfo.max_num == activityInfo.join_list.length" class="insufficient-text">(已满)</text>
            </view>
          </view>

          <!-- Cost -->
          <view class="detail-item">
            <view class="detail-icon">
              <image :src="`${iconUrl}currency-cny-line.svg`" mode="widthFix" />
            </view>
            <view class="detail-value">
              <view style="display: flex; align-items: center">
                <view>{{ costDescription }}</view>
                <wd-tag v-if="onlinePay" type="primary" custom-style="margin-left:30rpx;flex-shrink:0">线上收款</wd-tag>
              </view>
            </view>
          </view>
          <!-- Level -->
          <view class="detail-item">
            <view class="detail-icon">
              <image :src="`${iconUrl}vip-1-line.svg`" mode="widthFix" />
            </view>
            <view class="detail-value">{{ levelDescription }}</view>
          </view>

          <!-- Creator Contact -->
          <view class="detail-item">
            <view class="detail-icon">
              <image :src="contactIcon" mode="widthFix" />
            </view>
            <view class="creator-info" @click="viewUser(activityInfo.creator_info._id)">
              <image class="creator-avatar" :src="activityInfo.creator_info.avatar" mode="aspectFill" />
              <text class="creator-name">{{ activityInfo.creator_info.nickname }}</text>
            </view>
            <wd-button
              v-if="activityInfo.contact && activityInfo.contact !== 'none'"
              type="primary"
              size="small"
              @click="handleContactClick"
              class="contact-button"
              custom-style="background: #0171BC"
            >
              {{ activityInfo.contact === "mobile" ? "拨打电话" : "复制微信" }}
            </wd-button>
          </view>
        </view>

        <!-- Participants Section -->
        <view class="participants-section" v-if="canInvite || activityInfo.join_list.length > 0">
          <t-title>人员列表</t-title>
          <view class="participants-grid">
            <t-user-item
              v-for="item in activityInfo.join_list"
              :key="item._id"
              :type="item.type"
              :name="item.nickname"
              :data="item.user_info"
              @click="handleUserAction(item)"
            />

            <button v-if="canInvite" class="invite-button" open-type="share">
              <view class="invite-icon">
                <image src="/static/images/plus.svg" mode="widthFix" />
              </view>
              <view class="invite-text">邀请球友</view>
            </button>
          </view>
        </view>

        <view class="participants-section" v-if="myApplyList.length > 0">
          <t-title>我的申请</t-title>
          <view class="participants-grid">
            <t-user-item
              v-for="item in myApplyList"
              :key="item._id"
              :type="item.type"
              :name="item.nickname"
              :data="item.user_info"
              @click="handleMyApplyAction(item)"
            />
          </view>
        </view>
      </view>

      <!-- Action Buttons -->
      <t-bottom v-if="actionButtons.showAny">
        <view class="action-section">
          <wd-button v-if="actionButtons.manage" type="info" size="small" @click="navigateToManage" class="action-btn manage-btn">
            管理球局
          </wd-button>

          <wd-button v-if="actionButtons.help" type="warning" size="small" @click="handleHelpJoin" class="action-btn help-btn"> 活动代接 </wd-button>

          <wd-button
            v-if="actionButtons.join"
            type="primary"
            class="action-btn join-btn"
            size="small"
            custom-style="background: linear-gradient(135deg, #0171BC 0%, #0056A3 100%)"
            @click="handleJoinActivity"
          >
            {{ activityInfo.apply && !isManager ? "申请加入" : "加入球局" }}
          </wd-button>

          <wd-button v-else-if="actionButtons.apply" type="error" size="small" @click="handleExitActivity(joinInfo)" class="action-btn cancel-btn">
            取消申请
          </wd-button>

          <wd-button v-else-if="actionButtons.exit" type="error" size="small" @click="handleExitActivity(joinInfo)" class="action-btn exit-btn">
            退出活动
          </wd-button>
        </view>
      </t-bottom>
    </view>

    <!-- Agreement Popup -->
    <wd-popup custom-class="base-popup" v-model="agreementPopupVisible" position="center" :close-on-click-modal="false">
      <view class="popup-header">
        <text class="popup-title">活动免责声明</text>
      </view>
      <view class="popup-content">
        <view class="agreement-content">
          <text>请先阅读并同意来网球</text>
          <text class="agreement-link" @click="vk.navigateTo('/pages/public/agreement?type=disclaimer')"> 《活动免责声明》 </text>
        </view>
        <text class="agreement-note">点击确定表示您已阅读并同意相关条款</text>
      </view>
      <view class="popup-actions">
        <wd-button type="info" @click="cancelAgreement" class="popup-btn cancel-btn"> 取消 </wd-button>
        <wd-button type="primary" @click="confirmAgreement" class="popup-btn confirm-btn" custom-style="background: #0171BC">
          我已阅读并同意
        </wd-button>
      </view>
    </wd-popup>

    <!-- Help Join Popup -->
    <wd-popup custom-class="base-popup" v-model="helpPopupVisible" position="center" :close-on-click-modal="false">
      <view class="popup-header">
        <text class="popup-title">代接信息填写</text>
      </view>
      <view class="popup-content">
        <text class="help-description">请输入代接用户的昵称</text>
        <wd-input v-model="helpNickname" label="用户昵称" placeholder="请输入用户昵称" class="help-input" clearable />
      </view>
      <view class="popup-actions">
        <wd-button type="info" @click="cancelHelpJoin" class="popup-btn cancel-btn">取消</wd-button>
        <wd-button type="primary" @click="confirmHelpJoin" class="popup-btn confirm-btn" custom-style="background: #0171BC">确认代接</wd-button>
      </view>
    </wd-popup>

    <!-- Member Action Popup -->
    <wd-action-sheet v-model="memberPopupVisible" :actions="actions" @close="memberPopupVisible = false" @select="actionsSelect" />

    <wd-popup custom-class="base-popup" v-model="exitVisible" position="center" @close="closeExitVisible">
      <view class="popup-header">
        <text class="popup-title">退出活动提示</text>
      </view>
      <view class="popup-content">
        <text class="exit-description">{{ exitBaseText }}</text>
        <text class="exit-warn-text" v-if="exitWarningText">{{ exitWarningText }}</text>
        <wd-checkbox v-if="refundTimeout" v-model="agreeRefundCheck" shape="square" custom-style="margin-top:16rpx"
          >我已了解情况并决定退出</wd-checkbox
        >
      </view>
      <view class="popup-actions">
        <wd-button type="info" @click="exitVisible = false" class="popup-btn cancel-btn">取消</wd-button>
        <wd-button
          type="primary"
          @click="executeExitActivity"
          :disabled="refundTimeout && !agreeRefundCheck"
          class="popup-btn confirm-btn"
          custom-style="background: #0171BC"
          >确认</wd-button
        >
      </view>
    </wd-popup>
  </view>
</template>

<script>
import dayjs from "dayjs";

export default {
  name: "ActivityDetail",
  options: {
    styleIsolation: "shared",
  },
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      userInfo: vk.getVuex("$user.userInfo"),
      dangerColor: vk.getVuex("$app.color.warning"),

      // Activity data
      activityId: "",
      activityInfo: null,
      scrollTop: 0,

      // Join state
      joinState: "",
      joinInfo: null,
      activityStatus: null,

      // Modal states
      memberPopupVisible: false,
      selectedUser: null,
      hasAgreed: false,

      // Popup states
      agreementPopupVisible: false,
      helpPopupVisible: false,
      agreementResolve: null,

      // Help join data
      helpNickname: "",
      myApplyList: [],

      // 退款规则
      exitVisible: false,
      refundTimeout: false,
      agreeRefundCheck: false,
      exitInfo: null,
      exitBaseText: "",
      exitWarningText: "",
    };
  },

  watch: {
    "$store.state.$user.userInfo": {
      deep: true,
      handler(newVal) {
        this.userInfo = newVal;
      },
    },
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },

  computed: {
    // 可操作事项
    actions() {
      let ary = [];
      if (!this.activityInfo || !this.selectedUser) return ary;
      const { user_id, state } = this.selectedUser;
      if (user_id && user_id !== this.userInfo._id) ary.push({ name: "用户资料", key: "info" });
      if (this.isManager) ary.push({ name: "踢出活动", key: "kick", color: "#ff3030" });
      else if (this.canExitHelp && state == "joined") ary.push({ name: "退出活动", key: "exit", color: "#ff3030" });
      else if (user_id === this.userInfo._id && state == "applying") ary.push({ name: "取消申请", key: "exit", color: "#ff3030" });
      return ary;
    },

    // 是否可以邀请
    canInvite() {
      if (!this.activityInfo) return false;
      if (this.activityInfo.join_list.length >= this.activityInfo.max_num) return false;
      if (dayjs().isAfter(dayjs(this.activityInfo.end))) return false;
      return true;
    },

    // 联系方式图标
    contactIcon() {
      if (!this.activityInfo) return `${this.iconUrl}user-2-line.svg`;

      if (this.activityInfo.contact !== "none") {
        const iconType = this.activityInfo.contact === "phone" ? "cellphone-2-line" : "wechat-line";
        return `${this.iconUrl}${iconType}.svg`;
      }

      return `${this.iconUrl}user-2-line.svg`;
    },

    // 是否可以帮退活动
    canExitHelp() {
      if (!this.selectedUser || !this.userInfo) return false;
      return this.userInfo._id === this.selectedUser.user_id && this.selectedUser.type === "help";
    },

    // 操作按钮显示状态
    actionButtons() {
      const buttons = {
        showAny: false,
        manage: false,
        help: false,
        join: false,
        exit: false,
        apply: false,
      };

      if (!this.activityInfo) return buttons;

      const activity = this.activityInfo;

      // 活动已结束，不显示任何操作按钮
      if (dayjs().isAfter(dayjs(activity.end))) return buttons;

      // 管理权限
      if (this.userInfo._id === activity.creator_id) {
        buttons.manage = true;
      }

      // 代接按钮
      if (activity.help_join) {
        buttons.help = true;
      }

      // 加入/退出按钮
      switch (this.joinState) {
        case "none":
          buttons.join = true;
          break;
        case "applying":
          buttons.apply = true;
          break;
        case "joined":
          buttons.exit = true;
          break;
      }

      // 活动已满员时隐藏部分按钮
      if (activity.join_list.length >= activity.max_num) {
        buttons.help = false;
        buttons.join = false;
      }

      // 检查是否有任何按钮需要显示
      buttons.showAny = Object.keys(buttons).some((key) => key !== "showAny" && buttons[key]);

      return buttons;
    },

    // 活动状态
    currentActivityStatus() {
      if (!this.activityInfo) return "";

      const now = dayjs();
      const start = dayjs(this.activityInfo.start);
      const end = dayjs(this.activityInfo.end);

      if (now.isBefore(start)) return "not-started";
      if (now.isAfter(start) && now.isBefore(end)) return "ongoing";
      if (now.isAfter(end)) return "ended";

      return "";
    },

    // 是否为管理员
    isManager() {
      if (!this.activityInfo) return false;
      return this.userInfo._id === this.activityInfo.creator_id;
    },

    // 水平要求描述
    levelDescription() {
      if (!this.activityInfo) return "无";
      if (!this.activityInfo.level) return "无要求";
      return `${this.activityInfo.level.toFixed(1)}+`;
    },

    // 参与人数是否不足
    isParticipantInsufficient() {
      return this.activityInfo && this.activityInfo.min_num > this.activityInfo.join_list.length;
    },

    // 判断是否是线上收款
    onlinePay() {
      if (!this.activityInfo) return false;
      const { cost_type, cost_plan } = this.activityInfo;
      return cost_type !== "none" && cost_plan === 1;
    },

    // 退款规则
    costTips() {
      if (!this.activityInfo) return null;
      if (!this.onlinePay) return null;
      const { refund_hour, refund_money } = this.activityInfo;
      return `当距离活动开始不足${refund_hour}小时，${refund_money === 0 ? "退出活动不退款" : `退出活动仅退款${refund_money}元`}`;
    },

    // 费用描述
    costDescription() {
      if (!this.activityInfo) return "无";

      const { cost_type, cost } = this.activityInfo;

      switch (cost_type) {
        case "none":
          return "无需费用";
        case "average":
          return `平摊费用${cost}元`;
        case "fixed":
          return `${cost}元/人`;
        default:
          return "无";
      }
    },

    // 格式化的日期时间
    formattedDateTime() {
      if (!this.activityInfo) return "无";

      const startDate = dayjs(this.activityInfo.start).format("YYYY-MM-DD");
      const endDate = dayjs(this.activityInfo.end).format("YYYY-MM-DD");
      const startTime = dayjs(this.activityInfo.start).format("HH:mm");
      const endTime = dayjs(this.activityInfo.end).format("HH:mm");
      if (startDate === endDate) {
        return `${startDate} ${startTime}~${endTime}`;
      } else {
        return `${startDate} ${startTime} ~ ${endDate} ${endTime}`;
      }
    },

    // 格式化简易日期 今天明天后天
    formattedDateEasy() {
      if (!this.activityInfo) return null;
      let now = dayjs().format("YYYY-MM-DD");
      let startDay = dayjs(this.activityInfo.start).format("YYYY-MM-DD");
      let day = dayjs(startDay).diff(now, "day");

      if (day === 0) return { type: "danger", text: "今天" };
      else if (day === 1) return { type: "warning", text: "明天" };
      else if (day === 2) return { type: "primary", text: "后天" };
      return null;
    },

    // 活动类型描述
    activityTypeDescription() {
      if (!this.activityInfo) return "无";

      switch (this.activityInfo.type) {
        case "club":
          return "俱乐部活动";
        case "match":
          return "官方赛事";
        default:
          return "普通活动";
      }
    },

    // 场地名称
    siteName() {
      if (!this.activityInfo) return "无";

      const { site_info, club_site, club_id, club_info } = this.activityInfo;

      if (!site_info && !club_site) return "无场地信息";

      if (club_id) {
        return `${club_info?.name || ""} ${club_site?.name || ""}`.trim();
      } else {
        return site_info?.address || "无";
      }
    },
  },
  onShareAppMessage() {
    if (!this.activityInfo) return {};

    // #ifdef MP-WEIXIN
    const isOpen = this.activityInfo.open;
    wx.updateShareMenu({
      isPrivateMessage: !isOpen,
    });
    // #endif

    const userName = this.userInfo.nickname || "";
    let title = `${userName}邀请您加入<${this.activityInfo.name}>`;
    if (this.currentActivityStatus === "ended") {
      title = `${userName}给你分享活动详情<${this.activityInfo.name}>`;
    }

    return {
      title,
      path: `/pages/activity/detail?id=${this.activityInfo._id}`,
    };
  },

  onLoad(options) {
    this.activityId = options.id;

    this.hasAgreed = vk.getVuex("$app.activity_agreement") || false;

    // 监听活动刷新事件
    uni.$on("activity-page-refresh", this.handleActivityRefresh);

    this.fetchActivityDetail();
  },

  onUnload() {
    uni.$off("activity-page-refresh", this.handleActivityRefresh);
  },

  onShow() {
    if (this.activityInfo) {
      this.checkActivityState();
    }
  },
  methods: {
    // 关闭退出弹窗
    closeExitVisible() {
      this.exitVisible = false;
      this.agreeRefundCheck = false;
      this.refundTimeout = false;
    },
    // 操作
    actionsSelect({ item }) {
      switch (item.key) {
        case "info":
          this.viewUser();
          break;
        case "kick":
          this.handleExitActivity(this.selectedUser, true);
          break;
        case "exit":
          this.handleExitActivity(this.selectedUser);
          break;
      }
    },

    // 活动刷新事件处理
    handleActivityRefresh() {
      this.exitVisible = false;
      this.exitInfo = null;
      this.exitBaseText = "";
      this.exitWarningText = "";
      this.agreeRefundCheck = false;
      this.refundTimeout = false;

      this.fetchActivityDetail();
    },

    // 处理联系方式点击
    handleContactClick() {
      const contactType = this.activityInfo.contact;

      if (contactType === "mobile") {
        uni.makePhoneCall({
          phoneNumber: this.userInfo[contactType].toString(),
          success() {
            console.log("Phone call initiated");
          },
          fail(error) {
            console.error("Failed to make phone call:", error);
            vk.toast("拨打电话失败");
          },
        });
      } else {
        uni.setClipboardData({
          data: this.userInfo[contactType].toString(),
          success() {
            vk.toast("复制成功", "success");
          },
          fail(error) {
            console.error("Failed to copy to clipboard:", error);
            vk.toast("复制失败");
          },
        });
      }
    },

    // 显示协议确认弹窗
    showAgreementDialog() {
      return new Promise((resolve) => {
        this.agreementPopupVisible = true;
        this.agreementResolve = resolve;
      });
    },

    // 确认协议
    confirmAgreement() {
      this.hasAgreed = true;
      this.agreementPopupVisible = false;
      vk.setVuex("$app.activity_agreement", true);
      if (this.agreementResolve) {
        this.agreementResolve(true);
        this.agreementResolve = null;
      }
    },

    // 取消协议
    cancelAgreement() {
      this.agreementPopupVisible = false;
      if (this.agreementResolve) {
        this.agreementResolve(false);
        this.agreementResolve = null;
      }
    },

    // 处理用户操作
    handleUserAction(data) {
      const userId = data?.user_info?._id;
      if (dayjs().isAfter(dayjs(this.activityInfo.end))) {
        if (userId) {
          vk.navigateTo(`/pages/user/detail?id=${userId}`);
          this.memberPopupVisible = false;
        }
        return;
      }
      this.selectedUser = data;
      this.memberPopupVisible = true;
    },

    handleMyApplyAction(data) {
      this.selectedUser = data;
      this.memberPopupVisible = true;
    },

    // 查看用户详情
    viewUser(userId) {
      const targetId = userId || this.selectedUser?.user_info?._id;
      if (targetId) {
        vk.navigateTo(`/pages/user/detail?id=${targetId}`);
        this.memberPopupVisible = false;
      }
    },

    // 检查活动状态
    checkActivityState() {
      if (!this.activityInfo) return;

      const now = dayjs();
      const start = dayjs(this.activityInfo.start);
      const end = dayjs(this.activityInfo.end);

      if (now.isAfter(end)) {
        this.activityStatus = "活动已结束";
      } else if (now.isAfter(start) && now.isBefore(end)) {
        this.activityStatus = "活动进行中";
      } else {
        this.activityStatus = "活动未开始";
      }
    },

    // 获取加入状态
    async fetchJoinState() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/join_state/kh/query",
          title: "请求中...",
          data: {
            id: this.activityId,
            type: "myself",
          },
        });

        this.joinInfo = data.data;
        this.joinState = data.state || "none";
      } catch (error) {
        console.error("Failed to fetch join state:", error);
      }
    },

    // 跳转到管理页面
    navigateToManage() {
      vk.navigateTo(`/pages/activity/manage?id=${this.activityId}`);
    },

    // 打开位置
    openLocation() {
      if (!this.activityInfo?.site_info) {
        vk.toast("无位置信息");
        return;
      }

      const { name, location } = this.activityInfo.site_info;

      uni.openLocation({
        name,
        latitude: location.latitude,
        longitude: location.longitude,
        success() {
          console.log("Location opened successfully");
        },
        fail(error) {
          console.error("Failed to open location:", error);
          vk.toast("打开位置失败");
        },
      });
    },
    // 处理退出活动
    async handleExitActivity(data, isKick = false) {
      let confirmText = "";
      this.refundTimeout = false;
      console.log("data", data);

      if (isKick) {
        if (data.type === "help") {
          confirmText = `是否要将用户<${data.user_info.nickname}>代接的<${data.nickname}>踢出活动？`;
        } else if (data.type === "myself") {
          confirmText = `是否要将用户<${data.user_info.nickname}>踢出活动？`;
        }
      } else {
        if (data.type === "help") {
          confirmText = `是否要退出活动代接的<${data.nickname}>？`;
        } else if (data.type === "myself") {
          confirmText = `是否要${this.joinState === "joined" ? "退出" : "取消申请"}活动？`;
        }
      }
      const { cost_type, cost_plan, refund_hour, refund_money } = this.activityInfo;
      this.exitInfo = data;
      // 检测是否是线上收款并且超时并且自己点击退出
      let { endTime } = vk.pubfn.getHourOffsetStartAndEnd(refund_hour, new Date());
      if (!isKick && cost_type !== "none" && cost_plan === 1 && endTime > Date.now() && data.state === "joined" && !this.isManager) {
        this.exitWarningText = `当前距离活动已不足${refund_hour}小时\n根据规则仅退款${refund_money}元`;
        this.exitBaseText = confirmText;
        this.exitVisible = true;
        this.refundTimeout = true;
        return;
      }
      if (isKick && cost_type !== "none" && cost_plan === 1) {
        this.exitWarningText = "踢出用户后，用户支付的活动费用将全款退回";
      }
      this.exitBaseText = confirmText;
      this.exitVisible = true;
      // try {
      //   uni.showModal({
      //     title: "提示",
      //     content: confirmText,
      //     success: async (res) => {
      //       if (res.confirm) {
      //         await this.executeExitActivity(participantInfo);
      //       }
      //     },
      //   });
      // } catch (error) {
      //   console.error("Failed to show exit modal:", error);
      // }
    },

    // 执行退出活动
    async executeExitActivity() {
      let info = this.exitInfo;
      console.log(info);

      try {
        const data = await vk.callFunction({
          url: "client/activity/join_state/kh/exit",
          title: "请求中...",
          data: {
            id: info._id,
            type: info.type,
            activity_id: this.activityId,
          },
        });

        vk.toast(data.msg, "none", true, () => {
          this.memberPopupVisible = false;
          uni.$emit("activity-page-refresh");
          uni.$emit("activity-refresh");
        });
      } catch (error) {
        console.error("Failed to exit activity:", error);
        vk.toast(error.msg || "操作失败");
      }
    },

    // 处理代接加入
    async handleHelpJoin() {
      if (!this.hasAgreed) {
        const agreed = await this.showAgreementDialog();
        if (!agreed) return;
      }

      this.showHelpJoinDialog();
    },

    // 显示代接加入弹窗
    showHelpJoinDialog() {
      this.helpNickname = ""; // 清空之前的输入
      this.helpPopupVisible = true;
    },

    // 确认代接加入
    confirmHelpJoin() {
      if (!this.helpNickname.trim()) {
        vk.toast("请输入用户昵称");
        return;
      }

      this.helpPopupVisible = false;
      const { cost_type, cost_plan, creator_id } = this.activityInfo;
      // 检测我是否是创建人
      // 需要线上缴费的活动
      if (cost_type !== "none" && cost_plan === 1 && creator_id !== this.userInfo._id) {
        this.payToJoin({
          activity_id: this.activityId,
          join_type: "help",
          join_name: this.helpNickname,
        });
        return;
      }
      this.joinToActivity({ type: "help", name: this.helpNickname });
    },

    // 取消代接加入
    cancelHelpJoin() {
      this.helpPopupVisible = false;
      this.helpNickname = "";
    },

    // 处理加入活动
    async handleJoinActivity() {
      if (!this.hasAgreed) {
        const agreed = await this.showAgreementDialog();
        if (!agreed) return;
      }
      const { cost_type, cost_plan, creator_id } = this.activityInfo;
      // 需要线上缴费的活动
      if (cost_type !== "none" && cost_plan === 1 && creator_id !== this.userInfo._id) {
        this.payToJoin({ activity_id: this.activityInfo._id, join_type: "myself" });
        return;
      }
      this.joinToActivity({ type: "myself" });
    },
    async joinToActivity({ type, name, out_trade_no }) {
      let params = {
        id: this.activityId,
        type,
      };
      if (name) params.name = name;
      if (out_trade_no) params.out_trade_no = out_trade_no;
      try {
        const data = await vk.callFunction({
          url: "client/activity/join_state/kh/join",
          title: "请求中...",
          data: params,
        });

        vk.toast(data.msg, "none", true, () => {
          uni.$emit("activity-page-refresh");
          uni.$emit("activity-refresh");
        });

        // 如果不是创建者，请求订阅消息
        if (this.activityInfo.creator_id !== this.userInfo._id) {
          this.requestSubscribeMessage();
        }
      } catch (error) {
        console.error("Failed to join activity:", error);
        vk.toast("加入活动失败");
      }
    },
    async payToJoin(params) {
      const { cost_type, min_num, cost } = this.activityInfo;
      let total_fee;
      if (cost_type === "average") {
        total_fee = (cost / min_num) * 100;
      } else if (cost_type === "fixed") {
        total_fee = cost * 100;
      }
      if (!total_fee) {
        vk.toast("支付金额异常，请重新尝试");
        return;
      }
      try {
        let info = this.activityInfo;
        let data = await vk.callFunction({
          url: "client/order/kh/create",
          title: "请求中...",
          data: {
            subject: `缴纳活动<${info.name}>的费用`,
            total_fee,
            type: "activity",
            ...params,
          },
        });
        uni.requestPayment({
          provider: data.provider,
          orderInfo: data.orderInfo,
          timeStamp: data.orderInfo.timeStamp,
          nonceStr: data.orderInfo.nonceStr,
          package: data.orderInfo.package,
          signType: data.orderInfo.signType,
          paySign: data.orderInfo.paySign,
          success: (res) => {
            vk.toast("支付成功！", "none", true, () => {
              this.joinToActivity({ type: params.join_type, name: params.join_name, out_trade_no: data.out_trade_no });
            });
          },
          fail: (error) => {
            console.log("失败了", error);
          },
        });
      } catch (error) {}
    },

    // 请求订阅消息
    requestSubscribeMessage() {
      const tmplIds = this.activityInfo.apply
        ? ["kunnNz3xL48JH06YDvtvU4dcu1iLcueEO5T5mmYlvas", "E8nAcoeuMnAMhwWwnoMFrV9IXZ-Uc2A2J-zEKjqf8Gk"]
        : ["E8nAcoeuMnAMhwWwnoMFrV9IXZ-Uc2A2J-zEKjqf8Gk"];

      uni.requestSubscribeMessage({
        tmplIds,
        success(res) {
          console.log("Subscribe message success:", res);
        },
        fail(error) {
          console.error("Subscribe message failed:", error);
        },
      });
    },
    // 获取我的申请列表
    async getMyApplyList() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/kh/applyList",
        });
        this.myApplyList = data.rows;
      } catch (error) {
        console.error("Failed to get my apply list:", error);
      }
    },
    // 获取活动详情
    async fetchActivityDetail() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/pub/detail",
          title: "请求中...",
          data: {
            id: this.activityId,
          },
        });

        this.activityInfo = data.data;
        await this.fetchJoinState();
        this.checkActivityState();
        if (data.data.creator_id !== this.userInfo._id) this.getMyApplyList();
      } catch (error) {
        console.error("Failed to fetch activity detail:", error);
        vk.toast("获取活动详情失败");
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  padding: 30rpx;
  overflow-y: scroll;
  overflow-x: hidden;
  position: relative;
  width: 100%;
  box-sizing: border-box;
}
.background-decoration {
  position: absolute;
  top: -200rpx;
  right: -150rpx;
  width: 600rpx;
  height: 600rpx;
  border-radius: 50%;
  pointer-events: none;
  background-color: rgba(1, 113, 188, 0.16);
  filter: blur(50px);
}
// Navigation Title
.nav-title {
  width: 300rpx;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
  color: #333;
  font-weight: 500;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

// Content Wrapper
.content-wrapper {
  position: relative;
  z-index: 1;
}

// Participants Section
.participants-section {
  margin-bottom: 30rpx;
}

.participants-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20rpx;
  margin-top: 30rpx;
}

.invite-button {
  width: 160rpx;
  padding: 0 20rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  background: transparent;
  border: none;
  border-radius: 22rpx;

  &::after {
    content: none;
  }

  .invite-icon {
    width: 100rpx;
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 2rpx dashed #333;
    box-sizing: border-box;

    image {
      width: 50%;
      height: 50%;
    }
  }

  .invite-text {
    width: 100%;
    font-size: 28rpx;
    margin-top: 20rpx;
    text-align: center;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
    line-height: 2;
  }
}

// Main Content
.main-content {
  margin-bottom: 30rpx;
}

.activity-description {
  font-size: 28rpx;
  color: #666;
  white-space: pre-line;
  line-height: 1.6;
  margin-bottom: 30rpx;
}

// Details Section
.details-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.detail-item {
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  height: 80rpx;

  .detail-icon {
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-right: 40rpx;

    image {
      width: 48rpx;
      height: 48rpx;
    }
  }

  .detail-value {
    font-size: 28rpx;
    color: #333;
    line-height: 1.4;

    .insufficient {
      color: #ff3030;
      font-weight: bold;
    }

    .insufficient-text {
      color: #ff3030;
      font-size: 24rpx;
      margin-left: 20rpx;
    }
  }
  .action-button {
    width: 60rpx;
    height: 60rpx;
    background: #0171bc;
    border-radius: 12rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    margin-left: 20rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.95);
      background: #0056a3;
    }

    image {
      width: 30rpx;
      height: 30rpx;
    }
  }
}

.cost-warning {
  color: #ff3030;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  background-color: rgba(255, 48, 48, 0.1);
  width: 100%;
  padding: 20rpx;
  border-radius: 12rpx;
  box-sizing: border-box;
}

// Creator Info
.creator-info {
  display: flex;
  align-items: center;
  flex: 1;

  .creator-avatar {
    width: 60rpx;
    height: 60rpx;
    border-radius: 50%;
    margin-right: 16rpx;
  }

  .creator-name {
    font-size: 28rpx;
    color: #333;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.contact-button {
  margin-left: 20rpx;
  min-width: 120rpx;
  height: 60rpx;
  font-size: 24rpx;
}

// Action Section
.action-section {
  display: flex;
  gap: 20rpx;
}

// Agreement Content
.agreement-content {
  font-size: 26rpx;
  color: #666;
  padding: 20rpx;

  .agreement-link {
    margin: 0 8rpx;
    color: #0171bc;
    font-weight: 500;
    text-decoration: underline;
  }
}

// Member Actions
.member-actions {
  padding: 40rpx 30rpx;
  display: flex;
  flex-direction: column;
  gap: 20rpx;

  .member-action-btn {
    width: 100%;
    height: 88rpx;
    font-size: 28rpx;
    border-radius: 16rpx;
  }
}

// Help Input
.help-input {
  margin: 20rpx 0;
}

:deep(.base-popup) {
  background: #fff;
  border-radius: 20rpx;
  width: 80vw;
  overflow: hidden;
}

.popup-header {
  padding: 40rpx 30rpx 20rpx;
  text-align: center;
  border-bottom: 2rpx solid #f0f0f0;
}

.popup-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.popup-content {
  padding: 30rpx;
}

.agreement-note {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
  display: block;
}

.help-description {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  display: block;
}
.help-description.warning {
  color: #ff3030;
}
.help-input {
  margin-bottom: 20rpx;
}

.exit-description {
  font-size: 28rpx;
  color: #000;
  text-align: center;
  display: block;
}
.exit-warn-text {
  color: #ff3030;
  font-size: 24rpx;
  font-weight: bold;
  margin-top: 16rpx;
  text-align: center;
  display: block;
  line-height: 1.5;
}

.popup-actions {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  box-sizing: border-box;
  gap: 20rpx;
  padding: 20rpx 30rpx 30rpx;

  .popup-btn {
    flex: 1;
    height: 80rpx;
    font-size: 28rpx;
    border-radius: 16rpx;

    &.cancel-btn {
      background: #f0f0f0;
      color: #666;
    }

    &.confirm-btn {
      box-shadow: 0 8rpx 32rpx rgba(1, 113, 188, 0.4);
    }
  }
}

// Animation
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-content,
.participants-section {
  animation: fadeIn 0.3s ease-out;
}
</style>
