<template>
  <view class="page-content">
    <t-navbar title="附近球友"></t-navbar>
    <view class="header" :style="{ top: headerTop }">
      <view class="filter">
        <view class="filter-block" @click="changeLocation">
          <image class="icon" :src="`${iconUrl}location-line.svg`"></image>
          <text class="text truncate">{{ locationName }}</text>
        </view>
      </view>
      <view class="base-search">
        <wd-search v-model="searchValue" placeholder="请输入用户昵称" hide-cancel @search="confirmSearch" @clear="clearSearch"></wd-search>
      </view>
      <view class="add-btn" @click="filterVisible = true">
        <wd-icon name="filter" size="18px" color="#fff"></wd-icon>
      </view>
    </view>
    <view class="user">
      <view class="user-card" v-for="item in list" :key="item._id">
        <view class="flex" @click="viewUser(item.user_info._id)">
          <image class="avatar" :src="item.user_info.avatar" mode="aspectFill"></image>
          <view class="info">
            <view class="name">
              <text>{{ item.user_info.nickname }}</text>
              <image v-if="item.user_info.sex" :src="`/static/images/${item.user_info.sex == 1 ? 'male' : 'female'}.svg`" mode="widthFix"></image>
            </view>
            <view class="tags">
              <!-- <wd-tag v-if="item.user_info.birthday" type="primary">{{ userAge(item.user_info.birthday) }}</wd-tag> -->
              <wd-tag v-if="item.user_info.level" type="primary">{{ item.user_info.level.toFixed(1) }}</wd-tag>
              <wd-tag v-for="(tag, tagIndex) in item.tags" :key="tagIndex" type="primary">{{ tag }}</wd-tag>
            </view>
          </view>
        </view>
        <view class="flex bottom">
          <view class="distance">
            <text>距您</text>
            <text style="margin-left: 10rpx">{{ item.distance.toFixed(2) }}km</text>
          </view>
          <view class="flex">
            <view class="contact" v-if="item.contact.includes('mobile')" @click="getContact(item, 'mobile')">
              <image src="/static/images/phone-line.svg" mode="widthFix"></image>
            </view>
            <view class="contact" v-if="item.contact.includes('wechat')" @click="getContact(item, 'wechat')">
              <image src="/static/images/wechat-line.svg" mode="widthFix" style="color: #fff"></image>
            </view>
          </view>
        </view>
      </view>
      <t-empty v-if="pageIndex == 1 && list.length == 0 && !hasMore" data="search" text="附近暂无球友" />
    </view>
    <wd-popup v-model="filterVisible" position="top" :closable="true" :custom-style="`top: ${headerTop}`">
      <view class="filter-container">
        <view class="filter-header">
          <text class="filter-title">筛选条件</text>
        </view>

        <view class="filter-box">
          <!-- 状态筛选 -->
          <view class="filter-item">
            <view class="filter-label">状态</view>
            <wd-checkbox-group v-model="filterInfo.tags" inline class="tag-group">
              <wd-checkbox
                v-for="tag in ['可约球', '可教学', '有偿陪练', '免费陪练']"
                :key="tag"
                shape="square"
                :model-value="tag"
                size="small"
                class="tag-checkbox"
              >
                {{ tag }}
              </wd-checkbox>
            </wd-checkbox-group>
          </view>

          <!-- 性别筛选 -->
          <view class="filter-item">
            <view class="filter-label">性别</view>
            <wd-radio-group v-model="filterInfo.sex" inline class="sex-group">
              <wd-radio v-for="option in sexOptions" shape="dot" :key="option.value" :value="option.value" size="small" class="sex-radio">
                {{ option.label }}
              </wd-radio>
            </wd-radio-group>
          </view>

          <!-- 水平筛选 -->
          <!-- <view class="filter-item">
            <view class="filter-label">水平</view>
            <wd-picker v-model="filterInfo.level" :columns="levelOptions" placeholder="请选择水平" class="level-picker" />
          </view> -->
        </view>

        <view class="button-box">
          <wd-button type="info" @click="reset" class="reset-btn"> 重置 </wd-button>
          <wd-button type="primary" @click="filterConfirm" class="confirm-btn"> 确定筛选 </wd-button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script>
import dayjs from "dayjs";
import tEmpty from "../../../components/t-ui/t-empty.vue";

export default {
  components: { tEmpty },
  data() {
    return {
      system: uni.getSystemInfoSync(),
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      locationInfo: vk.getVuex("$app.locationInfo"),
      locationName: "南宁市",
      list: [],
      filterVisible: false,
      filterInfo: {
        tags: [],
        sex: "",
        level: "",
      },
      sexOptions: [
        {
          label: "不限",
          value: "",
        },
        {
          label: "男",
          value: 1,
        },
        {
          label: "女",
          value: 2,
        },
      ],
      levelOptions: [],
      searchValue: "",
      hasMore: true,
      pageIndex: 1,
    };
  },
  onShareAppMessage() {
    return {
      title: `看看附近都有哪些球友`,
    };
  },
  async onLoad() {
    this.getOptions();
    this.getAnalysisLocal();
  },
  onReachBottom() {
    if (!this.hasMore) return;
    this.pageIndex += 1;
    this.getData();
  },
  computed: {
    headerTop() {
      return `${this.system.statusBarHeight + 44}px`;
    },
  },
  methods: {
    // 获取用户联系方式
    async getContact(data, type) {
      const contactInfo = () => {
        switch (type) {
          case "mobile":
            this.callPhone(data.user_info.mobile);
            break;
          case "wechat":
            this.copyText(data.user_info.wechat);
            break;
        }
      };

      if (data.apply) {
        if (data.contact_white.includes(type)) return contactInfo();
        vk.confirm("用户设置了联系方式需申请，是否申请获取", "提示", "确定", "取消", async (res) => {
          if (res.confirm) {
            let res = await vk.callFunction({
              url: "client/user/status/kh/applyContact",
              data: {
                user_id: data.user_id,
                type,
              },
            });
            if (res.copy) contactInfo();
          }
        });
      } else {
        contactInfo();
      }
    },
    userAge(date) {
      return dayjs().diff(dayjs(date), "year") + "岁";
    },
    confirmSearch({ value }) {
      this.searchValue = value || "";
      this.init();
    },
    clearSearch() {
      this.searchValue = "";
      this.init();
    },
    reset() {
      this.filterInfo = {
        tags: [],
        sex: "",
        level: "",
      };
    },
    filterConfirm() {
      this.filterVisible = false;
      this.init();
    },
    async getOptions() {
      let level = await vk.callFunction({
        url: "client/options/pub/level",
      });
      this.levelOptions = level.data;
    },
    // 查看用户详情
    viewUser(id) {
      vk.navigateTo(`/pages/user/detail?id=${id}`);
    },
    copyText(text) {
      uni.setClipboardData({
        data: text,
        success: () => {
          vk.toast("复制成功！");
        },
      });
    },
    callPhone(phone) {
      uni.makePhoneCall({
        phoneNumber: phone,
      });
    },
    async getAnalysisLocal(info) {
      if (info) {
        let data = await vk.callFunction({
          url: "plugs/location/pub/analysis",
          title: "请求中...",
          data: {
            latitude: info.latitude,
            longitude: info.longitude,
          },
        });
        this.locationName = data.data.city;
        this.init();
      } else {
        uni.getLocation({
          type: "gcj02",
          success: (res) => {
            this.locationInfo = res;
            vk.setVuex("$app.locationInfo", res);
            this.getAnalysisLocal(res);
          },
          fail: () => {
            vk.toast("位置授权失败，请点击右上角···授权定位", "none", true);
          },
        });
      }
    },
    init() {
      this.list = [];
      this.pageIndex = 1;
      if (!this.locationInfo) {
        this.getAnalysisLocal();
      } else {
        this.getData();
      }
    },
    async getData() {
      try {
        let data = await vk.callFunction({
          url: "client/user/status/pub/list",
          title: "请求中...",
          data: {
            pageIndex: this.pageIndex,
            location: this.locationInfo,
            sex: this.filterInfo.sex,
            level: this.filterInfo.level,
            tags: this.filterInfo.tags,
            name: this.searchValue,
          },
        });
        this.list.push(...data.rows);
        this.hasMore = data.hasMore;
      } catch (err) {
        console.log(err);
      } finally {
        uni.hideLoading();
      }
    },
    changeLocation() {
      let latitude = "";
      let longitude = "";
      if (this.locationInfo) {
        latitude = this.locationInfo.latitude;
        longitude = this.locationInfo.longitude;
      }
      uni.chooseLocation({
        latitude,
        longitude,
        success: (res) => {
          this.locationInfo = {
            latitude: res.latitude,
            longitude: res.longitude,
          };
          this.getAnalysisLocal(this.locationInfo);
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.add-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background-color: #0171bc;
  flex-shrink: 0;
  margin-left: 20rpx;

  image {
    width: 20px;
    height: 20px;
  }
}

.header {
  position: sticky;
  width: 100%;
  z-index: 9;
  left: 0;
  height: 56px;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  background-color: #fff;
}

.filter {
  display: flex;
  align-items: center;

  &-block {
    display: flex;
    align-items: center;
    width: 160rpx;
    height: 30px;
    margin-right: 20rpx;

    .icon {
      width: 15px;
      height: 15px;
      margin-right: 10rpx;
      flex-shrink: 0;
    }

    .text {
      font-size: 28rpx;
      font-weight: bold;
      white-space: nowrap;
    }

    .multiline {
      @include multiline(1);
    }
  }
}

.user {
  padding: 30rpx;
  box-sizing: border-box;

  &-card {
    width: 100%;
    padding: 30rpx;
    box-sizing: border-box;
    border-radius: 24rpx;
    background-color: #fff;
    margin-bottom: 30rpx;
  }

  .avatar {
    width: 148rpx;
    height: 148rpx;
    border-radius: 24rpx;
    flex-shrink: 0;
    background-color: #fff;
  }

  .info {
    height: 148rpx;
    margin-left: 20rpx;
    display: flex;
    flex-direction: column;

    .name {
      display: flex;
      align-items: center;
      font-size: 36rpx;
      font-weight: bold;

      image {
        width: 30rpx;
        height: 30rpx;
        margin-left: 10rpx;
        flex-shrink: 0;
      }
    }

    .tags {
      display: flex;
      flex-wrap: wrap;
      gap: 12rpx;
      margin-top: 10rpx;
    }
  }

  .bottom {
    justify-content: space-between;
    margin-top: 20rpx;
    border-top: 2rpx solid #f8f8f8;
    padding-top: 20rpx;

    .distance {
      font-size: 28rpx;
    }

    .contact {
      width: 60rpx;
      height: 60rpx;
      background-color: #0171bc;
      border-radius: 12rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20rpx;
      transition: all 0.3s ease;

      &:active {
        background-color: #0056a3;
        transform: scale(0.95);
      }

      image {
        width: 40rpx;
        height: 40rpx;
      }
    }
  }
}

.filter-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.filter-header {
  padding: 40rpx 30rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;

  .filter-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.filter-box {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

.filter-item {
  margin-bottom: 40rpx;

  .filter-label {
    font-size: 28rpx;
    font-weight: 500;
    color: #333;
    margin-bottom: 20rpx;
  }

  .tag-group {
    :deep(.wd-checkbox-group) {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .wd-checkbox {
        margin-right: 0;

        .wd-checkbox__label {
          padding: 12rpx 24rpx;
          border: 1rpx solid #e0e0e0;
          border-radius: 20rpx;
          font-size: 26rpx;
          color: #666;
          background: #fff;
          transition: all 0.3s ease;
        }

        &.is-checked .wd-checkbox__label {
          background: rgba(1, 113, 188, 0.1);
          color: #0171bc;
          border-color: #0171bc;
        }
      }
    }
  }

  .sex-group {
    :deep(.wd-radio-group) {
      display: flex;
      gap: 16rpx;

      .wd-radio {
        .wd-radio__label {
          padding: 12rpx 24rpx;
          border: 1rpx solid #e0e0e0;
          border-radius: 20rpx;
          font-size: 26rpx;
          color: #666;
          background: #fff;
          transition: all 0.3s ease;
        }

        &.is-checked .wd-radio__label {
          background: rgba(1, 113, 188, 0.1);
          color: #0171bc;
          border-color: #0171bc;
        }
      }
    }
  }

  .level-picker {
    :deep(.wd-picker) {
      .wd-picker__input {
        border: 1rpx solid #e0e0e0;
        border-radius: 12rpx;
        padding: 24rpx;
        font-size: 28rpx;
        color: #333;
        background: #fff;

        &:focus {
          border-color: #0171bc;
        }
      }

      .wd-picker__placeholder {
        color: #999;
      }
    }
  }
}

.button-box {
  padding: 30rpx;
  display: flex;
  justify-content: space-around;
  gap: 20rpx;
  border-top: 1rpx solid #f0f0f0;
}
</style>
