{"bsonType": "object", "required": ["title", "type", "start_date", "end_date", "location", "status"], "permission": {"read": true, "create": "auth.uid != null", "update": "auth.uid != null", "delete": "auth.role == 'admin'"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "title": {"bsonType": "string", "description": "比赛标题", "trim": "both"}, "type": {"bsonType": "string", "description": "比赛类型：single-单打，double-双打", "enum": ["single", "double"]}, "level": {"bsonType": "string", "description": "比赛级别", "enum": ["amateur", "professional", "youth"]}, "description": {"bsonType": "string", "description": "比赛描述"}, "start_date": {"bsonType": "timestamp", "description": "开始时间"}, "end_date": {"bsonType": "timestamp", "description": "结束时间"}, "location": {"bsonType": "object", "description": "比赛地点", "properties": {"name": {"bsonType": "string", "description": "场地名称"}, "address": {"bsonType": "string", "description": "详细地址"}, "longitude": {"bsonType": "number", "description": "经度"}, "latitude": {"bsonType": "number", "description": "纬度"}}}, "status": {"bsonType": "string", "description": "比赛状态", "enum": ["draft", "registering", "registered", "in_progress", "completed", "cancelled"]}, "max_participants": {"bsonType": "int", "description": "最大参与人数"}, "current_participants": {"bsonType": "int", "description": "当前参与人数", "defaultValue": 0}, "registration_deadline": {"bsonType": "timestamp", "description": "报名截止时间"}, "prize_pool": {"bsonType": "object", "description": "奖金池", "properties": {"currency": {"bsonType": "string", "description": "货币类型", "defaultValue": "CNY"}, "amount": {"bsonType": "number", "description": "奖金总额"}, "distribution": {"bsonType": "array", "description": "奖金分配", "items": {"bsonType": "object", "properties": {"rank": {"bsonType": "int", "description": "名次"}, "amount": {"bsonType": "number", "description": "奖金额度"}}}}}}, "create_date": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}}}