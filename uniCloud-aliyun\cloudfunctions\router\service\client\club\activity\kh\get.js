'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/activity/kh/get 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.selects({
			dbName: "club-activity-data",
			getOne: true,
			// 主表where条件
			whereJson: {
				_id: id
			},
			// 副表列表
			foreignDB: [{
				dbName: "club-card-data",
				localKey: "card.id",
				localKeyType: "array",
				foreignKey: "_id",
				as: "card_list",
				limit: 1000
			}]
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}