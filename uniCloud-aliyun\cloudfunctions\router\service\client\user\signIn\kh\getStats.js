'use strict';
module.exports = {
	/**
	 * 获取总统计数据
	 * @url client/user/signIn/kh/getStats 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		// 获取起止年份
		let { startTime: start_year, endTime: end_year } = vk.pubfn.getYearOffsetStartAndEnd(0, new Date());
		// 获取起止月份
		let { startTime: start_month, endTime: end_month } = vk.pubfn.getMonthOffsetStartAndEnd(0, new Date());
		const dbName = 'sign-info-list'
		// 年度统计
		// 统计年度打卡次数、场地
		let totalSites = await vk.baseDao.count({
			dbName,
			whereJson: {
				user_id: uid,
			},
			groupJson: {
				_id: "$data_id",
			},
		});

		let yearSites = await vk.baseDao.count({
			dbName,
			whereJson: {
				user_id: uid,
				_add_time: _.lte(end_year).gte(start_year)
			},
			groupJson: {
				_id: "$data_id",
			},
		});

		let yearCount = await vk.baseDao.count({
			dbName,
			whereJson: {
				user_id: uid,
				_add_time: _.lte(end_year).gte(start_year)
			},
		});

		// 获取月份统计
		let monthSites = await vk.baseDao.count({
			dbName,
			// 主表where条件
			whereJson: {
				user_id: uid,
				_add_time: _.lte(end_month).gte(start_month)
			},
			groupJson: {
				_id: "$data_id",
			},
		});

		let monthCount = await vk.baseDao.count({
			dbName,
			// 主表where条件
			whereJson: {
				user_id: uid,
				_add_time: _.lte(end_month).gte(start_month)
			},
		});


		res.data = {
			totalSites: totalSites || 0,
			yearSites: yearSites || 0,
			yearCount: yearCount || 0,
			monthSites: monthSites || 0,
			monthCount: monthCount || 0
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}