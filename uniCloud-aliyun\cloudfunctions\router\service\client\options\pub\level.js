'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/options/pub/level 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		res.data = [
			{ label: '无要求', value: 0 },
			{ label: '1.0', value: 1.0 },
			{ label: '1.5', value: 1.5 },
			{ label: '2.0', value: 2.0 },
			{ label: '2.5', value: 2.5 },
			{ label: '3.0', value: 3.0 },
			{ label: '3.5', value: 3.5 },
			{ label: '4.0', value: 4.0 },
			{ label: '4.5', value: 4.5 },
			{ label: '5.0', value: 5.0 },
			{ label: '5.5', value: 5.5 },
			{ label: '6.0', value: 6.0 },
			{ label: '6.5', value: 6.5 },
			{ label: '7.0', value: 7.0 }
		]

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}