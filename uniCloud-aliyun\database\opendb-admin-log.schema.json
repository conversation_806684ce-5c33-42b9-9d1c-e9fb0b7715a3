{"bsonType": "object", "required": ["user_id", "content"], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "user_id": {"bsonType": "string", "description": "用户id，参考uni-id-users表"}, "user_name": {"bsonType": "string", "description": "用户名或昵称，冗余设计", "title": "用户"}, "content": {"bsonType": "string", "description": "日志内容，如：修改密码", "title": "内容"}, "ip": {"bsonType": "string", "description": "ip地址", "title": "IP", "forceDefaultValue": {"$env": "clientIP"}}, "create_date": {"bsonType": "timestamp", "description": "创建时间", "title": "时间", "forceDefaultValue": {"$env": "now"}}}, "version": "0.0.1"}