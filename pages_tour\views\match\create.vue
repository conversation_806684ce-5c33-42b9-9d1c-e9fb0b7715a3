<template>
  <view class="page">
    <t-navbar title="发起赛事" />
    <view class="content">
      <t-form ref="form" :model="formData" :rules="rules">
        <!-- 基本信息 -->
        <t-group title="基本信息">
          <t-form-item label="赛事名称" prop="title">
            <t-input v-model="formData.title" placeholder="请输入赛事名称" />
          </t-form-item>
          <t-form-item label="赛事级别" prop="level">
            <t-picker :options="levelOptions" v-model="formData.level" placeholder="请选择赛事级别" />
          </t-form-item>
          <t-form-item label="赛事说明" prop="desc">
            <t-input type="textarea" v-model="formData.desc" placeholder="请输入赛事说明" :maxlength="500" />
          </t-form-item>
        </t-group>

        <!-- 时间地点 -->
        <t-group title="时间地点" marginTop="20">
          <t-form-item label="开始日期" prop="start_date">
            <t-picker mode="date" v-model="formData.start_date" :start="today" placeholder="请选择开始日期" />
          </t-form-item>
          <t-form-item label="结束日期" prop="end_date">
            <t-picker mode="date" v-model="formData.end_date" :start="formData.start_date || today" placeholder="请选择结束日期" />
          </t-form-item>
          <t-form-item label="比赛场地" prop="venue">
            <t-input v-model="formData.venue.name" placeholder="请选择比赛场地" disabled @click="selectVenue" />
          </t-form-item>
        </t-group>

        <!-- 报名设置 -->
        <t-group title="报名设置" marginTop="20">
          <t-form-item label="报名费用" prop="registration.fee">
            <t-input type="number" v-model="formData.registration.fee" placeholder="请输入报名费用">
              <text slot="prefix">¥</text>
            </t-input>
          </t-form-item>
          <t-form-item label="报名人数" prop="registration.max">
            <t-input type="number" v-model="formData.registration.max" placeholder="请输入最大报名人数" />
          </t-form-item>
          <t-form-item label="报名截止" prop="registration.deadline">
            <t-picker
              mode="date"
              v-model="formData.registration.deadline"
              :start="today"
              :end="formData.start_date"
              placeholder="请选择报名截止日期"
            />
          </t-form-item>
          <t-form-item label="参赛要求" prop="registration.requirements">
            <t-picker :options="levelOptions" v-model="formData.registration.requirements.min_level" placeholder="请选择最低水平要求" />
          </t-form-item>
        </t-group>

        <!-- 赛制设置 -->
        <t-group title="赛制设置" marginTop="20">
          <t-form-item label="比赛形式" prop="format">
            <t-picker :options="formatOptions" v-model="formData.format" placeholder="请选择比赛形式" />
          </t-form-item>
          <t-form-item label="比赛规则" prop="rules.match_format">
            <t-input type="textarea" v-model="formData.rules.match_format" placeholder="请输入比赛规则说明" :maxlength="500" />
          </t-form-item>
        </t-group>

        <!-- 奖金设置 -->
        <t-group title="奖金设置" marginTop="20">
          <t-form-item label="总奖金" prop="prize.total_amount">
            <t-input type="number" v-model="formData.prize.total_amount" placeholder="请输入总奖金">
              <text slot="prefix">¥</text>
            </t-input>
          </t-form-item>
          <t-form-item label="分配方案" prop="prize.distribution">
            <t-input type="textarea" v-model="formData.prize.distribution_text" placeholder="请输入奖金分配方案，如：冠军:5000" :maxlength="500" />
          </t-form-item>
        </t-group>
      </t-form>

      <!-- 提交按钮 -->
      <t-bottom>
        <t-button type="primary" :loading="isSubmitting" @click="handleSubmit">发布赛事</t-button>
      </t-bottom>
    </view>
  </view>
</template>

<script>
import dayjs from "dayjs";

export default {
  data() {
    return {
      isSubmitting: false,
      formData: {
        title: "",
        level: "",
        desc: "",
        start_date: "",
        end_date: "",
        venue: {
          name: "",
          address: "",
          location: null,
        },
        registration: {
          fee: "",
          max: "",
          deadline: "",
          requirements: {
            min_level: "",
            age_limit: {
              min: 0,
              max: 100,
            },
            gender: "不限",
          },
        },
        format: "",
        rules: {
          match_format: "",
          scoring_system: "常规计分",
          group_format: "",
          rest_time: "场次间隔不少于30分钟",
        },
        prize: {
          total_amount: "",
          currency: "CNY",
          distribution_text: "",
          distribution: [],
        },
      },
      // 表单验证规则
      rules: {
        title: [{ required: true, message: "请输入赛事名称" }],
        level: [{ required: true, message: "请选择赛事级别" }],
        desc: [{ required: true, message: "请输入赛事说明" }],
        start_date: [{ required: true, message: "请选择开始日期" }],
        end_date: [{ required: true, message: "请选择结束日期" }],
        "venue.name": [{ required: true, message: "请选择比赛场地" }],
        "registration.fee": [{ required: true, message: "请输入报名费用" }],
        "registration.max": [{ required: true, message: "请输入最大报名人数" }],
        "registration.deadline": [{ required: true, message: "请选择报名截止日期" }],
        format: [{ required: true, message: "请选择比赛形式" }],
        "rules.match_format": [{ required: true, message: "请输入比赛规则说明" }],
      },
      // 选项数据
      levelOptions: [
        { text: "A级", value: "A" },
        { text: "B级", value: "B" },
        { text: "C级", value: "C" },
      ],
      formatOptions: [
        { text: "单打", value: "singles" },
        { text: "双打", value: "doubles" },
        { text: "团体赛", value: "team" },
      ],
    };
  },
  computed: {
    today() {
      return dayjs().format("YYYY-MM-DD");
    },
  },
  methods: {
    // 选择场地
    selectVenue() {
      // TODO: 打开场地选择页面
      uni.navigateTo({
        url: "/pages/public/select/venue",
      });
    },

    // 处理表单提交
    async handleSubmit() {
      try {
        // 表单验证
        await this.$refs.form.validate();

        if (this.isSubmitting) return;
        this.isSubmitting = true;

        // 处理奖金分配方案
        this.formData.prize.distribution = this.formData.prize.distribution_text
          .split("\n")
          .filter((line) => line.trim())
          .map((line) => {
            const [rank, amount] = line.split(":").map((s) => s.trim());
            return { rank, amount: Number(amount) };
          });

        // 提交数据
        const res = await vk.callFunction({
          url: "client/tour/match/kh/create",
          data: this.formData,
        });

        if (res.code === 0) {
          uni.showToast({
            title: "发布成功",
            icon: "success",
          });
          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        }
      } catch (e) {
        console.error(e);
        uni.showToast({
          title: e.message || "发布失败",
          icon: "none",
        });
      }
      this.isSubmitting = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content {
  padding: 20rpx;
  padding-bottom: 120rpx;
}
</style>
