<template>
	<view class="page-content">
		<t-navbar title="新增成员"></t-navbar>
		<t-input label="名称" v-model="info.name" placeholder="请输入用户昵称"></t-input>
		<t-input label="备注信息" textarea v-model="info.text" placeholder="请输入备注信息"></t-input>

		<view class="button-box">
			<t-button color="#0171BC" text-color="#fff" @click="addUser">提交</t-button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: null,
				info: {
					name: '',
					text: '',
				}
			}
		},
		onLoad() {
			this.id = vk.getVuex('$club.id')
		},
		methods: {
			async addUser() {
				try {
					await vk.callFunction({
						url: 'client/club/member/kh/add',
						title: '请求中...',
						data: {
							name: this.info.name,
							avatar: this.info.avatar,
							text: this.info.text,
							club_id: this.id
						},
					});
					uni.$emit('refresh-member')
					vk.toast('添加成功', 'success', true, () => {
						uni.navigateBack()
					});
				} catch (err) {
					vk.toast('添加失败:' + err);
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-content {
		padding: 30rpx;
		box-sizing: border-box;
	}

	.button-box {
		margin-top: 30rpx;
		padding-bottom: 80rpx;
		display: flex;
		align-items: center;
		justify-content: space-around;
	}
</style>