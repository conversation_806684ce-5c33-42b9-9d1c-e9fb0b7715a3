<template>
  <scroll-view class="t-tabs" enable-flex scroll-x :scroll-into-view="'tab' + modelValue" :style="[customStyle]">
    <div v-for="(item, index) in list" :key="index" :id="'tab' + index" class="t-tabs__box">
      <div class="t-tabs__box__block" @click="changeCurrent(index)">
        <text class="t-tabs__box__block__text" :class="[modelValue == index ? 'active-text' : '']">{{ item }}</text>
        <div class="t-tabs__box__block__slider" :class="[modelValue == index ? 'active-block' : '']" :style="[sliderStyle]" />
      </div>
    </div>
  </scroll-view>
</template>

<script>
export default {
  name: "t-tabs",
};
</script>

<script setup>
import { computed, onMounted } from "vue";

const props = defineProps({
  modelValue: {
    type: Number,
    default: 0,
  },
  list: {
    type: Array,
    default: () => [],
  },
  blockColor: {
    type: String,
    default: "#0171bc",
  },
  color: {
    type: String,
    default: "#333",
  },
  customStyle: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:modelValue", "change"]);

const sliderStyle = computed(() => {
  return {
    backgroundColor: props.blockColor,
  };
});

const changeCurrent = (index) => {
  emit("update:modelValue", index);
  emit("change", index);
};

onMounted(() => {});
</script>

<style lang="scss" scoped>
.t-tabs {
  width: 100%;
  height: 24px;
  display: flex;
  box-sizing: border-box;

  &__box {
    height: 100%;
    flex-shrink: 0;
    display: flex;
    align-items: flex-end;
    position: relative;
    margin-right: 30rpx;

    &__block {
      position: relative;
      flex-shrink: 0;

      &__text {
        font-size: 36rpx;
        color: #8a8a8a;
        font-family: usic;
        position: relative;
        z-index: 2;

        &.active-text {
          color: #000;
        }
      }

      &__slider {
        width: 0%;
        height: 20rpx;
        position: absolute;
        bottom: 10rpx;
        z-index: 1;
        transition: all 0.3s ease;

        &.active-block {
          width: 100%;
        }
      }
    }
  }
}
</style>
