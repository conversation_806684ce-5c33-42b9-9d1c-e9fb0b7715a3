'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/card/kh/addUserCard 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { user_id, card_id, club_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// 获取卡片信息
		let info = await vk.baseDao.findById({
			dbName: "club-card-data",
			id: card_id,
		});
		let value = info.value
		let end = info.end
		let start = vk.pubfn.getOffsetTime(new Date(), {})

		// 查找验证用户是否已经拥有此卡片
		let has = await vk.baseDao.findByWhereJson({
			dbName: "club-user-card",
			whereJson: {
				user_id,
				card_id,
				club_id,
			},
		});
		// 拥有该会员卡，重复购买续卡
		if (has && !info.repeat) {
			res.msg = "该卡已购买，无法重复购买"
			res.code = -1
			return res
		}
		// 可重复购买情况下处理值
		if (has) {
			switch (info.type) {
				case "day":
					value = has.value
					end = vk.pubfn.getOffsetTime(new Date(has.end), {
						day: Number(info.day),
						mode: "after",
					});
					break;
				case "date":
					res.msg = "定期卡不支持续购!"
					res.code = -1
					return res
				case "times":
				case "save":
					value += has.value
					end = vk.pubfn.getOffsetTime(new Date(), {
						day: Number(info.day),
						mode: "after",
					});
					break;
			}
			await vk.baseDao.update({
				dbName: "club-user-card",
				whereJson: {
					_id: has._id
				},
				dataJson: {
					value: Number(value),
					start,
					end,
				}
			});
			res.msg = "续卡成功!"
		} else {
			switch (info.type) {
				case "day":
					value = start
					end = vk.pubfn.getOffsetTime(new Date(), {
						day: info.day,
						mode: "after",
					});
					break;
				case "date":
					break;
				case "times":
				case "save":
					end = vk.pubfn.getOffsetTime(new Date(), {
						day: info.day,
						mode: "after",
					});
					break;
			}
			await vk.baseDao.add({
				dbName: "club-user-card",
				dataJson: {
					card_id,
					user_id,
					club_id,
					value,
					start,
					end,
				}
			});
			res.msg = "购买成功"
		}

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}