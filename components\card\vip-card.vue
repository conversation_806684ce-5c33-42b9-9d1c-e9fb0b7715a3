<template>
  <view class="vip-card" :style="[cardStyle, customStyle]" @click="handleCardClick">
    <!-- Card Header -->
    <view class="card-header">
      <text class="card-name">{{ cardData.name }}</text>
      <text class="card-type">{{ typeLabel }}</text>
    </view>

    <!-- Card Value -->
    <view class="card-value">
      <view v-if="isDateRange" class="date-range">
        <text class="start-date">{{ valueDisplay }}</text>
        <text class="separator">—</text>
        <text class="end-date">{{ endDateDisplay }}</text>
      </view>
      <text v-else class="value-text">{{ valueDisplay }}</text>
    </view>

    <!-- Card Footer -->
    <view class="card-footer">
      <view class="club-info">
        <text class="club-label">所属俱乐部</text>
        <text class="club-name">{{ cardData.club_info?.name || "未知俱乐部" }}</text>
      </view>

      <view v-if="showExpiryDate" class="expiry-info">
        <text class="expiry-label">到期时间</text>
        <text class="expiry-date">{{ endDateDisplay }}</text>
      </view>
    </view>

    <!-- Slot for additional content -->
    <slot></slot>
  </view>
</template>

<script>
import dayjs from "dayjs";

export default {
  name: "VipCard",

  props: {
    data: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    value: {
      type: Number,
      default: 0,
    },
    customStyle: {
      type: Object,
      default: () => ({}),
    },
  },

  emits: ["click"],

  computed: {
    // 卡片数据别名
    cardData() {
      return this.data || {};
    },

    // 是否显示到期日期
    showExpiryDate() {
      return this.cardData.type === "times" || this.cardData.type === "save";
    },

    // 是否为日期范围显示
    isDateRange() {
      return this.cardData.type === "day" || this.cardData.type === "date";
    },

    // 卡片类型标签
    typeLabel() {
      const typeMap = {
        times: "次卡",
        date: "定期卡",
        day: "天卡",
        save: "储蓄卡",
      };

      return typeMap[this.cardData.type] || "未知类型";
    },

    // 结束日期显示
    endDateDisplay() {
      const { end, type, value, start, day } = this.cardData;
      let targetDate = end;

      if (type === "day") {
        targetDate = dayjs(start).add(value, "day").valueOf();
      } else if (type === "date") {
        targetDate = dayjs(start).add(day, "day").valueOf();
      }

      return dayjs(targetDate).format("YYYY-MM-DD");
    },

    // 值显示
    valueDisplay() {
      const { type, start, value } = this.cardData;

      switch (type) {
        case "date":
          return value ? dayjs(value).format("YYYY-MM-DD") : dayjs().format("YYYY-MM-DD");
        case "day":
          return dayjs(start).format("YYYY-MM-DD");
        default:
          return value || 0;
      }
    },

    // 卡片样式
    cardStyle() {
      const style = this.cardData.style || {};
      const baseStyle = {
        color: style.text_color || "#333",
      };

      if (style.type === "image") {
        baseStyle.backgroundImage = `url(${style.background})`;
        baseStyle.backgroundSize = "cover";
        baseStyle.backgroundPosition = "center";
      } else if (style.type === "color") {
        baseStyle.background = `linear-gradient(120deg, ${style.main_color || "#0171BC"} 50%, ${style.second_color || "#0056A3"} 100%)`;
      } else {
        // 默认渐变背景
        baseStyle.background = "linear-gradient(120deg, #0171BC 50%, #0056A3 100%)";
      }

      return baseStyle;
    },
  },

  methods: {
    // 处理卡片点击
    handleCardClick() {
      this.$emit("click", this.cardData);
    },
  },
};
</script>

<style lang="scss" scoped>
.vip-card {
  width: 680rpx;
  height: 360rpx;
  border-radius: 24rpx;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(4rpx);
  }

  // 添加光泽效果
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
    pointer-events: none;
  }
}

// Card Header
.card-header {
  position: absolute;
  top: 50rpx;
  left: 30rpx;
  right: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.card-name {
  flex: 1;
  font-size: 40rpx;
  font-weight: bold;
  margin-right: 20rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.card-type {
  font-size: 40rpx;
  font-weight: bold;
  flex-shrink: 0;
  opacity: 0.9;
}

// Card Value
.card-value {
  position: absolute;
  right: 50rpx;
  top: 50%;
  transform: translateY(-50%);
  text-align: center;
}

.date-range {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8rpx;

  .start-date,
  .end-date {
    font-size: 32rpx;
    font-weight: bold;
    opacity: 0.9;
  }

  .separator {
    font-size: 28rpx;
    font-weight: normal;
    opacity: 0.7;
  }
}

.value-text {
  font-size: 80rpx;
  font-weight: bold;
  opacity: 0.9;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

// Card Footer
.card-footer {
  position: absolute;
  bottom: 30rpx;
  left: 30rpx;
  right: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.club-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;

  .club-label {
    font-size: 22rpx;
    opacity: 0.8;
  }

  .club-name {
    font-size: 26rpx;
    font-weight: 500;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 300rpx;
  }
}

.expiry-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4rpx;

  .expiry-label {
    font-size: 22rpx;
    opacity: 0.8;
  }

  .expiry-date {
    font-size: 26rpx;
    font-weight: 500;
  }
}

// Responsive Design
@media screen and (max-width: 750rpx) {
  .vip-card {
    width: 100%;
    max-width: 680rpx;
    height: 320rpx;
  }

  .card-header {
    top: 40rpx;
    left: 24rpx;
    right: 24rpx;
  }

  .card-name {
    font-size: 36rpx;
  }

  .card-type {
    font-size: 36rpx;
  }

  .card-value {
    right: 40rpx;
  }

  .date-range {
    .start-date,
    .end-date {
      font-size: 28rpx;
    }

    .separator {
      font-size: 24rpx;
    }
  }

  .value-text {
    font-size: 70rpx;
  }

  .card-footer {
    bottom: 24rpx;
    left: 24rpx;
    right: 24rpx;
  }

  .club-info {
    .club-label {
      font-size: 20rpx;
    }

    .club-name {
      font-size: 24rpx;
      max-width: 250rpx;
    }
  }

  .expiry-info {
    .expiry-label {
      font-size: 20rpx;
    }

    .expiry-date {
      font-size: 24rpx;
    }
  }
}

// 特殊卡片类型样式
.vip-card[data-type="times"] {
  .value-text {
    font-size: 100rpx;
  }
}

.vip-card[data-type="save"] {
  .value-text {
    font-size: 60rpx;

    &::after {
      content: "元";
      font-size: 32rpx;
      margin-left: 8rpx;
      opacity: 0.8;
    }
  }
}
</style>
