<script>
	import dayjs from 'dayjs'
	export default {
		data() {
			return {
				site_list: [],
				pages: 1,
				total: 0,
				searchValue: '',
				system: uni.getSystemInfoSync(),
				dayList: []
			}
		},
		onReachBottom() {
			if (this.site_list.length >= this.total) return
			this.pages += 1
			this.getData()
		},
		onLoad() {
			this.getData()
		},
		destroyed() {
			uni.$off('refresh-site')
		},
		methods: {
			async getData() {
				let data = await vk.callFunction({
					url: 'client/club/site/pub/list',
					title: '请求中...',
					data: {
						club_id: this.club_id,
						pageIndex: this.pages
					},
				});
				this.total = data.total
				this.site_list.push(...data.rows)
			},
			newSite() {
				uni.$once('refresh-site', () => {
					this.site_list = []
					this.pages = 1
					this.getData()
				})
				vk.navigateTo('/pages_club/views/site/submit');
			},
			siteDetail(id) {
				vk.navigateTo(`/pages_club/views/site/detail?id=${id}`);
			}
		},
		mounted() {
			const date = dayjs().date()
			const day = dayjs().day()
		}
	}
</script>

<template>
	<view>
		<t-navbar title="管理场地" />
		<view class="site-list">
			<view v-for="item in site_list" :key="item._id" class="site-block" @click="siteDetail(item._id)">
				<image class="site-cover" :src="item.images[0]" mode="aspectFill" />
				<view class="site-info">
					<view class="site-name">{{ item.name }}</view>
					<view class="site-tags">
						<t-tag v-for="tag in item.tags" :text="tag"></t-tag>
					</view>
				</view>
			</view>
			<l-empty v-if="site_list.length === 0" text="暂无数据" />
			<t-bottom>
				<view style="width: 100%;">
					<t-button width="100%" radius="68" @click="newSite">新增场地</t-button>
				</view>
			</t-bottom>
		</view>
	</view>
</template>

<style scoped lang="scss">
	.filter-top {
		position: sticky;
		width: 100%;
		background-color: #fff;
		padding: 10rpx 30rpx;
		box-sizing: border-box;
		display: flex;
		align-items: center;
		justify-content: space-between;
		z-index: 9;

		.search-box {
			flex: 1;
			height: 88rpx;
			background-color: #f8f8f8;
			border-radius: 14rpx;
			display: flex;
			align-items: center;
			padding: 20rpx;
			box-sizing: border-box;

			.search-icon {
				width: 36rpx;
				height: 36rpx;
				margin-right: 10rpx;
			}

			.search-input {
				font-size: 28rpx;
				flex: 1;
			}
		}

		.add-btn {
			background-color: #0171BC;
			width: 88rpx;
			height: 88rpx;
			border-radius: 14rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-left: 30rpx;

			image {
				width: 48rpx;
				height: 48rpx;
			}
		}
	}

	.site {
		&-list {
			padding: 0 20rpx;
		}

		&-block {
			display: flex;
			border-bottom: 2rpx solid #f8f8f8;
			position: relative;
			border-radius: 24rpx;
			overflow: hidden;
			width: 100%;
			height: 60vw;
			margin-top: 20rpx;
		}

		&-cover {
			width: 100%;
			height: 100%;
			position: absolute;
			top: 0;
			left: 0;
		}

		&-info {
			position: absolute;
			bottom: 10rpx;
			left: 10rpx;
			right: 10rpx;
			margin: 0 auto;
			background-color: rgba(255,255,255,0.6);
			backdrop-filter: blur(4px);
			border-radius: 24rpx;
			padding: 20rpx 40rpx;
			box-sizing: border-box;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
		}

		&-name {
			font-size: 36rpx;
			color: #000;
			font-weight: bold;
		}

		&-tags {
			display: flex;
			align-items: center;
			margin-top: 20rpx;
		}
	}
</style>