'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/signIn/kh/getDates 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, year } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let str = `${year}-01-01`
		let { startTime, endTime } = vk.pubfn.getYearOffsetStartAndEnd(0, new Date(str));
		let dates = await vk.baseDao.selects({
			dbName: "sign-info-list",
			getMain: true,
			pageIndex: 1,
			pageSize: 1000,
			// 主表where条件
			whereJson: {
				user_id: uid,
				_add_time: _.gte(startTime).lte(endTime)
			},
			groupJson: {
				_id: "$date",
			},
			sortArr: [{ name: "money", type: "desc" }], // 对分组后的结果进行排序
		});

		res.data = dates.map(item => item._id)


		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}