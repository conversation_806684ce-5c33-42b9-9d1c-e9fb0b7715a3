<template>
  <view class="page-content">
    <t-navbar navStyle="custom" :customStyle="style"></t-navbar>
    <view class="title">
      <view class="title-back">{{ titleInfo.en }}</view>
      <view class="title-text">{{ titleInfo.cn }}</view>
    </view>
    <template v-if="isLogined">
      <view>
        <button class="avatar" open-type="chooseAvatar" @chooseavatar="chooseAvatar">
          <image :src="loginInfo.avatar || defaultAvatar" mode="aspectFill"></image>
        </button>
        <wd-input
          label="昵称"
          label-width="100rpx"
          type="nickname"
          custom-style="border-radius:24rpx"
          placeholder="请输入用户昵称"
          v-model="loginInfo.nickname"
        ></wd-input>
      </view>
      <wd-button type="primary" block size="large" @click="saveInfo" custom-style="margin-top:30rpx;">保存信息</wd-button>
    </template>
    <template v-else>
      <view style="margin-top: 200rpx">
        <wd-button v-if="!agreement" type="primary" size="large" block @click="vk.toast('请先阅读并同意用户协议与隐私协议')">{{
          !unRegister ? "快捷登录" : "手机号快捷登录"
        }}</wd-button>
        <wd-button v-else-if="!unRegister" type="primary" size="large" block @click="login">快捷登录</wd-button>
        <wd-button v-else-if="unRegister" open-type="getPhoneNumber" type="primary" size="large" block @getphonenumber="register"
          >手机号快捷登录</wd-button
        >
        <view class="agreement">
          <wd-checkbox v-model="agreement" shape="square"></wd-checkbox>

          <text @click="agreement = !agreement">我已阅读并同意来网球</text>
          <text class="main" @click="toUrl('/pages/public/agreement?type=user')">《用户协议》</text>
          <text @click="agreement = !agreement">和</text>
          <text class="main" @click="toUrl('/pages/public/agreement?type=privacy')">《隐私协议》</text>
        </view>
      </view>
    </template>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      style: {
        "background-color": "#f8f8f8",
        color: "#000",
      },
      loginInfo: {
        avatar: "",
        nickname: "",
      },

      titleInfo: {
        en: "Login",
        cn: "用户登录",
      },

      agreement: false,
      defaultAvatar: "https://cdn.cometennis.cn/images/default-head.png",

      encryptedKey: "",
      unRegister: true,
      isLogined: false,
    };
  },
  watch: {
    "$store.state.$user.userInfo": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal, oldVal) {
        this.userInfo = newVal;
      },
    },
  },
  async onLoad() {
    // 检测是否已登录
    uni.login({
      provider: "weixin",
      success: async (result) => {
        let res = await vk.callFunction({
          url: "user/pub/code2SessionWeixin",
          title: "请求中...",
          data: {
            code: result.code,
          },
        });
        this.encryptedKey = res.encryptedKey;
        // 验证是否已是注册的用户
        let status = await vk.callFunction({
          url: "user/pub/checkUserInfo",
          title: "请求中...",
          data: {
            encryptedKey: this.encryptedKey,
          },
        });
        this.unRegister = status.data;
        if (status.data) return;
        // this.login();
      },
    });
  },
  methods: {
    async login() {
      uni.showLoading({
        title: "登录中...",
      });
      uni.vk.userCenter.loginByWeixin({
        success: () => {
          vk.toast("登陆成功", "success", true, () => {
            this.returnUrl();
          });
        },
      });
    },
    async register(e) {
      let { encryptedData, iv } = e;
      if (!encryptedData || !iv) return;
      vk.userCenter.loginByWeixinPhoneNumber({
        data: {
          encryptedData,
          iv,
          encryptedKey: this.encryptedKey,
        },
        success: (res) => {
          if (res.type === "register") {
            this.isLogined = true;
            this.titleInfo = {
              en: "Info",
              cn: "完善信息",
            };
          } else {
            uni.navigateBack({ delta: 1 });
          }
        },
      });
    },
    toUrl(url) {
      vk.navigateTo(url);
    },
    chooseAvatar(e) {
      this.loginInfo.avatar = e.detail.avatarUrl;
    },
    saveInfo() {
      vk.showLoading("更新中...");
      const { avatar, nickname } = this.loginInfo;
      if (!avatar) {
        vk.toast("请上传头像!");
        return;
      } else if (!nickname) {
        vk.toast("请填写昵称!");
        return;
      }
      vk.uploadFile({
        filePath: this.loginInfo.avatar,
        needSave: true,
        cloudDirectory: `photo/avatar`,
        category_id: "avatar",
        success: async (res) => {
          let url = res.url;
          vk.userCenter.updateUser({
            data: {
              nickname,
              avatar: url,
            },
            success: (data) => {
              // 成功后的逻辑
              vk.toast("登陆成功", "success", true, () => {
                this.returnUrl();
              });
            },
          });
        },
        fail: (err) => {
          // 上传失败
          vk.toast("头像上传失败，请重新尝试", "none");
        },
      });
    },
    returnUrl() {
      let url = uni.getStorageSync("next_url");
      const pageNum = getCurrentPages().length;
      if (url) {
        vk.reLaunch(url);
        uni.removeStorageSync("next_url");
      } else {
        if (pageNum >= 2) {
          uni.navigateBack();
        } else {
          uni.reLaunch({
            url: "/pages/index/index",
          });
        }
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-content {
  height: 100vh;
  padding: 60rpx 60rpx 0rpx;

  .title {
    position: relative;
    margin-bottom: 100rpx;

    &-back {
      font-size: 120rpx;
      font-weight: bold;
      color: #f1f1f1;
    }

    &-text {
      font-size: 60rpx;
      font-weight: bold;
      color: #000;
      position: absolute;
      bottom: 0;
      left: 20rpx;
    }
  }

  .avatar {
    width: 160rpx;
    height: 160rpx;
    padding: 0;
    background-color: transparent;
    margin-bottom: 30rpx;
    border-radius: 50%;

    &::after {
      content: none;
    }

    image {
      width: 100%;
      height: 100%;
    }
  }
}

.agreement {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  color: #ccc;
  margin-top: 20rpx;

  .main {
    margin: 0 6rpx;
    color: $primary-color;
  }

  .checkbox {
    width: 40rpx;
    height: 40rpx;
  }
}
</style>
