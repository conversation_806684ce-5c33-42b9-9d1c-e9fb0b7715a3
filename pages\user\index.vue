<template>
  <view class="page-container" v-if="userInfo">
    <t-navbar
      nav-style="custom"
      :backIcon="false"
      :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop > 170 ? 1 : 0})` }"
      :placeholder="false"
    >
      <view v-if="scrollTop > 170" class="nav-info">
        <image class="avatar" :src="userInfo.avatar" mode="aspectFill" />
        <view class="name">{{ userInfo.nickname }}</view>
      </view>
    </t-navbar>
    <view class="top-info">
      <image class="background" :src="backgroundImage" mode="aspectFill" @click="updateBackground"></image>
    </view>

    <view class="content">
      <view class="user-info">
        <image class="avatar" :src="userInfo.avatar" mode="aspectFill" @click="toUrl('/pages/user/edit/base')"> </image>
        <view class="name" :style="{ color: darkTheme ? '#fff' : '#000' }">{{ userInfo.nickname }}</view>
        <view class="tag">
          <!-- <wd-tag type="primary">Lv 1</wd-tag> -->
          <wd-tag v-if="userInfo.level" type="primary">{{ userInfo.level.toFixed(1) }}</wd-tag>
        </view>
        <view class="statistic">
          <view class="block">
            <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.created" :fontSize="20" color="#000"></wd-count-to>
            <view class="label">发起活动</view>
          </view>
          <view class="block">
            <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.joined" :fontSize="20" color="#000"></wd-count-to>
            <view class="label">参与活动</view>
          </view>
          <view class="block" @click="viewUserList('follow')">
            <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.follow" :fontSize="20" color="#000"></wd-count-to>
            <view class="label">关注</view>
          </view>
          <view class="block" @click="viewUserList('fans')">
            <wd-count-to custom-class="value" :startVal="0" :endVal="dataStatistic.fans" :fontSize="20" color="#000"></wd-count-to>
            <view class="label">粉丝</view>
          </view>
          <!-- <view class="block">
						<view class="value">0</view>
						<view class="label">参赛记录</view>
					</view>
					<view class="block">
						<view class="value">100</view>
						<view class="label">信用值</view>
					</view> -->
        </view>
      </view>
      <!-- <view class="order">
				<view class="order-top">
					<text>我的订单</text>
				</view>
				<view class="order-list">
					<view class="order-block">
						<view class="order-title">全部</view>
						<view class="order-value">0</view>
					</view>
					<view class="order-block">
						<view class="order-title">待付款</view>
						<view class="order-value">0</view>
					</view>
					<view class="order-block">
						<view class="order-title">已付款</view>
						<view class="order-value">0</view>
					</view>
					<view class="order-block">
						<view class="order-title">退款</view>
						<view class="order-value">0</view>
					</view>
				</view>
			</view> -->
      <view class="action">
        <wd-cell-group custom-class="action-group">
          <wd-cell title="个人资料" value="完善用户信息" center is-link @click="toUrl('/pages/user/edit/base')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}user-edit-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
          <wd-cell title="我的钱包" value="查看我的钱包" center is-link @click="toUrl('/pages/user/wallet')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}wallet-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
          <wd-cell title="打卡日历" value="球场打卡统计日历" center is-link @click="toUrl('/pages/user/signIn')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}calendar-month-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
          <wd-cell title="约球信息" value="公开信息找球友" center is-link @click="toUrl('/pages/explore/player/submit')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}emoji-2-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
          <wd-cell title="水平测验" value="测验网球水平等级" center is-link @click="toUrl('/pages/user/level')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}vip-3-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
          <!-- <wd-cell title="比赛记录" is-link></wd-cell> -->
          <wd-cell title="平台协议" value="查看平台协议内容" center is-link @click="toUrl('/pages/user/agreement')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}paper-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
          <wd-cell title="使用反馈" value="使用体验或系统问题" center is-link @click="toUrl('/pages/user/feedback')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}edit-4-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
          <wd-cell title="系统设置" is-link center @click="toUrl('/pages/user/setting')">
            <template #icon>
              <image class="cell-icon" :src="`${iconUrl}settings-3-line.svg`" mode="aspectFit"></image>
            </template>
          </wd-cell>
        </wd-cell-group>
      </view>
    </view>
    <t-tabbar></t-tabbar>
  </view>
</template>

<script>
export default {
  data() {
    return {
      imgUrl: vk.getVuex("$app.config.staticUrl.image"),
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      userInfo: vk.getVuex("$user.userInfo"),
      scrollTop: 0,
      current: 0,
      commentNum: 0,
      scoreData: null,
      darkTheme: false,
      dataStatistic: { created: 0, joined: 0, follow: 0, fans: 0 },
    };
  },
  watch: {
    "$store.state.$user.userInfo": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal) {
        this.userInfo = newVal;
      },
    },
  },
  computed: {
    backgroundImage() {
      let image = this.userInfo.back_cover || `${this.imgUrl}default-back_cover.png`;
      return image;
    },
    checkText() {
      return this.userInfo.level ? "重新测评" : "开始测评";
    },
    levelValue() {
      if (this.userInfo.level) {
        return this.userInfo.level.toFixed(1);
      } else {
        return "未检测";
      }
    },
  },
  onShow() {
    let status = vk.getVuex("$status.user_reload");
    if (status) {
      this.getUserStatistic();
      // this.getUserOrder()
      vk.setVuex("$status.user_reload", false);
    }
  },
  onLoad() {
    uni.hideTabBar();
    this.getUserStatistic();
    this.getUserInfo();
    // this.getUserOrder()
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onShareAppMessage() {
    return {
      title: `${this.userInfo.nickname}的个人主页`,
      path: `/pages/user/detail?id=${this.userInfo._id}`,
      imageUrl: this.userInfo.avatar,
    };
  },
  methods: {
    // 更新用户信息
    getUserInfo() {
      vk.userCenter.getCurrentUserInfo({
        loading: false,
      });
    },
    // 去查看用户列表
    viewUserList(type) {
      let user_id = this.userInfo._id;
      vk.navigateTo(`/pages/user/list?type=${type}&user_id=${user_id}`);
    },
    async getUserStatistic() {
      let data = await vk.callFunction({
        url: "client/user/kh/statistic",
        title: "请求中...",
      });
      this.$set(this, "dataStatistic", data.data);
    },
    toUrl(url) {
      vk.navigateTo(url);
    },
    updateBackground() {
      uni.chooseImage({
        count: 1,
        success: async (res) => {
          let value = vk.pubfn.deepClone(this.value);
          for (let item of res.tempFiles) {
            await vk.uploadFile({
              title: "上传中...",
              file: item,
              needSave: true,
              cloudDirectory: `photo/bgCover`,
              category_id: "bgCover",
              success: async (res) => {
                vk.callFunction({
                  url: "user/kh/updateUser",
                  title: "更新中...",
                  data: {
                    back_cover: res.url,
                  },
                  success: (data) => {
                    this.userInfo = data.userInfo;
                  },
                });
              },
              fail: (err) => {
                // 上传失败
                console.log(err);
              },
            });
          }
          this.$emit("input", value);
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.nav-info {
  display: flex;
  align-items: center;
  margin-left: 30rpx;

  .avatar {
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
    margin-right: 40rpx;
    background-color: #fff;
  }

  .name {
    font-size: 32rpx;
    color: #333;
  }
}

.top-info {
  margin-bottom: 20rpx;
  position: relative;
  height: 480rpx;

  .background {
    width: 100%;
    height: 500rpx;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(to bottom, transparent 70%, #fff 100%);
    }
  }
}

.user-info {
  position: relative;
  z-index: 2;
  width: 100%;
  min-height: 400rpx;
  background-color: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(6px);
  margin-top: -200rpx;
  padding: 30rpx 40rpx;
  border-radius: 24rpx;
  box-sizing: border-box;

  .avatar {
    width: 140rpx;
    height: 140rpx;
    border-radius: 50%;
    border: 1px solid #fff;
    margin-top: -80rpx;
    background-color: #fff;
  }

  .name {
    margin-top: 20rpx;
    font-size: 48rpx;
    font-weight: bold;
    color: #000;
    @include multiline(1);
  }

  .tag {
    display: flex;
    margin-top: 20rpx;
  }

  .statistic {
    margin-top: 40rpx;
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
    gap: 20rpx;
    grid-template-rows: 100rpx;

    .block {
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      :deep(.value) {
        font-weight: bold;
      }

      .label {
        font-size: 24rpx;
        color: #8a8a8a;
      }
    }
  }
}

.content {
  padding: 0 30rpx 100rpx;
}

.order {
  width: 100%;
  background-color: #fff;
  position: relative;
  padding: 20rpx 30rpx;
  margin-top: 20rpx;
  box-sizing: border-box;
  border-radius: 24rpx;

  &-top {
    margin-bottom: 20rpx;

    text {
      font-family: usic;
      font-size: 40rpx;
      color: $primary-color;
    }
  }

  &-list {
    display: grid;
    grid-template-columns: 25% 25% 25% 25%;
    grid-template-rows: 100rpx;
  }

  &-block {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
  }

  &-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #000;
  }

  &-value {
    font-size: 40rpx;
    font-family: usic;
    color: #333;
  }
}

.action {
  background-color: #fff;
  border-radius: 24rpx;
  margin-top: 20rpx;

  :deep(.action-group) {
    border-radius: 24rpx;
    overflow: hidden;
  }
  :deep(.wd-cell) {
    padding: 0;
  }
  :deep(.wd-cell__wrapper) {
    padding: 30rpx;
  }
  :deep(.wd-cell__value) {
    color: #8a8a8a;
    font-size: 24rpx;
  }

  .cell-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 30rpx;
  }
}

// Tag styles with theme color
:deep(.wd-tag) {
  padding: 3px 6px !important;
  margin-right: 20rpx;
}
</style>
