<template>
  <view class="page-content">
    <t-navbar title="打卡日历"></t-navbar>

    <!-- 数据统计区域 -->
    <view class="stats-section">
      <view class="stats-grid">
        <view class="stat-card" v-for="(stat, index) in statsData" :key="index">
          <view class="stat-content">
            <wd-count-to :startVal="0" :endVal="stat.value" :duration="1500" :fontSize="32" color="#0171BC" class="stat-number" />
            <text class="stat-label">{{ stat.label }}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 日历区域 -->
    <view class="calendar-section">
      <!-- 视图切换 -->
      <t-tabs v-model="activeTab" :list="['月视图', '年视图']" @change="handleTabChange"></t-tabs>
      <!-- 月视图 -->
      <view v-if="activeTab === 0" class="month-view">
        <!-- 月份导航 -->
        <view class="month-header">
          <view class="month-nav" @click="changeMonth(-1)">
            <text class="nav-arrow">‹</text>
          </view>
          <view class="month-title">
            <picker mode="date" fields="month" @change="pickerMonthChange">
              <text class="month-text">{{ currentMonthText }}</text></picker
            >
          </view>
          <view class="month-nav" @click="changeMonth(1)">
            <text class="nav-arrow">›</text>
          </view>
        </view>

        <!-- 星期标题 -->
        <view class="week-header">
          <view class="week-day" v-for="day in weekDays" :key="day">
            <text class="week-text">{{ day }}</text>
          </view>
        </view>

        <!-- 日历网格 -->
        <view class="calendar-grid">
          <view v-for="(day, index) in calendarDays" :key="index" class="calendar-day" :class="getDayClass(day)">
            <text class="day-text">{{ day.day }}</text>
            <view v-if="day.hasSignIn" class="sign-mark"></view>
          </view>
        </view>
      </view>

      <!-- 年视图 -->
      <view v-if="activeTab === 1" class="year-view">
        <view class="year-header">
          <view class="year-nav">
            <wd-button type="text" size="small" @click="changeYear(-1)" custom-class="year-btn"> ‹ </wd-button>
            <text class="year-text">{{ currentYear }}年打卡记录</text>
            <wd-button type="text" size="small" @click="changeYear(1)" custom-class="year-btn"> › </wd-button>
          </view>
        </view>

        <view class="heatmap-container">
          <!-- 周标签 -->
          <view class="week-labels">
            <text v-for="day in weekLabels" :key="day" class="week-label">
              {{ day }}
            </text>
          </view>
          <view class="heatmap-content">
            <!-- 月份标签 -->
            <view class="month-labels">
              <text v-for="month in monthLabels" :key="month.index" class="month-label">
                {{ month.name }}
              </text>
            </view>

            <!-- 热力图网格 -->
            <view class="heatmap-grid">
              <view v-for="(day, index) in yearDays" :key="index" class="day-cell" :class="getDayCellClass(day)" :style="getDayCellStyle(day)">
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 打卡记录列表 -->
    <view class="records-section">
      <view class="section-header">
        <t-title>打卡记录</t-title>
      </view>

      <!-- 记录列表 -->
      <view class="records-list">
        <t-empty v-if="!loading && recordsList.length === 0" data="data" top="50rpx" text="暂无打卡记录"></t-empty>

        <view v-for="(record, index) in recordsList" :key="record._id" class="record-card" :style="{ 'animation-delay': `${index * 0.1}s` }">
          <view class="record-header">
            <view class="record-date">
              <text class="date-text">{{ formatDate(record.create_date) }}</text>
              <text class="time-text">{{ formatTime(record.create_date) }}</text>
            </view>
            <wd-tag type="primary" size="small" custom-class="record-tag"> 已打卡 </wd-tag>
          </view>

          <view class="record-content">
            <view class="site-info">
              <text class="site-name">{{ record.site_name }}</text>
            </view>

            <view class="record-actions" v-if="record.site_id">
              <wd-button type="text" size="small" @click="viewSiteDetail(record)" custom-class="action-btn"> 查看详情 </wd-button>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      // 基础数据
      loading: false,
      loadingMore: false,
      hasMore: true,

      // 统计数据
      statsData: [
        { label: "总计打卡球场", value: 0 },
        { label: "今年打卡球场", value: 0 },
        { label: "今年打卡次数", value: 0 },
        { label: "本月打卡球场", value: 0 },
        { label: "本月打卡次数", value: 0 },
      ],

      // 视图控制
      activeTab: 0,

      // 日历相关
      currentYear: new Date().getFullYear(),
      currentMonth: new Date().getMonth() + 1,
      currentMonthYear: new Date().getFullYear(),
      signInDates: [], // 年视图打卡日期数组
      monthSignInDates: [], // 月视图打卡日期数组
      weekDays: ["日", "一", "二", "三", "四", "五", "六"],
      calendarDays: [],

      // 年视图数据
      yearDays: [],
      monthLabels: [],
      weekLabels: ["一", "二", "三", "四", "五", "六", "日"],
      totalWeeks: 53,

      // 记录列表
      recordsList: [],
      pageIndex: 1,
      pageSize: 10,
    };
  },

  computed: {
    // 格式化的年视图打卡日期集合
    signInDateSet() {
      return new Set(this.signInDates.map((date) => this.formatDateKey(date)));
    },

    // 格式化的月视图打卡日期集合
    monthSignInDateSet() {
      return new Set(this.monthSignInDates.map((date) => this.formatDateKey(date)));
    },

    // 当前月份显示文本
    currentMonthText() {
      const months = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
      return `${this.currentMonthYear}年${months[this.currentMonth - 1]}`;
    },
  },

  onReachBottom() {
    this.loadMoreRecords();
  },

  onLoad() {
    this.initPage();
  },

  onPullDownRefresh() {
    this.refreshData();
  },

  methods: {
    // 初始化页面
    async initPage() {
      this.loading = true;
      try {
        await Promise.all([
          this.loadStatsData(),
          this.loadSignInDates(),
          this.loadMonthSignInDates(), // 加载月视图数据
          this.loadRecords(),
        ]);
        this.generateYearView();
      } catch (error) {
        console.error("初始化失败:", error);
        uni.showToast({
          title: "加载失败",
          icon: "error",
        });
      } finally {
        this.loading = false;
      }
    },

    // 刷新数据
    async refreshData() {
      try {
        this.pageIndex = 1;
        this.recordsList = [];
        await this.initPage();
        uni.showToast({
          title: "刷新成功",
          icon: "success",
        });
      } catch (error) {
        uni.showToast({
          title: "刷新失败",
          icon: "error",
        });
      } finally {
        uni.stopPullDownRefresh();
      }
    },

    // 加载统计数据
    async loadStatsData() {
      try {
        const res = await vk.callFunction({
          url: "client/user/signIn/kh/getStats",
          title: false,
        });

        if (res.code === 0) {
          this.statsData[0].value = res.data.totalSites || 0; // 今年球场
          this.statsData[1].value = res.data.yearSites || 0; // 本月球场
          this.statsData[2].value = res.data.yearCount || 0; // 今年打卡
          this.statsData[3].value = res.data.monthSites || 0; // 本月打卡
          this.statsData[4].value = res.data.monthCount || 0; // 总打卡球场
        }
      } catch (error) {
        console.error("加载统计数据失败:", error);
      }
    },

    // 加载打卡日期
    async loadSignInDates() {
      try {
        const res = await vk.callFunction({
          url: "client/user/signIn/kh/getDates",
          data: {
            year: this.currentYear,
          },
          title: false,
        });

        if (res.code === 0) {
          this.signInDates = res.data || [];
          // 只为年视图重新生成数据
          if (this.activeTab === 1) {
            this.generateYearView();
          }
        }
      } catch (error) {
        console.error("加载打卡日期失败:", error);
      }
    },

    // 加载月视图打卡数据
    async loadMonthSignInDates() {
      try {
        const res = await vk.callFunction({
          url: "client/user/signIn/kh/getMonthDates",
          data: {
            year: this.currentMonthYear,
            month: this.currentMonth, // 直接使用1-12的月份值
          },
        });

        if (res.code === 0) {
          this.monthSignInDates = res.data || [];
          // 重新生成月视图日历数据
          this.generateCalendarDays();
        }
      } catch (error) {
        console.error("加载月视图打卡数据失败:", error);
      }
    },

    // 加载打卡记录
    async loadRecords() {
      try {
        const res = await vk.callFunction({
          url: "client/user/signIn/kh/getList",
          title: "请求中...",
          data: {
            pageIndex: this.pageIndex,
            pageSize: this.pageSize,
          },
        });

        if (res.code === 0) {
          const newRecords = res.data || [];
          if (this.pageIndex === 1) {
            this.recordsList = newRecords;
          } else {
            this.recordsList.push(...newRecords);
          }
          this.hasMore = newRecords.length === this.pageSize;
        }
      } catch (error) {
        console.error("加载记录失败:", error);
      }
    },

    // 加载更多记录
    async loadMoreRecords() {
      if (this.loadingMore || !this.hasMore) return;

      this.loadingMore = true;
      this.pageIndex++;

      try {
        await this.loadRecords();
      } catch (error) {
        this.pageIndex--;
        uni.showToast({
          title: "加载失败",
          icon: "error",
        });
      } finally {
        this.loadingMore = false;
      }
    },

    // 标签切换
    async handleTabChange(index) {
      this.activeTab = index;
      if (index === 1) {
        this.generateYearView();
      } else if (index === 0) {
        // 切换到月视图时，确保有当前月份的数据
        if (this.monthSignInDates.length === 0) {
          await this.loadMonthSignInDates();
        } else {
          this.generateCalendarDays();
        }
      }
    },

    // 生成月视图日历数据
    generateCalendarDays() {
      const year = this.currentMonthYear;
      const month = this.currentMonth - 1; // 转换为JavaScript月份索引（0-11）

      // 获取当月第一天和最后一天
      const firstDay = new Date(year, month, 1);
      const lastDay = new Date(year, month + 1, 0);

      // 获取第一天是星期几（0=周日，1=周一...）
      const firstDayOfWeek = firstDay.getDay();

      // 获取当月天数
      const daysInMonth = lastDay.getDate();

      // 生成日历数组
      this.calendarDays = [];

      // 添加上个月的尾部日期（填充第一周）
      const prevMonth = month === 0 ? 11 : month - 1;
      const prevYear = month === 0 ? year - 1 : year;
      const prevMonthLastDay = new Date(prevYear, prevMonth, 0).getDate();

      for (let i = firstDayOfWeek - 1; i >= 0; i--) {
        const day = prevMonthLastDay - i;
        this.calendarDays.push({
          day,
          date: new Date(prevYear, prevMonth, day),
          isCurrentMonth: false,
          isToday: false,
          hasSignIn: false,
        });
      }

      // 添加当月日期
      for (let day = 1; day <= daysInMonth; day++) {
        const date = new Date(year, month, day);
        const dateKey = this.formatDateKey(date);
        const isToday = this.isToday(date);
        const hasSignIn = this.monthSignInDateSet.has(dateKey);

        this.calendarDays.push({
          day,
          date,
          isCurrentMonth: true,
          isToday,
          hasSignIn,
        });
      }

      // 添加下个月的开头日期（填充最后一周）
      const nextMonth = month === 11 ? 0 : month + 1;
      const nextYear = month === 11 ? year + 1 : year;
      const remainingDays = 42 - this.calendarDays.length; // 6行7列 = 42个格子

      for (let day = 1; day <= remainingDays; day++) {
        this.calendarDays.push({
          day,
          date: new Date(nextYear, nextMonth, day),
          isCurrentMonth: false,
          isToday: false,
          hasSignIn: false,
        });
      }
    },

    async pickerMonthChange(e) {
      try {
        vk.showLoading("请求中...");
        let val = e.detail.value.split("-");
        this.currentMonthYear = Number(val[0]);
        this.currentMonth = Number(val[1]);
        await this.loadMonthSignInDates();
      } catch (err) {
        console.log(err);
      } finally {
        vk.hideLoading();
      }
    },

    // 月份切换
    async changeMonth(direction) {
      try {
        this.currentMonth += direction;

        if (this.currentMonth > 12) {
          this.currentMonth = 1;
          this.currentMonthYear++;
        } else if (this.currentMonth < 1) {
          this.currentMonth = 12;
          this.currentMonthYear--;
        }

        vk.showLoading("请求中...");

        // 加载新月份的打卡数据
        await this.loadMonthSignInDates();
      } catch (err) {
        console.log(err);
      } finally {
        vk.hideLoading();
      }
    },

    // 获取日期样式类
    getDayClass(day) {
      const classes = [];

      if (!day.isCurrentMonth) {
        classes.push("other-month");
      }

      if (day.isToday) {
        classes.push("today");
      }

      if (day.hasSignIn) {
        classes.push("has-sign-in");
      }

      return classes;
    },

    // 处理日期点击
    handleDayClick(day) {
      if (!day.isCurrentMonth) return;

      if (day.hasSignIn) {
        this.showDayDetail(day.date);
      }
    },

    // 判断是否为今天
    isToday(date) {
      const today = new Date();
      return date.getFullYear() === today.getFullYear() && date.getMonth() === today.getMonth() && date.getDate() === today.getDate();
    },

    // 年份切换
    async changeYear(direction) {
      this.currentYear += direction;
      await this.loadSignInDates();
      this.generateYearView();
    },

    // 生成年视图数据
    generateYearView() {
      const year = this.currentYear;
      const startDate = new Date(year, 0, 1);
      const endDate = new Date(year, 11, 31);

      // 生成一年中的每一天
      this.yearDays = [];
      const currentDate = new Date(startDate);

      // 计算第一天是周几，调整为周一开始
      const firstDayOfWeek = (startDate.getDay() + 6) % 7; // 转换为周一开始

      // 添加前面的空白天数
      for (let i = 0; i < firstDayOfWeek; i++) {
        this.yearDays.push({ isEmpty: true });
      }

      // 添加一年中的每一天
      while (currentDate <= endDate) {
        const dateKey = this.formatDateKey(currentDate);
        const hasSignIn = this.signInDateSet.has(dateKey);

        this.yearDays.push({
          date: new Date(currentDate),
          dateKey,
          hasSignIn,
          month: currentDate.getMonth(),
          day: currentDate.getDate(),
          week: Math.floor(this.yearDays.length / 7),
        });

        currentDate.setDate(currentDate.getDate() + 1);
      }

      // 补齐最后一周的空白天数
      const remainingDays = 7 - (this.yearDays.length % 7);
      if (remainingDays < 7) {
        for (let i = 0; i < remainingDays; i++) {
          this.yearDays.push({ isEmpty: true });
        }
      }

      // 计算总周数
      this.totalWeeks = Math.ceil(this.yearDays.length / 7);

      // 生成月份标签
      this.generateMonthLabels();
    },

    // 生成月份标签
    generateMonthLabels() {
      const months = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
      this.monthLabels = [];

      let currentMonth = -1;
      let processedMonths = new Set();

      // 格子大小 + 间距 = 22rpx + 10rpx = 32rpx
      const cellWithGap = 32;

      this.yearDays.forEach((day, index) => {
        if (!day.isEmpty && day.month !== currentMonth && !processedMonths.has(day.month)) {
          currentMonth = day.month;
          processedMonths.add(day.month);

          const weekIndex = Math.floor(index / 7);

          this.monthLabels.push({
            index: currentMonth,
            name: months[currentMonth],
            left: weekIndex * cellWithGap + cellWithGap / 2, // 居中对齐到格子中心
            week: weekIndex,
          });
        }
      });
    },

    // 获取日期格子样式类
    getDayCellClass(day) {
      if (day.isEmpty) return "day-empty";
      return day.hasSignIn ? "day-signed" : "day-normal";
    },

    // 获取日期格子样式
    getDayCellStyle(day) {
      if (day.isEmpty) return {};

      return {
        backgroundColor: day.hasSignIn ? "#0171BC" : "#ebedf0",
        opacity: day.hasSignIn ? 1 : 0.6,
      };
    },

    // 显示日期详情
    async showDayDetail(date) {
      try {
        const res = await vk.callFunction({
          url: "client/user/signIn/pub/getDayDetail",
          data: {
            date: this.formatDateKey(date),
          },
          title: "加载中...",
        });

        if (res.code === 0 && res.data.length > 0) {
          const records = res.data;
          const content = records.map((record) => `${record.site_name}\n${this.formatTime(record.create_date)}`).join("\n\n");

          uni.showModal({
            title: `${this.formatDate(date)} 打卡记录`,
            content,
            showCancel: false,
            confirmText: "知道了",
          });
        }
      } catch (error) {
        console.error("获取日期详情失败:", error);
      }
    },

    // 查看球场详情
    viewSiteDetail(record) {
      if (record.site_id) {
        vk.navigateTo(`/pages/explore/site/detail?id=${record.site_id}`);
      }
    },

    // 工具方法
    formatDateKey(date) {
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, "0");
      const day = String(d.getDate()).padStart(2, "0");
      return `${year}-${month}-${day}`;
    },

    formatDate(date) {
      const d = new Date(date);
      const month = d.getMonth() + 1;
      const day = d.getDate();
      return `${month}月${day}日`;
    },

    formatTime(date) {
      const d = new Date(date);
      const hours = String(d.getHours()).padStart(2, "0");
      const minutes = String(d.getMinutes()).padStart(2, "0");
      return `${hours}:${minutes}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.page-content {
  min-height: 100vh;
  background: #f8f8f8;
}

// 数据统计区域
.stats-section {
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: auto auto auto;
  gap: 20rpx;
  grid-template-areas:
    "first first"
    "second third"
    "fourth fifth";

  // 使用 grid-area 精确定位每个卡片
  .stat-card:nth-child(1) {
    grid-area: first;
  }

  .stat-card:nth-child(2) {
    grid-area: second;
  }

  .stat-card:nth-child(3) {
    grid-area: third;
  }

  .stat-card:nth-child(4) {
    grid-area: fourth;
  }

  .stat-card:nth-child(5) {
    grid-area: fifth;
  }
}

.stat-card {
  border-radius: 16rpx;
  padding: 24rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  background-color: #fff;
}

.stat-content {
  flex: 1;

  .stat-number {
    display: block;
    font-size: 32rpx;
    font-weight: 600;
    color: $primary-color;
    line-height: 1.2;
    margin-bottom: 4rpx;
  }

  .stat-label {
    font-size: 24rpx;
    color: #666;
    line-height: 1;
  }
}

// 视图切换
.view-switch {
  background: #fff;
  padding: 0 30rpx;

  :deep(.calendar-tabs) {
    .wd-tabs__nav {
      background: #f5f5f5;
      border-radius: 12rpx;
      padding: 6rpx;
      margin: 20rpx 0;
    }

    .wd-tab {
      border-radius: 8rpx;
      transition: all 0.3s ease;

      &.is-active {
        background: #0171bc;
        color: #fff;
      }
    }
  }
}

// 日历区域
.calendar-section {
  background: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

// 月视图
.month-view {
  padding: 30rpx;
}

// 月份导航
.month-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
  padding: 0 20rpx;
}

.month-nav {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;

  .nav-arrow {
    font-size: 32rpx;
    font-weight: bold;
    color: #000;
  }
}

.month-title {
  flex: 1;
  text-align: center;

  .month-text {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

// 星期标题
.week-header {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  margin-bottom: 20rpx;
  padding: 0 10rpx;
}

.week-day {
  text-align: center;
  padding: 15rpx 0;

  .week-text {
    font-size: 26rpx;
    color: #666;
    font-weight: 500;
  }
}

// 日历网格
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 8rpx;
  padding: 0 10rpx;
}

.calendar-day {
  position: relative;
  aspect-ratio: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  transition: all 0.2s ease;
  min-height: 80rpx;

  .day-text {
    font-size: 28rpx;
    font-weight: 500;
    z-index: 2;
  }

  .sign-mark {
    position: absolute;
    bottom: 8rpx;
    left: 50%;
    transform: translateX(-50%);
    width: 8rpx;
    height: 8rpx;
    border-radius: 50%;
    background: #0171bc;
    z-index: 1;
  }

  // 当前月份日期
  &:not(.other-month) {
    .day-text {
      color: #333;
    }
  }

  // 其他月份日期
  &.other-month {
    .day-text {
      color: #ccc;
    }
  }

  // 今天
  &.today {
    background: rgba(1, 113, 188, 0.1);
    border: 2rpx solid #0171bc;

    .day-text {
      color: #0171bc;
      font-weight: 600;
    }
  }

  // 有打卡记录的日期
  &.has-sign-in {
    background: #0171bc;

    .day-text {
      color: #fff;
      font-weight: 600;
    }

    .sign-mark {
      display: none; // 背景色已经表示有打卡，不需要小圆点
    }
  }

  // 今天且有打卡记录
  &.today.has-sign-in {
    background: #0171bc;
    border-color: #0056a3;

    .day-text {
      color: #fff;
    }
  }
}

// 年视图
.year-view {
  padding: 30rpx;
}

.year-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .year-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }

  .year-nav {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .year-text {
      font-size: 28rpx;
      color: #666;
      min-width: 80rpx;
      text-align: center;
    }

    :deep(.year-btn) {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      color: #000;
      font-size: 32rpx;
      font-weight: bold;
    }
  }
}

.heatmap-container {
  position: relative;
  overflow-x: scroll;
  overflow-y: hidden;
}
.heatmap-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  min-width: fit-content;
}
.month-labels {
  position: relative;
  height: 40rpx;
  margin-left: 60rpx;
  margin-right: 20rpx;
  display: flex;
  justify-content: space-around;

  .month-label {
    font-size: 24rpx;
    color: #666;
    line-height: 40rpx;
    white-space: nowrap;
  }
}

.week-labels {
  position: absolute;
  left: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;

  .week-label {
    height: 22rpx;
    font-size: 22rpx;
    color: #666;
    text-align: center;
    line-height: 1;
    margin-top: 10rpx;
  }
}

.heatmap-grid {
  margin-left: 40rpx;
  display: grid;
  grid-template-rows: repeat(7, 22rpx);
  grid-auto-flow: column;
  grid-auto-columns: 22rpx;
  gap: 10rpx;
  min-width: fit-content;
}

.day-cell {
  width: 100%;
  height: 100%;
  border-radius: 3rpx;
  cursor: pointer;
  transition: all 0.2s ease;

  &.day-empty {
    background: transparent;
  }

  &.day-normal {
    background: #ebedf0;
  }

  &.day-signed {
    background: #0171bc;
  }
}

// 记录区域
.records-section {
  padding: 30rpx;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;

  .section-title {
    font-size: 32rpx;
    font-weight: 600;
    color: #333;
  }
}

.records-list {
  .record-card {
    background: #fff;
    border-radius: 16rpx;
    padding: 30rpx;
    margin-bottom: 20rpx;
    opacity: 0;
    transform: translateY(20rpx);
    animation: slideInUp 0.4s ease forwards;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.record-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.record-date {
  .date-text {
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    display: block;
    margin-bottom: 4rpx;
  }

  .time-text {
    font-size: 24rpx;
    color: #666;
  }
}

:deep(.record-tag) {
  background: rgba(1, 113, 188, 0.1);
  color: #0171bc;
  border: 1rpx solid rgba(1, 113, 188, 0.2);
}

.record-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.site-info {
  flex: 1;

  .site-name {
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    display: block;
    margin-bottom: 8rpx;
  }

  .site-address {
    font-size: 24rpx;
    color: #666;
    line-height: 1.4;
  }
}

.record-actions {
  :deep(.action-btn) {
    color: #0171bc;
    font-size: 26rpx;
  }
}

// 加载更多
.load-more {
  text-align: center;
  padding: 40rpx 0;

  :deep(.load-more-btn) {
    background: #f5f5f5;
    color: #666;
    border: none;
    border-radius: 50rpx;
    padding: 20rpx 60rpx;

    &.is-loading {
      background: rgba(1, 113, 188, 0.1);
      color: #0171bc;
    }
  }
}

// 加载状态
.page-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 9999;
}

// 动画
@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 响应式设计
@media (max-width: 750rpx) {
  .month-view {
    padding: 20rpx;
  }

  .month-header {
    margin-bottom: 20rpx;
    padding: 0 10rpx;
  }

  .month-nav {
    width: 50rpx;
    height: 50rpx;

    .nav-arrow {
      font-size: 28rpx;
    }
  }

  .month-title .month-text {
    font-size: 28rpx;
  }

  .week-day {
    padding: 10rpx 0;

    .week-text {
      font-size: 24rpx;
    }
  }

  .calendar-grid {
    gap: 6rpx;
    padding: 0 5rpx;
  }

  .calendar-day {
    min-height: 70rpx;
    border-radius: 10rpx;

    .day-text {
      font-size: 26rpx;
    }

    .sign-mark {
      bottom: 6rpx;
      width: 6rpx;
      height: 6rpx;
    }
  }
}
</style>
