'use strict';
/**
 * 支付报名费
 * @url client/tour/match/kh/pay 前端调用的url参数地址
 * @description 支付报名费
 * @param {String} registration_id 报名记录ID
 */
module.exports = {
  /**
   * main 方法
   */
  main: async (event) => {
    let { data = {}, userInfo, util, filterResponse, originalParam } = event;
    let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
    let { uid } = data;
    let res = { code: 0, msg: "" };
    // 业务逻辑开始-----------------------------------------------------------
    const { registration_id } = data;
    if (!registration_id) return { code: -1, msg: '报名ID不能为空' };

    if (!uid) return { code: -1, msg: '请先登录' };

    // 获取报名信息
    const registration = await vk.baseDao.findById({
      dbName: 'tour-registration',
      id: registration_id
    });

    if (!registration) return { code: -1, msg: '报名记录不存在' };
    if (registration.user_id !== uid) return { code: -1, msg: '无权操作此报名记录' };
    if (registration.payment_status === 'paid') return { code: -1, msg: '该报名已支付' };

    // 获取比赛信息
    const match = await vk.baseDao.findById({
      dbName: 'tour-matches',
      id: registration.match_id
    });

    if (!match) return { code: -1, msg: '比赛不存在' };

    // 创建支付订单
    const orderInfo = {
      out_trade_no: 'tour_' + Date.now() + '_' + registration_id,
      total_fee: match.entry_fee * 100, // 单位：分
      subject: `${match.title}报名费`,
      body: `${match.title}比赛报名费用`,
      type: 'tour_registration'
    };

    res.data = orderInfo;
    // 业务逻辑结束-----------------------------------------------------------
    return res;
  }
} 