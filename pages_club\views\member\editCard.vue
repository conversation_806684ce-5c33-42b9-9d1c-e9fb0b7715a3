<template>
	<view class="page-content">
		<t-navbar title="编辑会员卡信息"></t-navbar>
		<t-group title="更改前">
			<template v-if="this.info.type === 'date'">
				<t-picker label="开始时间" mode="date" marginTop="0" disabled v-model="info.start"></t-picker>
			</template>
			<t-input :label="label" v-model="origin.value" disabled :unit="unit"></t-input>
		</t-group>

		<!-- 会员卡日期 -->
		<t-group title="更改后">
			<template v-if="this.info.type === 'date'">
				<t-picker label="开始时间" mode="date" marginTop="0" disabled v-model="startTime" placeholder="请选择开始时间"></t-picker>
			</template>
			<t-input :label="label" v-model="value" :unit="unit"></t-input>
		</t-group>

		<t-input label="编辑原因" textarea v-model="text" placeholder="请输入编辑原因"></t-input>

		<view class="button-box">
			<t-button color="#0171BC" text-color="#fff" @click="save">确认更改</t-button>
			<t-button color="transparent" text-color="#ff3030" @click="deleteCard">删除会员卡</t-button>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	export default {
		data() {
			return {
				club_id: null,
				startTime: '',
				value: '',
				text: '',
				unit: '',
				label: "",

				origin: {
					value: '',
				},

				info: null,
			}
		},
		onLoad(options) {
			this.club_id = vk.getVuex('$club.id')
			this.card_id = options.id
			this.getCardInfo()
		},
		methods: {
			async getCardInfo() {
				let data = await vk.callFunction({
					url: 'client/club/card/kh/userCardDetail',
					title: '请求中...',
					data: {
						id: this.card_id
					},
				});
				this.info = data.data
				this.handleValue()
			},
			handleValue() {
				switch (this.info.type) {
					case "times":
						this.unit = '次'
						this.label = "次数"
						this.origin.value = this.info.value
						break;
					case "date":
						this.unit = '天'
						this.label = "有效天数"
						this.startTime = this.info.start
						this.origin.value = dayjs(this.info.start).diff(dayjs(this.info.value), 'day')
						break;
					case "day":
						this.unit = '天'
						this.label = "有效天数"
						this.origin.value = this.info.value
						break;
					case "save":
						this.unit = '元'
						this.label = "余额"
						this.origin.value = this.info.value
						break;
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-content {
		padding: 30rpx;
		box-sizing: border-box;
	}

	.button-box {
		margin-top: 80rpx;
		height: 200rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
	}
</style>