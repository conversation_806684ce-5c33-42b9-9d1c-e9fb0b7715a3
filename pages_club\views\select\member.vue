<template>
	<view>
		<t-navbar title="选择成员"></t-navbar>
		<view class="user-list">
			<view v-for="item in list" :key="item._id" class="user-block" @click="selectUser(item)">
				<image class="user-avatar" :src="item.user_info.avatar" mode="aspectFill" />
				<view class="user-info">
					<view class="user-name">
						{{ item.user_info.nickname }}
					</view>
					<view class="user-type">
						<text>
							{{userRole(item.state)}}
						</text>
					</view>
				</view>
				<image v-show="item.checked" class="checked"
					src="https://cdn.cometennis.cn/icon/check-on.svg"
					mode="aspectFill"></image>
			</view>
			<l-empty v-if="list.length === 0" text="暂无数据" />
		</view>
	</view>
</template>

<script>
	import mixins from '@/pages_club/mixins.js'
	export default {
		mixins: [mixins],
		data() {
			return {
				ids: [],
				type: "member",
				list: [],
			}
		},
		onLoad(options) {
			if (options.ids) this.ids = options.ids.split(',')
			if (options.type) this.type = options.type
			this.getData()
		},
		methods: {
			selectUser(info) {
				info.checked = !info.checked
				let data = null
				if (this.ids.includes(info._id)) {
					this.ids = this.ids.filter(item => item != info._id)
				} else {
					this.ids.push(info._id)
					data = info
				}
				uni.$emit('select-member', {
					ids: this.ids,
					data,
				})
			},
			userRole(state) {
				switch (state) {
					case 'applying':
						return '审核中'
					case 'member':
						return '普通成员'
					case 'admin':
						return '超级管理员'
					case 'manage':
						return '管理员'
					case 'coach':
						return '教练'
					default:
						return '未知'
				}
			},
			async getData() {
				let data = await vk.callFunction({
					url: 'client/club/kh/member',
					title: '请求中...',
					data: {
						pageIndex: this.pages,
						club_id: this.club_id,
						state: this.type,
					},
				});
				this.total = data.total
				data.rows.forEach(item => {
					item.checked = this.ids.includes(item._id)
					this.list.push(item)
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.user {
		&-list {
			padding: 0 30rpx;
		}

		&-block {
			display: flex;
			padding: 20rpx 0;
			box-sizing: border-box;
			border-bottom: 2rpx solid #f8f8f8;
			position: relative;
		}

		&-avatar {
			width: 88rpx;
			height: 88rpx;
			border-radius: 14rpx;
			margin-right: 20rpx;
			flex-shrink: 0;
		}

		&-name {
			font-size: 36rpx;
			color: #000;
			font-weight: bold;
		}

		&-type {
			font-size: 28rpx;
			color: #333;
		}

	}

	.checked {
		width: 48rpx;
		height: 48rpx;
		position: absolute;
		right: 20rpx;
		top: 20rpx;
	}
</style>