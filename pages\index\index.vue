<template>
  <view>
    <t-navbar
      :back-icon="false"
      nav-style="custom"
      :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 200})` }"
      :placeholder="false"
    >
      <image v-if="scrollTop > 200" class="nav-logo" src="https://cdn.cometennis.cn/images/logo_horizontal.png" mode="aspectFill"></image>
    </t-navbar>
    <swiper class="banner" :autoplay="true" indicator-dots circular :interval="3000" :duration="600">
      <template v-if="!pageLoad">
        <swiper-item>
          <wd-skeleton theme="image" animation="gradient" :row-col="[{ width: '100%', height: '56.25vw' }]" />
        </swiper-item>
      </template>
      <template v-else>
        <swiper-item v-for="item in info.banner" :key="item.label">
          <image :src="item.image" mode="aspectFill" @click="toPage(item.url)"></image>
        </swiper-item>
      </template>
    </swiper>
    <view class="content">
      <view class="block">
        <view class="title">功能一览</view>
        <view class="intro">提供网球穿线、场地公开信息等</view>
        <view class="list">
          <template v-if="!pageLoad">
            <wd-skeleton
              v-for="index in 6"
              :key="index"
              theme="image"
              animation="gradient"
              :row-col="index === 1 ? [{ width: '100%', height: '316rpx' }] : [{ width: '100%', height: '150rpx' }]"
            />
          </template>
          <template v-else>
            <view
              class="card"
              v-for="item in info.action"
              :key="item.label"
              :style="{ backgroundImage: `url(${item.image})` }"
              @click="toPage(item.url)"
            >
              <view class="label">{{ item.label }}</view>
            </view>
          </template>
        </view>
      </view>
      <view class="block">
        <view class="title">推荐活动</view>
        <view class="intro">一些可能你感兴趣的约球信息</view>
        <view class="box">
          <template v-if="!pageLoad">
            <wd-skeleton
              theme="paragraph"
              animation="gradient"
              :row-col="[
                [{ width: '40%' }, { width: '40%', marginLeft: '20rpx' }],
                { width: '70%' },
                { width: '70%' },
                [{ width: '40%' }, { width: '40%' }],
                1,
              ]"
            />
          </template>
          <template v-else>
            <t-empty v-if="info.activity.length == 0" top="30" data="data">
              <view class="empty-text">附近暂无约球信息,<text class="theme" @click="vk.navigateTo('/pages/activity/start')">去发起</text></view>
            </t-empty>
            <activity-card
              v-for="(item, index) in info.activity"
              :key="item._id"
              :animateIndex="index"
              :value="item"
              :customStyle="cardStyle"
            ></activity-card>
          </template>
        </view>
      </view>
      <view class="block">
        <view class="title">附近球场</view>
        <view class="intro">距离你最近的网球场</view>
        <view class="box">
          <template v-if="!pageLoad">
            <view style="display: flex">
              <wd-skeleton animation="gradient" :row-col="[{ width: '180rpx', height: '180rpx', type: 'image' }]" />
              <wd-skeleton animation="gradient" :custom-style="{ width: '100%', marginLeft: '12px' }" :row-col="[{ width: '50%' }, 2, 1]" />
              <wd-skeleton animation="gradient" :row-col="[[1, 1]]" />
            </view>
          </template>
          <template v-else>
            <t-empty v-if="info.site.length == 0" top="30" data="data">
              <view class="empty-text"
                >附近暂无球场信息,<text class="theme" @click="vk.navigateTo('/pages/public/submitInfo?type=site')">去新增</text></view
              >
            </t-empty>
            <site-card v-for="(item, index) in info.site" :key="item._id" :animate-index="index" :value="item" :customStyle="cardStyle"></site-card>
          </template>
        </view>
      </view>
    </view>
    <!-- 幕帘广告 -->
    <template v-if="adInfo">
      <wd-curtain
        v-model="adVisible"
        :src="adInfo.image"
        :to="adInfo.url"
        close-position="bottom"
        close-on-click-modal
        :width="curtainWith"
      ></wd-curtain>
    </template>
    <t-tabbar></t-tabbar>
  </view>
</template>
<script>
import activityCard from "@/components/card/activity-card.vue";
import siteCard from "@/components/card/site-card.vue";
import { getLocation } from "@/utils/location";
export default {
  components: {
    activityCard,
    siteCard,
  },
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      imgUrl: vk.getVuex("$app.config.staticUrl.image"),
      scrollTop: 0,
      info: null,
      userInfo: vk.getVuex("$user.userInfo"),
      pageLoad: false,
      locationInfo: null,
      cardStyle: {
        backgroundColor: "#f8f8f8",
      },
      adInfo: null,
      adVisible: false,
    };
  },
  computed: {
    curtainWith() {
      let { windowWidth } = uni.getWindowInfo();
      return windowWidth * 0.8 || 280;
    },
  },
  onShareAppMessage() {
    let userName = this.userInfo.nickname || "";
    return {
      title: `oi！${userName}邀请你来打球了`,
      imageUrl: `${this.imgUrl}share/share.jpeg`,
    };
  },
  onPullDownRefresh() {
    this.getData();
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  async onLoad() {
    console.log(uni.getWindowInfo());

    this.locationInfo = await getLocation();
    await this.getWelcome();
    this.getData();

    uni.$on("activity-refresh", () => {
      this.getData();
    });
  },
  methods: {
    async getWelcome() {
      try {
        let data = await vk.callFunction({
          url: "client/public/pub/system",
          data: {
            type: "ad-home",
          },
        });
        this.adInfo = data.data;
        let ad_storage = vk.getVuex("$app.ad_home");
        if (data.data.open && !ad_storage) {
          this.adVisible = true;
          vk.setVuex("$app.ad_home", true);
        }
      } catch (error) {
        console.log("error", error);
      }
    },
    async getData() {
      this.pageLoad = false;
      this.info = null;
      try {
        let data = await vk.callFunction({
          url: "client/public/pub/home",
          data: {
            location: this.locationInfo,
          },
        });
        this.info = data.data;
        this.pageLoad = true;
      } catch (error) {
        console.log(error);
      } finally {
        uni.hideLoading();
        uni.stopPullDownRefresh();
      }
    },
    toPage(url) {
      if (!url) {
        vk.toast("功能开发中,敬请期待");
        return;
      }
      let tabbar = ["/pages/index/index", "/pages/user/index", "/pages/activity/index", "/pages/index/notify"];
      if (tabbar.includes(url)) {
        vk.switchTab(url);
      } else {
        vk.navigateTo(url);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.nav-logo {
  width: 90px;
  height: 30px;
  margin-left: 15px;
}

.banner {
  width: 100%;
  height: 56.25vw;

  image {
    width: 100%;
    height: 100%;
  }
}

.content {
  width: 100%;
  background-color: #fff;
  min-height: 20vh;
  padding: 30rpx 30rpx 100rpx;
  box-sizing: border-box;

  .block {
    padding-top: 30rpx;

    &:first-child {
      padding-top: 0;
    }

    .title {
      font-size: 48rpx;
      font-family: usic;
    }

    .intro {
      font-size: 24rpx;
      color: #8a8a8a;
      margin-top: 6rpx;
    }

    .box {
      margin-top: 20rpx;
    }
  }

  .list {
    margin-top: 30rpx;
    display: grid;
    grid-template-columns: repeat(2, 49%);
    grid-template-rows: repeat(auto-fill, 150rpx);
    gap: 16rpx;

    .card {
      background-color: #eee;
      background-size: cover;
      background-repeat: no-repeat;
      width: 100%;
      height: 150rpx;
      border-radius: 12px;
      position: relative;

      &:first-child {
        grid-column: 1;
        grid-row: span 2;
        height: 316rpx;
      }

      .label {
        color: #fff;
        position: absolute;
        left: 20rpx;
        top: 20rpx;
        font-size: 32rpx;
        font-weight: bold;
        text-shadow: 0 0 6rpx #333;
      }
    }
  }
}
</style>
