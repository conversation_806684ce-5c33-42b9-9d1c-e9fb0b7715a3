"use strict";function e(e){return e&&"object"==typeof e&&"default"in e?e.default:e}var t=e(require("events")),i=e(require("url")),a=e(require("util")),s=e(require("fs")),n=e(require("http")),o=e(require("https")),r=e(require("zlib")),p=e(require("stream")),c=e(require("dns")),l=e(require("net")),d=e(require("os")),m=e(require("path")),h=e(require("crypto")),u=e(require("punycode")),x=e(require("tls")),g=e(require("child_process"));function f(e){return e&&e.default||e}var v=class{constructor(e){this.options=e||{},this.cookies=[]}set(e,t){let a,s=i.parse(t||""),n=this.parse(e);return n.domain?(a=n.domain.replace(/^\./,""),(s.hostname.length<a.length||("."+s.hostname).substr(1-a.length)!=="."+a)&&(n.domain=s.hostname)):n.domain=s.hostname,n.path||(n.path=this.getPath(s.pathname)),n.expires||(n.expires=new Date(Date.now()+1e3*(Number(this.options.sessionTimeout||1800)||1800))),this.add(n)}get(e){return this.list(e).map(e=>e.name+"="+e.value).join("; ")}list(e){let t,i,a=[];for(t=this.cookies.length-1;t>=0;t--)i=this.cookies[t],this.isExpired(i)?this.cookies.splice(t,t):this.match(i,e)&&a.unshift(i);return a}parse(e){let t={};return(e||"").toString().split(";").forEach(e=>{let i,a=e.split("="),s=a.shift().trim().toLowerCase(),n=a.join("=").trim();if(s)switch(s){case"expires":n=new Date(n),"Invalid Date"!==n.toString()&&(t.expires=n);break;case"path":t.path=n;break;case"domain":i=n.toLowerCase(),i.length&&"."!==i.charAt(0)&&(i="."+i),t.domain=i;break;case"max-age":t.expires=new Date(Date.now()+1e3*(Number(n)||0));break;case"secure":t.secure=!0;break;case"httponly":t.httponly=!0;break;default:t.name||(t.name=s,t.value=n)}}),t}match(e,t){let a=i.parse(t||"");return(a.hostname===e.domain||"."===e.domain.charAt(0)&&("."+a.hostname).substr(-e.domain.length)===e.domain)&&(this.getPath(a.pathname).substr(0,e.path.length)===e.path&&(!e.secure||"https:"===a.protocol))}add(e){let t,i;if(!e||!e.name)return!1;for(t=0,i=this.cookies.length;t<i;t++)if(this.compare(this.cookies[t],e))return this.isExpired(e)?(this.cookies.splice(t,1),!1):(this.cookies[t]=e,!0);return this.isExpired(e)||this.cookies.push(e),!0}compare(e,t){return e.name===t.name&&e.path===t.path&&e.domain===t.domain&&e.secure===t.secure&&e.httponly==e.httponly}isExpired(e){return e.expires&&e.expires<new Date||!e.value}getPath(e){let t=(e||"/").split("/");return t.pop(),t=t.join("/").trim(),"/"!==t.charAt(0)&&(t="/"+t),"/"!==t.substr(-1)&&(t+="/"),t}},b="nodemailer@^6.7.2",y="nodemailer@6.7.3",_="sha512-KUdDsspqx89sD4UUyUKzdlUOper3hRkDVkrKh/89G+d9WKsU5ox51NWS4tB1XR5dPUdR4SP0E3molyEfOvSa3g==",w={},k={type:"range",registry:!0,raw:"nodemailer@^6.7.2",name:"nodemailer",escapedName:"nodemailer",rawSpec:"^6.7.2",saveSpec:null,fetchSpec:"^6.7.2"},E=["#DEV:/"],T="https://registry.npmjs.org/nodemailer/-/nodemailer-6.7.3.tgz",S="b73f9a81b9c8fa8acb4ea14b608f5e725ea8e018",A="nodemailer@^6.7.2",C="D:\\uniapp-work\\vk-send-mail-package",j={name:"Andris Reinman"},I={url:"https://github.com/nodemailer/nodemailer/issues"},L="Easy as cake e-mail sending from your Node.js applications",H={"@aws-sdk/client-ses":"3.54.1","aws-sdk":"2.1096.0",bunyan:"1.8.15",chai:"4.3.6","eslint-config-nodemailer":"1.2.0","eslint-config-prettier":"8.5.0",grunt:"1.4.1","grunt-cli":"1.4.3","grunt-eslint":"24.0.0","grunt-mocha-test":"0.13.3",libbase64:"1.2.1",libmime:"5.0.0",libqp:"1.1.0",mocha:"9.2.2","nodemailer-ntlm-auth":"1.0.1",proxy:"1.0.2","proxy-test-server":"1.0.0",sinon:"13.0.1","smtp-server":"3.10.0"},O={node:">=6.0.0"},z=["Nodemailer"],N="lib/nodemailer.js",M={type:"git",url:"git+https://github.com/nodemailer/nodemailer.git"},B={test:"grunt"},q={_from:b,_id:y,_inBundle:!1,_integrity:_,_location:"/nodemailer",_phantomChildren:w,_requested:k,_requiredBy:E,_resolved:T,_shasum:S,_spec:A,_where:C,author:j,bugs:I,bundleDependencies:!1,deprecated:!1,description:L,devDependencies:H,engines:O,homepage:"https://nodemailer.com/",keywords:z,license:"MIT",main:N,name:"nodemailer",repository:M,scripts:B,version:"6.7.3"},U=f(Object.freeze({__proto__:null,_from:b,_id:y,_inBundle:!1,_integrity:_,_location:"/nodemailer",_phantomChildren:w,_requested:k,_requiredBy:E,_resolved:T,_shasum:S,_spec:A,_where:C,author:j,bugs:I,bundleDependencies:!1,deprecated:!1,description:L,devDependencies:H,engines:O,homepage:"https://nodemailer.com/",keywords:z,license:"MIT",main:N,name:"nodemailer",repository:M,scripts:B,version:"6.7.3",default:q}));const P=p.PassThrough;var R=function(e,t){return F(e,t)};function F(e,t){(t=t||{}).fetchRes=t.fetchRes||new P,t.cookies=t.cookies||new v,t.redirects=t.redirects||0,t.maxRedirects=isNaN(t.maxRedirects)?5:t.maxRedirects,t.cookie&&([].concat(t.cookie||[]).forEach(i=>{t.cookies.set(i,e)}),t.cookie=!1);let a,s,p,c=t.fetchRes,l=i.parse(e),d=(t.method||"").toString().trim().toUpperCase()||"GET",m=!1,h="https:"===l.protocol?o:n,u={"accept-encoding":"gzip,deflate","user-agent":"nodemailer/"+U.version};if(Object.keys(t.headers||{}).forEach(e=>{u[e.toLowerCase().trim()]=t.headers[e]}),t.userAgent&&(u["user-agent"]=t.userAgent),l.auth&&(u.Authorization="Basic "+Buffer.from(l.auth).toString("base64")),(a=t.cookies.get(e))&&(u.cookie=a),t.body){if(!1!==t.contentType&&(u["Content-Type"]=t.contentType||"application/x-www-form-urlencoded"),"function"==typeof t.body.pipe)u["Transfer-Encoding"]="chunked",s=t.body,s.on("error",t=>{m||(m=!0,t.type="FETCH",t.sourceUrl=e,c.emit("error",t))});else{if(t.body instanceof Buffer)s=t.body;else if("object"==typeof t.body)try{s=Buffer.from(Object.keys(t.body).map(e=>{let i=t.body[e].toString().trim();return encodeURIComponent(e)+"="+encodeURIComponent(i)}).join("&"))}catch(t){if(m)return;return m=!0,t.type="FETCH",t.sourceUrl=e,void c.emit("error",t)}else s=Buffer.from(t.body.toString().trim());u["Content-Type"]=t.contentType||"application/x-www-form-urlencoded",u["Content-Length"]=s.length}d=(t.method||"").toString().trim().toUpperCase()||"POST"}let x={method:d,host:l.hostname,path:l.path,port:l.port?l.port:"https:"===l.protocol?443:80,headers:u,rejectUnauthorized:!1,agent:!1};t.tls&&Object.keys(t.tls).forEach(e=>{x[e]=t.tls[e]});try{p=h.request(x)}catch(t){return m=!0,setImmediate(()=>{t.type="FETCH",t.sourceUrl=e,c.emit("error",t)}),c}return t.timeout&&p.setTimeout(t.timeout,()=>{if(m)return;m=!0,p.abort();let t=new Error("Request Timeout");t.type="FETCH",t.sourceUrl=e,c.emit("error",t)}),p.on("error",t=>{m||(m=!0,t.type="FETCH",t.sourceUrl=e,c.emit("error",t))}),p.on("response",a=>{let s;if(!m){switch(a.headers["content-encoding"]){case"gzip":case"deflate":s=r.createUnzip()}if(a.headers["set-cookie"]&&[].concat(a.headers["set-cookie"]||[]).forEach(i=>{t.cookies.set(i,e)}),[301,302,303,307,308].includes(a.statusCode)&&a.headers.location){if(t.redirects++,t.redirects>t.maxRedirects){m=!0;let t=new Error("Maximum redirect count exceeded");return t.type="FETCH",t.sourceUrl=e,c.emit("error",t),void p.abort()}return t.method="GET",t.body=!1,F(i.resolve(e,a.headers.location),t)}if(c.statusCode=a.statusCode,c.headers=a.headers,a.statusCode>=300&&!t.allowErrorResponse){m=!0;let t=new Error("Invalid status code "+a.statusCode);return t.type="FETCH",t.sourceUrl=e,c.emit("error",t),void p.abort()}a.on("error",t=>{m||(m=!0,t.type="FETCH",t.sourceUrl=e,c.emit("error",t),p.abort())}),s?(a.pipe(s).pipe(c),s.on("error",t=>{m||(m=!0,t.type="FETCH",t.sourceUrl=e,c.emit("error",t),p.abort())})):a.pipe(c)}}),setImmediate(()=>{if(s)try{if("function"==typeof s.pipe)return s.pipe(p);p.write(s)}catch(t){return m=!0,t.type="FETCH",t.sourceUrl=e,void c.emit("error",t)}p.end()}),c}R.Cookies=v;var D=function(e,t){return e(t={exports:{}},t.exports),t.exports}((function(e){const t=e.exports.networkInterfaces=d.networkInterfaces(),n=(e,i,a)=>{if(!(Object.keys(t).map(e=>t[e]).reduce((e,t)=>e.concat(t),[]).filter(e=>!e.internal).filter(t=>t.family==="IPv"+e).length>0))return a(null,[]);c["resolve"+e](i,(e,t)=>{if(e){switch(e.code){case c.NODATA:case c.NOTFOUND:case c.NOTIMP:case c.SERVFAIL:case c.CONNREFUSED:case"EAI_AGAIN":return a(null,[])}return a(e)}return a(null,Array.isArray(t)?t:[].concat(t||[]))})},o=e.exports.dnsCache=new Map,r=(e,t)=>e?Object.assign({servername:e.servername,host:e.addresses&&e.addresses.length?1===e.addresses.length?e.addresses[0]:e.addresses[Math.floor(Math.random()*e.addresses.length)]:null},t||{}):Object.assign({},t||{});function p(e,t){let i=!1,a=[],s=0;e.on("error",e=>{i||(i=!0,t(e))}),e.on("readable",()=>{let t;for(;null!==(t=e.read());)a.push(t),s+=t.length}),e.on("end",()=>{if(i)return;let e;i=!0;try{e=Buffer.concat(a,s)}catch(e){return t(e)}t(null,e)})}e.exports.resolveHostname=(e,t)=>{if(!(e=e||{}).host&&e.servername&&(e.host=e.servername),!e.host||l.isIP(e.host)){let i={addresses:[e.host],servername:e.servername||!1};return t(null,r(i,{cached:!1}))}let i;if(o.has(e.host)&&(i=o.get(e.host),!i.expires||i.expires>=Date.now()))return t(null,r(i.value,{cached:!0}));n(4,e.host,(a,s)=>{if(a)return i?t(null,r(i.value,{cached:!0,error:a})):t(a);if(s&&s.length){let i={addresses:s,servername:e.servername||e.host};return o.set(e.host,{value:i,expires:Date.now()+3e5}),t(null,r(i,{cached:!1}))}n(6,e.host,(a,s)=>{if(a)return i?t(null,r(i.value,{cached:!0,error:a})):t(a);if(s&&s.length){let i={addresses:s,servername:e.servername||e.host};return o.set(e.host,{value:i,expires:Date.now()+3e5}),t(null,r(i,{cached:!1}))}try{c.lookup(e.host,{},(a,s)=>{if(a)return i?t(null,r(i.value,{cached:!0,error:a})):t(a);if(!s&&i)return t(null,r(i.value,{cached:!0}));let n={addresses:s?[s]:[e.host],servername:e.servername||e.host};return o.set(e.host,{value:n,expires:Date.now()+3e5}),t(null,r(n,{cached:!1}))})}catch(a){return i?t(null,r(i.value,{cached:!0,error:a})):t(a)}})})},e.exports.parseConnectionUrl=e=>{e=e||"";let t={};return[i.parse(e,!0)].forEach(e=>{let i;switch(e.protocol){case"smtp:":t.secure=!1;break;case"smtps:":t.secure=!0;break;case"direct:":t.direct=!0}!isNaN(e.port)&&Number(e.port)&&(t.port=Number(e.port)),e.hostname&&(t.host=e.hostname),e.auth&&(i=e.auth.split(":"),t.auth||(t.auth={}),t.auth.user=i.shift(),t.auth.pass=i.join(":")),Object.keys(e.query||{}).forEach(i=>{let a=t,s=i,n=e.query[i];switch(isNaN(n)||(n=Number(n)),n){case"true":n=!0;break;case"false":n=!1}if(0===i.indexOf("tls."))s=i.substr(4),t.tls||(t.tls={}),a=t.tls;else if(i.indexOf(".")>=0)return;s in a||(a[s]=n)})}),t},e.exports._logFunc=(e,t,i,a,s,...n)=>{let o={};Object.keys(i||{}).forEach(e=>{"level"!==e&&(o[e]=i[e])}),Object.keys(a||{}).forEach(e=>{"level"!==e&&(o[e]=a[e])}),e[t](o,s,...n)},e.exports.getLogger=(t,i)=>{let s={},n=["trace","debug","info","warn","error","fatal"];if(!(t=t||{}).logger)return n.forEach(e=>{s[e]=()=>!1}),s;let o=t.logger;return!0===t.logger&&(o=function(e){let t=0,i=new Map;e.forEach(e=>{e.length>t&&(t=e.length)}),e.forEach(e=>{let a=e.toUpperCase();a.length<t&&(a+=" ".repeat(t-a.length)),i.set(e,a)});let s=(e,t,s,...n)=>{let o="";t&&("server"===t.tnx?o="S: ":"client"===t.tnx&&(o="C: "),t.sid&&(o="["+t.sid+"] "+o),t.cid&&(o="[#"+t.cid+"] "+o)),(s=a.format(s,...n)).split(/\r?\n/).forEach(t=>{console.log("[%s] %s %s",(new Date).toISOString().substr(0,19).replace(/T/," "),i.get(e),o+t)})},n={};return e.forEach(e=>{n[e]=s.bind(null,e)}),n}(n)),n.forEach(t=>{s[t]=(a,s,...n)=>{e.exports._logFunc(o,t,i,a,s,...n)}}),s},e.exports.callbackPromise=(e,t)=>function(){let i=Array.from(arguments),a=i.shift();a?t(a):e(...i)},e.exports.resolveContent=(t,i,a)=>{let n;a||(n=new Promise((t,i)=>{a=e.exports.callbackPromise(t,i)}));let o,r=t&&t[i]&&t[i].content||t[i],c=("object"==typeof t[i]&&t[i].encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");if(!r)return a(null,r);if("object"==typeof r){if("function"==typeof r.pipe)return p(r,(e,s)=>{if(e)return a(e);t[i].content?t[i].content=s:t[i]=s,a(null,s)});if(/^https?:\/\//i.test(r.path||r.href))return o=R(r.path||r.href),p(o,a);if(/^data:/i.test(r.path||r.href)){let e=(r.path||r.href).match(/^data:((?:[^;]*;)*(?:[^,]*)),(.*)$/i);return a(null,e?/\bbase64$/i.test(e[1])?Buffer.from(e[2],"base64"):Buffer.from(decodeURIComponent(e[2])):Buffer.from(0))}if(r.path)return p(s.createReadStream(r.path),a)}return"string"!=typeof t[i].content||["utf8","usascii","ascii"].includes(c)||(r=Buffer.from(t[i].content,c)),setImmediate(()=>a(null,r)),n},e.exports.assign=function(){let e=Array.from(arguments),t=e.shift()||{};return e.forEach(e=>{Object.keys(e||{}).forEach(i=>{["tls","auth"].includes(i)&&e[i]&&"object"==typeof e[i]?(t[i]||(t[i]={}),Object.keys(e[i]).forEach(a=>{t[i][a]=e[i][a]})):t[i]=e[i]})}),t},e.exports.encodeXText=e=>{if(!/[^\x21-\x2A\x2C-\x3C\x3E-\x7E]/.test(e))return e;let t=Buffer.from(e),i="";for(let e=0,a=t.length;e<a;e++){let a=t[e];i+=a<33||a>126||43===a||61===a?"+"+(a<16?"0":"")+a.toString(16).toUpperCase():String.fromCharCode(a)}return i}}));D.networkInterfaces,D.dnsCache,D.resolveHostname,D.parseConnectionUrl,D._logFunc,D.getLogger,D.callbackPromise,D.resolveContent,D.assign,D.encodeXText;const G=new Map([["application/acad","dwg"],["application/applixware","aw"],["application/arj","arj"],["application/atom+xml","xml"],["application/atomcat+xml","atomcat"],["application/atomsvc+xml","atomsvc"],["application/base64",["mm","mme"]],["application/binhex","hqx"],["application/binhex4","hqx"],["application/book",["book","boo"]],["application/ccxml+xml,","ccxml"],["application/cdf","cdf"],["application/cdmi-capability","cdmia"],["application/cdmi-container","cdmic"],["application/cdmi-domain","cdmid"],["application/cdmi-object","cdmio"],["application/cdmi-queue","cdmiq"],["application/clariscad","ccad"],["application/commonground","dp"],["application/cu-seeme","cu"],["application/davmount+xml","davmount"],["application/drafting","drw"],["application/dsptype","tsp"],["application/dssc+der","dssc"],["application/dssc+xml","xdssc"],["application/dxf","dxf"],["application/ecmascript",["js","es"]],["application/emma+xml","emma"],["application/envoy","evy"],["application/epub+zip","epub"],["application/excel",["xls","xl","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/exi","exi"],["application/font-tdpfr","pfr"],["application/fractals","fif"],["application/freeloader","frl"],["application/futuresplash","spl"],["application/gnutar","tgz"],["application/groupwise","vew"],["application/hlp","hlp"],["application/hta","hta"],["application/hyperstudio","stk"],["application/i-deas","unv"],["application/iges",["iges","igs"]],["application/inf","inf"],["application/internet-property-stream","acx"],["application/ipfix","ipfix"],["application/java","class"],["application/java-archive","jar"],["application/java-byte-code","class"],["application/java-serialized-object","ser"],["application/java-vm","class"],["application/javascript","js"],["application/json","json"],["application/lha","lha"],["application/lzx","lzx"],["application/mac-binary","bin"],["application/mac-binhex","hqx"],["application/mac-binhex40","hqx"],["application/mac-compactpro","cpt"],["application/macbinary","bin"],["application/mads+xml","mads"],["application/marc","mrc"],["application/marcxml+xml","mrcx"],["application/mathematica","ma"],["application/mathml+xml","mathml"],["application/mbedlet","mbd"],["application/mbox","mbox"],["application/mcad","mcd"],["application/mediaservercontrol+xml","mscml"],["application/metalink4+xml","meta4"],["application/mets+xml","mets"],["application/mime","aps"],["application/mods+xml","mods"],["application/mp21","m21"],["application/mp4","mp4"],["application/mspowerpoint",["ppt","pot","pps","ppz"]],["application/msword",["doc","dot","w6w","wiz","word"]],["application/mswrite","wri"],["application/mxf","mxf"],["application/netmc","mcp"],["application/octet-stream",["*"]],["application/oda","oda"],["application/oebps-package+xml","opf"],["application/ogg","ogx"],["application/olescript","axs"],["application/onenote","onetoc"],["application/patch-ops-error+xml","xer"],["application/pdf","pdf"],["application/pgp-encrypted","asc"],["application/pgp-signature","pgp"],["application/pics-rules","prf"],["application/pkcs-12","p12"],["application/pkcs-crl","crl"],["application/pkcs10","p10"],["application/pkcs7-mime",["p7c","p7m"]],["application/pkcs7-signature","p7s"],["application/pkcs8","p8"],["application/pkix-attr-cert","ac"],["application/pkix-cert",["cer","crt"]],["application/pkix-crl","crl"],["application/pkix-pkipath","pkipath"],["application/pkixcmp","pki"],["application/plain","text"],["application/pls+xml","pls"],["application/postscript",["ps","ai","eps"]],["application/powerpoint","ppt"],["application/pro_eng",["part","prt"]],["application/prs.cww","cww"],["application/pskc+xml","pskcxml"],["application/rdf+xml","rdf"],["application/reginfo+xml","rif"],["application/relax-ng-compact-syntax","rnc"],["application/resource-lists+xml","rl"],["application/resource-lists-diff+xml","rld"],["application/ringing-tones","rng"],["application/rls-services+xml","rs"],["application/rsd+xml","rsd"],["application/rss+xml","xml"],["application/rtf",["rtf","rtx"]],["application/sbml+xml","sbml"],["application/scvp-cv-request","scq"],["application/scvp-cv-response","scs"],["application/scvp-vp-request","spq"],["application/scvp-vp-response","spp"],["application/sdp","sdp"],["application/sea","sea"],["application/set","set"],["application/set-payment-initiation","setpay"],["application/set-registration-initiation","setreg"],["application/shf+xml","shf"],["application/sla","stl"],["application/smil",["smi","smil"]],["application/smil+xml","smi"],["application/solids","sol"],["application/sounder","sdr"],["application/sparql-query","rq"],["application/sparql-results+xml","srx"],["application/srgs","gram"],["application/srgs+xml","grxml"],["application/sru+xml","sru"],["application/ssml+xml","ssml"],["application/step",["step","stp"]],["application/streamingmedia","ssm"],["application/tei+xml","tei"],["application/thraud+xml","tfi"],["application/timestamped-data","tsd"],["application/toolbook","tbk"],["application/vda","vda"],["application/vnd.3gpp.pic-bw-large","plb"],["application/vnd.3gpp.pic-bw-small","psb"],["application/vnd.3gpp.pic-bw-var","pvb"],["application/vnd.3gpp2.tcap","tcap"],["application/vnd.3m.post-it-notes","pwn"],["application/vnd.accpac.simply.aso","aso"],["application/vnd.accpac.simply.imp","imp"],["application/vnd.acucobol","acu"],["application/vnd.acucorp","atc"],["application/vnd.adobe.air-application-installer-package+zip","air"],["application/vnd.adobe.fxp","fxp"],["application/vnd.adobe.xdp+xml","xdp"],["application/vnd.adobe.xfdf","xfdf"],["application/vnd.ahead.space","ahead"],["application/vnd.airzip.filesecure.azf","azf"],["application/vnd.airzip.filesecure.azs","azs"],["application/vnd.amazon.ebook","azw"],["application/vnd.americandynamics.acc","acc"],["application/vnd.amiga.ami","ami"],["application/vnd.android.package-archive","apk"],["application/vnd.anser-web-certificate-issue-initiation","cii"],["application/vnd.anser-web-funds-transfer-initiation","fti"],["application/vnd.antix.game-component","atx"],["application/vnd.apple.installer+xml","mpkg"],["application/vnd.apple.mpegurl","m3u8"],["application/vnd.aristanetworks.swi","swi"],["application/vnd.audiograph","aep"],["application/vnd.blueice.multipass","mpm"],["application/vnd.bmi","bmi"],["application/vnd.businessobjects","rep"],["application/vnd.chemdraw+xml","cdxml"],["application/vnd.chipnuts.karaoke-mmd","mmd"],["application/vnd.cinderella","cdy"],["application/vnd.claymore","cla"],["application/vnd.cloanto.rp9","rp9"],["application/vnd.clonk.c4group","c4g"],["application/vnd.cluetrust.cartomobile-config","c11amc"],["application/vnd.cluetrust.cartomobile-config-pkg","c11amz"],["application/vnd.commonspace","csp"],["application/vnd.contact.cmsg","cdbcmsg"],["application/vnd.cosmocaller","cmc"],["application/vnd.crick.clicker","clkx"],["application/vnd.crick.clicker.keyboard","clkk"],["application/vnd.crick.clicker.palette","clkp"],["application/vnd.crick.clicker.template","clkt"],["application/vnd.crick.clicker.wordbank","clkw"],["application/vnd.criticaltools.wbs+xml","wbs"],["application/vnd.ctc-posml","pml"],["application/vnd.cups-ppd","ppd"],["application/vnd.curl.car","car"],["application/vnd.curl.pcurl","pcurl"],["application/vnd.data-vision.rdz","rdz"],["application/vnd.denovo.fcselayout-link","fe_launch"],["application/vnd.dna","dna"],["application/vnd.dolby.mlp","mlp"],["application/vnd.dpgraph","dpg"],["application/vnd.dreamfactory","dfac"],["application/vnd.dvb.ait","ait"],["application/vnd.dvb.service","svc"],["application/vnd.dynageo","geo"],["application/vnd.ecowin.chart","mag"],["application/vnd.enliven","nml"],["application/vnd.epson.esf","esf"],["application/vnd.epson.msf","msf"],["application/vnd.epson.quickanime","qam"],["application/vnd.epson.salt","slt"],["application/vnd.epson.ssf","ssf"],["application/vnd.eszigno3+xml","es3"],["application/vnd.ezpix-album","ez2"],["application/vnd.ezpix-package","ez3"],["application/vnd.fdf","fdf"],["application/vnd.fdsn.seed","seed"],["application/vnd.flographit","gph"],["application/vnd.fluxtime.clip","ftc"],["application/vnd.framemaker","fm"],["application/vnd.frogans.fnc","fnc"],["application/vnd.frogans.ltf","ltf"],["application/vnd.fsc.weblaunch","fsc"],["application/vnd.fujitsu.oasys","oas"],["application/vnd.fujitsu.oasys2","oa2"],["application/vnd.fujitsu.oasys3","oa3"],["application/vnd.fujitsu.oasysgp","fg5"],["application/vnd.fujitsu.oasysprs","bh2"],["application/vnd.fujixerox.ddd","ddd"],["application/vnd.fujixerox.docuworks","xdw"],["application/vnd.fujixerox.docuworks.binder","xbd"],["application/vnd.fuzzysheet","fzs"],["application/vnd.genomatix.tuxedo","txd"],["application/vnd.geogebra.file","ggb"],["application/vnd.geogebra.tool","ggt"],["application/vnd.geometry-explorer","gex"],["application/vnd.geonext","gxt"],["application/vnd.geoplan","g2w"],["application/vnd.geospace","g3w"],["application/vnd.gmx","gmx"],["application/vnd.google-earth.kml+xml","kml"],["application/vnd.google-earth.kmz","kmz"],["application/vnd.grafeq","gqf"],["application/vnd.groove-account","gac"],["application/vnd.groove-help","ghf"],["application/vnd.groove-identity-message","gim"],["application/vnd.groove-injector","grv"],["application/vnd.groove-tool-message","gtm"],["application/vnd.groove-tool-template","tpl"],["application/vnd.groove-vcard","vcg"],["application/vnd.hal+xml","hal"],["application/vnd.handheld-entertainment+xml","zmm"],["application/vnd.hbci","hbci"],["application/vnd.hhe.lesson-player","les"],["application/vnd.hp-hpgl",["hgl","hpg","hpgl"]],["application/vnd.hp-hpid","hpid"],["application/vnd.hp-hps","hps"],["application/vnd.hp-jlyt","jlt"],["application/vnd.hp-pcl","pcl"],["application/vnd.hp-pclxl","pclxl"],["application/vnd.hydrostatix.sof-data","sfd-hdstx"],["application/vnd.hzn-3d-crossword","x3d"],["application/vnd.ibm.minipay","mpy"],["application/vnd.ibm.modcap","afp"],["application/vnd.ibm.rights-management","irm"],["application/vnd.ibm.secure-container","sc"],["application/vnd.iccprofile","icc"],["application/vnd.igloader","igl"],["application/vnd.immervision-ivp","ivp"],["application/vnd.immervision-ivu","ivu"],["application/vnd.insors.igm","igm"],["application/vnd.intercon.formnet","xpw"],["application/vnd.intergeo","i2g"],["application/vnd.intu.qbo","qbo"],["application/vnd.intu.qfx","qfx"],["application/vnd.ipunplugged.rcprofile","rcprofile"],["application/vnd.irepository.package+xml","irp"],["application/vnd.is-xpr","xpr"],["application/vnd.isac.fcs","fcs"],["application/vnd.jam","jam"],["application/vnd.jcp.javame.midlet-rms","rms"],["application/vnd.jisp","jisp"],["application/vnd.joost.joda-archive","joda"],["application/vnd.kahootz","ktz"],["application/vnd.kde.karbon","karbon"],["application/vnd.kde.kchart","chrt"],["application/vnd.kde.kformula","kfo"],["application/vnd.kde.kivio","flw"],["application/vnd.kde.kontour","kon"],["application/vnd.kde.kpresenter","kpr"],["application/vnd.kde.kspread","ksp"],["application/vnd.kde.kword","kwd"],["application/vnd.kenameaapp","htke"],["application/vnd.kidspiration","kia"],["application/vnd.kinar","kne"],["application/vnd.koan","skp"],["application/vnd.kodak-descriptor","sse"],["application/vnd.las.las+xml","lasxml"],["application/vnd.llamagraphics.life-balance.desktop","lbd"],["application/vnd.llamagraphics.life-balance.exchange+xml","lbe"],["application/vnd.lotus-1-2-3","123"],["application/vnd.lotus-approach","apr"],["application/vnd.lotus-freelance","pre"],["application/vnd.lotus-notes","nsf"],["application/vnd.lotus-organizer","org"],["application/vnd.lotus-screencam","scm"],["application/vnd.lotus-wordpro","lwp"],["application/vnd.macports.portpkg","portpkg"],["application/vnd.mcd","mcd"],["application/vnd.medcalcdata","mc1"],["application/vnd.mediastation.cdkey","cdkey"],["application/vnd.mfer","mwf"],["application/vnd.mfmp","mfm"],["application/vnd.micrografx.flo","flo"],["application/vnd.micrografx.igx","igx"],["application/vnd.mif","mif"],["application/vnd.mobius.daf","daf"],["application/vnd.mobius.dis","dis"],["application/vnd.mobius.mbk","mbk"],["application/vnd.mobius.mqy","mqy"],["application/vnd.mobius.msl","msl"],["application/vnd.mobius.plc","plc"],["application/vnd.mobius.txf","txf"],["application/vnd.mophun.application","mpn"],["application/vnd.mophun.certificate","mpc"],["application/vnd.mozilla.xul+xml","xul"],["application/vnd.ms-artgalry","cil"],["application/vnd.ms-cab-compressed","cab"],["application/vnd.ms-excel",["xls","xla","xlc","xlm","xlt","xlw","xlb","xll"]],["application/vnd.ms-excel.addin.macroenabled.12","xlam"],["application/vnd.ms-excel.sheet.binary.macroenabled.12","xlsb"],["application/vnd.ms-excel.sheet.macroenabled.12","xlsm"],["application/vnd.ms-excel.template.macroenabled.12","xltm"],["application/vnd.ms-fontobject","eot"],["application/vnd.ms-htmlhelp","chm"],["application/vnd.ms-ims","ims"],["application/vnd.ms-lrm","lrm"],["application/vnd.ms-officetheme","thmx"],["application/vnd.ms-outlook","msg"],["application/vnd.ms-pki.certstore","sst"],["application/vnd.ms-pki.pko","pko"],["application/vnd.ms-pki.seccat","cat"],["application/vnd.ms-pki.stl","stl"],["application/vnd.ms-pkicertstore","sst"],["application/vnd.ms-pkiseccat","cat"],["application/vnd.ms-pkistl","stl"],["application/vnd.ms-powerpoint",["ppt","pot","pps","ppa","pwz"]],["application/vnd.ms-powerpoint.addin.macroenabled.12","ppam"],["application/vnd.ms-powerpoint.presentation.macroenabled.12","pptm"],["application/vnd.ms-powerpoint.slide.macroenabled.12","sldm"],["application/vnd.ms-powerpoint.slideshow.macroenabled.12","ppsm"],["application/vnd.ms-powerpoint.template.macroenabled.12","potm"],["application/vnd.ms-project","mpp"],["application/vnd.ms-word.document.macroenabled.12","docm"],["application/vnd.ms-word.template.macroenabled.12","dotm"],["application/vnd.ms-works",["wks","wcm","wdb","wps"]],["application/vnd.ms-wpl","wpl"],["application/vnd.ms-xpsdocument","xps"],["application/vnd.mseq","mseq"],["application/vnd.musician","mus"],["application/vnd.muvee.style","msty"],["application/vnd.neurolanguage.nlu","nlu"],["application/vnd.noblenet-directory","nnd"],["application/vnd.noblenet-sealer","nns"],["application/vnd.noblenet-web","nnw"],["application/vnd.nokia.configuration-message","ncm"],["application/vnd.nokia.n-gage.data","ngdat"],["application/vnd.nokia.n-gage.symbian.install","n-gage"],["application/vnd.nokia.radio-preset","rpst"],["application/vnd.nokia.radio-presets","rpss"],["application/vnd.nokia.ringing-tone","rng"],["application/vnd.novadigm.edm","edm"],["application/vnd.novadigm.edx","edx"],["application/vnd.novadigm.ext","ext"],["application/vnd.oasis.opendocument.chart","odc"],["application/vnd.oasis.opendocument.chart-template","otc"],["application/vnd.oasis.opendocument.database","odb"],["application/vnd.oasis.opendocument.formula","odf"],["application/vnd.oasis.opendocument.formula-template","odft"],["application/vnd.oasis.opendocument.graphics","odg"],["application/vnd.oasis.opendocument.graphics-template","otg"],["application/vnd.oasis.opendocument.image","odi"],["application/vnd.oasis.opendocument.image-template","oti"],["application/vnd.oasis.opendocument.presentation","odp"],["application/vnd.oasis.opendocument.presentation-template","otp"],["application/vnd.oasis.opendocument.spreadsheet","ods"],["application/vnd.oasis.opendocument.spreadsheet-template","ots"],["application/vnd.oasis.opendocument.text","odt"],["application/vnd.oasis.opendocument.text-master","odm"],["application/vnd.oasis.opendocument.text-template","ott"],["application/vnd.oasis.opendocument.text-web","oth"],["application/vnd.olpc-sugar","xo"],["application/vnd.oma.dd2+xml","dd2"],["application/vnd.openofficeorg.extension","oxt"],["application/vnd.openxmlformats-officedocument.presentationml.presentation","pptx"],["application/vnd.openxmlformats-officedocument.presentationml.slide","sldx"],["application/vnd.openxmlformats-officedocument.presentationml.slideshow","ppsx"],["application/vnd.openxmlformats-officedocument.presentationml.template","potx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","xlsx"],["application/vnd.openxmlformats-officedocument.spreadsheetml.template","xltx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.document","docx"],["application/vnd.openxmlformats-officedocument.wordprocessingml.template","dotx"],["application/vnd.osgeo.mapguide.package","mgp"],["application/vnd.osgi.dp","dp"],["application/vnd.palm","pdb"],["application/vnd.pawaafile","paw"],["application/vnd.pg.format","str"],["application/vnd.pg.osasli","ei6"],["application/vnd.picsel","efif"],["application/vnd.pmi.widget","wg"],["application/vnd.pocketlearn","plf"],["application/vnd.powerbuilder6","pbd"],["application/vnd.previewsystems.box","box"],["application/vnd.proteus.magazine","mgz"],["application/vnd.publishare-delta-tree","qps"],["application/vnd.pvi.ptid1","ptid"],["application/vnd.quark.quarkxpress","qxd"],["application/vnd.realvnc.bed","bed"],["application/vnd.recordare.musicxml","mxl"],["application/vnd.recordare.musicxml+xml","musicxml"],["application/vnd.rig.cryptonote","cryptonote"],["application/vnd.rim.cod","cod"],["application/vnd.rn-realmedia","rm"],["application/vnd.rn-realplayer","rnx"],["application/vnd.route66.link66+xml","link66"],["application/vnd.sailingtracker.track","st"],["application/vnd.seemail","see"],["application/vnd.sema","sema"],["application/vnd.semd","semd"],["application/vnd.semf","semf"],["application/vnd.shana.informed.formdata","ifm"],["application/vnd.shana.informed.formtemplate","itp"],["application/vnd.shana.informed.interchange","iif"],["application/vnd.shana.informed.package","ipk"],["application/vnd.simtech-mindmapper","twd"],["application/vnd.smaf","mmf"],["application/vnd.smart.teacher","teacher"],["application/vnd.solent.sdkm+xml","sdkm"],["application/vnd.spotfire.dxp","dxp"],["application/vnd.spotfire.sfs","sfs"],["application/vnd.stardivision.calc","sdc"],["application/vnd.stardivision.draw","sda"],["application/vnd.stardivision.impress","sdd"],["application/vnd.stardivision.math","smf"],["application/vnd.stardivision.writer","sdw"],["application/vnd.stardivision.writer-global","sgl"],["application/vnd.stepmania.stepchart","sm"],["application/vnd.sun.xml.calc","sxc"],["application/vnd.sun.xml.calc.template","stc"],["application/vnd.sun.xml.draw","sxd"],["application/vnd.sun.xml.draw.template","std"],["application/vnd.sun.xml.impress","sxi"],["application/vnd.sun.xml.impress.template","sti"],["application/vnd.sun.xml.math","sxm"],["application/vnd.sun.xml.writer","sxw"],["application/vnd.sun.xml.writer.global","sxg"],["application/vnd.sun.xml.writer.template","stw"],["application/vnd.sus-calendar","sus"],["application/vnd.svd","svd"],["application/vnd.symbian.install","sis"],["application/vnd.syncml+xml","xsm"],["application/vnd.syncml.dm+wbxml","bdm"],["application/vnd.syncml.dm+xml","xdm"],["application/vnd.tao.intent-module-archive","tao"],["application/vnd.tmobile-livetv","tmo"],["application/vnd.trid.tpt","tpt"],["application/vnd.triscape.mxs","mxs"],["application/vnd.trueapp","tra"],["application/vnd.ufdl","ufd"],["application/vnd.uiq.theme","utz"],["application/vnd.umajin","umj"],["application/vnd.unity","unityweb"],["application/vnd.uoml+xml","uoml"],["application/vnd.vcx","vcx"],["application/vnd.visio","vsd"],["application/vnd.visionary","vis"],["application/vnd.vsf","vsf"],["application/vnd.wap.wbxml","wbxml"],["application/vnd.wap.wmlc","wmlc"],["application/vnd.wap.wmlscriptc","wmlsc"],["application/vnd.webturbo","wtb"],["application/vnd.wolfram.player","nbp"],["application/vnd.wordperfect","wpd"],["application/vnd.wqd","wqd"],["application/vnd.wt.stf","stf"],["application/vnd.xara",["web","xar"]],["application/vnd.xfdl","xfdl"],["application/vnd.yamaha.hv-dic","hvd"],["application/vnd.yamaha.hv-script","hvs"],["application/vnd.yamaha.hv-voice","hvp"],["application/vnd.yamaha.openscoreformat","osf"],["application/vnd.yamaha.openscoreformat.osfpvg+xml","osfpvg"],["application/vnd.yamaha.smaf-audio","saf"],["application/vnd.yamaha.smaf-phrase","spf"],["application/vnd.yellowriver-custom-menu","cmp"],["application/vnd.zul","zir"],["application/vnd.zzazz.deck+xml","zaz"],["application/vocaltec-media-desc","vmd"],["application/vocaltec-media-file","vmf"],["application/voicexml+xml","vxml"],["application/widget","wgt"],["application/winhlp","hlp"],["application/wordperfect",["wp","wp5","wp6","wpd"]],["application/wordperfect6.0",["w60","wp5"]],["application/wordperfect6.1","w61"],["application/wsdl+xml","wsdl"],["application/wspolicy+xml","wspolicy"],["application/x-123","wk1"],["application/x-7z-compressed","7z"],["application/x-abiword","abw"],["application/x-ace-compressed","ace"],["application/x-aim","aim"],["application/x-authorware-bin","aab"],["application/x-authorware-map","aam"],["application/x-authorware-seg","aas"],["application/x-bcpio","bcpio"],["application/x-binary","bin"],["application/x-binhex40","hqx"],["application/x-bittorrent","torrent"],["application/x-bsh",["bsh","sh","shar"]],["application/x-bytecode.elisp","elc"],["applicaiton/x-bytecode.python","pyc"],["application/x-bzip","bz"],["application/x-bzip2",["boz","bz2"]],["application/x-cdf","cdf"],["application/x-cdlink","vcd"],["application/x-chat",["cha","chat"]],["application/x-chess-pgn","pgn"],["application/x-cmu-raster","ras"],["application/x-cocoa","cco"],["application/x-compactpro","cpt"],["application/x-compress","z"],["application/x-compressed",["tgz","gz","z","zip"]],["application/x-conference","nsc"],["application/x-cpio","cpio"],["application/x-cpt","cpt"],["application/x-csh","csh"],["application/x-debian-package","deb"],["application/x-deepv","deepv"],["application/x-director",["dir","dcr","dxr"]],["application/x-doom","wad"],["application/x-dtbncx+xml","ncx"],["application/x-dtbook+xml","dtb"],["application/x-dtbresource+xml","res"],["application/x-dvi","dvi"],["application/x-elc","elc"],["application/x-envoy",["env","evy"]],["application/x-esrehber","es"],["application/x-excel",["xls","xla","xlb","xlc","xld","xlk","xll","xlm","xlt","xlv","xlw"]],["application/x-font-bdf","bdf"],["application/x-font-ghostscript","gsf"],["application/x-font-linux-psf","psf"],["application/x-font-otf","otf"],["application/x-font-pcf","pcf"],["application/x-font-snf","snf"],["application/x-font-ttf","ttf"],["application/x-font-type1","pfa"],["application/x-font-woff","woff"],["application/x-frame","mif"],["application/x-freelance","pre"],["application/x-futuresplash","spl"],["application/x-gnumeric","gnumeric"],["application/x-gsp","gsp"],["application/x-gss","gss"],["application/x-gtar","gtar"],["application/x-gzip",["gz","gzip"]],["application/x-hdf","hdf"],["application/x-helpfile",["help","hlp"]],["application/x-httpd-imap","imap"],["application/x-ima","ima"],["application/x-internet-signup",["ins","isp"]],["application/x-internett-signup","ins"],["application/x-inventor","iv"],["application/x-ip2","ip"],["application/x-iphone","iii"],["application/x-java-class","class"],["application/x-java-commerce","jcm"],["application/x-java-jnlp-file","jnlp"],["application/x-javascript","js"],["application/x-koan",["skd","skm","skp","skt"]],["application/x-ksh","ksh"],["application/x-latex",["latex","ltx"]],["application/x-lha","lha"],["application/x-lisp","lsp"],["application/x-livescreen","ivy"],["application/x-lotus","wq1"],["application/x-lotusscreencam","scm"],["application/x-lzh","lzh"],["application/x-lzx","lzx"],["application/x-mac-binhex40","hqx"],["application/x-macbinary","bin"],["application/x-magic-cap-package-1.0","mc$"],["application/x-mathcad","mcd"],["application/x-meme","mm"],["application/x-midi",["mid","midi"]],["application/x-mif","mif"],["application/x-mix-transfer","nix"],["application/x-mobipocket-ebook","prc"],["application/x-mplayer2","asx"],["application/x-ms-application","application"],["application/x-ms-wmd","wmd"],["application/x-ms-wmz","wmz"],["application/x-ms-xbap","xbap"],["application/x-msaccess","mdb"],["application/x-msbinder","obd"],["application/x-mscardfile","crd"],["application/x-msclip","clp"],["application/x-msdownload",["exe","dll"]],["application/x-msexcel",["xls","xla","xlw"]],["application/x-msmediaview",["mvb","m13","m14"]],["application/x-msmetafile","wmf"],["application/x-msmoney","mny"],["application/x-mspowerpoint","ppt"],["application/x-mspublisher","pub"],["application/x-msschedule","scd"],["application/x-msterminal","trm"],["application/x-mswrite","wri"],["application/x-navi-animation","ani"],["application/x-navidoc","nvd"],["application/x-navimap","map"],["application/x-navistyle","stl"],["application/x-netcdf",["cdf","nc"]],["application/x-newton-compatible-pkg","pkg"],["application/x-nokia-9000-communicator-add-on-software","aos"],["application/x-omc","omc"],["application/x-omcdatamaker","omcd"],["application/x-omcregerator","omcr"],["application/x-pagemaker",["pm4","pm5"]],["application/x-pcl","pcl"],["application/x-perfmon",["pma","pmc","pml","pmr","pmw"]],["application/x-pixclscript","plx"],["application/x-pkcs10","p10"],["application/x-pkcs12",["p12","pfx"]],["application/x-pkcs7-certificates",["p7b","spc"]],["application/x-pkcs7-certreqresp","p7r"],["application/x-pkcs7-mime",["p7m","p7c"]],["application/x-pkcs7-signature",["p7s","p7a"]],["application/x-pointplus","css"],["application/x-portable-anymap","pnm"],["application/x-project",["mpc","mpt","mpv","mpx"]],["application/x-qpro","wb1"],["application/x-rar-compressed","rar"],["application/x-rtf","rtf"],["application/x-sdp","sdp"],["application/x-sea","sea"],["application/x-seelogo","sl"],["application/x-sh","sh"],["application/x-shar",["shar","sh"]],["application/x-shockwave-flash","swf"],["application/x-silverlight-app","xap"],["application/x-sit","sit"],["application/x-sprite",["spr","sprite"]],["application/x-stuffit","sit"],["application/x-stuffitx","sitx"],["application/x-sv4cpio","sv4cpio"],["application/x-sv4crc","sv4crc"],["application/x-tar","tar"],["application/x-tbook",["sbk","tbk"]],["application/x-tcl","tcl"],["application/x-tex","tex"],["application/x-tex-tfm","tfm"],["application/x-texinfo",["texi","texinfo"]],["application/x-troff",["roff","t","tr"]],["application/x-troff-man","man"],["application/x-troff-me","me"],["application/x-troff-ms","ms"],["application/x-troff-msvideo","avi"],["application/x-ustar","ustar"],["application/x-visio",["vsd","vst","vsw"]],["application/x-vnd.audioexplosion.mzz","mzz"],["application/x-vnd.ls-xpix","xpix"],["application/x-vrml","vrml"],["application/x-wais-source",["src","wsrc"]],["application/x-winhelp","hlp"],["application/x-wintalk","wtk"],["application/x-world",["wrl","svr"]],["application/x-wpwin","wpd"],["application/x-wri","wri"],["application/x-x509-ca-cert",["cer","crt","der"]],["application/x-x509-user-cert","crt"],["application/x-xfig","fig"],["application/x-xpinstall","xpi"],["application/x-zip-compressed","zip"],["application/xcap-diff+xml","xdf"],["application/xenc+xml","xenc"],["application/xhtml+xml","xhtml"],["application/xml","xml"],["application/xml-dtd","dtd"],["application/xop+xml","xop"],["application/xslt+xml","xslt"],["application/xspf+xml","xspf"],["application/xv+xml","mxml"],["application/yang","yang"],["application/yin+xml","yin"],["application/ynd.ms-pkipko","pko"],["application/zip","zip"],["audio/adpcm","adp"],["audio/aiff",["aiff","aif","aifc"]],["audio/basic",["snd","au"]],["audio/it","it"],["audio/make",["funk","my","pfunk"]],["audio/make.my.funk","pfunk"],["audio/mid",["mid","rmi"]],["audio/midi",["midi","kar","mid"]],["audio/mod","mod"],["audio/mp4","mp4a"],["audio/mpeg",["mpga","mp3","m2a","mp2","mpa","mpg"]],["audio/mpeg3","mp3"],["audio/nspaudio",["la","lma"]],["audio/ogg","oga"],["audio/s3m","s3m"],["audio/tsp-audio","tsi"],["audio/tsplayer","tsp"],["audio/vnd.dece.audio","uva"],["audio/vnd.digital-winds","eol"],["audio/vnd.dra","dra"],["audio/vnd.dts","dts"],["audio/vnd.dts.hd","dtshd"],["audio/vnd.lucent.voice","lvp"],["audio/vnd.ms-playready.media.pya","pya"],["audio/vnd.nuera.ecelp4800","ecelp4800"],["audio/vnd.nuera.ecelp7470","ecelp7470"],["audio/vnd.nuera.ecelp9600","ecelp9600"],["audio/vnd.qcelp","qcp"],["audio/vnd.rip","rip"],["audio/voc","voc"],["audio/voxware","vox"],["audio/wav","wav"],["audio/webm","weba"],["audio/x-aac","aac"],["audio/x-adpcm","snd"],["audio/x-aiff",["aiff","aif","aifc"]],["audio/x-au","au"],["audio/x-gsm",["gsd","gsm"]],["audio/x-jam","jam"],["audio/x-liveaudio","lam"],["audio/x-mid",["mid","midi"]],["audio/x-midi",["midi","mid"]],["audio/x-mod","mod"],["audio/x-mpeg","mp2"],["audio/x-mpeg-3","mp3"],["audio/x-mpegurl","m3u"],["audio/x-mpequrl","m3u"],["audio/x-ms-wax","wax"],["audio/x-ms-wma","wma"],["audio/x-nspaudio",["la","lma"]],["audio/x-pn-realaudio",["ra","ram","rm","rmm","rmp"]],["audio/x-pn-realaudio-plugin",["ra","rmp","rpm"]],["audio/x-psid","sid"],["audio/x-realaudio","ra"],["audio/x-twinvq","vqf"],["audio/x-twinvq-plugin",["vqe","vql"]],["audio/x-vnd.audioexplosion.mjuicemediafile","mjf"],["audio/x-voc","voc"],["audio/x-wav","wav"],["audio/xm","xm"],["chemical/x-cdx","cdx"],["chemical/x-cif","cif"],["chemical/x-cmdf","cmdf"],["chemical/x-cml","cml"],["chemical/x-csml","csml"],["chemical/x-pdb",["pdb","xyz"]],["chemical/x-xyz","xyz"],["drawing/x-dwf","dwf"],["i-world/i-vrml","ivr"],["image/bmp",["bmp","bm"]],["image/cgm","cgm"],["image/cis-cod","cod"],["image/cmu-raster",["ras","rast"]],["image/fif","fif"],["image/florian",["flo","turbot"]],["image/g3fax","g3"],["image/gif","gif"],["image/ief",["ief","iefs"]],["image/jpeg",["jpeg","jpe","jpg","jfif","jfif-tbnl"]],["image/jutvision","jut"],["image/ktx","ktx"],["image/naplps",["nap","naplps"]],["image/pict",["pic","pict"]],["image/pipeg","jfif"],["image/pjpeg",["jfif","jpe","jpeg","jpg"]],["image/png",["png","x-png"]],["image/prs.btif","btif"],["image/svg+xml","svg"],["image/tiff",["tif","tiff"]],["image/vasa","mcf"],["image/vnd.adobe.photoshop","psd"],["image/vnd.dece.graphic","uvi"],["image/vnd.djvu","djvu"],["image/vnd.dvb.subtitle","sub"],["image/vnd.dwg",["dwg","dxf","svf"]],["image/vnd.dxf","dxf"],["image/vnd.fastbidsheet","fbs"],["image/vnd.fpx","fpx"],["image/vnd.fst","fst"],["image/vnd.fujixerox.edmics-mmr","mmr"],["image/vnd.fujixerox.edmics-rlc","rlc"],["image/vnd.ms-modi","mdi"],["image/vnd.net-fpx",["fpx","npx"]],["image/vnd.rn-realflash","rf"],["image/vnd.rn-realpix","rp"],["image/vnd.wap.wbmp","wbmp"],["image/vnd.xiff","xif"],["image/webp","webp"],["image/x-cmu-raster","ras"],["image/x-cmx","cmx"],["image/x-dwg",["dwg","dxf","svf"]],["image/x-freehand","fh"],["image/x-icon","ico"],["image/x-jg","art"],["image/x-jps","jps"],["image/x-niff",["niff","nif"]],["image/x-pcx","pcx"],["image/x-pict",["pct","pic"]],["image/x-portable-anymap","pnm"],["image/x-portable-bitmap","pbm"],["image/x-portable-graymap","pgm"],["image/x-portable-greymap","pgm"],["image/x-portable-pixmap","ppm"],["image/x-quicktime",["qif","qti","qtif"]],["image/x-rgb","rgb"],["image/x-tiff",["tif","tiff"]],["image/x-windows-bmp","bmp"],["image/x-xbitmap","xbm"],["image/x-xbm","xbm"],["image/x-xpixmap",["xpm","pm"]],["image/x-xwd","xwd"],["image/x-xwindowdump","xwd"],["image/xbm","xbm"],["image/xpm","xpm"],["message/rfc822",["eml","mht","mhtml","nws","mime"]],["model/iges",["iges","igs"]],["model/mesh","msh"],["model/vnd.collada+xml","dae"],["model/vnd.dwf","dwf"],["model/vnd.gdl","gdl"],["model/vnd.gtw","gtw"],["model/vnd.mts","mts"],["model/vnd.vtu","vtu"],["model/vrml",["vrml","wrl","wrz"]],["model/x-pov","pov"],["multipart/x-gzip","gzip"],["multipart/x-ustar","ustar"],["multipart/x-zip","zip"],["music/crescendo",["mid","midi"]],["music/x-karaoke","kar"],["paleovu/x-pv","pvu"],["text/asp","asp"],["text/calendar","ics"],["text/css","css"],["text/csv","csv"],["text/ecmascript","js"],["text/h323","323"],["text/html",["html","htm","stm","acgi","htmls","htx","shtml"]],["text/iuls","uls"],["text/javascript","js"],["text/mcf","mcf"],["text/n3","n3"],["text/pascal","pas"],["text/plain",["txt","bas","c","h","c++","cc","com","conf","cxx","def","f","f90","for","g","hh","idc","jav","java","list","log","lst","m","mar","pl","sdml","text"]],["text/plain-bas","par"],["text/prs.lines.tag","dsc"],["text/richtext",["rtx","rt","rtf"]],["text/scriplet","wsc"],["text/scriptlet","sct"],["text/sgml",["sgm","sgml"]],["text/tab-separated-values","tsv"],["text/troff","t"],["text/turtle","ttl"],["text/uri-list",["uni","unis","uri","uris"]],["text/vnd.abc","abc"],["text/vnd.curl","curl"],["text/vnd.curl.dcurl","dcurl"],["text/vnd.curl.mcurl","mcurl"],["text/vnd.curl.scurl","scurl"],["text/vnd.fly","fly"],["text/vnd.fmi.flexstor","flx"],["text/vnd.graphviz","gv"],["text/vnd.in3d.3dml","3dml"],["text/vnd.in3d.spot","spot"],["text/vnd.rn-realtext","rt"],["text/vnd.sun.j2me.app-descriptor","jad"],["text/vnd.wap.wml","wml"],["text/vnd.wap.wmlscript","wmls"],["text/webviewhtml","htt"],["text/x-asm",["asm","s"]],["text/x-audiosoft-intra","aip"],["text/x-c",["c","cc","cpp"]],["text/x-component","htc"],["text/x-fortran",["for","f","f77","f90"]],["text/x-h",["h","hh"]],["text/x-java-source",["java","jav"]],["text/x-java-source,java","java"],["text/x-la-asf","lsx"],["text/x-m","m"],["text/x-pascal","p"],["text/x-script","hlb"],["text/x-script.csh","csh"],["text/x-script.elisp","el"],["text/x-script.guile","scm"],["text/x-script.ksh","ksh"],["text/x-script.lisp","lsp"],["text/x-script.perl","pl"],["text/x-script.perl-module","pm"],["text/x-script.phyton","py"],["text/x-script.rexx","rexx"],["text/x-script.scheme","scm"],["text/x-script.sh","sh"],["text/x-script.tcl","tcl"],["text/x-script.tcsh","tcsh"],["text/x-script.zsh","zsh"],["text/x-server-parsed-html",["shtml","ssi"]],["text/x-setext","etx"],["text/x-sgml",["sgm","sgml"]],["text/x-speech",["spc","talk"]],["text/x-uil","uil"],["text/x-uuencode",["uu","uue"]],["text/x-vcalendar","vcs"],["text/x-vcard","vcf"],["text/xml","xml"],["video/3gpp","3gp"],["video/3gpp2","3g2"],["video/animaflex","afl"],["video/avi","avi"],["video/avs-video","avs"],["video/dl","dl"],["video/fli","fli"],["video/gl","gl"],["video/h261","h261"],["video/h263","h263"],["video/h264","h264"],["video/jpeg","jpgv"],["video/jpm","jpm"],["video/mj2","mj2"],["video/mp4","mp4"],["video/mpeg",["mpeg","mp2","mpa","mpe","mpg","mpv2","m1v","m2v","mp3"]],["video/msvideo","avi"],["video/ogg","ogv"],["video/quicktime",["mov","qt","moov"]],["video/vdo","vdo"],["video/vivo",["viv","vivo"]],["video/vnd.dece.hd","uvh"],["video/vnd.dece.mobile","uvm"],["video/vnd.dece.pd","uvp"],["video/vnd.dece.sd","uvs"],["video/vnd.dece.video","uvv"],["video/vnd.fvt","fvt"],["video/vnd.mpegurl","mxu"],["video/vnd.ms-playready.media.pyv","pyv"],["video/vnd.rn-realvideo","rv"],["video/vnd.uvvu.mp4","uvu"],["video/vnd.vivo",["viv","vivo"]],["video/vosaic","vos"],["video/webm","webm"],["video/x-amt-demorun","xdr"],["video/x-amt-showrun","xsr"],["video/x-atomic3d-feature","fmf"],["video/x-dl","dl"],["video/x-dv",["dif","dv"]],["video/x-f4v","f4v"],["video/x-fli","fli"],["video/x-flv","flv"],["video/x-gl","gl"],["video/x-isvideo","isu"],["video/x-la-asf",["lsf","lsx"]],["video/x-m4v","m4v"],["video/x-motion-jpeg","mjpg"],["video/x-mpeg",["mp3","mp2"]],["video/x-mpeq2a","mp2"],["video/x-ms-asf",["asf","asr","asx"]],["video/x-ms-asf-plugin","asx"],["video/x-ms-wm","wm"],["video/x-ms-wmv","wmv"],["video/x-ms-wmx","wmx"],["video/x-ms-wvx","wvx"],["video/x-msvideo","avi"],["video/x-qtc","qtc"],["video/x-scm","scm"],["video/x-sgi-movie",["movie","mv"]],["windows/metafile","wmf"],["www/mime","mime"],["x-conference/x-cooltalk","ice"],["x-music/x-midi",["mid","midi"]],["x-world/x-3dmf",["3dm","3dmf","qd3","qd3d"]],["x-world/x-svr","svr"],["x-world/x-vrml",["flr","vrml","wrl","wrz","xaf","xof"]],["x-world/x-vrt","vrt"],["xgl/drawing","xgz"],["xgl/movie","xmz"]]),$=new Map([["123","application/vnd.lotus-1-2-3"],["323","text/h323"],["*","application/octet-stream"],["3dm","x-world/x-3dmf"],["3dmf","x-world/x-3dmf"],["3dml","text/vnd.in3d.3dml"],["3g2","video/3gpp2"],["3gp","video/3gpp"],["7z","application/x-7z-compressed"],["a","application/octet-stream"],["aab","application/x-authorware-bin"],["aac","audio/x-aac"],["aam","application/x-authorware-map"],["aas","application/x-authorware-seg"],["abc","text/vnd.abc"],["abw","application/x-abiword"],["ac","application/pkix-attr-cert"],["acc","application/vnd.americandynamics.acc"],["ace","application/x-ace-compressed"],["acgi","text/html"],["acu","application/vnd.acucobol"],["acx","application/internet-property-stream"],["adp","audio/adpcm"],["aep","application/vnd.audiograph"],["afl","video/animaflex"],["afp","application/vnd.ibm.modcap"],["ahead","application/vnd.ahead.space"],["ai","application/postscript"],["aif",["audio/aiff","audio/x-aiff"]],["aifc",["audio/aiff","audio/x-aiff"]],["aiff",["audio/aiff","audio/x-aiff"]],["aim","application/x-aim"],["aip","text/x-audiosoft-intra"],["air","application/vnd.adobe.air-application-installer-package+zip"],["ait","application/vnd.dvb.ait"],["ami","application/vnd.amiga.ami"],["ani","application/x-navi-animation"],["aos","application/x-nokia-9000-communicator-add-on-software"],["apk","application/vnd.android.package-archive"],["application","application/x-ms-application"],["apr","application/vnd.lotus-approach"],["aps","application/mime"],["arc","application/octet-stream"],["arj",["application/arj","application/octet-stream"]],["art","image/x-jg"],["asf","video/x-ms-asf"],["asm","text/x-asm"],["aso","application/vnd.accpac.simply.aso"],["asp","text/asp"],["asr","video/x-ms-asf"],["asx",["video/x-ms-asf","application/x-mplayer2","video/x-ms-asf-plugin"]],["atc","application/vnd.acucorp"],["atomcat","application/atomcat+xml"],["atomsvc","application/atomsvc+xml"],["atx","application/vnd.antix.game-component"],["au",["audio/basic","audio/x-au"]],["avi",["video/avi","video/msvideo","application/x-troff-msvideo","video/x-msvideo"]],["avs","video/avs-video"],["aw","application/applixware"],["axs","application/olescript"],["azf","application/vnd.airzip.filesecure.azf"],["azs","application/vnd.airzip.filesecure.azs"],["azw","application/vnd.amazon.ebook"],["bas","text/plain"],["bcpio","application/x-bcpio"],["bdf","application/x-font-bdf"],["bdm","application/vnd.syncml.dm+wbxml"],["bed","application/vnd.realvnc.bed"],["bh2","application/vnd.fujitsu.oasysprs"],["bin",["application/octet-stream","application/mac-binary","application/macbinary","application/x-macbinary","application/x-binary"]],["bm","image/bmp"],["bmi","application/vnd.bmi"],["bmp",["image/bmp","image/x-windows-bmp"]],["boo","application/book"],["book","application/book"],["box","application/vnd.previewsystems.box"],["boz","application/x-bzip2"],["bsh","application/x-bsh"],["btif","image/prs.btif"],["bz","application/x-bzip"],["bz2","application/x-bzip2"],["c",["text/plain","text/x-c"]],["c++","text/plain"],["c11amc","application/vnd.cluetrust.cartomobile-config"],["c11amz","application/vnd.cluetrust.cartomobile-config-pkg"],["c4g","application/vnd.clonk.c4group"],["cab","application/vnd.ms-cab-compressed"],["car","application/vnd.curl.car"],["cat",["application/vnd.ms-pkiseccat","application/vnd.ms-pki.seccat"]],["cc",["text/plain","text/x-c"]],["ccad","application/clariscad"],["cco","application/x-cocoa"],["ccxml","application/ccxml+xml,"],["cdbcmsg","application/vnd.contact.cmsg"],["cdf",["application/cdf","application/x-cdf","application/x-netcdf"]],["cdkey","application/vnd.mediastation.cdkey"],["cdmia","application/cdmi-capability"],["cdmic","application/cdmi-container"],["cdmid","application/cdmi-domain"],["cdmio","application/cdmi-object"],["cdmiq","application/cdmi-queue"],["cdx","chemical/x-cdx"],["cdxml","application/vnd.chemdraw+xml"],["cdy","application/vnd.cinderella"],["cer",["application/pkix-cert","application/x-x509-ca-cert"]],["cgm","image/cgm"],["cha","application/x-chat"],["chat","application/x-chat"],["chm","application/vnd.ms-htmlhelp"],["chrt","application/vnd.kde.kchart"],["cif","chemical/x-cif"],["cii","application/vnd.anser-web-certificate-issue-initiation"],["cil","application/vnd.ms-artgalry"],["cla","application/vnd.claymore"],["class",["application/octet-stream","application/java","application/java-byte-code","application/java-vm","application/x-java-class"]],["clkk","application/vnd.crick.clicker.keyboard"],["clkp","application/vnd.crick.clicker.palette"],["clkt","application/vnd.crick.clicker.template"],["clkw","application/vnd.crick.clicker.wordbank"],["clkx","application/vnd.crick.clicker"],["clp","application/x-msclip"],["cmc","application/vnd.cosmocaller"],["cmdf","chemical/x-cmdf"],["cml","chemical/x-cml"],["cmp","application/vnd.yellowriver-custom-menu"],["cmx","image/x-cmx"],["cod",["image/cis-cod","application/vnd.rim.cod"]],["com",["application/octet-stream","text/plain"]],["conf","text/plain"],["cpio","application/x-cpio"],["cpp","text/x-c"],["cpt",["application/mac-compactpro","application/x-compactpro","application/x-cpt"]],["crd","application/x-mscardfile"],["crl",["application/pkix-crl","application/pkcs-crl"]],["crt",["application/pkix-cert","application/x-x509-user-cert","application/x-x509-ca-cert"]],["cryptonote","application/vnd.rig.cryptonote"],["csh",["text/x-script.csh","application/x-csh"]],["csml","chemical/x-csml"],["csp","application/vnd.commonspace"],["css",["text/css","application/x-pointplus"]],["csv","text/csv"],["cu","application/cu-seeme"],["curl","text/vnd.curl"],["cww","application/prs.cww"],["cxx","text/plain"],["dae","model/vnd.collada+xml"],["daf","application/vnd.mobius.daf"],["davmount","application/davmount+xml"],["dcr","application/x-director"],["dcurl","text/vnd.curl.dcurl"],["dd2","application/vnd.oma.dd2+xml"],["ddd","application/vnd.fujixerox.ddd"],["deb","application/x-debian-package"],["deepv","application/x-deepv"],["def","text/plain"],["der","application/x-x509-ca-cert"],["dfac","application/vnd.dreamfactory"],["dif","video/x-dv"],["dir","application/x-director"],["dis","application/vnd.mobius.dis"],["djvu","image/vnd.djvu"],["dl",["video/dl","video/x-dl"]],["dll","application/x-msdownload"],["dms","application/octet-stream"],["dna","application/vnd.dna"],["doc","application/msword"],["docm","application/vnd.ms-word.document.macroenabled.12"],["docx","application/vnd.openxmlformats-officedocument.wordprocessingml.document"],["dot","application/msword"],["dotm","application/vnd.ms-word.template.macroenabled.12"],["dotx","application/vnd.openxmlformats-officedocument.wordprocessingml.template"],["dp",["application/commonground","application/vnd.osgi.dp"]],["dpg","application/vnd.dpgraph"],["dra","audio/vnd.dra"],["drw","application/drafting"],["dsc","text/prs.lines.tag"],["dssc","application/dssc+der"],["dtb","application/x-dtbook+xml"],["dtd","application/xml-dtd"],["dts","audio/vnd.dts"],["dtshd","audio/vnd.dts.hd"],["dump","application/octet-stream"],["dv","video/x-dv"],["dvi","application/x-dvi"],["dwf",["model/vnd.dwf","drawing/x-dwf"]],["dwg",["application/acad","image/vnd.dwg","image/x-dwg"]],["dxf",["application/dxf","image/vnd.dwg","image/vnd.dxf","image/x-dwg"]],["dxp","application/vnd.spotfire.dxp"],["dxr","application/x-director"],["ecelp4800","audio/vnd.nuera.ecelp4800"],["ecelp7470","audio/vnd.nuera.ecelp7470"],["ecelp9600","audio/vnd.nuera.ecelp9600"],["edm","application/vnd.novadigm.edm"],["edx","application/vnd.novadigm.edx"],["efif","application/vnd.picsel"],["ei6","application/vnd.pg.osasli"],["el","text/x-script.elisp"],["elc",["application/x-elc","application/x-bytecode.elisp"]],["eml","message/rfc822"],["emma","application/emma+xml"],["env","application/x-envoy"],["eol","audio/vnd.digital-winds"],["eot","application/vnd.ms-fontobject"],["eps","application/postscript"],["epub","application/epub+zip"],["es",["application/ecmascript","application/x-esrehber"]],["es3","application/vnd.eszigno3+xml"],["esf","application/vnd.epson.esf"],["etx","text/x-setext"],["evy",["application/envoy","application/x-envoy"]],["exe",["application/octet-stream","application/x-msdownload"]],["exi","application/exi"],["ext","application/vnd.novadigm.ext"],["ez2","application/vnd.ezpix-album"],["ez3","application/vnd.ezpix-package"],["f",["text/plain","text/x-fortran"]],["f4v","video/x-f4v"],["f77","text/x-fortran"],["f90",["text/plain","text/x-fortran"]],["fbs","image/vnd.fastbidsheet"],["fcs","application/vnd.isac.fcs"],["fdf","application/vnd.fdf"],["fe_launch","application/vnd.denovo.fcselayout-link"],["fg5","application/vnd.fujitsu.oasysgp"],["fh","image/x-freehand"],["fif",["application/fractals","image/fif"]],["fig","application/x-xfig"],["fli",["video/fli","video/x-fli"]],["flo",["image/florian","application/vnd.micrografx.flo"]],["flr","x-world/x-vrml"],["flv","video/x-flv"],["flw","application/vnd.kde.kivio"],["flx","text/vnd.fmi.flexstor"],["fly","text/vnd.fly"],["fm","application/vnd.framemaker"],["fmf","video/x-atomic3d-feature"],["fnc","application/vnd.frogans.fnc"],["for",["text/plain","text/x-fortran"]],["fpx",["image/vnd.fpx","image/vnd.net-fpx"]],["frl","application/freeloader"],["fsc","application/vnd.fsc.weblaunch"],["fst","image/vnd.fst"],["ftc","application/vnd.fluxtime.clip"],["fti","application/vnd.anser-web-funds-transfer-initiation"],["funk","audio/make"],["fvt","video/vnd.fvt"],["fxp","application/vnd.adobe.fxp"],["fzs","application/vnd.fuzzysheet"],["g","text/plain"],["g2w","application/vnd.geoplan"],["g3","image/g3fax"],["g3w","application/vnd.geospace"],["gac","application/vnd.groove-account"],["gdl","model/vnd.gdl"],["geo","application/vnd.dynageo"],["gex","application/vnd.geometry-explorer"],["ggb","application/vnd.geogebra.file"],["ggt","application/vnd.geogebra.tool"],["ghf","application/vnd.groove-help"],["gif","image/gif"],["gim","application/vnd.groove-identity-message"],["gl",["video/gl","video/x-gl"]],["gmx","application/vnd.gmx"],["gnumeric","application/x-gnumeric"],["gph","application/vnd.flographit"],["gqf","application/vnd.grafeq"],["gram","application/srgs"],["grv","application/vnd.groove-injector"],["grxml","application/srgs+xml"],["gsd","audio/x-gsm"],["gsf","application/x-font-ghostscript"],["gsm","audio/x-gsm"],["gsp","application/x-gsp"],["gss","application/x-gss"],["gtar","application/x-gtar"],["gtm","application/vnd.groove-tool-message"],["gtw","model/vnd.gtw"],["gv","text/vnd.graphviz"],["gxt","application/vnd.geonext"],["gz",["application/x-gzip","application/x-compressed"]],["gzip",["multipart/x-gzip","application/x-gzip"]],["h",["text/plain","text/x-h"]],["h261","video/h261"],["h263","video/h263"],["h264","video/h264"],["hal","application/vnd.hal+xml"],["hbci","application/vnd.hbci"],["hdf","application/x-hdf"],["help","application/x-helpfile"],["hgl","application/vnd.hp-hpgl"],["hh",["text/plain","text/x-h"]],["hlb","text/x-script"],["hlp",["application/winhlp","application/hlp","application/x-helpfile","application/x-winhelp"]],["hpg","application/vnd.hp-hpgl"],["hpgl","application/vnd.hp-hpgl"],["hpid","application/vnd.hp-hpid"],["hps","application/vnd.hp-hps"],["hqx",["application/mac-binhex40","application/binhex","application/binhex4","application/mac-binhex","application/x-binhex40","application/x-mac-binhex40"]],["hta","application/hta"],["htc","text/x-component"],["htke","application/vnd.kenameaapp"],["htm","text/html"],["html","text/html"],["htmls","text/html"],["htt","text/webviewhtml"],["htx","text/html"],["hvd","application/vnd.yamaha.hv-dic"],["hvp","application/vnd.yamaha.hv-voice"],["hvs","application/vnd.yamaha.hv-script"],["i2g","application/vnd.intergeo"],["icc","application/vnd.iccprofile"],["ice","x-conference/x-cooltalk"],["ico","image/x-icon"],["ics","text/calendar"],["idc","text/plain"],["ief","image/ief"],["iefs","image/ief"],["ifm","application/vnd.shana.informed.formdata"],["iges",["application/iges","model/iges"]],["igl","application/vnd.igloader"],["igm","application/vnd.insors.igm"],["igs",["application/iges","model/iges"]],["igx","application/vnd.micrografx.igx"],["iif","application/vnd.shana.informed.interchange"],["iii","application/x-iphone"],["ima","application/x-ima"],["imap","application/x-httpd-imap"],["imp","application/vnd.accpac.simply.imp"],["ims","application/vnd.ms-ims"],["inf","application/inf"],["ins",["application/x-internet-signup","application/x-internett-signup"]],["ip","application/x-ip2"],["ipfix","application/ipfix"],["ipk","application/vnd.shana.informed.package"],["irm","application/vnd.ibm.rights-management"],["irp","application/vnd.irepository.package+xml"],["isp","application/x-internet-signup"],["isu","video/x-isvideo"],["it","audio/it"],["itp","application/vnd.shana.informed.formtemplate"],["iv","application/x-inventor"],["ivp","application/vnd.immervision-ivp"],["ivr","i-world/i-vrml"],["ivu","application/vnd.immervision-ivu"],["ivy","application/x-livescreen"],["jad","text/vnd.sun.j2me.app-descriptor"],["jam",["application/vnd.jam","audio/x-jam"]],["jar","application/java-archive"],["jav",["text/plain","text/x-java-source"]],["java",["text/plain","text/x-java-source,java","text/x-java-source"]],["jcm","application/x-java-commerce"],["jfif",["image/pipeg","image/jpeg","image/pjpeg"]],["jfif-tbnl","image/jpeg"],["jisp","application/vnd.jisp"],["jlt","application/vnd.hp-jlyt"],["jnlp","application/x-java-jnlp-file"],["joda","application/vnd.joost.joda-archive"],["jpe",["image/jpeg","image/pjpeg"]],["jpeg",["image/jpeg","image/pjpeg"]],["jpg",["image/jpeg","image/pjpeg"]],["jpgv","video/jpeg"],["jpm","video/jpm"],["jps","image/x-jps"],["js",["application/javascript","application/ecmascript","text/javascript","text/ecmascript","application/x-javascript"]],["json","application/json"],["jut","image/jutvision"],["kar",["audio/midi","music/x-karaoke"]],["karbon","application/vnd.kde.karbon"],["kfo","application/vnd.kde.kformula"],["kia","application/vnd.kidspiration"],["kml","application/vnd.google-earth.kml+xml"],["kmz","application/vnd.google-earth.kmz"],["kne","application/vnd.kinar"],["kon","application/vnd.kde.kontour"],["kpr","application/vnd.kde.kpresenter"],["ksh",["application/x-ksh","text/x-script.ksh"]],["ksp","application/vnd.kde.kspread"],["ktx","image/ktx"],["ktz","application/vnd.kahootz"],["kwd","application/vnd.kde.kword"],["la",["audio/nspaudio","audio/x-nspaudio"]],["lam","audio/x-liveaudio"],["lasxml","application/vnd.las.las+xml"],["latex","application/x-latex"],["lbd","application/vnd.llamagraphics.life-balance.desktop"],["lbe","application/vnd.llamagraphics.life-balance.exchange+xml"],["les","application/vnd.hhe.lesson-player"],["lha",["application/octet-stream","application/lha","application/x-lha"]],["lhx","application/octet-stream"],["link66","application/vnd.route66.link66+xml"],["list","text/plain"],["lma",["audio/nspaudio","audio/x-nspaudio"]],["log","text/plain"],["lrm","application/vnd.ms-lrm"],["lsf","video/x-la-asf"],["lsp",["application/x-lisp","text/x-script.lisp"]],["lst","text/plain"],["lsx",["video/x-la-asf","text/x-la-asf"]],["ltf","application/vnd.frogans.ltf"],["ltx","application/x-latex"],["lvp","audio/vnd.lucent.voice"],["lwp","application/vnd.lotus-wordpro"],["lzh",["application/octet-stream","application/x-lzh"]],["lzx",["application/lzx","application/octet-stream","application/x-lzx"]],["m",["text/plain","text/x-m"]],["m13","application/x-msmediaview"],["m14","application/x-msmediaview"],["m1v","video/mpeg"],["m21","application/mp21"],["m2a","audio/mpeg"],["m2v","video/mpeg"],["m3u",["audio/x-mpegurl","audio/x-mpequrl"]],["m3u8","application/vnd.apple.mpegurl"],["m4v","video/x-m4v"],["ma","application/mathematica"],["mads","application/mads+xml"],["mag","application/vnd.ecowin.chart"],["man","application/x-troff-man"],["map","application/x-navimap"],["mar","text/plain"],["mathml","application/mathml+xml"],["mbd","application/mbedlet"],["mbk","application/vnd.mobius.mbk"],["mbox","application/mbox"],["mc$","application/x-magic-cap-package-1.0"],["mc1","application/vnd.medcalcdata"],["mcd",["application/mcad","application/vnd.mcd","application/x-mathcad"]],["mcf",["image/vasa","text/mcf"]],["mcp","application/netmc"],["mcurl","text/vnd.curl.mcurl"],["mdb","application/x-msaccess"],["mdi","image/vnd.ms-modi"],["me","application/x-troff-me"],["meta4","application/metalink4+xml"],["mets","application/mets+xml"],["mfm","application/vnd.mfmp"],["mgp","application/vnd.osgeo.mapguide.package"],["mgz","application/vnd.proteus.magazine"],["mht","message/rfc822"],["mhtml","message/rfc822"],["mid",["audio/mid","audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["midi",["audio/midi","music/crescendo","x-music/x-midi","audio/x-midi","application/x-midi","audio/x-mid"]],["mif",["application/vnd.mif","application/x-mif","application/x-frame"]],["mime",["message/rfc822","www/mime"]],["mj2","video/mj2"],["mjf","audio/x-vnd.audioexplosion.mjuicemediafile"],["mjpg","video/x-motion-jpeg"],["mlp","application/vnd.dolby.mlp"],["mm",["application/base64","application/x-meme"]],["mmd","application/vnd.chipnuts.karaoke-mmd"],["mme","application/base64"],["mmf","application/vnd.smaf"],["mmr","image/vnd.fujixerox.edmics-mmr"],["mny","application/x-msmoney"],["mod",["audio/mod","audio/x-mod"]],["mods","application/mods+xml"],["moov","video/quicktime"],["mov","video/quicktime"],["movie","video/x-sgi-movie"],["mp2",["video/mpeg","audio/mpeg","video/x-mpeg","audio/x-mpeg","video/x-mpeq2a"]],["mp3",["audio/mpeg","audio/mpeg3","video/mpeg","audio/x-mpeg-3","video/x-mpeg"]],["mp4",["video/mp4","application/mp4"]],["mp4a","audio/mp4"],["mpa",["video/mpeg","audio/mpeg"]],["mpc",["application/vnd.mophun.certificate","application/x-project"]],["mpe","video/mpeg"],["mpeg","video/mpeg"],["mpg",["video/mpeg","audio/mpeg"]],["mpga","audio/mpeg"],["mpkg","application/vnd.apple.installer+xml"],["mpm","application/vnd.blueice.multipass"],["mpn","application/vnd.mophun.application"],["mpp","application/vnd.ms-project"],["mpt","application/x-project"],["mpv","application/x-project"],["mpv2","video/mpeg"],["mpx","application/x-project"],["mpy","application/vnd.ibm.minipay"],["mqy","application/vnd.mobius.mqy"],["mrc","application/marc"],["mrcx","application/marcxml+xml"],["ms","application/x-troff-ms"],["mscml","application/mediaservercontrol+xml"],["mseq","application/vnd.mseq"],["msf","application/vnd.epson.msf"],["msg","application/vnd.ms-outlook"],["msh","model/mesh"],["msl","application/vnd.mobius.msl"],["msty","application/vnd.muvee.style"],["mts","model/vnd.mts"],["mus","application/vnd.musician"],["musicxml","application/vnd.recordare.musicxml+xml"],["mv","video/x-sgi-movie"],["mvb","application/x-msmediaview"],["mwf","application/vnd.mfer"],["mxf","application/mxf"],["mxl","application/vnd.recordare.musicxml"],["mxml","application/xv+xml"],["mxs","application/vnd.triscape.mxs"],["mxu","video/vnd.mpegurl"],["my","audio/make"],["mzz","application/x-vnd.audioexplosion.mzz"],["n-gage","application/vnd.nokia.n-gage.symbian.install"],["n3","text/n3"],["nap","image/naplps"],["naplps","image/naplps"],["nbp","application/vnd.wolfram.player"],["nc","application/x-netcdf"],["ncm","application/vnd.nokia.configuration-message"],["ncx","application/x-dtbncx+xml"],["ngdat","application/vnd.nokia.n-gage.data"],["nif","image/x-niff"],["niff","image/x-niff"],["nix","application/x-mix-transfer"],["nlu","application/vnd.neurolanguage.nlu"],["nml","application/vnd.enliven"],["nnd","application/vnd.noblenet-directory"],["nns","application/vnd.noblenet-sealer"],["nnw","application/vnd.noblenet-web"],["npx","image/vnd.net-fpx"],["nsc","application/x-conference"],["nsf","application/vnd.lotus-notes"],["nvd","application/x-navidoc"],["nws","message/rfc822"],["o","application/octet-stream"],["oa2","application/vnd.fujitsu.oasys2"],["oa3","application/vnd.fujitsu.oasys3"],["oas","application/vnd.fujitsu.oasys"],["obd","application/x-msbinder"],["oda","application/oda"],["odb","application/vnd.oasis.opendocument.database"],["odc","application/vnd.oasis.opendocument.chart"],["odf","application/vnd.oasis.opendocument.formula"],["odft","application/vnd.oasis.opendocument.formula-template"],["odg","application/vnd.oasis.opendocument.graphics"],["odi","application/vnd.oasis.opendocument.image"],["odm","application/vnd.oasis.opendocument.text-master"],["odp","application/vnd.oasis.opendocument.presentation"],["ods","application/vnd.oasis.opendocument.spreadsheet"],["odt","application/vnd.oasis.opendocument.text"],["oga","audio/ogg"],["ogv","video/ogg"],["ogx","application/ogg"],["omc","application/x-omc"],["omcd","application/x-omcdatamaker"],["omcr","application/x-omcregerator"],["onetoc","application/onenote"],["opf","application/oebps-package+xml"],["org","application/vnd.lotus-organizer"],["osf","application/vnd.yamaha.openscoreformat"],["osfpvg","application/vnd.yamaha.openscoreformat.osfpvg+xml"],["otc","application/vnd.oasis.opendocument.chart-template"],["otf","application/x-font-otf"],["otg","application/vnd.oasis.opendocument.graphics-template"],["oth","application/vnd.oasis.opendocument.text-web"],["oti","application/vnd.oasis.opendocument.image-template"],["otp","application/vnd.oasis.opendocument.presentation-template"],["ots","application/vnd.oasis.opendocument.spreadsheet-template"],["ott","application/vnd.oasis.opendocument.text-template"],["oxt","application/vnd.openofficeorg.extension"],["p","text/x-pascal"],["p10",["application/pkcs10","application/x-pkcs10"]],["p12",["application/pkcs-12","application/x-pkcs12"]],["p7a","application/x-pkcs7-signature"],["p7b","application/x-pkcs7-certificates"],["p7c",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7m",["application/pkcs7-mime","application/x-pkcs7-mime"]],["p7r","application/x-pkcs7-certreqresp"],["p7s",["application/pkcs7-signature","application/x-pkcs7-signature"]],["p8","application/pkcs8"],["par","text/plain-bas"],["part","application/pro_eng"],["pas","text/pascal"],["paw","application/vnd.pawaafile"],["pbd","application/vnd.powerbuilder6"],["pbm","image/x-portable-bitmap"],["pcf","application/x-font-pcf"],["pcl",["application/vnd.hp-pcl","application/x-pcl"]],["pclxl","application/vnd.hp-pclxl"],["pct","image/x-pict"],["pcurl","application/vnd.curl.pcurl"],["pcx","image/x-pcx"],["pdb",["application/vnd.palm","chemical/x-pdb"]],["pdf","application/pdf"],["pfa","application/x-font-type1"],["pfr","application/font-tdpfr"],["pfunk",["audio/make","audio/make.my.funk"]],["pfx","application/x-pkcs12"],["pgm",["image/x-portable-graymap","image/x-portable-greymap"]],["pgn","application/x-chess-pgn"],["pgp","application/pgp-signature"],["pic",["image/pict","image/x-pict"]],["pict","image/pict"],["pkg","application/x-newton-compatible-pkg"],["pki","application/pkixcmp"],["pkipath","application/pkix-pkipath"],["pko",["application/ynd.ms-pkipko","application/vnd.ms-pki.pko"]],["pl",["text/plain","text/x-script.perl"]],["plb","application/vnd.3gpp.pic-bw-large"],["plc","application/vnd.mobius.plc"],["plf","application/vnd.pocketlearn"],["pls","application/pls+xml"],["plx","application/x-pixclscript"],["pm",["text/x-script.perl-module","image/x-xpixmap"]],["pm4","application/x-pagemaker"],["pm5","application/x-pagemaker"],["pma","application/x-perfmon"],["pmc","application/x-perfmon"],["pml",["application/vnd.ctc-posml","application/x-perfmon"]],["pmr","application/x-perfmon"],["pmw","application/x-perfmon"],["png","image/png"],["pnm",["application/x-portable-anymap","image/x-portable-anymap"]],["portpkg","application/vnd.macports.portpkg"],["pot",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["potm","application/vnd.ms-powerpoint.template.macroenabled.12"],["potx","application/vnd.openxmlformats-officedocument.presentationml.template"],["pov","model/x-pov"],["ppa","application/vnd.ms-powerpoint"],["ppam","application/vnd.ms-powerpoint.addin.macroenabled.12"],["ppd","application/vnd.cups-ppd"],["ppm","image/x-portable-pixmap"],["pps",["application/vnd.ms-powerpoint","application/mspowerpoint"]],["ppsm","application/vnd.ms-powerpoint.slideshow.macroenabled.12"],["ppsx","application/vnd.openxmlformats-officedocument.presentationml.slideshow"],["ppt",["application/vnd.ms-powerpoint","application/mspowerpoint","application/powerpoint","application/x-mspowerpoint"]],["pptm","application/vnd.ms-powerpoint.presentation.macroenabled.12"],["pptx","application/vnd.openxmlformats-officedocument.presentationml.presentation"],["ppz","application/mspowerpoint"],["prc","application/x-mobipocket-ebook"],["pre",["application/vnd.lotus-freelance","application/x-freelance"]],["prf","application/pics-rules"],["prt","application/pro_eng"],["ps","application/postscript"],["psb","application/vnd.3gpp.pic-bw-small"],["psd",["application/octet-stream","image/vnd.adobe.photoshop"]],["psf","application/x-font-linux-psf"],["pskcxml","application/pskc+xml"],["ptid","application/vnd.pvi.ptid1"],["pub","application/x-mspublisher"],["pvb","application/vnd.3gpp.pic-bw-var"],["pvu","paleovu/x-pv"],["pwn","application/vnd.3m.post-it-notes"],["pwz","application/vnd.ms-powerpoint"],["py","text/x-script.phyton"],["pya","audio/vnd.ms-playready.media.pya"],["pyc","applicaiton/x-bytecode.python"],["pyv","video/vnd.ms-playready.media.pyv"],["qam","application/vnd.epson.quickanime"],["qbo","application/vnd.intu.qbo"],["qcp","audio/vnd.qcelp"],["qd3","x-world/x-3dmf"],["qd3d","x-world/x-3dmf"],["qfx","application/vnd.intu.qfx"],["qif","image/x-quicktime"],["qps","application/vnd.publishare-delta-tree"],["qt","video/quicktime"],["qtc","video/x-qtc"],["qti","image/x-quicktime"],["qtif","image/x-quicktime"],["qxd","application/vnd.quark.quarkxpress"],["ra",["audio/x-realaudio","audio/x-pn-realaudio","audio/x-pn-realaudio-plugin"]],["ram","audio/x-pn-realaudio"],["rar","application/x-rar-compressed"],["ras",["image/cmu-raster","application/x-cmu-raster","image/x-cmu-raster"]],["rast","image/cmu-raster"],["rcprofile","application/vnd.ipunplugged.rcprofile"],["rdf","application/rdf+xml"],["rdz","application/vnd.data-vision.rdz"],["rep","application/vnd.businessobjects"],["res","application/x-dtbresource+xml"],["rexx","text/x-script.rexx"],["rf","image/vnd.rn-realflash"],["rgb","image/x-rgb"],["rif","application/reginfo+xml"],["rip","audio/vnd.rip"],["rl","application/resource-lists+xml"],["rlc","image/vnd.fujixerox.edmics-rlc"],["rld","application/resource-lists-diff+xml"],["rm",["application/vnd.rn-realmedia","audio/x-pn-realaudio"]],["rmi","audio/mid"],["rmm","audio/x-pn-realaudio"],["rmp",["audio/x-pn-realaudio-plugin","audio/x-pn-realaudio"]],["rms","application/vnd.jcp.javame.midlet-rms"],["rnc","application/relax-ng-compact-syntax"],["rng",["application/ringing-tones","application/vnd.nokia.ringing-tone"]],["rnx","application/vnd.rn-realplayer"],["roff","application/x-troff"],["rp","image/vnd.rn-realpix"],["rp9","application/vnd.cloanto.rp9"],["rpm","audio/x-pn-realaudio-plugin"],["rpss","application/vnd.nokia.radio-presets"],["rpst","application/vnd.nokia.radio-preset"],["rq","application/sparql-query"],["rs","application/rls-services+xml"],["rsd","application/rsd+xml"],["rt",["text/richtext","text/vnd.rn-realtext"]],["rtf",["application/rtf","text/richtext","application/x-rtf"]],["rtx",["text/richtext","application/rtf"]],["rv","video/vnd.rn-realvideo"],["s","text/x-asm"],["s3m","audio/s3m"],["saf","application/vnd.yamaha.smaf-audio"],["saveme","application/octet-stream"],["sbk","application/x-tbook"],["sbml","application/sbml+xml"],["sc","application/vnd.ibm.secure-container"],["scd","application/x-msschedule"],["scm",["application/vnd.lotus-screencam","video/x-scm","text/x-script.guile","application/x-lotusscreencam","text/x-script.scheme"]],["scq","application/scvp-cv-request"],["scs","application/scvp-cv-response"],["sct","text/scriptlet"],["scurl","text/vnd.curl.scurl"],["sda","application/vnd.stardivision.draw"],["sdc","application/vnd.stardivision.calc"],["sdd","application/vnd.stardivision.impress"],["sdkm","application/vnd.solent.sdkm+xml"],["sdml","text/plain"],["sdp",["application/sdp","application/x-sdp"]],["sdr","application/sounder"],["sdw","application/vnd.stardivision.writer"],["sea",["application/sea","application/x-sea"]],["see","application/vnd.seemail"],["seed","application/vnd.fdsn.seed"],["sema","application/vnd.sema"],["semd","application/vnd.semd"],["semf","application/vnd.semf"],["ser","application/java-serialized-object"],["set","application/set"],["setpay","application/set-payment-initiation"],["setreg","application/set-registration-initiation"],["sfd-hdstx","application/vnd.hydrostatix.sof-data"],["sfs","application/vnd.spotfire.sfs"],["sgl","application/vnd.stardivision.writer-global"],["sgm",["text/sgml","text/x-sgml"]],["sgml",["text/sgml","text/x-sgml"]],["sh",["application/x-shar","application/x-bsh","application/x-sh","text/x-script.sh"]],["shar",["application/x-bsh","application/x-shar"]],["shf","application/shf+xml"],["shtml",["text/html","text/x-server-parsed-html"]],["sid","audio/x-psid"],["sis","application/vnd.symbian.install"],["sit",["application/x-stuffit","application/x-sit"]],["sitx","application/x-stuffitx"],["skd","application/x-koan"],["skm","application/x-koan"],["skp",["application/vnd.koan","application/x-koan"]],["skt","application/x-koan"],["sl","application/x-seelogo"],["sldm","application/vnd.ms-powerpoint.slide.macroenabled.12"],["sldx","application/vnd.openxmlformats-officedocument.presentationml.slide"],["slt","application/vnd.epson.salt"],["sm","application/vnd.stepmania.stepchart"],["smf","application/vnd.stardivision.math"],["smi",["application/smil","application/smil+xml"]],["smil","application/smil"],["snd",["audio/basic","audio/x-adpcm"]],["snf","application/x-font-snf"],["sol","application/solids"],["spc",["text/x-speech","application/x-pkcs7-certificates"]],["spf","application/vnd.yamaha.smaf-phrase"],["spl",["application/futuresplash","application/x-futuresplash"]],["spot","text/vnd.in3d.spot"],["spp","application/scvp-vp-response"],["spq","application/scvp-vp-request"],["spr","application/x-sprite"],["sprite","application/x-sprite"],["src","application/x-wais-source"],["sru","application/sru+xml"],["srx","application/sparql-results+xml"],["sse","application/vnd.kodak-descriptor"],["ssf","application/vnd.epson.ssf"],["ssi","text/x-server-parsed-html"],["ssm","application/streamingmedia"],["ssml","application/ssml+xml"],["sst",["application/vnd.ms-pkicertstore","application/vnd.ms-pki.certstore"]],["st","application/vnd.sailingtracker.track"],["stc","application/vnd.sun.xml.calc.template"],["std","application/vnd.sun.xml.draw.template"],["step","application/step"],["stf","application/vnd.wt.stf"],["sti","application/vnd.sun.xml.impress.template"],["stk","application/hyperstudio"],["stl",["application/vnd.ms-pkistl","application/sla","application/vnd.ms-pki.stl","application/x-navistyle"]],["stm","text/html"],["stp","application/step"],["str","application/vnd.pg.format"],["stw","application/vnd.sun.xml.writer.template"],["sub","image/vnd.dvb.subtitle"],["sus","application/vnd.sus-calendar"],["sv4cpio","application/x-sv4cpio"],["sv4crc","application/x-sv4crc"],["svc","application/vnd.dvb.service"],["svd","application/vnd.svd"],["svf",["image/vnd.dwg","image/x-dwg"]],["svg","image/svg+xml"],["svr",["x-world/x-svr","application/x-world"]],["swf","application/x-shockwave-flash"],["swi","application/vnd.aristanetworks.swi"],["sxc","application/vnd.sun.xml.calc"],["sxd","application/vnd.sun.xml.draw"],["sxg","application/vnd.sun.xml.writer.global"],["sxi","application/vnd.sun.xml.impress"],["sxm","application/vnd.sun.xml.math"],["sxw","application/vnd.sun.xml.writer"],["t",["text/troff","application/x-troff"]],["talk","text/x-speech"],["tao","application/vnd.tao.intent-module-archive"],["tar","application/x-tar"],["tbk",["application/toolbook","application/x-tbook"]],["tcap","application/vnd.3gpp2.tcap"],["tcl",["text/x-script.tcl","application/x-tcl"]],["tcsh","text/x-script.tcsh"],["teacher","application/vnd.smart.teacher"],["tei","application/tei+xml"],["tex","application/x-tex"],["texi","application/x-texinfo"],["texinfo","application/x-texinfo"],["text",["application/plain","text/plain"]],["tfi","application/thraud+xml"],["tfm","application/x-tex-tfm"],["tgz",["application/gnutar","application/x-compressed"]],["thmx","application/vnd.ms-officetheme"],["tif",["image/tiff","image/x-tiff"]],["tiff",["image/tiff","image/x-tiff"]],["tmo","application/vnd.tmobile-livetv"],["torrent","application/x-bittorrent"],["tpl","application/vnd.groove-tool-template"],["tpt","application/vnd.trid.tpt"],["tr","application/x-troff"],["tra","application/vnd.trueapp"],["trm","application/x-msterminal"],["tsd","application/timestamped-data"],["tsi","audio/tsp-audio"],["tsp",["application/dsptype","audio/tsplayer"]],["tsv","text/tab-separated-values"],["ttf","application/x-font-ttf"],["ttl","text/turtle"],["turbot","image/florian"],["twd","application/vnd.simtech-mindmapper"],["txd","application/vnd.genomatix.tuxedo"],["txf","application/vnd.mobius.txf"],["txt","text/plain"],["ufd","application/vnd.ufdl"],["uil","text/x-uil"],["uls","text/iuls"],["umj","application/vnd.umajin"],["uni","text/uri-list"],["unis","text/uri-list"],["unityweb","application/vnd.unity"],["unv","application/i-deas"],["uoml","application/vnd.uoml+xml"],["uri","text/uri-list"],["uris","text/uri-list"],["ustar",["application/x-ustar","multipart/x-ustar"]],["utz","application/vnd.uiq.theme"],["uu",["application/octet-stream","text/x-uuencode"]],["uue","text/x-uuencode"],["uva","audio/vnd.dece.audio"],["uvh","video/vnd.dece.hd"],["uvi","image/vnd.dece.graphic"],["uvm","video/vnd.dece.mobile"],["uvp","video/vnd.dece.pd"],["uvs","video/vnd.dece.sd"],["uvu","video/vnd.uvvu.mp4"],["uvv","video/vnd.dece.video"],["vcd","application/x-cdlink"],["vcf","text/x-vcard"],["vcg","application/vnd.groove-vcard"],["vcs","text/x-vcalendar"],["vcx","application/vnd.vcx"],["vda","application/vda"],["vdo","video/vdo"],["vew","application/groupwise"],["vis","application/vnd.visionary"],["viv",["video/vivo","video/vnd.vivo"]],["vivo",["video/vivo","video/vnd.vivo"]],["vmd","application/vocaltec-media-desc"],["vmf","application/vocaltec-media-file"],["voc",["audio/voc","audio/x-voc"]],["vos","video/vosaic"],["vox","audio/voxware"],["vqe","audio/x-twinvq-plugin"],["vqf","audio/x-twinvq"],["vql","audio/x-twinvq-plugin"],["vrml",["model/vrml","x-world/x-vrml","application/x-vrml"]],["vrt","x-world/x-vrt"],["vsd",["application/vnd.visio","application/x-visio"]],["vsf","application/vnd.vsf"],["vst","application/x-visio"],["vsw","application/x-visio"],["vtu","model/vnd.vtu"],["vxml","application/voicexml+xml"],["w60","application/wordperfect6.0"],["w61","application/wordperfect6.1"],["w6w","application/msword"],["wad","application/x-doom"],["wav",["audio/wav","audio/x-wav"]],["wax","audio/x-ms-wax"],["wb1","application/x-qpro"],["wbmp","image/vnd.wap.wbmp"],["wbs","application/vnd.criticaltools.wbs+xml"],["wbxml","application/vnd.wap.wbxml"],["wcm","application/vnd.ms-works"],["wdb","application/vnd.ms-works"],["web","application/vnd.xara"],["weba","audio/webm"],["webm","video/webm"],["webp","image/webp"],["wg","application/vnd.pmi.widget"],["wgt","application/widget"],["wiz","application/msword"],["wk1","application/x-123"],["wks","application/vnd.ms-works"],["wm","video/x-ms-wm"],["wma","audio/x-ms-wma"],["wmd","application/x-ms-wmd"],["wmf",["windows/metafile","application/x-msmetafile"]],["wml","text/vnd.wap.wml"],["wmlc","application/vnd.wap.wmlc"],["wmls","text/vnd.wap.wmlscript"],["wmlsc","application/vnd.wap.wmlscriptc"],["wmv","video/x-ms-wmv"],["wmx","video/x-ms-wmx"],["wmz","application/x-ms-wmz"],["woff","application/x-font-woff"],["word","application/msword"],["wp","application/wordperfect"],["wp5",["application/wordperfect","application/wordperfect6.0"]],["wp6","application/wordperfect"],["wpd",["application/wordperfect","application/vnd.wordperfect","application/x-wpwin"]],["wpl","application/vnd.ms-wpl"],["wps","application/vnd.ms-works"],["wq1","application/x-lotus"],["wqd","application/vnd.wqd"],["wri",["application/mswrite","application/x-wri","application/x-mswrite"]],["wrl",["model/vrml","x-world/x-vrml","application/x-world"]],["wrz",["model/vrml","x-world/x-vrml"]],["wsc","text/scriplet"],["wsdl","application/wsdl+xml"],["wspolicy","application/wspolicy+xml"],["wsrc","application/x-wais-source"],["wtb","application/vnd.webturbo"],["wtk","application/x-wintalk"],["wvx","video/x-ms-wvx"],["x-png","image/png"],["x3d","application/vnd.hzn-3d-crossword"],["xaf","x-world/x-vrml"],["xap","application/x-silverlight-app"],["xar","application/vnd.xara"],["xbap","application/x-ms-xbap"],["xbd","application/vnd.fujixerox.docuworks.binder"],["xbm",["image/xbm","image/x-xbm","image/x-xbitmap"]],["xdf","application/xcap-diff+xml"],["xdm","application/vnd.syncml.dm+xml"],["xdp","application/vnd.adobe.xdp+xml"],["xdr","video/x-amt-demorun"],["xdssc","application/dssc+xml"],["xdw","application/vnd.fujixerox.docuworks"],["xenc","application/xenc+xml"],["xer","application/patch-ops-error+xml"],["xfdf","application/vnd.adobe.xfdf"],["xfdl","application/vnd.xfdl"],["xgz","xgl/drawing"],["xhtml","application/xhtml+xml"],["xif","image/vnd.xiff"],["xl","application/excel"],["xla",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlam","application/vnd.ms-excel.addin.macroenabled.12"],["xlb",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlc",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xld",["application/excel","application/x-excel"]],["xlk",["application/excel","application/x-excel"]],["xll",["application/excel","application/vnd.ms-excel","application/x-excel"]],["xlm",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xls",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xlsb","application/vnd.ms-excel.sheet.binary.macroenabled.12"],["xlsm","application/vnd.ms-excel.sheet.macroenabled.12"],["xlsx","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"],["xlt",["application/vnd.ms-excel","application/excel","application/x-excel"]],["xltm","application/vnd.ms-excel.template.macroenabled.12"],["xltx","application/vnd.openxmlformats-officedocument.spreadsheetml.template"],["xlv",["application/excel","application/x-excel"]],["xlw",["application/vnd.ms-excel","application/excel","application/x-msexcel","application/x-excel"]],["xm","audio/xm"],["xml",["application/xml","text/xml","application/atom+xml","application/rss+xml"]],["xmz","xgl/movie"],["xo","application/vnd.olpc-sugar"],["xof","x-world/x-vrml"],["xop","application/xop+xml"],["xpi","application/x-xpinstall"],["xpix","application/x-vnd.ls-xpix"],["xpm",["image/xpm","image/x-xpixmap"]],["xpr","application/vnd.is-xpr"],["xps","application/vnd.ms-xpsdocument"],["xpw","application/vnd.intercon.formnet"],["xslt","application/xslt+xml"],["xsm","application/vnd.syncml+xml"],["xspf","application/xspf+xml"],["xsr","video/x-amt-showrun"],["xul","application/vnd.mozilla.xul+xml"],["xwd",["image/x-xwd","image/x-xwindowdump"]],["xyz",["chemical/x-xyz","chemical/x-pdb"]],["yang","application/yang"],["yin","application/yin+xml"],["z",["application/x-compressed","application/x-compress"]],["zaz","application/vnd.zzazz.deck+xml"],["zip",["application/zip","multipart/x-zip","application/x-zip-compressed","application/x-compressed"]],["zir","application/vnd.zul"],["zmm","application/vnd.handheld-entertainment+xml"],["zoo","application/octet-stream"],["zsh","text/x-script.zsh"]]);var Q={detectMimeType(e){if(!e)return"application/octet-stream";let t=m.parse(e),i=(t.ext.substr(1)||t.name||"").split("?").shift().trim().toLowerCase(),a="application/octet-stream";return $.has(i)&&(a=$.get(i)),Array.isArray(a)?a[0]:a},detectExtension(e){if(!e)return"bin";let t=(e||"").toLowerCase().trim().split("/"),i=t.shift().trim(),a=t.join("/").trim();if(G.has(i+"/"+a)){let e=G.get(i+"/"+a);return Array.isArray(e)?e[0]:e}switch(i){case"text":return"txt";default:return"bin"}}};const K=p.Transform;function V(e){return"string"==typeof e&&(e=Buffer.from(e,"utf-8")),e.toString("base64")}function X(e,t){if(t=t||76,(e=(e||"").toString()).length<=t)return e;let i=[],a=0,s=1024*t;for(;a<e.length;){let n=e.substr(a,s).replace(new RegExp(".{"+t+"}","g"),"$&\r\n").trim();i.push(n),a+=s}return i.join("\r\n").trim()}var J={encode:V,wrap:X,Encoder:class extends K{constructor(e){super(),this.options=e||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this._remainingBytes=!1,this.inputBytes=0,this.outputBytes=0}_transform(e,t,i){if("buffer"!==t&&(e=Buffer.from(e,t)),!e||!e.length)return setImmediate(i);this.inputBytes+=e.length,this._remainingBytes&&this._remainingBytes.length&&(e=Buffer.concat([this._remainingBytes,e],this._remainingBytes.length+e.length),this._remainingBytes=!1),e.length%3?(this._remainingBytes=e.slice(e.length-e.length%3),e=e.slice(0,e.length-e.length%3)):this._remainingBytes=!1;let a=this._curLine+V(e);if(this.options.lineLength){a=X(a,this.options.lineLength);let e=a.lastIndexOf("\n");e<0?(this._curLine=a,a=""):e===a.length-1?this._curLine="":(this._curLine=a.substr(e+1),a=a.substr(0,e+1))}a&&(this.outputBytes+=a.length,this.push(Buffer.from(a,"ascii"))),setImmediate(i)}_flush(e){this._remainingBytes&&this._remainingBytes.length&&(this._curLine+=V(this._remainingBytes)),this._curLine&&(this._curLine=X(this._curLine,this.options.lineLength),this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii"),this._curLine=""),e()}}};const W=p.Transform;function Y(e){"string"==typeof e&&(e=Buffer.from(e,"utf-8"));let t,i=[[9],[10],[13],[32,60],[62,126]],a="";for(let s=0,n=e.length;s<n;s++)t=e[s],ee(t,i)&&(32!==t&&9!==t||s!==n-1&&10!==e[s+1]&&13!==e[s+1])?a+=String.fromCharCode(t):a+="="+(t<16?"0":"")+t.toString(16).toUpperCase();return a}function Z(e,t){if(t=t||76,(e=(e||"").toString()).length<=t)return e;let i,a,s,n=0,o=e.length,r=Math.floor(t/3),p="";for(;n<o;)if(s=e.substr(n,t),i=s.match(/\r\n/))s=s.substr(0,i.index+i[0].length),p+=s,n+=s.length;else if("\n"!==s.substr(-1))if(i=s.substr(-r).match(/\n.*?$/))s=s.substr(0,s.length-(i[0].length-1)),p+=s,n+=s.length;else{if(s.length>t-r&&(i=s.substr(-r).match(/[ \t.,!?][^ \t.,!?]*$/)))s=s.substr(0,s.length-(i[0].length-1));else if(s.match(/[=][\da-f]{0,2}$/i))for((i=s.match(/[=][\da-f]{0,1}$/i))&&(s=s.substr(0,s.length-i[0].length));s.length>3&&s.length<o-n&&!s.match(/^(?:=[\da-f]{2}){1,4}$/i)&&(i=s.match(/[=][\da-f]{2}$/gi))&&(a=parseInt(i[0].substr(1,2),16),!(a<128))&&(s=s.substr(0,s.length-3),!(a>=192)););n+s.length<o&&"\n"!==s.substr(-1)?(s.length===t&&s.match(/[=][\da-f]{2}$/i)?s=s.substr(0,s.length-3):s.length===t&&(s=s.substr(0,s.length-1)),n+=s.length,s+="=\r\n"):n+=s.length,p+=s}else p+=s,n+=s.length;return p}function ee(e,t){for(let i=t.length-1;i>=0;i--)if(t[i].length){if(1===t[i].length&&e===t[i][0])return!0;if(2===t[i].length&&e>=t[i][0]&&e<=t[i][1])return!0}return!1}var te={encode:Y,wrap:Z,Encoder:class extends W{constructor(e){super(),this.options=e||{},!1!==this.options.lineLength&&(this.options.lineLength=this.options.lineLength||76),this._curLine="",this.inputBytes=0,this.outputBytes=0}_transform(e,t,i){let a;if("buffer"!==t&&(e=Buffer.from(e,t)),!e||!e.length)return i();this.inputBytes+=e.length,this.options.lineLength?(a=this._curLine+Y(e),a=Z(a,this.options.lineLength),a=a.replace(/(^|\n)([^\n]*)$/,(e,t,i)=>(this._curLine=i,t)),a&&(this.outputBytes+=a.length,this.push(a))):(a=Y(e),this.outputBytes+=a.length,this.push(a,"ascii")),i()}_flush(e){this._curLine&&(this.outputBytes+=this._curLine.length,this.push(this._curLine,"ascii")),e()}}},ie={isPlainText:(e,t)=>"string"==typeof e&&!(t?/[\x00-\x08\x0b\x0c\x0e-\x1f"\u0080-\uFFFF]/:/[\x00-\x08\x0b\x0c\x0e-\x1f\u0080-\uFFFF]/).test(e),hasLongerLines:(e,t)=>e.length>131072||new RegExp("^.{"+(t+1)+",}","m").test(e),encodeWord(e,t,i){let a;t=(t||"Q").toString().toUpperCase().trim().charAt(0);if((i=i||0)&&i>7+"UTF-8".length&&(i-=7+"UTF-8".length),"Q"===t?a=te.encode(e).replace(/[^a-z0-9!*+\-/=]/gi,e=>{let t=e.charCodeAt(0).toString(16).toUpperCase();return" "===e?"_":"="+(1===t.length?"0"+t:t)}):"B"===t&&(a="string"==typeof e?e:J.encode(e),i=i?Math.max(3,(i-i%4)/4*3):0),i&&("B"!==t?a:J.encode(e)).length>i)if("Q"===t)a=this.splitMimeEncodedString(a,i).join("?= =?UTF-8?"+t+"?");else{let e=[],s="";for(let t=0,n=a.length;t<n;t++){let n=a.charAt(t);Buffer.byteLength(s+n)<=i||0===t?s+=n:(e.push(J.encode(s)),s=n)}s&&e.push(J.encode(s)),a=e.length>1?e.join("?= =?UTF-8?"+t+"?"):e.join("")}else"B"===t&&(a=J.encode(e));return"=?UTF-8?"+t+"?"+a+("?="===a.substr(-2)?"":"?=")},encodeWords(e,t,i,a){let s;i=i||0;let n=e.match(/(?:^|\s)([^\s]*["\u0080-\uFFFF])/);if(!n)return e;if(a)return this.encodeWord(e,t,i);let o=e.match(/(["\u0080-\uFFFF][^\s]*)[^"\u0080-\uFFFF]*$/);if(!o)return e;let r=n.index+(n[0].match(/[^\s]/)||{index:0}).index,p=o.index+(o[1]||"").length;return s=(r?e.substr(0,r):"")+this.encodeWord(e.substring(r,p),t||"Q",i)+(p<e.length?e.substr(p):""),s},buildHeaderValue(e){let t=[];return Object.keys(e.params||{}).forEach(i=>{let a=e.params[i];!this.isPlainText(a,!0)||a.length>=75?this.buildHeaderParam(i,a,50).forEach(e=>{/[\s"\\;:/=(),<>@[\]?]|^[-']|'$/.test(e.value)&&"*"!==e.key.substr(-1)?t.push(e.key+"="+JSON.stringify(e.value)):t.push(e.key+"="+e.value)}):/[\s'"\\;:/=(),<>@[\]?]|^-/.test(a)?t.push(i+"="+JSON.stringify(a)):t.push(i+"="+a)}),e.value+(t.length?"; "+t.join("; "):"")},buildHeaderParam(e,t,i){let a,s,n,o,r,p,c=[],l="string"==typeof t?t:(t||"").toString(),d=0;if(i=i||50,this.isPlainText(t,!0)){if(l.length<=i)return[{key:e,value:l}];l=l.replace(new RegExp(".{"+i+"}","g"),e=>(c.push({line:e}),"")),l&&c.push({line:l})}else{if(/[\uD800-\uDBFF]/.test(l)){for(a=[],r=0,p=l.length;r<p;r++)s=l.charAt(r),n=s.charCodeAt(0),n>=55296&&n<=56319&&r<p-1?(s+=l.charAt(r+1),a.push(s),r++):a.push(s);l=a}o="utf-8''";let e=!0;for(d=0,r=0,p=l.length;r<p;r++){if(s=l[r],e)s=this.safeEncodeURIComponent(s);else if(s=" "===s?s:this.safeEncodeURIComponent(s),s!==l[r]){if(!((this.safeEncodeURIComponent(o)+s).length>=i)){e=!0,r=d,o="";continue}c.push({line:o,encoded:e}),o="",d=r-1}(o+s).length>=i?(c.push({line:o,encoded:e}),o=s=" "===l[r]?" ":this.safeEncodeURIComponent(l[r]),s===l[r]?(e=!1,d=r-1):e=!0):o+=s}o&&c.push({line:o,encoded:e})}return c.map((t,i)=>({key:e+"*"+i+(t.encoded?"*":""),value:t.line}))},parseHeaderValue(e){let t,i={value:!1,params:{}},a=!1,s="",n="value",o=!1,r=!1;for(let p=0,c=e.length;p<c;p++)if(t=e.charAt(p),"key"===n){if("="===t){a=s.trim().toLowerCase(),n="value",s="";continue}s+=t}else{if(r)s+=t;else{if("\\"===t){r=!0;continue}o&&t===o?o=!1:o||'"'!==t?o||";"!==t?s+=t:(!1===a?i.value=s.trim():i.params[a]=s.trim(),n="key",s=""):o=t}r=!1}return"value"===n?!1===a?i.value=s.trim():i.params[a]=s.trim():s.trim()&&(i.params[s.trim().toLowerCase()]=""),Object.keys(i.params).forEach(e=>{let t,a,s,n;(s=e.match(/(\*(\d+)|\*(\d+)\*|\*)$/))&&(t=e.substr(0,s.index),a=Number(s[2]||s[3])||0,i.params[t]&&"object"==typeof i.params[t]||(i.params[t]={charset:!1,values:[]}),n=i.params[e],0===a&&"*"===s[0].substr(-1)&&(s=n.match(/^([^']*)'[^']*'(.*)$/))&&(i.params[t].charset=s[1]||"iso-8859-1",n=s[2]),i.params[t].values[a]=n,delete i.params[e])}),Object.keys(i.params).forEach(e=>{let t;i.params[e]&&Array.isArray(i.params[e].values)&&(t=i.params[e].values.map(e=>e||"").join(""),i.params[e].charset?i.params[e]="=?"+i.params[e].charset+"?Q?"+t.replace(/[=?_\s]/g,e=>{let t=e.charCodeAt(0).toString(16);return" "===e?"_":"%"+(t.length<2?"0":"")+t}).replace(/%/g,"=")+"?=":i.params[e]=t)}),i},detectExtension:e=>Q.detectExtension(e),detectMimeType:e=>Q.detectMimeType(e),foldLines(e,t,i){t=t||76;let a,s,n=0,o=(e=(e||"").toString()).length,r="";for(;n<o;){if(a=e.substr(n,t),a.length<t){r+=a;break}(s=a.match(/^[^\n\r]*(\r?\n|\r)/))?(a=s[0],r+=a,n+=a.length):((s=a.match(/(\s+)[^\s]*$/))&&s[0].length-(i?(s[1]||"").length:0)<a.length?a=a.substr(0,a.length-(s[0].length-(i?(s[1]||"").length:0))):(s=e.substr(n+a.length).match(/^[^\s]+(\s*)/))&&(a+=s[0].substr(0,s[0].length-(i?0:(s[1]||"").length))),r+=a,n+=a.length,n<o&&(r+="\r\n"))}return r},splitMimeEncodedString:(e,t)=>{let i,a,s,n,o=[];for(t=Math.max(t||0,12);e.length;){for(i=e.substr(0,t),(a=i.match(/[=][0-9A-F]?$/i))&&(i=i.substr(0,a.index)),n=!1;!n;)n=!0,(a=e.substr(i.length).match(/^[=]([0-9A-F]{2})/i))&&(s=parseInt(a[1],16),s<194&&s>127&&(i=i.substr(0,i.length-3),n=!1));i.length&&o.push(i),e=e.substr(i.length)}return o},encodeURICharComponent:e=>{let t="",i=e.charCodeAt(0).toString(16).toUpperCase();if(i.length%2&&(i="0"+i),i.length>2)for(let e=0,a=i.length/2;e<a;e++)t+="%"+i.substr(e,2);else t+="%"+i;return t},safeEncodeURIComponent(e){e=(e||"").toString();try{e=encodeURIComponent(e)}catch(t){return e.replace(/[^\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]+/g,"")}return e.replace(/[\x00-\x1F *'()<>@,;:\\"[\]?=\u007F-\uFFFF]/g,e=>this.encodeURICharComponent(e))}};class ae{constructor(e){this.str=(e||"").toString(),this.operatorCurrent="",this.operatorExpecting="",this.node=null,this.escaped=!1,this.list=[],this.operators={'"':'"',"(":")","<":">",",":"",":":";",";":""}}tokenize(){let e,t=[];for(let t=0,i=this.str.length;t<i;t++)e=this.str.charAt(t),this.checkChar(e);return this.list.forEach(e=>{e.value=(e.value||"").toString().trim(),e.value&&t.push(e)}),t}checkChar(e){if(this.escaped);else{if(e===this.operatorExpecting)return this.node={type:"operator",value:e},this.list.push(this.node),this.node=null,this.operatorExpecting="",void(this.escaped=!1);if(!this.operatorExpecting&&e in this.operators)return this.node={type:"operator",value:e},this.list.push(this.node),this.node=null,this.operatorExpecting=this.operators[e],void(this.escaped=!1);if(['"',"'"].includes(this.operatorExpecting)&&"\\"===e)return void(this.escaped=!0)}this.node||(this.node={type:"text",value:""},this.list.push(this.node)),"\n"===e&&(e=" "),(e.charCodeAt(0)>=33||[" ","\t"].includes(e))&&(this.node.value+=e),this.escaped=!1}}function se(e,t){t=t||{};let i=new ae(e).tokenize(),a=[],s=[],n=[];if(i.forEach(e=>{"operator"!==e.type||","!==e.value&&";"!==e.value?s.push(e):(s.length&&a.push(s),s=[])}),s.length&&a.push(s),a.forEach(e=>{(e=function(e){let t,i,a,s,n=!1,o="text",r=[],p={address:[],comment:[],group:[],text:[]};for(a=0,s=e.length;a<s;a++)if(t=e[a],"operator"===t.type)switch(t.value){case"<":o="address";break;case"(":o="comment";break;case":":o="group",n=!0;break;default:o="text"}else t.value&&("address"===o&&(t.value=t.value.replace(/^[^<]*<\s*/,"")),p[o].push(t.value));if(!p.text.length&&p.comment.length&&(p.text=p.comment,p.comment=[]),n)p.text=p.text.join(" "),r.push({name:p.text||i&&i.name,group:p.group.length?se(p.group.join(",")):[]});else{if(!p.address.length&&p.text.length){for(a=p.text.length-1;a>=0;a--)if(p.text[a].match(/^[^@\s]+@[^@\s]+$/)){p.address=p.text.splice(a,1);break}let e=function(e){return p.address.length?e:(p.address=[e.trim()]," ")};if(!p.address.length)for(a=p.text.length-1;a>=0&&(p.text[a]=p.text[a].replace(/\s*\b[^@\s]+@[^\s]+\b\s*/,e).trim(),!p.address.length);a--);}if(!p.text.length&&p.comment.length&&(p.text=p.comment,p.comment=[]),p.address.length>1&&(p.text=p.text.concat(p.address.splice(1))),p.text=p.text.join(" "),p.address=p.address.join(" "),!p.address&&n)return[];i={address:p.address||p.text||"",name:p.text||p.address||""},i.address===i.name&&((i.address||"").match(/@/)?i.name="":i.address=""),r.push(i)}return r}(e)).length&&(n=n.concat(e))}),t.flatten){let e=[],t=i=>{i.forEach(i=>{if(i.group)return t(i.group);e.push(i)})};return t(n),e}return n}var ne=se;const oe=p.Transform;var re=class extends oe{constructor(){super(),this.lastByte=!1}_transform(e,t,i){e.length&&(this.lastByte=e[e.length-1]),this.push(e),i()}_flush(e){return 10===this.lastByte?e():13===this.lastByte?(this.push(Buffer.from("\n")),e()):(this.push(Buffer.from("\r\n")),e())}};const pe=p.Transform;var ce=class extends pe{constructor(e){super(e),this.options=e||{},this.lastByte=!1}_transform(e,t,i){let a,s=0;for(let t=0,i=e.length;t<i;t++)10===e[t]&&(t&&13!==e[t-1]||!t&&13!==this.lastByte)&&(t>s&&(a=e.slice(s,t),this.push(a)),this.push(Buffer.from("\r\n")),s=t+1);s&&s<e.length?(a=e.slice(s),this.push(a)):s||this.push(e),this.lastByte=e[e.length-1],i()}};const le=p.Transform;var de=class extends le{constructor(e){super(e),this.options=e||{}}_transform(e,t,i){let a,s=0;for(let t=0,i=e.length;t<i;t++)13===e[t]&&(a=e.slice(s,t),s=t+1,this.push(a));s&&s<e.length?(a=e.slice(s),this.push(a)):s||this.push(e),i()}};const me=p.PassThrough;class he{constructor(e,t){this.nodeCounter=0,t=t||{},this.baseBoundary=t.baseBoundary||h.randomBytes(8).toString("hex"),this.boundaryPrefix=t.boundaryPrefix||"--_NmP",this.disableFileAccess=!!t.disableFileAccess,this.disableUrlAccess=!!t.disableUrlAccess,this.normalizeHeaderKey=t.normalizeHeaderKey,this.date=new Date,this.rootNode=t.rootNode||this,this.keepBcc=!!t.keepBcc,t.filename&&(this.filename=t.filename,e||(e=ie.detectMimeType(this.filename.split(".").pop()))),this.textEncoding=(t.textEncoding||"").toString().trim().charAt(0).toUpperCase(),this.parentNode=t.parentNode,this.hostname=t.hostname,this.newline=t.newline,this.childNodes=[],this._nodeId=++this.rootNode.nodeCounter,this._headers=[],this._isPlainText=!1,this._hasLongLines=!1,this._envelope=!1,this._raw=!1,this._transforms=[],this._processFuncs=[],e&&this.setHeader("Content-Type",e)}createChild(e,t){t||"object"!=typeof e||(t=e,e=void 0);let i=new he(e,t);return this.appendChild(i),i}appendChild(e){return e.rootNode!==this.rootNode&&(e.rootNode=this.rootNode,e._nodeId=++this.rootNode.nodeCounter),e.parentNode=this,this.childNodes.push(e),e}replace(e){return e===this?this:(this.parentNode.childNodes.forEach((t,i)=>{t===this&&(e.rootNode=this.rootNode,e.parentNode=this.parentNode,e._nodeId=this._nodeId,this.rootNode=this,this.parentNode=void 0,e.parentNode.childNodes[i]=e)}),e)}remove(){if(!this.parentNode)return this;for(let e=this.parentNode.childNodes.length-1;e>=0;e--)if(this.parentNode.childNodes[e]===this)return this.parentNode.childNodes.splice(e,1),this.parentNode=void 0,this.rootNode=this,this}setHeader(e,t){let i,a=!1;if(!t&&e&&"object"==typeof e)return e.key&&"value"in e?this.setHeader(e.key,e.value):Array.isArray(e)?e.forEach(e=>{this.setHeader(e.key,e.value)}):Object.keys(e).forEach(t=>{this.setHeader(t,e[t])}),this;i={key:e=this._normalizeHeaderKey(e),value:t};for(let t=0,s=this._headers.length;t<s;t++)this._headers[t].key===e&&(a?(this._headers.splice(t,1),t--,s--):(this._headers[t]=i,a=!0));return a||this._headers.push(i),this}addHeader(e,t){return!t&&e&&"object"==typeof e?(e.key&&e.value?this.addHeader(e.key,e.value):Array.isArray(e)?e.forEach(e=>{this.addHeader(e.key,e.value)}):Object.keys(e).forEach(t=>{this.addHeader(t,e[t])}),this):Array.isArray(t)?(t.forEach(t=>{this.addHeader(e,t)}),this):(this._headers.push({key:this._normalizeHeaderKey(e),value:t}),this)}getHeader(e){e=this._normalizeHeaderKey(e);for(let t=0,i=this._headers.length;t<i;t++)if(this._headers[t].key===e)return this._headers[t].value}setContent(e){return this.content=e,"function"==typeof this.content.pipe?(this._contentErrorHandler=e=>{this.content.removeListener("error",this._contentErrorHandler),this.content=e},this.content.once("error",this._contentErrorHandler)):"string"==typeof this.content&&(this._isPlainText=ie.isPlainText(this.content),this._isPlainText&&ie.hasLongerLines(this.content,76)&&(this._hasLongLines=!0)),this}build(e){let t;e||(t=new Promise((t,i)=>{e=D.callbackPromise(t,i)}));let i=this.createReadStream(),a=[],s=0,n=!1;return i.on("readable",()=>{let e;for(;null!==(e=i.read());)a.push(e),s+=e.length}),i.once("error",t=>{if(!n)return n=!0,e(t)}),i.once("end",t=>{if(!n)return n=!0,t&&t.length&&(a.push(t),s+=t.length),e(null,Buffer.concat(a,s))}),t}getTransferEncoding(){let e=!1,t=(this.getHeader("Content-Type")||"").toString().toLowerCase().trim();return this.content&&(e=(this.getHeader("Content-Transfer-Encoding")||"").toString().toLowerCase().trim(),e&&["base64","quoted-printable"].includes(e)||(/^text\//i.test(t)?e=this._isPlainText&&!this._hasLongLines?"7bit":"string"==typeof this.content||this.content instanceof Buffer?"Q"===this._getTextEncoding(this.content)?"quoted-printable":"base64":"B"===this.textEncoding?"base64":"quoted-printable":/^(multipart|message)\//i.test(t)||(e=e||"base64"))),e}buildHeaders(){let e=this.getTransferEncoding(),t=[];return e&&this.setHeader("Content-Transfer-Encoding",e),this.filename&&!this.getHeader("Content-Disposition")&&this.setHeader("Content-Disposition","attachment"),this.rootNode===this&&(this.getHeader("Date")||this.setHeader("Date",this.date.toUTCString().replace(/GMT/,"+0000")),this.messageId(),this.getHeader("MIME-Version")||this.setHeader("MIME-Version","1.0")),this._headers.forEach(e=>{let i,a,s=e.key,n=e.value,o={};if(!n||"object"!=typeof n||["From","Sender","To","Cc","Bcc","Reply-To","Date","References"].includes(s)||(Object.keys(n).forEach(e=>{"value"!==e&&(o[e]=n[e])}),n=(n.value||"").toString(),n.trim()))if(o.prepared)o.foldLines?t.push(ie.foldLines(s+": "+n)):t.push(s+": "+n);else{switch(e.key){case"Content-Disposition":i=ie.parseHeaderValue(n),this.filename&&(i.params.filename=this.filename),n=ie.buildHeaderValue(i);break;case"Content-Type":i=ie.parseHeaderValue(n),this._handleContentType(i),i.value.match(/^text\/plain\b/)&&"string"==typeof this.content&&/[\u0080-\uFFFF]/.test(this.content)&&(i.params.charset="utf-8"),n=ie.buildHeaderValue(i),this.filename&&(a=this._encodeWords(this.filename),(a!==this.filename||/[\s'"\\;:/=(),<>@[\]?]|^-/.test(a))&&(a='"'+a+'"'),n+="; name="+a);break;case"Bcc":if(!this.keepBcc)return}if(n=this._encodeHeaderValue(s,n),(n||"").toString().trim()){if("function"==typeof this.normalizeHeaderKey){let e=this.normalizeHeaderKey(s,n);e&&"string"==typeof e&&e.length&&(s=e)}t.push(ie.foldLines(s+": "+n,76))}}}),t.join("\r\n")}createReadStream(e){let t,i=new me(e=e||{}),a=i;this.stream(i,e,e=>{e?a.emit("error",e):i.end()});for(let e=0,i=this._transforms.length;e<i;e++)t="function"==typeof this._transforms[e]?this._transforms[e]():this._transforms[e],a.once("error",e=>{t.emit("error",e)}),a=a.pipe(t);t=new re,a.once("error",e=>{t.emit("error",e)}),a=a.pipe(t);for(let e=0,i=this._processFuncs.length;e<i;e++)t=this._processFuncs[e],a=t(a);if(this.newline){const e=["win","windows","dos","\r\n"].includes(this.newline.toString().toLowerCase())?new ce:new de,t=a.pipe(e);return a.on("error",e=>t.emit("error",e)),t}return a}transform(e){this._transforms.push(e)}processFunc(e){this._processFuncs.push(e)}stream(e,t,i){let a,s,n=this.getTransferEncoding(),o=!1,r=e=>{o||(o=!0,i(e))},p=()=>{let i=0,a=()=>{if(i>=this.childNodes.length)return e.write("\r\n--"+this.boundary+"--\r\n"),r();let s=this.childNodes[i++];e.write((i>1?"\r\n":"")+"--"+this.boundary+"\r\n"),s.stream(e,t,e=>{if(e)return r(e);setImmediate(a)})};if(!this.multipart)return r();setImmediate(a)},c=()=>{if(!this.content)return setImmediate(p);{if("[object Error]"===Object.prototype.toString.call(this.content))return r(this.content);"function"==typeof this.content.pipe&&(this.content.removeListener("error",this._contentErrorHandler),this._contentErrorHandler=e=>r(e),this.content.once("error",this._contentErrorHandler));let i=()=>{["quoted-printable","base64"].includes(n)?(a=new("base64"===n?J:te).Encoder(t),a.pipe(e,{end:!1}),a.once("end",p),a.once("error",e=>r(e)),s=this._getStream(this.content),s.pipe(a)):(s=this._getStream(this.content),s.pipe(e,{end:!1}),s.once("end",p)),s.once("error",e=>r(e))};if(this.content._resolve){let e=[],t=0,a=!1,s=this._getStream(this.content);s.on("error",e=>{a||(a=!0,r(e))}),s.on("readable",()=>{let i;for(;null!==(i=s.read());)e.push(i),t+=i.length}),s.on("end",()=>{a||(a=!0,this.content._resolve=!1,this.content._resolvedValue=Buffer.concat(e,t),setImmediate(i))})}else setImmediate(i)}};this._raw?setImmediate(()=>{if("[object Error]"===Object.prototype.toString.call(this._raw))return r(this._raw);"function"==typeof this._raw.pipe&&this._raw.removeListener("error",this._contentErrorHandler);let t=this._getStream(this._raw);t.pipe(e,{end:!1}),t.on("error",t=>e.emit("error",t)),t.on("end",p)}):(e.write(this.buildHeaders()+"\r\n\r\n"),setImmediate(c))}setEnvelope(e){let t;this._envelope={from:!1,to:[]},e.from&&(t=[],this._convertAddresses(this._parseAddresses(e.from),t),t=t.filter(e=>e&&e.address),t.length&&t[0]&&(this._envelope.from=t[0].address)),["to","cc","bcc"].forEach(t=>{e[t]&&this._convertAddresses(this._parseAddresses(e[t]),this._envelope.to)}),this._envelope.to=this._envelope.to.map(e=>e.address).filter(e=>e);let i=["to","cc","bcc","from"];return Object.keys(e).forEach(t=>{i.includes(t)||(this._envelope[t]=e[t])}),this}getAddresses(){let e={};return this._headers.forEach(t=>{let i=t.key.toLowerCase();["from","sender","reply-to","to","cc","bcc"].includes(i)&&(Array.isArray(e[i])||(e[i]=[]),this._convertAddresses(this._parseAddresses(t.value),e[i]))}),e}getEnvelope(){if(this._envelope)return this._envelope;let e={from:!1,to:[]};return this._headers.forEach(t=>{let i=[];"From"===t.key||!e.from&&["Reply-To","Sender"].includes(t.key)?(this._convertAddresses(this._parseAddresses(t.value),i),i.length&&i[0]&&(e.from=i[0].address)):["To","Cc","Bcc"].includes(t.key)&&this._convertAddresses(this._parseAddresses(t.value),e.to)}),e.to=e.to.map(e=>e.address),e}messageId(){let e=this.getHeader("Message-ID");return e||(e=this._generateMessageId(),this.setHeader("Message-ID",e)),e}setRaw(e){return this._raw=e,this._raw&&"function"==typeof this._raw.pipe&&(this._contentErrorHandler=e=>{this._raw.removeListener("error",this._contentErrorHandler),this._raw=e},this._raw.once("error",this._contentErrorHandler)),this}_getStream(e){let t;return e._resolvedValue?(t=new me,setImmediate(()=>t.end(e._resolvedValue)),t):"function"==typeof e.pipe?e:e&&"string"==typeof e.path&&!e.href?this.disableFileAccess?(t=new me,setImmediate(()=>t.emit("error",new Error("File access rejected for "+e.path))),t):s.createReadStream(e.path):e&&"string"==typeof e.href?this.disableUrlAccess?(t=new me,setImmediate(()=>t.emit("error",new Error("Url access rejected for "+e.href))),t):R(e.href,{headers:e.httpHeaders}):(t=new me,setImmediate(()=>t.end(e||"")),t)}_parseAddresses(e){return[].concat.apply([],[].concat(e).map(e=>e&&e.address?(e.address=this._normalizeAddress(e.address),e.name=e.name||"",[e]):ne(e)))}_normalizeHeaderKey(e){return e=(e||"").toString().replace(/\r?\n|\r/g," ").trim().toLowerCase().replace(/^X-SMTPAPI$|^(MIME|DKIM|ARC|BIMI)\b|^[a-z]|-(SPF|FBL|ID|MD5)$|-[a-z]/gi,e=>e.toUpperCase()).replace(/^Content-Features$/i,"Content-features")}_handleContentType(e){this.contentType=e.value.trim().toLowerCase(),this.multipart=!!/^multipart\//i.test(this.contentType)&&this.contentType.substr(this.contentType.indexOf("/")+1),this.multipart?this.boundary=e.params.boundary=e.params.boundary||this.boundary||this._generateBoundary():this.boundary=!1}_generateBoundary(){return this.rootNode.boundaryPrefix+"-"+this.rootNode.baseBoundary+"-Part_"+this._nodeId}_encodeHeaderValue(e,t){switch(e=this._normalizeHeaderKey(e)){case"From":case"Sender":case"To":case"Cc":case"Bcc":case"Reply-To":return this._convertAddresses(this._parseAddresses(t));case"Message-ID":case"In-Reply-To":case"Content-Id":return"<"!==(t=(t||"").toString().replace(/\r?\n|\r/g," ")).charAt(0)&&(t="<"+t),">"!==t.charAt(t.length-1)&&(t+=">"),t;case"References":return(t=[].concat.apply([],[].concat(t||"").map(e=>(e=(e||"").toString().replace(/\r?\n|\r/g," ").trim()).replace(/<[^>]*>/g,e=>e.replace(/\s/g,"")).split(/\s+/))).map(e=>("<"!==e.charAt(0)&&(e="<"+e),">"!==e.charAt(e.length-1)&&(e+=">"),e))).join(" ").trim();case"Date":return"[object Date]"===Object.prototype.toString.call(t)?t.toUTCString().replace(/GMT/,"+0000"):(t=(t||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(t));case"Content-Type":case"Content-Disposition":return(t||"").toString().replace(/\r?\n|\r/g," ");default:return t=(t||"").toString().replace(/\r?\n|\r/g," "),this._encodeWords(t)}}_convertAddresses(e,t){let i=[];return t=t||[],[].concat(e||[]).forEach(e=>{if(e.address)e.address=this._normalizeAddress(e.address),e.name?e.name&&i.push(`${this._encodeAddressName(e.name)} <${e.address}>`):i.push(e.address.indexOf(" ")>=0?`<${e.address}>`:""+e.address),e.address&&(t.filter(t=>t.address===e.address).length||t.push(e));else if(e.group){let a=(e.group.length?this._convertAddresses(e.group,t):"").trim();i.push(`${this._encodeAddressName(e.name)}:${a};`)}}),i.join(", ")}_normalizeAddress(e){let t=(e=(e||"").toString().replace(/[\x00-\x1F<>]+/g," ").trim()).lastIndexOf("@");if(t<0)return e;let i,a=e.substr(0,t),s=e.substr(t+1);try{i=u.toASCII(s.toLowerCase())}catch(e){}return a.indexOf(" ")>=0&&('"'!==a.charAt(0)&&(a='"'+a),'"'!==a.substr(-1)&&(a+='"')),`${a}@${i}`}_encodeAddressName(e){return/^[\w ']*$/.test(e)?e:/^[\x20-\x7e]*$/.test(e)?'"'+e.replace(/([\\"])/g,"\\$1")+'"':ie.encodeWord(e,this._getTextEncoding(e),52)}_encodeWords(e){return ie.encodeWords(e,this._getTextEncoding(e),52,!0)}_getTextEncoding(e){e=(e||"").toString();let t,i,a=this.textEncoding;return a||(i=(e.match(/[\x00-\x08\x0B\x0C\x0E-\x1F\u0080-\uFFFF]/g)||[]).length,t=(e.match(/[a-z]/gi)||[]).length,a=i<t?"Q":"B"),a}_generateMessageId(){return"<"+[2,2,2,6].reduce((e,t)=>e+"-"+h.randomBytes(t).toString("hex"),h.randomBytes(4).toString("hex"))+"@"+(this.getEnvelope().from||this.hostname||d.hostname()||"localhost").split("@").pop()+">"}}var ue=he;var xe=class{constructor(e){this.mail=e||{},this.message=!1}compile(){return this._alternatives=this.getAlternatives(),this._htmlNode=this._alternatives.filter(e=>/^text\/html\b/i.test(e.contentType)).pop(),this._attachments=this.getAttachments(!!this._htmlNode),this._useRelated=!(!this._htmlNode||!this._attachments.related.length),this._useAlternative=this._alternatives.length>1,this._useMixed=this._attachments.attached.length>1||this._alternatives.length&&1===this._attachments.attached.length,this.mail.raw?this.message=new ue("message/rfc822",{newline:this.mail.newline}).setRaw(this.mail.raw):this._useMixed?this.message=this._createMixed():this._useAlternative?this.message=this._createAlternative():this._useRelated?this.message=this._createRelated():this.message=this._createContentNode(!1,[].concat(this._alternatives||[]).concat(this._attachments.attached||[]).shift()||{contentType:"text/plain",content:""}),this.mail.headers&&this.message.addHeader(this.mail.headers),["from","sender","to","cc","bcc","reply-to","in-reply-to","references","subject","message-id","date"].forEach(e=>{let t=e.replace(/-(\w)/g,(e,t)=>t.toUpperCase());this.mail[t]&&this.message.setHeader(e,this.mail[t])}),this.mail.envelope&&this.message.setEnvelope(this.mail.envelope),this.message.messageId(),this.message}getAttachments(e){let t,i,a=[].concat(this.mail.attachments||[]).map((e,t)=>{let i,a=/^message\//i.test(e.contentType);return/^data:/i.test(e.path||e.href)&&(e=this._processDataUrl(e)),i={contentType:e.contentType||ie.detectMimeType(e.filename||e.path||e.href||"bin"),contentDisposition:e.contentDisposition||(a?"inline":"attachment"),contentTransferEncoding:"contentTransferEncoding"in e?e.contentTransferEncoding:"base64"},e.filename?i.filename=e.filename:a||!1===e.filename||(i.filename=(e.path||e.href||"").split("/").pop().split("?").shift()||"attachment-"+(t+1),i.filename.indexOf(".")<0&&(i.filename+="."+ie.detectExtension(i.contentType))),/^https?:\/\//i.test(e.path)&&(e.href=e.path,e.path=void 0),e.cid&&(i.cid=e.cid),e.raw?i.raw=e.raw:e.path?i.content={path:e.path}:e.href?i.content={href:e.href,httpHeaders:e.httpHeaders}:i.content=e.content||"",e.encoding&&(i.encoding=e.encoding),e.headers&&(i.headers=e.headers),i});return this.mail.icalEvent&&(t="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},i={},Object.keys(t).forEach(e=>{i[e]=t[e]}),i.contentType="application/ics",i.headers||(i.headers={}),i.filename=i.filename||"invite.ics",i.headers["Content-Disposition"]="attachment",i.headers["Content-Transfer-Encoding"]="base64"),e?{attached:a.filter(e=>!e.cid).concat(i||[]),related:a.filter(e=>!!e.cid)}:{attached:a.concat(i||[]),related:[]}}getAlternatives(){let e,t,i,a,s,n,o=[];return this.mail.text&&(e="object"==typeof this.mail.text&&(this.mail.text.content||this.mail.text.path||this.mail.text.href||this.mail.text.raw)?this.mail.text:{content:this.mail.text},e.contentType="text/plain; charset=utf-8"),this.mail.watchHtml&&(i="object"==typeof this.mail.watchHtml&&(this.mail.watchHtml.content||this.mail.watchHtml.path||this.mail.watchHtml.href||this.mail.watchHtml.raw)?this.mail.watchHtml:{content:this.mail.watchHtml},i.contentType="text/watch-html; charset=utf-8"),this.mail.amp&&(a="object"==typeof this.mail.amp&&(this.mail.amp.content||this.mail.amp.path||this.mail.amp.href||this.mail.amp.raw)?this.mail.amp:{content:this.mail.amp},a.contentType="text/x-amp-html; charset=utf-8"),this.mail.icalEvent&&(s="object"==typeof this.mail.icalEvent&&(this.mail.icalEvent.content||this.mail.icalEvent.path||this.mail.icalEvent.href||this.mail.icalEvent.raw)?this.mail.icalEvent:{content:this.mail.icalEvent},n={},Object.keys(s).forEach(e=>{n[e]=s[e]}),n.content&&"object"==typeof n.content&&(n.content._resolve=!0),n.filename=!1,n.contentType="text/calendar; charset=utf-8; method="+(n.method||"PUBLISH").toString().trim().toUpperCase(),n.headers||(n.headers={})),this.mail.html&&(t="object"==typeof this.mail.html&&(this.mail.html.content||this.mail.html.path||this.mail.html.href||this.mail.html.raw)?this.mail.html:{content:this.mail.html},t.contentType="text/html; charset=utf-8"),[].concat(e||[]).concat(i||[]).concat(a||[]).concat(t||[]).concat(n||[]).concat(this.mail.alternatives||[]).forEach(e=>{let t;/^data:/i.test(e.path||e.href)&&(e=this._processDataUrl(e)),t={contentType:e.contentType||ie.detectMimeType(e.filename||e.path||e.href||"txt"),contentTransferEncoding:e.contentTransferEncoding},e.filename&&(t.filename=e.filename),/^https?:\/\//i.test(e.path)&&(e.href=e.path,e.path=void 0),e.raw?t.raw=e.raw:e.path?t.content={path:e.path}:e.href?t.content={href:e.href}:t.content=e.content||"",e.encoding&&(t.encoding=e.encoding),e.headers&&(t.headers=e.headers),o.push(t)}),o}_createMixed(e){let t;return t=e?e.createChild("multipart/mixed",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new ue("multipart/mixed",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._useAlternative?this._createAlternative(t):this._useRelated&&this._createRelated(t),[].concat(!this._useAlternative&&this._alternatives||[]).concat(this._attachments.attached||[]).forEach(e=>{this._useRelated&&e===this._htmlNode||this._createContentNode(t,e)}),t}_createAlternative(e){let t;return t=e?e.createChild("multipart/alternative",{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new ue("multipart/alternative",{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._alternatives.forEach(e=>{this._useRelated&&this._htmlNode===e?this._createRelated(t):this._createContentNode(t,e)}),t}_createRelated(e){let t;return t=e?e.createChild('multipart/related; type="text/html"',{disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new ue('multipart/related; type="text/html"',{baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),this._createContentNode(t,this._htmlNode),this._attachments.related.forEach(e=>this._createContentNode(t,e)),t}_createContentNode(e,t){let i;(t=t||{}).content=t.content||"";let a=(t.encoding||"utf8").toString().toLowerCase().replace(/[-_\s]/g,"");return i=e?e.createChild(t.contentType,{filename:t.filename,textEncoding:this.mail.textEncoding,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}):new ue(t.contentType,{filename:t.filename,baseBoundary:this.mail.baseBoundary,textEncoding:this.mail.textEncoding,boundaryPrefix:this.mail.boundaryPrefix,disableUrlAccess:this.mail.disableUrlAccess,disableFileAccess:this.mail.disableFileAccess,normalizeHeaderKey:this.mail.normalizeHeaderKey,newline:this.mail.newline}),t.headers&&i.addHeader(t.headers),t.cid&&i.setHeader("Content-Id","<"+t.cid.replace(/[<>]/g,"")+">"),t.contentTransferEncoding?i.setHeader("Content-Transfer-Encoding",t.contentTransferEncoding):this.mail.encoding&&/^text\//i.test(t.contentType)&&i.setHeader("Content-Transfer-Encoding",this.mail.encoding),/^text\//i.test(t.contentType)&&!t.contentDisposition||i.setHeader("Content-Disposition",t.contentDisposition||(t.cid?"inline":"attachment")),"string"!=typeof t.content||["utf8","usascii","ascii"].includes(a)||(t.content=Buffer.from(t.content,a)),t.raw?i.setRaw(t.raw):i.setContent(t.content),i}_processDataUrl(e){let t=(e.path||e.href).match(/^data:((?:[^;]*;)*(?:[^,]*)),(.*)$/i);return t?(e.content=/\bbase64$/i.test(t[1])?Buffer.from(t[2],"base64"):Buffer.from(decodeURIComponent(t[2])),"path"in e&&(e.path=!1),"href"in e&&(e.href=!1),t[1].split(";").forEach(t=>{/^\w+\/[^/]+$/i.test(t)&&(e.contentType=e.contentType||t.toLowerCase())}),e):e}};const ge=p.Transform;var fe=class extends ge{constructor(e){super(e),this.lastBytes=Buffer.alloc(4),this.headersParsed=!1,this.headerBytes=0,this.headerChunks=[],this.rawHeaders=!1,this.bodySize=0}updateLastBytes(e){let t=this.lastBytes.length,i=Math.min(e.length,t);for(let e=0,a=t-i;e<a;e++)this.lastBytes[e]=this.lastBytes[e+i];for(let a=1;a<=i;a++)this.lastBytes[t-a]=e[e.length-a]}checkHeaders(e){if(this.headersParsed)return!0;let t=this.lastBytes.length,i=0;this.curLinePos=0;for(let a=0,s=this.lastBytes.length+e.length;a<s;a++){let s;if(s=a<t?this.lastBytes[a]:e[a-t],10===s&&a){let s=a-1<t?this.lastBytes[a-1]:e[a-1-t],n=a>1&&(a-2<t?this.lastBytes[a-2]:e[a-2-t]);if(10===s){this.headersParsed=!0,i=a-t+1,this.headerBytes+=i;break}if(13===s&&10===n){this.headersParsed=!0,i=a-t+1,this.headerBytes+=i;break}}}if(this.headersParsed){if(this.headerChunks.push(e.slice(0,i)),this.rawHeaders=Buffer.concat(this.headerChunks,this.headerBytes),this.headerChunks=null,this.emit("headers",this.parseHeaders()),e.length-1>i){let t=e.slice(i);this.bodySize+=t.length,setImmediate(()=>this.push(t))}return!1}return this.headerBytes+=e.length,this.headerChunks.push(e),this.updateLastBytes(e),!1}_transform(e,t,i){if(!e||!e.length)return i();let a;"string"==typeof e&&(e=Buffer.from(e,t));try{a=this.checkHeaders(e)}catch(e){return i(e)}a&&(this.bodySize+=e.length,this.push(e)),setImmediate(i)}_flush(e){if(this.headerChunks){let e=Buffer.concat(this.headerChunks,this.headerBytes);this.bodySize+=e.length,this.push(e),this.headerChunks=null}e()}parseHeaders(){let e=(this.rawHeaders||"").toString().split(/\r?\n/);for(let t=e.length-1;t>0;t--)/^\s/.test(e[t])&&(e[t-1]+="\n"+e[t],e.splice(t,1));return e.filter(e=>e.trim()).map(e=>({key:e.substr(0,e.indexOf(":")).trim().toLowerCase(),line:e}))}};const ve=p.Transform;var be=class extends ve{constructor(e){super(),e=e||{},this.chunkBuffer=[],this.chunkBufferLen=0,this.bodyHash=h.createHash(e.hashAlgo||"sha1"),this.remainder="",this.byteLength=0,this.debug=e.debug,this._debugBody=!!e.debug&&[]}updateHash(e){let t,i="",a="file";for(let t=e.length-1;t>=0;t--){let s=e[t];if("file"!==a||10!==s&&13!==s)if("file"!==a||9!==s&&32!==s)if("line"!==a||9!==s&&32!==s){if(("file"===a||"line"===a)&&(a="body",t===e.length-1))break}else;else a="line";else;if(0===t){if("file"===a&&(!this.remainder||/[\r\n]$/.test(this.remainder))||"line"===a&&(!this.remainder||/[ \t]$/.test(this.remainder)))return void(this.remainder+=e.toString("binary"));if("line"===a||"file"===a){i=e.toString("binary"),e=!1;break}}if("body"===a){i=e.slice(t+1).toString("binary"),e=e.slice(0,t+1);break}}let s=!!this.remainder;if(e&&!s)for(let t=0,i=e.length;t<i;t++){if(t&&10===e[t]&&13!==e[t-1]){s=!0;break}if(t&&13===e[t]&&32===e[t-1]){s=!0;break}if(t&&32===e[t]&&32===e[t-1]){s=!0;break}if(9===e[t]){s=!0;break}}s?(t=this.remainder+(e?e.toString("binary"):""),this.remainder=i,t=t.replace(/\r?\n/g,"\n").replace(/[ \t]*$/gm,"").replace(/[ \t]+/gm," ").replace(/\n/g,"\r\n"),e=Buffer.from(t,"binary")):i&&(this.remainder=i),this.debug&&this._debugBody.push(e),this.bodyHash.update(e)}_transform(e,t,i){if(!e||!e.length)return i();"string"==typeof e&&(e=Buffer.from(e,t)),this.updateHash(e),this.byteLength+=e.length,this.push(e),i()}_flush(e){/[\r\n]$/.test(this.remainder)&&this.byteLength>2&&this.bodyHash.update(Buffer.from("\r\n")),this.byteLength||this.push(Buffer.from("\r\n")),this.emit("hash",this.bodyHash.digest("base64"),!!this.debug&&Buffer.concat(this._debugBody)),e()}},ye=(e,t,i,a)=>{let s,n,o=_e(e,(a=a||{}).headerFieldNames||"From:Sender:Reply-To:Subject:Date:Message-ID:To:Cc:MIME-Version:Content-Type:Content-Transfer-Encoding:Content-ID:Content-Description:Resent-Date:Resent-From:Resent-Sender:Resent-To:Resent-Cc:Resent-Message-ID:In-Reply-To:References:List-Id:List-Help:List-Unsubscribe:List-Subscribe:List-Post:List-Owner:List-Archive",a.skipFields),r=function(e,t,i,a,s){let n=["v=1","a=rsa-"+a,"c=relaxed/relaxed","d="+u.toASCII(e),"q=dns/txt","s="+t,"bh="+s,"h="+i].join("; ");return ie.foldLines("DKIM-Signature: "+n,76)+";\r\n b="}(a.domainName,a.keySelector,o.fieldNames,t,i);o.headers+="dkim-signature:"+we(r),s=h.createSign(("rsa-"+t).toUpperCase()),s.update(o.headers);try{n=s.sign(a.privateKey,"base64")}catch(e){return!1}return r+n.replace(/(^.{73}|.{75}(?!\r?\n|\r))/g,"$&\r\n ").trim()};function _e(e,t,i){let a=new Set,s=new Set,n=new Map;(i||"").toLowerCase().split(":").forEach(e=>{s.add(e.trim())}),(t||"").toLowerCase().split(":").filter(e=>!s.has(e.trim())).forEach(e=>{a.add(e.trim())});for(let t=e.length-1;t>=0;t--){let i=e[t];a.has(i.key)&&!n.has(i.key)&&n.set(i.key,we(i.line))}let o=[],r=[];return a.forEach(e=>{n.has(e)&&(r.push(e),o.push(e+":"+n.get(e)))}),{headers:o.join("\r\n")+"\r\n",fieldNames:r.join(":")}}function we(e){return e.substr(e.indexOf(":")+1).replace(/\r?\n/g,"").replace(/\s+/g," ").trim()}ye.relaxedHeaders=_e;const ke=p.PassThrough;class Ee{constructor(e,t,i,a){this.options=e||{},this.keys=t,this.cacheTreshold=Number(this.options.cacheTreshold)||131072,this.hashAlgo=this.options.hashAlgo||"sha256",this.cacheDir=this.options.cacheDir||!1,this.chunks=[],this.chunklen=0,this.readPos=0,this.cachePath=!!this.cacheDir&&m.join(this.cacheDir,"message."+Date.now()+"-"+h.randomBytes(14).toString("hex")),this.cache=!1,this.headers=!1,this.bodyHash=!1,this.parser=!1,this.relaxedBody=!1,this.input=i,this.output=a,this.output.usingCache=!1,this.errored=!1,this.input.on("error",e=>{this.errored=!0,this.cleanup(),a.emit("error",e)})}cleanup(){this.cache&&this.cachePath&&s.unlink(this.cachePath,()=>!1)}createReadCache(){this.cache=s.createReadStream(this.cachePath),this.cache.once("error",e=>{this.cleanup(),this.output.emit("error",e)}),this.cache.once("close",()=>{this.cleanup()}),this.cache.pipe(this.output)}sendNextChunk(){if(this.errored)return;if(this.readPos>=this.chunks.length)return this.cache?this.createReadCache():this.output.end();let e=this.chunks[this.readPos++];if(!1===this.output.write(e))return this.output.once("drain",()=>{this.sendNextChunk()});setImmediate(()=>this.sendNextChunk())}sendSignedOutput(){let e=0,t=()=>{if(e>=this.keys.length)return this.output.write(this.parser.rawHeaders),setImmediate(()=>this.sendNextChunk());let i=this.keys[e++],a=ye(this.headers,this.hashAlgo,this.bodyHash,{domainName:i.domainName,keySelector:i.keySelector,privateKey:i.privateKey,headerFieldNames:this.options.headerFieldNames,skipFields:this.options.skipFields});return a&&this.output.write(Buffer.from(a+"\r\n")),setImmediate(t)};if(this.bodyHash&&this.headers)return t();this.output.write(this.parser.rawHeaders),this.sendNextChunk()}createWriteCache(){this.output.usingCache=!0,this.cache=s.createWriteStream(this.cachePath),this.cache.once("error",e=>{this.cleanup(),this.relaxedBody.unpipe(this.cache),this.relaxedBody.on("readable",()=>{for(;null!==this.relaxedBody.read(););}),this.errored=!0,this.output.emit("error",e)}),this.cache.once("close",()=>{this.sendSignedOutput()}),this.relaxedBody.removeAllListeners("readable"),this.relaxedBody.pipe(this.cache)}signStream(){this.parser=new fe,this.relaxedBody=new be({hashAlgo:this.hashAlgo}),this.parser.on("headers",e=>{this.headers=e}),this.relaxedBody.on("hash",e=>{this.bodyHash=e}),this.relaxedBody.on("readable",()=>{let e;if(!this.cache)for(;null!==(e=this.relaxedBody.read());)if(this.chunks.push(e),this.chunklen+=e.length,this.chunklen>=this.cacheTreshold&&this.cachePath)return this.createWriteCache()}),this.relaxedBody.on("end",()=>{this.cache||this.sendSignedOutput()}),this.parser.pipe(this.relaxedBody),setImmediate(()=>this.input.pipe(this.parser))}}var Te=class{constructor(e){this.options=e||{},this.keys=[].concat(this.options.keys||{domainName:e.domainName,keySelector:e.keySelector,privateKey:e.privateKey})}sign(e,t){let i=new ke,a=e,s=!1;Buffer.isBuffer(e)?(s=e,a=new ke):"string"==typeof e&&(s=Buffer.from(e),a=new ke);let n=this.options;t&&Object.keys(t).length&&(n={},Object.keys(this.options||{}).forEach(e=>{n[e]=this.options[e]}),Object.keys(t||{}).forEach(e=>{e in n||(n[e]=t[e])}));let o=new Ee(n,this.keys,a,i);return setImmediate(()=>{o.signStream(),s&&setImmediate(()=>{a.end(s)})}),i}};var Se=function(e,t,a,s){let n,o,r,p=i.parse(e);n={host:p.hostname,port:Number(p.port)?Number(p.port):"https:"===p.protocol?443:80},"https:"===p.protocol?(n.rejectUnauthorized=!1,o=x.connect.bind(x)):o=l.connect.bind(l);let c=!1,d=function(e){if(!c){c=!0;try{r.destroy()}catch(e){}s(e)}};r=o(n,()=>{if(c)return;let e={Host:a+":"+t,Connection:"close"};p.auth&&(e["Proxy-Authorization"]="Basic "+Buffer.from(p.auth).toString("base64")),r.write("CONNECT "+a+":"+t+" HTTP/1.1\r\n"+Object.keys(e).map(t=>t+": "+e[t]).join("\r\n")+"\r\n\r\n");let i="",n=e=>{let t,a;if(!c&&(i+=e.toString("binary"),t=i.match(/\r\n\r\n/))){if(r.removeListener("data",n),a=i.substr(t.index+t[0].length),i=i.substr(0,t.index),a&&r.unshift(Buffer.from(a,"binary")),c=!0,t=i.match(/^HTTP\/\d+\.\d+ (\d+)/i),!t||"2"!==(t[1]||"").charAt(0)){try{r.destroy()}catch(e){}return s(new Error("Invalid response from proxy"+(t&&": "+t[1]||"")))}return r.removeListener("error",d),s(null,r)}};r.on("data",n)}),r.once("error",d)};var Ae=class{constructor(e,t){this.mailer=e,this.data={},this.message=null,t=t||{};let i=e.options||{},a=e._defaults||{};Object.keys(t).forEach(e=>{this.data[e]=t[e]}),this.data.headers=this.data.headers||{},Object.keys(a).forEach(e=>{e in this.data?"headers"===e&&Object.keys(a.headers).forEach(e=>{e in this.data.headers||(this.data.headers[e]=a.headers[e])}):this.data[e]=a[e]}),["disableFileAccess","disableUrlAccess","normalizeHeaderKey"].forEach(e=>{e in i&&(this.data[e]=i[e])})}resolveContent(...e){return D.resolveContent(...e)}resolveAll(e){let t=[[this.data,"html"],[this.data,"text"],[this.data,"watchHtml"],[this.data,"amp"],[this.data,"icalEvent"]];this.data.alternatives&&this.data.alternatives.length&&this.data.alternatives.forEach((e,i)=>{t.push([this.data.alternatives,i])}),this.data.attachments&&this.data.attachments.length&&this.data.attachments.forEach((e,i)=>{e.filename||(e.filename=(e.path||e.href||"").split("/").pop().split("?").shift()||"attachment-"+(i+1),e.filename.indexOf(".")<0&&(e.filename+="."+ie.detectExtension(e.contentType))),e.contentType||(e.contentType=ie.detectMimeType(e.filename||e.path||e.href||"bin")),t.push([this.data.attachments,i])});let i=new ue;["from","to","cc","bcc","sender","replyTo"].forEach(e=>{let t;this.message?t=[].concat(i._parseAddresses(this.message.getHeader("replyTo"===e?"reply-to":e))||[]):this.data[e]&&(t=[].concat(i._parseAddresses(this.data[e])||[])),t&&t.length?this.data[e]=t:e in this.data&&(this.data[e]=null)});["from","sender","replyTo"].forEach(e=>{this.data[e]&&(this.data[e]=this.data[e].shift())});let a=0,s=()=>{if(a>=t.length)return e(null,this.data);let i=t[a++];if(!i[0]||!i[0][i[1]])return s();D.resolveContent(...i,(t,a)=>{if(t)return e(t);let n={content:a};i[0][i[1]]&&"object"==typeof i[0][i[1]]&&!Buffer.isBuffer(i[0][i[1]])&&Object.keys(i[0][i[1]]).forEach(e=>{e in n||["content","path","href","raw"].includes(e)||(n[e]=i[0][i[1]][e])}),i[0][i[1]]=n,s()})};setImmediate(()=>s())}normalize(e){let t=this.data.envelope||this.message.getEnvelope(),i=this.message.messageId();this.resolveAll((a,s)=>{if(a)return e(a);if(s.envelope=t,s.messageId=i,["html","text","watchHtml","amp"].forEach(e=>{s[e]&&s[e].content&&("string"==typeof s[e].content?s[e]=s[e].content:Buffer.isBuffer(s[e].content)&&(s[e]=s[e].content.toString()))}),s.icalEvent&&Buffer.isBuffer(s.icalEvent.content)&&(s.icalEvent.content=s.icalEvent.content.toString("base64"),s.icalEvent.encoding="base64"),s.alternatives&&s.alternatives.length&&s.alternatives.forEach(e=>{e&&e.content&&Buffer.isBuffer(e.content)&&(e.content=e.content.toString("base64"),e.encoding="base64")}),s.attachments&&s.attachments.length&&s.attachments.forEach(e=>{e&&e.content&&Buffer.isBuffer(e.content)&&(e.content=e.content.toString("base64"),e.encoding="base64")}),s.normalizedHeaders={},Object.keys(s.headers||{}).forEach(e=>{let t=[].concat(s.headers[e]||[]).shift();t=t&&t.value||t,t&&(["references","in-reply-to","message-id","content-id"].includes(e)&&(t=this.message._encodeHeaderValue(e,t)),s.normalizedHeaders[e]=t)}),s.list&&"object"==typeof s.list){this._getListHeaders(s.list).forEach(e=>{s.normalizedHeaders[e.key]=e.value.map(e=>e&&e.value||e).join(", ")})}return s.references&&(s.normalizedHeaders.references=this.message._encodeHeaderValue("references",s.references)),s.inReplyTo&&(s.normalizedHeaders["in-reply-to"]=this.message._encodeHeaderValue("in-reply-to",s.inReplyTo)),e(null,s)})}setMailerHeader(){this.message&&this.data.xMailer&&this.message.setHeader("X-Mailer",this.data.xMailer)}setPriorityHeaders(){if(this.message&&this.data.priority)switch((this.data.priority||"").toString().toLowerCase()){case"high":this.message.setHeader("X-Priority","1 (Highest)"),this.message.setHeader("X-MSMail-Priority","High"),this.message.setHeader("Importance","High");break;case"low":this.message.setHeader("X-Priority","5 (Lowest)"),this.message.setHeader("X-MSMail-Priority","Low"),this.message.setHeader("Importance","Low")}}setListHeaders(){this.message&&this.data.list&&"object"==typeof this.data.list&&this.data.list&&"object"==typeof this.data.list&&this._getListHeaders(this.data.list).forEach(e=>{e.value.forEach(t=>{this.message.addHeader(e.key,t)})})}_getListHeaders(e){return Object.keys(e).map(t=>({key:"list-"+t.toLowerCase().trim(),value:[].concat(e[t]||[]).map(e=>({prepared:!0,foldLines:!0,value:[].concat(e||[]).map(e=>{if("string"==typeof e&&(e={url:e}),e&&e.url){if("id"===t.toLowerCase().trim()){let t=e.comment||"";return t=ie.isPlainText(t)?'"'+t+'"':ie.encodeWord(t),(e.comment?t+" ":"")+this._formatListUrl(e.url).replace(/^<[^:]+\/{,2}/,"")}let i=e.comment||"";return ie.isPlainText(i)||(i=ie.encodeWord(i)),this._formatListUrl(e.url)+(e.comment?" ("+i+")":"")}return""}).filter(e=>e).join(", ")}))}))}_formatListUrl(e){return e=e.replace(/[\s<]+|[\s>]+/g,""),/^(https?|mailto|ftp):/.test(e)?"<"+e+">":/^[^@]+@[^@]+$/.test(e)?"<mailto:"+e+">":"<http://"+e+">"}};var Ce=class extends t{constructor(e,t,i){super(),this.options=t||{},this._defaults=i||{},this._defaultPlugins={compile:[(...e)=>this._convertDataImages(...e)],stream:[]},this._userPlugins={compile:[],stream:[]},this.meta=new Map,this.dkim=!!this.options.dkim&&new Te(this.options.dkim),this.transporter=e,this.transporter.mailer=this,this.logger=D.getLogger(this.options,{component:this.options.component||"mail"}),this.logger.debug({tnx:"create"},"Creating transport: %s",this.getVersionString()),"function"==typeof this.transporter.on&&(this.transporter.on("log",e=>{this.logger.debug({tnx:"transport"},"%s: %s",e.type,e.message)}),this.transporter.on("error",e=>{this.logger.error({err:e,tnx:"transport"},"Transport Error: %s",e.message),this.emit("error",e)}),this.transporter.on("idle",(...e)=>{this.emit("idle",...e)})),["close","isIdle","verify"].forEach(e=>{this[e]=(...t)=>"function"==typeof this.transporter[e]?("verify"===e&&"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1),this.transporter[e](...t)):(this.logger.warn({tnx:"transport",methodName:e},"Non existing method %s called for transport",e),!1)}),this.options.proxy&&"string"==typeof this.options.proxy&&this.setupProxy(this.options.proxy)}use(e,t){return e=(e||"").toString(),this._userPlugins.hasOwnProperty(e)?this._userPlugins[e].push(t):this._userPlugins[e]=[t],this}sendMail(e,t){let i;t||(i=new Promise((e,i)=>{t=D.callbackPromise(e,i)})),"function"==typeof this.getSocket&&(this.transporter.getSocket=this.getSocket,this.getSocket=!1);let a=new Ae(this,e);return this.logger.debug({tnx:"transport",name:this.transporter.name,version:this.transporter.version,action:"send"},"Sending mail using %s/%s",this.transporter.name,this.transporter.version),this._processPlugins("compile",a,e=>{if(e)return this.logger.error({err:e,tnx:"plugin",action:"compile"},"PluginCompile Error: %s",e.message),t(e);a.message=new xe(a.data).compile(),a.setMailerHeader(),a.setPriorityHeaders(),a.setListHeaders(),this._processPlugins("stream",a,e=>{if(e)return this.logger.error({err:e,tnx:"plugin",action:"stream"},"PluginStream Error: %s",e.message),t(e);(a.data.dkim||this.dkim)&&a.message.processFunc(e=>{let t=a.data.dkim?new Te(a.data.dkim):this.dkim;return this.logger.debug({tnx:"DKIM",messageId:a.message.messageId(),dkimDomains:t.keys.map(e=>e.keySelector+"."+e.domainName).join(", ")},"Signing outgoing message with %s keys",t.keys.length),t.sign(e,a.data._dkim)}),this.transporter.send(a,(...e)=>{e[0]&&this.logger.error({err:e[0],tnx:"transport",action:"send"},"Send Error: %s",e[0].message),t(...e)})})}),i}getVersionString(){return a.format("%s (%s; +%s; %s/%s)",U.name,U.version,U.homepage,this.transporter.name,this.transporter.version)}_processPlugins(e,t,i){if(e=(e||"").toString(),!this._userPlugins.hasOwnProperty(e))return i();let a=this._userPlugins[e]||[],s=this._defaultPlugins[e]||[];if(a.length&&this.logger.debug({tnx:"transaction",pluginCount:a.length,step:e},"Using %s plugins for %s",a.length,e),a.length+s.length===0)return i();let n=0,o="default",r=()=>{let e="default"===o?s:a;if(n>=e.length){if("default"!==o||!a.length)return i();o="user",n=0,e=a}(0,e[n++])(t,e=>{if(e)return i(e);r()})};r()}setupProxy(e){let t=i.parse(e);this.getSocket=(e,i)=>{let a=t.protocol.replace(/:$/,"").toLowerCase();if(this.meta.has("proxy_handler_"+a))return this.meta.get("proxy_handler_"+a)(t,e,i);switch(a){case"http":case"https":return void Se(t.href,e.port,e.host,(e,t)=>e?i(e):i(null,{connection:t}));case"socks":case"socks5":case"socks4":case"socks4a":{if(!this.meta.has("proxy_socks_module"))return i(new Error("Socks module not loaded"));let a=a=>{let s=!!this.meta.get("proxy_socks_module").SocksClient,n=s?this.meta.get("proxy_socks_module").SocksClient:this.meta.get("proxy_socks_module"),o=Number(t.protocol.replace(/\D/g,""))||5,r={proxy:{ipaddress:a,port:Number(t.port),type:o},[s?"destination":"target"]:{host:e.host,port:e.port},command:"connect"};if(t.auth){let e=decodeURIComponent(t.auth.split(":").shift()),i=decodeURIComponent(t.auth.split(":").pop());s?(r.proxy.userId=e,r.proxy.password=i):4===o?r.userid=e:r.authentication={username:e,password:i}}n.createConnection(r,(e,t)=>e?i(e):i(null,{connection:t.socket||t}))};return l.isIP(t.hostname)?a(t.hostname):c.resolve(t.hostname,(e,t)=>{if(e)return i(e);a(Array.isArray(t)?t[0]:t)})}}i(new Error("Unknown proxy configuration"))}}_convertDataImages(e,t){if(!this.options.attachDataUrls&&!e.data.attachDataUrls||!e.data.html)return t();e.resolveContent(e.data,"html",(i,a)=>{if(i)return t(i);let s=0;a=(a||"").toString().replace(/(<img\b[^>]* src\s*=[\s"']*)(data:([^;]+);[^"'>\s]+)/gi,(t,i,a,n)=>{let o=h.randomBytes(10).toString("hex")+"@localhost";return e.data.attachments||(e.data.attachments=[]),Array.isArray(e.data.attachments)||(e.data.attachments=[].concat(e.data.attachments||[])),e.data.attachments.push({path:a,cid:o,filename:"image-"+ ++s+"."+Q.detectExtension(n)}),i+"cid:"+o}),e.data.html=a,t()})}set(e,t){return this.meta.set(e,t)}get(e){return this.meta.get(e)}};const je=p.Transform;var Ie=class extends je{constructor(e){super(e),this.options=e||{},this._curLine="",this.inByteCount=0,this.outByteCount=0,this.lastByte=!1}_transform(e,t,i){let a,s,n,o=[],r=0,p=0;if(!e||!e.length)return i();for("string"==typeof e&&(e=Buffer.from(e)),this.inByteCount+=e.length,a=0,s=e.length;a<s;a++)46===e[a]?(a&&10===e[a-1]||!a&&(!this.lastByte||10===this.lastByte))&&(n=e.slice(p,a+1),o.push(n),o.push(Buffer.from(".")),r+=n.length+1,p=a+1):10===e[a]&&(a&&13!==e[a-1]||!a&&13!==this.lastByte)&&(a>p?(n=e.slice(p,a),o.push(n),r+=n.length+2):r+=2,o.push(Buffer.from("\r\n")),p=a+1);r?(p<e.length&&(n=e.slice(p),o.push(n),r+=n.length),this.outByteCount+=r,this.push(Buffer.concat(o,r))):(this.outByteCount+=e.length,this.push(e)),this.lastByte=e[e.length-1],i()}_flush(e){let t;t=10===this.lastByte?Buffer.from(".\r\n"):13===this.lastByte?Buffer.from("\n.\r\n"):Buffer.from("\r\n.\r\n"),this.outByteCount+=t.length,this.push(t),e()}};const Le=t.EventEmitter,He=p.PassThrough;var Oe=class extends Le{constructor(e){super(e),this.id=h.randomBytes(8).toString("base64").replace(/\W/g,""),this.stage="init",this.options=e||{},this.secureConnection=!!this.options.secure,this.alreadySecured=!!this.options.secured,this.port=Number(this.options.port)||(this.secureConnection?465:587),this.host=this.options.host||"localhost",void 0===this.options.secure&&465===this.port&&(this.secureConnection=!0),this.name=this.options.name||this._getHostname(),this.logger=D.getLogger(this.options,{component:this.options.component||"smtp-connection",sid:this.id}),this.customAuth=new Map,Object.keys(this.options.customAuth||{}).forEach(e=>{let t=(e||"").toString().trim().toUpperCase();t&&this.customAuth.set(t,this.options.customAuth[e])}),this.version=U.version,this.authenticated=!1,this.destroyed=!1,this.secure=!!this.secureConnection,this._remainder="",this._responseQueue=[],this.lastServerResponse=!1,this._socket=!1,this._supportedAuth=[],this.allowsAuth=!1,this._envelope=!1,this._supportedExtensions=[],this._maxAllowedSize=0,this._responseActions=[],this._recipientQueue=[],this._greetingTimeout=!1,this._connectionTimeout=!1,this._destroyed=!1,this._closing=!1,this._onSocketData=e=>this._onData(e),this._onSocketError=e=>this._onError(e,"ESOCKET",!1,"CONN"),this._onSocketClose=()=>this._onClose(),this._onSocketEnd=()=>this._onEnd(),this._onSocketTimeout=()=>this._onTimeout()}connect(e){if("function"==typeof e){this.once("connect",()=>{this.logger.debug({tnx:"smtp"},"SMTP handshake finished"),e()});const t=this._isDestroyedMessage("connect");if(t)return e(this._formatError(t,"ECONNECTION",!1,"CONN"))}let t={port:this.port,host:this.host};this.options.localAddress&&(t.localAddress=this.options.localAddress);let i=()=>{this._connectionTimeout=setTimeout(()=>{this._onError("Connection timeout","ETIMEDOUT",!1,"CONN")},this.options.connectionTimeout||12e4),this._socket.on("error",this._onSocketError)};return this.options.connection?(this._socket=this.options.connection,void(this.secureConnection&&!this.alreadySecured?setImmediate(()=>this._upgradeConnection(e=>{e?this._onError(new Error("Error initiating TLS - "+(e.message||e)),"ETLS",!1,"CONN"):this._onConnect()})):setImmediate(()=>this._onConnect()))):this.options.socket?(this._socket=this.options.socket,D.resolveHostname(t,(e,a)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:a.host,cached:!!a.cached},"Resolved %s as %s [cache %s]",t.host,a.host,a.cached?"hit":"miss"),Object.keys(a).forEach(e=>{"_"!==e.charAt(0)&&a[e]&&(t[e]=a[e])});try{this._socket.connect(this.port,this.host,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})):this.secureConnection?(this.options.tls&&Object.keys(this.options.tls).forEach(e=>{t[e]=this.options.tls[e]}),D.resolveHostname(t,(e,a)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:a.host,cached:!!a.cached},"Resolved %s as %s [cache %s]",t.host,a.host,a.cached?"hit":"miss"),Object.keys(a).forEach(e=>{"_"!==e.charAt(0)&&a[e]&&(t[e]=a[e])});try{this._socket=x.connect(t,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})):D.resolveHostname(t,(e,a)=>{if(e)return setImmediate(()=>this._onError(e,"EDNS",!1,"CONN"));this.logger.debug({tnx:"dns",source:t.host,resolved:a.host,cached:!!a.cached},"Resolved %s as %s [cache %s]",t.host,a.host,a.cached?"hit":"miss"),Object.keys(a).forEach(e=>{"_"!==e.charAt(0)&&a[e]&&(t[e]=a[e])});try{this._socket=l.connect(t,()=>{this._socket.setKeepAlive(!0),this._onConnect()}),i()}catch(e){return setImmediate(()=>this._onError(e,"ECONNECTION",!1,"CONN"))}})}quit(){this._sendCommand("QUIT"),this._responseActions.push(this.close)}close(){if(clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._responseActions=[],this._closing)return;this._closing=!0;let e="end";"init"===this.stage&&(e="destroy"),this.logger.debug({tnx:"smtp"},'Closing connection to the server using "%s"',e);let t=this._socket&&this._socket.socket||this._socket;if(t&&!t.destroyed)try{this._socket[e]()}catch(e){}this._destroy()}login(e,t){const i=this._isDestroyedMessage("login");if(i)return t(this._formatError(i,"ECONNECTION",!1,"API"));if(this._auth=e||{},this._authMethod=(this._auth.method||"").toString().trim().toUpperCase()||!1,this._authMethod||!this._auth.oauth2||this._auth.credentials?this._authMethod&&("XOAUTH2"!==this._authMethod||this._auth.oauth2)||(this._authMethod=(this._supportedAuth[0]||"PLAIN").toUpperCase().trim()):this._authMethod="XOAUTH2",!("XOAUTH2"===this._authMethod||this._auth.credentials&&this._auth.credentials.user&&this._auth.credentials.pass)){if(!this._auth.user||!this._auth.pass)return t(this._formatError('Missing credentials for "'+this._authMethod+'"',"EAUTH",!1,"API"));this._auth.credentials={user:this._auth.user,pass:this._auth.pass,options:this._auth.options}}if(!this.customAuth.has(this._authMethod)){switch(this._authMethod){case"XOAUTH2":return void this._handleXOauth2Token(!1,t);case"LOGIN":return this._responseActions.push(e=>{this._actionAUTH_LOGIN_USER(e,t)}),void this._sendCommand("AUTH LOGIN");case"PLAIN":return this._responseActions.push(e=>{this._actionAUTHComplete(e,t)}),void this._sendCommand("AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0"+this._auth.credentials.pass,"utf-8").toString("base64"),"AUTH PLAIN "+Buffer.from("\0"+this._auth.credentials.user+"\0/* secret */","utf-8").toString("base64"));case"CRAM-MD5":return this._responseActions.push(e=>{this._actionAUTH_CRAM_MD5(e,t)}),void this._sendCommand("AUTH CRAM-MD5")}return t(this._formatError('Unknown authentication method "'+this._authMethod+'"',"EAUTH",!1,"API"))}{let e,i=this.customAuth.get(this._authMethod),a=!1,s=()=>{a||(a=!0,this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,t(null,!0))},n=i=>{a||(a=!0,t(this._formatError(i,"EAUTH",e,"AUTH "+this._authMethod)))},o=i({auth:this._auth,method:this._authMethod,extensions:[].concat(this._supportedExtensions),authMethods:[].concat(this._supportedAuth),maxAllowedSize:this._maxAllowedSize||!1,sendCommand:(t,i)=>{let a;return i||(a=new Promise((e,t)=>{i=D.callbackPromise(e,t)})),this._responseActions.push(a=>{e=a;let s=a.match(/^(\d+)(?:\s(\d+\.\d+\.\d+))?\s/),n={command:t,response:a};s?(n.status=Number(s[1])||0,s[2]&&(n.code=s[2]),n.text=a.substr(s[0].length)):(n.text=a,n.status=0),i(null,n)}),setImmediate(()=>this._sendCommand(t)),a},resolve:s,reject:n});o&&"function"==typeof o.catch&&o.then(s).catch(n)}}send(e,t,i){if(!t)return i(this._formatError("Empty message","EMESSAGE",!1,"API"));const a=this._isDestroyedMessage("send message");if(a)return i(this._formatError(a,"ECONNECTION",!1,"API"));if(this._maxAllowedSize&&e.size>this._maxAllowedSize)return setImmediate(()=>{i(this._formatError("Message size larger than allowed "+this._maxAllowedSize,"EMESSAGE",!1,"MAIL FROM"))});let s=!1,n=function(){s||(s=!0,i(...arguments))};"function"==typeof t.on&&t.on("error",e=>n(this._formatError(e,"ESTREAM",!1,"API")));let o=Date.now();this._setEnvelope(e,(e,i)=>{if(e)return n(e);let a=Date.now(),s=this._createSendStream((e,t)=>e?n(e):(i.envelopeTime=a-o,i.messageTime=Date.now()-a,i.messageSize=s.outByteCount,i.response=t,n(null,i)));"function"==typeof t.pipe?t.pipe(s):(s.write(t),s.end())})}reset(e){this._sendCommand("RSET"),this._responseActions.push(t=>"2"!==t.charAt(0)?e(this._formatError("Could not reset session state. response="+t,"EPROTOCOL",t,"RSET")):(this._envelope=!1,e(null,!0)))}_onConnect(){clearTimeout(this._connectionTimeout),this.logger.info({tnx:"network",localAddress:this._socket.localAddress,localPort:this._socket.localPort,remoteAddress:this._socket.remoteAddress,remotePort:this._socket.remotePort},"%s established to %s:%s",this.secure?"Secure connection":"Connection",this._socket.remoteAddress,this._socket.remotePort),this._destroyed?this.close():(this.stage="connected",this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout),this._socket.removeListener("close",this._onSocketClose),this._socket.removeListener("end",this._onSocketEnd),this._socket.on("data",this._onSocketData),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),this._greetingTimeout=setTimeout(()=>{this._socket&&!this._destroyed&&this._responseActions[0]===this._actionGreeting&&this._onError("Greeting never received","ETIMEDOUT",!1,"CONN")},this.options.greetingTimeout||3e4),this._responseActions.push(this._actionGreeting),this._socket.resume())}_onData(e){if(this._destroyed||!e||!e.length)return;let t,i=(e||"").toString("binary"),a=(this._remainder+i).split(/\r?\n/);this._remainder=a.pop();for(let e=0,i=a.length;e<i;e++)this._responseQueue.length&&(t=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(t.split("\n").pop()))?this._responseQueue[this._responseQueue.length-1]+="\n"+a[e]:this._responseQueue.push(a[e]);this._responseQueue.length&&(t=this._responseQueue[this._responseQueue.length-1],/^\d+-/.test(t.split("\n").pop()))||this._processResponse()}_onError(e,t,i,a){clearTimeout(this._connectionTimeout),clearTimeout(this._greetingTimeout),this._destroyed||(e=this._formatError(e,t,i,a),this.logger.error(i,e.message),this.emit("error",e),this.close())}_formatError(e,t,i,a){let s;s=/Error\]$/i.test(Object.prototype.toString.call(e))?e:new Error(e),t&&"Error"!==t&&(s.code=t),i&&(s.response=i,s.message+=": "+i);let n="string"==typeof i&&Number((i.match(/^\d+/)||[])[0])||!1;return n&&(s.responseCode=n),a&&(s.command=a),s}_onClose(){return this.logger.info({tnx:"network"},"Connection closed"),this.upgrading&&!this._destroyed?this._onError(new Error("Connection closed unexpectedly"),"ETLS",!1,"CONN"):[this._actionGreeting,this.close].includes(this._responseActions[0])||this._destroyed?void this._destroy():this._onError(new Error("Connection closed unexpectedly"),"ECONNECTION",!1,"CONN")}_onEnd(){this._socket&&!this._socket.destroyed&&this._socket.destroy()}_onTimeout(){return this._onError(new Error("Timeout"),"ETIMEDOUT",!1,"CONN")}_destroy(){this._destroyed||(this._destroyed=!0,this.emit("end"))}_upgradeConnection(e){this._socket.removeListener("data",this._onSocketData),this._socket.removeListener("timeout",this._onSocketTimeout);let t=this._socket,i={socket:this._socket,host:this.host};Object.keys(this.options.tls||{}).forEach(e=>{i[e]=this.options.tls[e]}),this.upgrading=!0;try{this._socket=x.connect(i,()=>(this.secure=!0,this.upgrading=!1,this._socket.on("data",this._onSocketData),t.removeListener("close",this._onSocketClose),t.removeListener("end",this._onSocketEnd),e(null,!0)))}catch(t){return e(t)}this._socket.on("error",this._onSocketError),this._socket.once("close",this._onSocketClose),this._socket.once("end",this._onSocketEnd),this._socket.setTimeout(this.options.socketTimeout||6e5),this._socket.on("timeout",this._onSocketTimeout),t.resume()}_processResponse(){if(!this._responseQueue.length)return!1;let e=this.lastServerResponse=(this._responseQueue.shift()||"").toString();if(/^\d+-/.test(e.split("\n").pop()))return;(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"server"},e.replace(/\r?\n$/,"")),e.trim()||setImmediate(()=>this._processResponse(!0));let t=this._responseActions.shift();if("function"!=typeof t)return this._onError(new Error("Unexpected Response"),"EPROTOCOL",e,"CONN");t.call(this,e),setImmediate(()=>this._processResponse(!0))}_sendCommand(e,t){if(!this._destroyed){if(this._socket.destroyed)return this.close();(this.options.debug||this.options.transactionLog)&&this.logger.debug({tnx:"client"},(t||e||"").toString().replace(/\r?\n$/,"")),this._socket.write(Buffer.from(e+"\r\n","utf-8"))}}_setEnvelope(e,t){let i=[],a=!1;if(this._envelope=e||{},this._envelope.from=(this._envelope.from&&this._envelope.from.address||this._envelope.from||"").toString().trim(),this._envelope.to=[].concat(this._envelope.to||[]).map(e=>(e&&e.address||e||"").toString().trim()),!this._envelope.to.length)return t(this._formatError("No recipients defined","EENVELOPE",!1,"API"));if(this._envelope.from&&/[\r\n<>]/.test(this._envelope.from))return t(this._formatError("Invalid sender "+JSON.stringify(this._envelope.from),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.from)&&(a=!0);for(let e=0,i=this._envelope.to.length;e<i;e++){if(!this._envelope.to[e]||/[\r\n<>]/.test(this._envelope.to[e]))return t(this._formatError("Invalid recipient "+JSON.stringify(this._envelope.to[e]),"EENVELOPE",!1,"API"));/[\x80-\uFFFF]/.test(this._envelope.to[e])&&(a=!0)}if(this._envelope.rcptQueue=JSON.parse(JSON.stringify(this._envelope.to||[])),this._envelope.rejected=[],this._envelope.rejectedErrors=[],this._envelope.accepted=[],this._envelope.dsn)try{this._envelope.dsn=this._setDsnEnvelope(this._envelope.dsn)}catch(e){return t(this._formatError("Invalid DSN "+e.message,"EENVELOPE",!1,"API"))}this._responseActions.push(e=>{this._actionMAIL(e,t)}),a&&this._supportedExtensions.includes("SMTPUTF8")&&(i.push("SMTPUTF8"),this._usingSmtpUtf8=!0),this._envelope.use8BitMime&&this._supportedExtensions.includes("8BITMIME")&&(i.push("BODY=8BITMIME"),this._using8BitMime=!0),this._envelope.size&&this._supportedExtensions.includes("SIZE")&&i.push("SIZE="+this._envelope.size),this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.ret&&i.push("RET="+D.encodeXText(this._envelope.dsn.ret)),this._envelope.dsn.envid&&i.push("ENVID="+D.encodeXText(this._envelope.dsn.envid))),this._sendCommand("MAIL FROM:<"+this._envelope.from+">"+(i.length?" "+i.join(" "):""))}_setDsnEnvelope(e){let t=(e.ret||e.return||"").toString().toUpperCase()||null;if(t)switch(t){case"HDRS":case"HEADERS":t="HDRS";break;case"FULL":case"BODY":t="FULL"}if(t&&!["FULL","HDRS"].includes(t))throw new Error("ret: "+JSON.stringify(t));let i=(e.envid||e.id||"").toString()||null,a=e.notify||null;if(a){"string"==typeof a&&(a=a.split(",")),a=a.map(e=>e.trim().toUpperCase());let e=["NEVER","SUCCESS","FAILURE","DELAY"];if(a.filter(t=>!e.includes(t)).length||a.length>1&&a.includes("NEVER"))throw new Error("notify: "+JSON.stringify(a.join(",")));a=a.join(",")}let s=(e.recipient||e.orcpt||"").toString()||null;return s&&s.indexOf(";")<0&&(s="rfc822;"+s),{ret:t,envid:i,notify:a,orcpt:s}}_getDsnRcptToArgs(){let e=[];return this._envelope.dsn&&this._supportedExtensions.includes("DSN")&&(this._envelope.dsn.notify&&e.push("NOTIFY="+D.encodeXText(this._envelope.dsn.notify)),this._envelope.dsn.orcpt&&e.push("ORCPT="+D.encodeXText(this._envelope.dsn.orcpt))),e.length?" "+e.join(" "):""}_createSendStream(e){let t,i=new Ie;return this.options.lmtp?this._envelope.accepted.forEach((t,i)=>{let a=i===this._envelope.accepted.length-1;this._responseActions.push(i=>{this._actionLMTPStream(t,a,i,e)})}):this._responseActions.push(t=>{this._actionSMTPStream(t,e)}),i.pipe(this._socket,{end:!1}),this.options.debug&&(t=new He,t.on("readable",()=>{let e;for(;e=t.read();)this.logger.debug({tnx:"message"},e.toString("binary").replace(/\r?\n$/,""))}),i.pipe(t)),i.once("end",()=>{this.logger.info({tnx:"message",inByteCount:i.inByteCount,outByteCount:i.outByteCount},"<%s bytes encoded mime message (source size %s bytes)>",i.outByteCount,i.inByteCount)}),i}_actionGreeting(e){clearTimeout(this._greetingTimeout),"220"===e.substr(0,3)?this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name)):this._onError(new Error("Invalid greeting. response="+e),"EPROTOCOL",e,"CONN")}_actionLHLO(e){"2"===e.charAt(0)?this._actionEHLO(e):this._onError(new Error("Invalid LHLO. response="+e),"EPROTOCOL",e,"LHLO")}_actionEHLO(e){let t;if("421"!==e.substr(0,3)){if("2"!==e.charAt(0))return this.options.requireTLS?void this._onError(new Error("EHLO failed but HELO does not support required STARTTLS. response="+e),"ECONNECTION",e,"EHLO"):(this._responseActions.push(this._actionHELO),void this._sendCommand("HELO "+this.name));if(!this.secure&&!this.options.ignoreTLS&&(/[ -]STARTTLS\b/im.test(e)||this.options.requireTLS))return this._sendCommand("STARTTLS"),void this._responseActions.push(this._actionSTARTTLS);/[ -]SMTPUTF8\b/im.test(e)&&this._supportedExtensions.push("SMTPUTF8"),/[ -]DSN\b/im.test(e)&&this._supportedExtensions.push("DSN"),/[ -]8BITMIME\b/im.test(e)&&this._supportedExtensions.push("8BITMIME"),/[ -]PIPELINING\b/im.test(e)&&this._supportedExtensions.push("PIPELINING"),/[ -]AUTH\b/i.test(e)&&(this.allowsAuth=!0),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)PLAIN/i.test(e)&&this._supportedAuth.push("PLAIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)LOGIN/i.test(e)&&this._supportedAuth.push("LOGIN"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)CRAM-MD5/i.test(e)&&this._supportedAuth.push("CRAM-MD5"),/[ -]AUTH(?:(\s+|=)[^\n]*\s+|\s+|=)XOAUTH2/i.test(e)&&this._supportedAuth.push("XOAUTH2"),(t=e.match(/[ -]SIZE(?:[ \t]+(\d+))?/im))&&(this._supportedExtensions.push("SIZE"),this._maxAllowedSize=Number(t[1])||0),this.emit("connect")}else this._onError(new Error("Server terminates connection. response="+e),"ECONNECTION",e,"EHLO")}_actionHELO(e){"2"===e.charAt(0)?(this.allowsAuth=!0,this.emit("connect")):this._onError(new Error("Invalid HELO. response="+e),"EPROTOCOL",e,"HELO")}_actionSTARTTLS(e){if("2"!==e.charAt(0))return this.options.opportunisticTLS?(this.logger.info({tnx:"smtp"},"Failed STARTTLS upgrade, continuing unencrypted"),this.emit("connect")):void this._onError(new Error("Error upgrading connection with STARTTLS"),"ETLS",e,"STARTTLS");this._upgradeConnection((e,t)=>{e?this._onError(new Error("Error initiating TLS - "+(e.message||e)),"ETLS",!1,"STARTTLS"):(this.logger.info({tnx:"smtp"},"Connection upgraded with STARTTLS"),t?this.options.lmtp?(this._responseActions.push(this._actionLHLO),this._sendCommand("LHLO "+this.name)):(this._responseActions.push(this._actionEHLO),this._sendCommand("EHLO "+this.name)):this.emit("connect"))})}_actionAUTH_LOGIN_USER(e,t){/^334[ -]/.test(e)?(this._responseActions.push(e=>{this._actionAUTH_LOGIN_PASS(e,t)}),this._sendCommand(Buffer.from(this._auth.credentials.user+"","utf-8").toString("base64"))):t(this._formatError('Invalid login sequence while waiting for "334 VXNlcm5hbWU6"',"EAUTH",e,"AUTH LOGIN"))}_actionAUTH_CRAM_MD5(e,t){let i=e.match(/^334\s+(.+)$/),a="";if(!i)return t(this._formatError("Invalid login sequence while waiting for server challenge string","EAUTH",e,"AUTH CRAM-MD5"));a=i[1];let s=Buffer.from(a,"base64").toString("ascii"),n=h.createHmac("md5",this._auth.credentials.pass);n.update(s);let o=this._auth.credentials.user+" "+n.digest("hex");this._responseActions.push(e=>{this._actionAUTH_CRAM_MD5_PASS(e,t)}),this._sendCommand(Buffer.from(o).toString("base64"),Buffer.from(this._auth.credentials.user+" /* secret */").toString("base64"))}_actionAUTH_CRAM_MD5_PASS(e,t){if(!e.match(/^235\s+/))return t(this._formatError('Invalid login sequence while waiting for "235"',"EAUTH",e,"AUTH CRAM-MD5"));this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,t(null,!0)}_actionAUTH_LOGIN_PASS(e,t){if(!/^334[ -]/.test(e))return t(this._formatError('Invalid login sequence while waiting for "334 UGFzc3dvcmQ6"',"EAUTH",e,"AUTH LOGIN"));this._responseActions.push(e=>{this._actionAUTHComplete(e,t)}),this._sendCommand(Buffer.from((this._auth.credentials.pass||"").toString(),"utf-8").toString("base64"),Buffer.from("/* secret */","utf-8").toString("base64"))}_actionAUTHComplete(e,t,i){return i||"function"!=typeof t||(i=t,t=!1),"334"===e.substr(0,3)?(this._responseActions.push(e=>{t||"XOAUTH2"!==this._authMethod?this._actionAUTHComplete(e,!0,i):setImmediate(()=>this._handleXOauth2Token(!0,i))}),void this._sendCommand("")):"2"!==e.charAt(0)?(this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),i(this._formatError("Invalid login","EAUTH",e,"AUTH "+this._authMethod))):(this.logger.info({tnx:"smtp",username:this._auth.user,action:"authenticated",method:this._authMethod},"User %s authenticated",JSON.stringify(this._auth.user)),this.authenticated=!0,void i(null,!0))}_actionMAIL(e,t){let i,a;if(2!==Number(e.charAt(0)))return i=this._usingSmtpUtf8&&/^550 /.test(e)&&/[\x80-\uFFFF]/.test(this._envelope.from)?"Internationalized mailbox name not allowed":"Mail command failed",t(this._formatError(i,"EENVELOPE",e,"MAIL FROM"));if(!this._envelope.rcptQueue.length)return t(this._formatError("Can't send mail - no recipients defined","EENVELOPE",!1,"API"));if(this._recipientQueue=[],this._supportedExtensions.includes("PIPELINING"))for(;this._envelope.rcptQueue.length;)a=this._envelope.rcptQueue.shift(),this._recipientQueue.push(a),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+a+">"+this._getDsnRcptToArgs());else a=this._envelope.rcptQueue.shift(),this._recipientQueue.push(a),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+a+">"+this._getDsnRcptToArgs())}_actionRCPT(e,t){let i,a,s=this._recipientQueue.shift();if(2!==Number(e.charAt(0))?(i=this._usingSmtpUtf8&&/^553 /.test(e)&&/[\x80-\uFFFF]/.test(s)?"Internationalized mailbox name not allowed":"Recipient command failed",this._envelope.rejected.push(s),a=this._formatError(i,"EENVELOPE",e,"RCPT TO"),a.recipient=s,this._envelope.rejectedErrors.push(a)):this._envelope.accepted.push(s),this._envelope.rcptQueue.length||this._recipientQueue.length)this._envelope.rcptQueue.length&&(s=this._envelope.rcptQueue.shift(),this._recipientQueue.push(s),this._responseActions.push(e=>{this._actionRCPT(e,t)}),this._sendCommand("RCPT TO:<"+s+">"+this._getDsnRcptToArgs()));else{if(!(this._envelope.rejected.length<this._envelope.to.length))return a=this._formatError("Can't send mail - all recipients were rejected","EENVELOPE",e,"RCPT TO"),a.rejected=this._envelope.rejected,a.rejectedErrors=this._envelope.rejectedErrors,t(a);this._responseActions.push(e=>{this._actionDATA(e,t)}),this._sendCommand("DATA")}}_actionDATA(e,t){if(!/^[23]/.test(e))return t(this._formatError("Data command failed","EENVELOPE",e,"DATA"));let i={accepted:this._envelope.accepted,rejected:this._envelope.rejected};this._envelope.rejectedErrors.length&&(i.rejectedErrors=this._envelope.rejectedErrors),t(null,i)}_actionSMTPStream(e,t){return 2!==Number(e.charAt(0))?t(this._formatError("Message failed","EMESSAGE",e,"DATA")):t(null,e)}_actionLMTPStream(e,t,i,a){let s;if(2!==Number(i.charAt(0))){s=this._formatError("Message failed for recipient "+e,"EMESSAGE",i,"DATA"),s.recipient=e,this._envelope.rejected.push(e),this._envelope.rejectedErrors.push(s);for(let t=0,i=this._envelope.accepted.length;t<i;t++)this._envelope.accepted[t]===e&&this._envelope.accepted.splice(t,1)}if(t)return a(null,i)}_handleXOauth2Token(e,t){this._auth.oauth2.getToken(e,(i,a)=>{if(i)return this.logger.info({tnx:"smtp",username:this._auth.user,action:"authfail",method:this._authMethod},"User %s failed to authenticate",JSON.stringify(this._auth.user)),t(this._formatError(i,"EAUTH",!1,"AUTH XOAUTH2"));this._responseActions.push(i=>{this._actionAUTHComplete(i,e,t)}),this._sendCommand("AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token(a),"AUTH XOAUTH2 "+this._auth.oauth2.buildXOAuth2Token("/* secret */"))})}_isDestroyedMessage(e){if(this._destroyed)return"Cannot "+e+" - smtp connection is already destroyed.";if(this._socket){if(this._socket.destroyed)return"Cannot "+e+" - smtp connection socket is already destroyed.";if(!this._socket.writable)return"Cannot "+e+" - smtp connection socket is already half-closed."}}_getHostname(){let e=d.hostname()||"";return e.indexOf(".")<0&&(e="[127.0.0.1]"),e.match(/^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/)&&(e="["+e+"]"),e}};const ze=p.Stream;var Ne=class extends ze{constructor(e,t){if(super(),this.options=e||{},e&&e.serviceClient){if(!e.privateKey||!e.user)return void setImmediate(()=>this.emit("error",new Error('Options "privateKey" and "user" are required for service account!')));let t=Math.min(Math.max(Number(this.options.serviceRequestTimeout)||0,0),3600);this.options.serviceRequestTimeout=t||300}if(this.logger=D.getLogger({logger:t},{component:this.options.component||"OAuth2"}),this.provisionCallback="function"==typeof this.options.provisionCallback&&this.options.provisionCallback,this.options.accessUrl=this.options.accessUrl||"https://accounts.google.com/o/oauth2/token",this.options.customHeaders=this.options.customHeaders||{},this.options.customParams=this.options.customParams||{},this.accessToken=this.options.accessToken||!1,this.options.expires&&Number(this.options.expires))this.expires=this.options.expires;else{let e=Math.max(Number(this.options.timeout)||0,0);this.expires=e&&Date.now()+1e3*e||0}}getToken(e,t){if(!e&&this.accessToken&&(!this.expires||this.expires>Date.now()))return t(null,this.accessToken);let i=(...e)=>{e[0]?this.logger.error({err:e[0],tnx:"OAUTH2",user:this.options.user,action:"renew"},"Failed generating new Access Token for %s",this.options.user):this.logger.info({tnx:"OAUTH2",user:this.options.user,action:"renew"},"Generated new Access Token for %s",this.options.user),t(...e)};this.provisionCallback?this.provisionCallback(this.options.user,!!e,(e,t,a)=>{!e&&t&&(this.accessToken=t,this.expires=a||0),i(e,t)}):this.generateToken(i)}updateToken(e,t){this.accessToken=e,t=Math.max(Number(t)||0,0),this.expires=t&&Date.now()+1e3*t||0,this.emit("token",{user:this.options.user,accessToken:e||"",expires:this.expires})}generateToken(e){let t,i;if(this.options.serviceClient){let a,s=Math.floor(Date.now()/1e3),n={iss:this.options.serviceClient,scope:this.options.scope||"https://mail.google.com/",sub:this.options.user,aud:this.options.accessUrl,iat:s,exp:s+this.options.serviceRequestTimeout};try{a=this.jwtSignRS256(n)}catch(t){return e(new Error("Can't generate token. Check your auth options"))}t={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:a},i={grant_type:"urn:ietf:params:oauth:grant-type:jwt-bearer",assertion:n}}else{if(!this.options.refreshToken)return e(new Error("Can't create new access token for user"));t={client_id:this.options.clientId||"",client_secret:this.options.clientSecret||"",refresh_token:this.options.refreshToken,grant_type:"refresh_token"},i={client_id:this.options.clientId||"",client_secret:(this.options.clientSecret||"").substr(0,6)+"...",refresh_token:(this.options.refreshToken||"").substr(0,6)+"...",grant_type:"refresh_token"}}Object.keys(this.options.customParams).forEach(e=>{t[e]=this.options.customParams[e],i[e]=this.options.customParams[e]}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"generate"},"Requesting token using: %s",JSON.stringify(i)),this.postRequest(this.options.accessUrl,t,this.options,(t,i)=>{let a;if(t)return e(t);try{a=JSON.parse(i.toString())}catch(t){return e(t)}if(!a||"object"!=typeof a)return this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",(i||"").toString()),e(new Error("Invalid authentication response"));let s={};if(Object.keys(a).forEach(e=>{s[e]="access_token"!==e?a[e]:(a[e]||"").toString().substr(0,6)+"..."}),this.logger.debug({tnx:"OAUTH2",user:this.options.user,action:"post"},"Response: %s",JSON.stringify(s)),a.error){let t=a.error;return a.error_description&&(t+=": "+a.error_description),a.error_uri&&(t+=" ("+a.error_uri+")"),e(new Error(t))}return a.access_token?(this.updateToken(a.access_token,a.expires_in),e(null,this.accessToken)):e(new Error("No access token"))})}buildXOAuth2Token(e){let t=["user="+(this.options.user||""),"auth=Bearer "+(e||this.accessToken),"",""];return Buffer.from(t.join(""),"utf-8").toString("base64")}postRequest(e,t,i,a){let s=!1,n=[],o=0,r=R(e,{method:"post",headers:i.customHeaders,body:t,allowErrorResponse:!0});r.on("readable",()=>{let e;for(;null!==(e=r.read());)n.push(e),o+=e.length}),r.once("error",e=>{if(!s)return s=!0,a(e)}),r.once("end",()=>{if(!s)return s=!0,a(null,Buffer.concat(n,o))})}toBase64URL(e){return"string"==typeof e&&(e=Buffer.from(e)),e.toString("base64").replace(/[=]+/g,"").replace(/\+/g,"-").replace(/\//g,"_")}jwtSignRS256(e){e=['{"alg":"RS256","typ":"JWT"}',JSON.stringify(e)].map(e=>this.toBase64URL(e)).join(".");let t=h.createSign("RSA-SHA256").update(e).sign(this.options.privateKey);return e+"."+this.toBase64URL(t)}};const Me=D.assign;var Be=class extends t{constructor(e){if(super(),this.pool=e,this.options=e.options,this.logger=this.pool.logger,this.options.auth)switch((this.options.auth.type||"").toString().toUpperCase()){case"OAUTH2":{let e=new Ne(this.options.auth,this.logger);e.provisionCallback=this.pool.mailer&&this.pool.mailer.get("oauth2_provision_cb")||e.provisionCallback,this.auth={type:"OAUTH2",user:this.options.auth.user,oauth2:e,method:"XOAUTH2"},e.on("token",e=>this.pool.mailer.emit("token",e)),e.on("error",e=>this.emit("error",e));break}default:if(!this.options.auth.user&&!this.options.auth.pass)break;this.auth={type:(this.options.auth.type||"").toString().toUpperCase()||"LOGIN",user:this.options.auth.user,credentials:{user:this.options.auth.user||"",pass:this.options.auth.pass,options:this.options.auth.options},method:(this.options.auth.method||"").trim().toUpperCase()||this.options.authMethod||!1}}this._connection=!1,this._connected=!1,this.messages=0,this.available=!0}connect(e){this.pool.getSocket(this.options,(t,i)=>{if(t)return e(t);let a=!1,s=this.options;i&&i.connection&&(this.logger.info({tnx:"proxy",remoteAddress:i.connection.remoteAddress,remotePort:i.connection.remotePort,destHost:s.host||"",destPort:s.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",i.connection.remoteAddress,i.connection.remotePort,s.host||"",s.port||""),s=Me(!1,s),Object.keys(i).forEach(e=>{s[e]=i[e]})),this.connection=new Oe(s),this.connection.once("error",t=>{if(this.emit("error",t),!a)return a=!0,e(t)}),this.connection.once("end",()=>{if(this.close(),a)return;a=!0;let t=setTimeout(()=>{if(a)return;let t=new Error("Unexpected socket close");this.connection&&this.connection._socket&&this.connection._socket.upgrading&&(t.code="ETLS"),e(t)},1e3);try{t.unref()}catch(e){}}),this.connection.connect(()=>{if(!a)return this.auth&&(this.connection.allowsAuth||s.forceAuth)?void this.connection.login(this.auth,t=>{if(!a){if(a=!0,t)return this.connection.close(),this.emit("error",t),e(t);this._connected=!0,e(null,!0)}}):(a=!0,this._connected=!0,e(null,!0))})})}send(e,t){if(!this._connected)return this.connect(i=>i?t(i):this.send(e,t));let i=e.message.getEnvelope(),a=e.message.messageId(),s=[].concat(i.to||[]);s.length>3&&s.push("...and "+s.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:a,cid:this.id},"Sending message %s using #%s to <%s>",a,this.id,s.join(", ")),e.data.dsn&&(i.dsn=e.data.dsn),this.connection.send(i,e.message.createReadStream(),(e,s)=>{if(this.messages++,e)return this.connection.close(),this.emit("error",e),t(e);s.envelope={from:i.from,to:i.to},s.messageId=a,setImmediate(()=>{let e;this.messages>=this.options.maxMessages?(e=new Error("Resource exhausted"),e.code="EMAXLIMIT",this.connection.close(),this.emit("error",e)):this.pool._checkRateLimit(()=>{this.available=!0,this.emit("available")})}),t(null,s)})}close(){this._connected=!1,this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.connection&&this.connection.close(),this.emit("close")}},qe={domains:["aol.com"],host:"smtp.aol.com",port:587},Ue={host:"debugmail.io",port:25},Pe={aliases:["Dynect"],host:"smtp.dynect.net",port:25},Re={aliases:["ethereal.email"],host:"smtp.ethereal.email",port:587},Fe={domains:["fastmail.fm"],host:"smtp.fastmail.com",port:465,secure:!0},De={aliases:["Gandi","Gandi Mail"],host:"mail.gandi.net",port:587},Ge={aliases:["Google Mail"],domains:["gmail.com","googlemail.com"],host:"smtp.gmail.com",port:465,secure:!0},$e={host:"smtpout.secureserver.net",port:25},Qe={host:"smtp.asia.secureserver.net",port:25},Ke={host:"smtp.europe.secureserver.net",port:25},Ve={aliases:["Outlook","Outlook.com","Hotmail.com"],domains:["hotmail.com","outlook.com"],host:"smtp-mail.outlook.com",port:587},Xe={aliases:["Me","Mac"],domains:["me.com","mac.com"],host:"smtp.mail.me.com",port:587},Je={host:"mail.infomaniak.com",domains:["ik.me","ikmail.com","etik.com"],port:587},We={port:1025,ignoreTLS:!0},Ye={host:"smtp.mailgun.org",port:465,secure:!0},Ze={host:"in.mailjet.com",port:587},et={host:"mailosaur.io",port:25},tt={host:"smtp.mailtrap.io",port:2525},it={host:"smtp.mandrillapp.com",port:587},at={host:"smtp.naver.com",port:587},st={host:"send.one.com",port:465,secure:!0},nt={aliases:["OMB","openmailbox.org"],host:"smtp.openmailbox.org",port:465,secure:!0},ot={host:"smtp.office365.com",port:587,secure:!1},rt={host:"smtp.ohmysmtp.com",port:587,secure:!1},pt={aliases:["PostmarkApp"],host:"smtp.postmarkapp.com",port:2525},ct={domains:["qq.com"],host:"smtp.qq.com",port:465,secure:!0},lt={aliases:["QQ Enterprise"],domains:["exmail.qq.com"],host:"smtp.exmail.qq.com",port:465,secure:!0},dt={host:"smtpcloud.sohu.com",port:25},mt={host:"smtp.sendgrid.net",port:587},ht={host:"smtp-relay.sendinblue.com",port:587},ut={host:"smtp-pulse.com",port:465,secure:!0},xt={host:"email-smtp.us-east-1.amazonaws.com",port:465,secure:!0},gt={aliases:["SparkPost","SparkPost Mail"],domains:["sparkpost.com"],host:"smtp.sparkpostmail.com",port:587,secure:!1},ft={host:"smtp.tipimail.com",port:587},vt={domains:["yahoo.com"],host:"smtp.mail.yahoo.com",port:465,secure:!0},bt={domains:["yandex.ru"],host:"smtp.yandex.ru",port:465,secure:!0},yt={host:"smtp.zoho.com",port:465,secure:!0,authMethod:"LOGIN"},_t={126:{host:"smtp.126.com",port:465,secure:!0},163:{host:"smtp.163.com",port:465,secure:!0},"1und1":{host:"smtp.1und1.de",port:465,secure:!0,authMethod:"LOGIN"},AOL:qe,DebugMail:Ue,DynectEmail:Pe,Ethereal:Re,FastMail:Fe,GandiMail:De,Gmail:Ge,Godaddy:$e,GodaddyAsia:Qe,GodaddyEurope:Ke,"hot.ee":{host:"mail.hot.ee"},Hotmail:Ve,iCloud:Xe,Infomaniak:Je,"mail.ee":{host:"smtp.mail.ee"},"Mail.ru":{host:"smtp.mail.ru",port:465,secure:!0},Maildev:We,Mailgun:Ye,Mailjet:Ze,Mailosaur:et,Mailtrap:tt,Mandrill:it,Naver:at,One:st,OpenMailBox:nt,Outlook365:ot,OhMySMTP:rt,Postmark:pt,"qiye.aliyun":{host:"smtp.mxhichina.com",port:"465",secure:!0},QQ:ct,QQex:lt,SendCloud:dt,SendGrid:mt,SendinBlue:ht,SendPulse:ut,SES:xt,"SES-US-EAST-1":{host:"email-smtp.us-east-1.amazonaws.com",port:465,secure:!0},"SES-US-WEST-2":{host:"email-smtp.us-west-2.amazonaws.com",port:465,secure:!0},"SES-EU-WEST-1":{host:"email-smtp.eu-west-1.amazonaws.com",port:465,secure:!0},Sparkpost:gt,Tipimail:ft,Yahoo:vt,Yandex:bt,Zoho:yt},wt=f(Object.freeze({__proto__:null,AOL:qe,DebugMail:Ue,DynectEmail:Pe,Ethereal:Re,FastMail:Fe,GandiMail:De,Gmail:Ge,Godaddy:$e,GodaddyAsia:Qe,GodaddyEurope:Ke,Hotmail:Ve,iCloud:Xe,Infomaniak:Je,Maildev:We,Mailgun:Ye,Mailjet:Ze,Mailosaur:et,Mailtrap:tt,Mandrill:it,Naver:at,One:st,OpenMailBox:nt,Outlook365:ot,OhMySMTP:rt,Postmark:pt,QQ:ct,QQex:lt,SendCloud:dt,SendGrid:mt,SendinBlue:ht,SendPulse:ut,SES:xt,Sparkpost:gt,Tipimail:ft,Yahoo:vt,Yandex:bt,Zoho:yt,default:_t}));const kt={};function Et(e){return e.replace(/[^a-zA-Z0-9.-]/g,"").toLowerCase()}function Tt(e){let t=["domains","aliases"],i={};return Object.keys(e).forEach(a=>{t.indexOf(a)<0&&(i[a]=e[a])}),i}Object.keys(wt).forEach(e=>{let t=wt[e];kt[Et(e)]=Tt(t),[].concat(t.aliases||[]).forEach(e=>{kt[Et(e)]=Tt(t)}),[].concat(t.domains||[]).forEach(e=>{kt[Et(e)]=Tt(t)})});var St=function(e){return e=Et(e.split("@").pop()),kt[e]||!1};var At=class extends t{constructor(e){let t;super(),"string"==typeof(e=e||{})&&(e={url:e});let i=e.service;"function"==typeof e.getSocket&&(this.getSocket=e.getSocket),e.url&&(t=D.parseConnectionUrl(e.url),i=i||t.service),this.options=D.assign(!1,e,t,i&&St(i)),this.options.maxConnections=this.options.maxConnections||5,this.options.maxMessages=this.options.maxMessages||100,this.logger=D.getLogger(this.options,{component:this.options.component||"smtp-pool"});let a=new Oe(this.options);this.name="SMTP (pool)",this.version=U.version+"[client:"+a.version+"]",this._rateLimit={counter:0,timeout:null,waiting:[],checkpoint:!1,delta:Number(this.options.rateDelta)||1e3,limit:Number(this.options.rateLimit)||0},this._closed=!1,this._queue=[],this._connections=[],this._connectionCounter=0,this.idling=!0,setImmediate(()=>{this.idling&&this.emit("idle")})}getSocket(e,t){return setImmediate(()=>t(null,!1))}send(e,t){return!this._closed&&(this._queue.push({mail:e,requeueAttempts:0,callback:t}),this.idling&&this._queue.length>=this.options.maxConnections&&(this.idling=!1),setImmediate(()=>this._processMessages()),!0)}close(){let e,t=this._connections.length;if(this._closed=!0,clearTimeout(this._rateLimit.timeout),!t&&!this._queue.length)return;for(let i=t-1;i>=0;i--)this._connections[i]&&this._connections[i].available&&(e=this._connections[i],e.close(),this.logger.info({tnx:"connection",cid:e.id,action:"removed"},"Connection #%s removed",e.id));if(t&&!this._connections.length&&this.logger.debug({tnx:"connection"},"All connections removed"),!this._queue.length)return;let i=()=>{if(!this._queue.length)return void this.logger.debug({tnx:"connection"},"Pending queue entries cleared");let t=this._queue.shift();if(t&&"function"==typeof t.callback)try{t.callback(new Error("Connection pool was closed"))}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}setImmediate(i)};setImmediate(i)}_processMessages(){let e,t,i;if(this._closed)return;if(!this._queue.length)return void(this.idling||(this.idling=!0,this.emit("idle")));for(t=0,i=this._connections.length;t<i;t++)if(this._connections[t].available){e=this._connections[t];break}if(!e&&this._connections.length<this.options.maxConnections&&(e=this._createConnection()),!e)return void(this.idling=!1);!this.idling&&this._queue.length<this.options.maxConnections&&(this.idling=!0,this.emit("idle"));let a=e.queueEntry=this._queue.shift();a.messageId=(e.queueEntry.mail.message.getHeader("message-id")||"").replace(/[<>\s]/g,""),e.available=!1,this.logger.debug({tnx:"pool",cid:e.id,messageId:a.messageId,action:"assign"},"Assigned message <%s> to #%s (%s)",a.messageId,e.id,e.messages+1),this._rateLimit.limit&&(this._rateLimit.counter++,this._rateLimit.checkpoint||(this._rateLimit.checkpoint=Date.now())),e.send(a.mail,(t,i)=>{if(a===e.queueEntry){try{a.callback(t,i)}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}})}_createConnection(){let e=new Be(this);return e.id=++this._connectionCounter,this.logger.info({tnx:"pool",cid:e.id,action:"conection"},"Created new pool resource #%s",e.id),e.on("available",()=>{this.logger.debug({tnx:"connection",cid:e.id,action:"available"},"Connection #%s became available",e.id),this._closed?this.close():this._processMessages()}),e.once("error",t=>{if("EMAXLIMIT"!==t.code?this.logger.error({err:t,tnx:"pool",cid:e.id},"Pool Error for #%s: %s",e.id,t.message):this.logger.debug({tnx:"pool",cid:e.id,action:"maxlimit"},"Max messages limit exchausted for #%s",e.id),e.queueEntry){try{e.queueEntry.callback(t)}catch(t){this.logger.error({err:t,tnx:"callback",cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}this._removeConnection(e),this._continueProcessing()}),e.once("close",()=>{this.logger.info({tnx:"connection",cid:e.id,action:"closed"},"Connection #%s was closed",e.id),this._removeConnection(e),e.queueEntry?setTimeout(()=>{e.queueEntry&&(this._shouldRequeuOnConnectionClose(e.queueEntry)?this._requeueEntryOnConnectionClose(e):this._failDeliveryOnConnectionClose(e)),this._continueProcessing()},50):this._continueProcessing()}),this._connections.push(e),e}_shouldRequeuOnConnectionClose(e){return void 0===this.options.maxRequeues||this.options.maxRequeues<0||e.requeueAttempts<this.options.maxRequeues}_failDeliveryOnConnectionClose(e){if(e.queueEntry&&e.queueEntry.callback){try{e.queueEntry.callback(new Error("Reached maximum number of retries after connection was closed"))}catch(t){this.logger.error({err:t,tnx:"callback",messageId:e.queueEntry.messageId,cid:e.id},"Callback error for #%s: %s",e.id,t.message)}e.queueEntry=!1}}_requeueEntryOnConnectionClose(e){e.queueEntry.requeueAttempts=e.queueEntry.requeueAttempts+1,this.logger.debug({tnx:"pool",cid:e.id,messageId:e.queueEntry.messageId,action:"requeue"},"Re-queued message <%s> for #%s. Attempt: #%s",e.queueEntry.messageId,e.id,e.queueEntry.requeueAttempts),this._queue.unshift(e.queueEntry),e.queueEntry=!1}_continueProcessing(){this._closed?this.close():setTimeout(()=>this._processMessages(),100)}_removeConnection(e){let t=this._connections.indexOf(e);-1!==t&&this._connections.splice(t,1)}_checkRateLimit(e){if(!this._rateLimit.limit)return e();let t=Date.now();return this._rateLimit.counter<this._rateLimit.limit?e():(this._rateLimit.waiting.push(e),this._rateLimit.checkpoint<=t-this._rateLimit.delta?this._clearRateLimit():void(this._rateLimit.timeout||(this._rateLimit.timeout=setTimeout(()=>this._clearRateLimit(),this._rateLimit.delta-(t-this._rateLimit.checkpoint)),this._rateLimit.checkpoint=t)))}_clearRateLimit(){for(clearTimeout(this._rateLimit.timeout),this._rateLimit.timeout=null,this._rateLimit.counter=0,this._rateLimit.checkpoint=!1;this._rateLimit.waiting.length;){let e=this._rateLimit.waiting.shift();setImmediate(e)}}isIdle(){return this.idling}verify(e){let t;e||(t=new Promise((t,i)=>{e=D.callbackPromise(t,i)}));let i=new Be(this).auth;return this.getSocket(this.options,(t,a)=>{if(t)return e(t);let s=this.options;a&&a.connection&&(this.logger.info({tnx:"proxy",remoteAddress:a.connection.remoteAddress,remotePort:a.connection.remotePort,destHost:s.host||"",destPort:s.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",a.connection.remoteAddress,a.connection.remotePort,s.host||"",s.port||""),s=D.assign(!1,s),Object.keys(a).forEach(e=>{s[e]=a[e]}));let n=new Oe(s),o=!1;n.once("error",t=>{if(!o)return o=!0,n.close(),e(t)}),n.once("end",()=>{if(!o)return o=!0,e(new Error("Connection closed"))});let r=()=>{if(!o)return o=!0,n.quit(),e(null,!0)};n.connect(()=>{o||(i&&(n.allowsAuth||s.forceAuth)?n.login(i,t=>{if(!o)return t?(o=!0,n.close(),e(t)):void r()}):r())})}),t}};var Ct=class extends t{constructor(e){let t;super(),"string"==typeof(e=e||{})&&(e={url:e});let i=e.service;"function"==typeof e.getSocket&&(this.getSocket=e.getSocket),e.url&&(t=D.parseConnectionUrl(e.url),i=i||t.service),this.options=D.assign(!1,e,t,i&&St(i)),this.logger=D.getLogger(this.options,{component:this.options.component||"smtp-transport"});let a=new Oe(this.options);this.name="SMTP",this.version=U.version+"[client:"+a.version+"]",this.options.auth&&(this.auth=this.getAuth({}))}getSocket(e,t){return setImmediate(()=>t(null,!1))}getAuth(e){if(!e)return this.auth;let t=!1,i={};if(this.options.auth&&"object"==typeof this.options.auth&&Object.keys(this.options.auth).forEach(e=>{t=!0,i[e]=this.options.auth[e]}),e&&"object"==typeof e&&Object.keys(e).forEach(a=>{t=!0,i[a]=e[a]}),!t)return!1;switch((i.type||"").toString().toUpperCase()){case"OAUTH2":{if(!i.service&&!i.user)return!1;let e=new Ne(i,this.logger);return e.provisionCallback=this.mailer&&this.mailer.get("oauth2_provision_cb")||e.provisionCallback,e.on("token",e=>this.mailer.emit("token",e)),e.on("error",e=>this.emit("error",e)),{type:"OAUTH2",user:i.user,oauth2:e,method:"XOAUTH2"}}default:return{type:(i.type||"").toString().toUpperCase()||"LOGIN",user:i.user,credentials:{user:i.user||"",pass:i.pass,options:i.options},method:(i.method||"").trim().toUpperCase()||this.options.authMethod||!1}}}send(e,t){this.getSocket(this.options,(i,a)=>{if(i)return t(i);let s=!1,n=this.options;a&&a.connection&&(this.logger.info({tnx:"proxy",remoteAddress:a.connection.remoteAddress,remotePort:a.connection.remotePort,destHost:n.host||"",destPort:n.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",a.connection.remoteAddress,a.connection.remotePort,n.host||"",n.port||""),n=D.assign(!1,n),Object.keys(a).forEach(e=>{n[e]=a[e]}));let o=new Oe(n);o.once("error",e=>{if(!s)return s=!0,o.close(),t(e)}),o.once("end",()=>{if(s)return;let e=setTimeout(()=>{if(s)return;s=!0;let e=new Error("Unexpected socket close");o&&o._socket&&o._socket.upgrading&&(e.code="ETLS"),t(e)},1e3);try{e.unref()}catch(e){}});let r=()=>{let i=e.message.getEnvelope(),a=e.message.messageId(),n=[].concat(i.to||[]);n.length>3&&n.push("...and "+n.splice(2).length+" more"),e.data.dsn&&(i.dsn=e.data.dsn),this.logger.info({tnx:"send",messageId:a},"Sending message %s to <%s>",a,n.join(", ")),o.send(i,e.message.createReadStream(),(e,n)=>{if(s=!0,o.close(),e)return this.logger.error({err:e,tnx:"send"},"Send error for %s: %s",a,e.message),t(e);n.envelope={from:i.from,to:i.to},n.messageId=a;try{return t(null,n)}catch(e){this.logger.error({err:e,tnx:"callback"},"Callback error for %s: %s",a,e.message)}})};o.connect(()=>{if(s)return;let i=this.getAuth(e.data.auth);i&&(o.allowsAuth||n.forceAuth)?o.login(i,e=>{if(i&&i!==this.auth&&i.oauth2&&i.oauth2.removeAllListeners(),!s)return e?(s=!0,o.close(),t(e)):void r()}):r()})})}verify(e){let t;return e||(t=new Promise((t,i)=>{e=D.callbackPromise(t,i)})),this.getSocket(this.options,(t,i)=>{if(t)return e(t);let a=this.options;i&&i.connection&&(this.logger.info({tnx:"proxy",remoteAddress:i.connection.remoteAddress,remotePort:i.connection.remotePort,destHost:a.host||"",destPort:a.port||"",action:"connected"},"Using proxied socket from %s:%s to %s:%s",i.connection.remoteAddress,i.connection.remotePort,a.host||"",a.port||""),a=D.assign(!1,a),Object.keys(i).forEach(e=>{a[e]=i[e]}));let s=new Oe(a),n=!1;s.once("error",t=>{if(!n)return n=!0,s.close(),e(t)}),s.once("end",()=>{if(!n)return n=!0,e(new Error("Connection closed"))});let o=()=>{if(!n)return n=!0,s.quit(),e(null,!0)};s.connect(()=>{if(n)return;let t=this.getAuth({});t&&(s.allowsAuth||a.forceAuth)?s.login(t,t=>{if(!n)return t?(n=!0,s.close(),e(t)):void o()}):o()})}),t}close(){this.auth&&this.auth.oauth2&&this.auth.oauth2.removeAllListeners(),this.emit("close")}};const jt=g.spawn;var It=class{constructor(e){e=e||{},this._spawn=jt,this.options=e||{},this.name="Sendmail",this.version=U.version,this.path="sendmail",this.args=!1,this.winbreak=!1,this.logger=D.getLogger(this.options,{component:this.options.component||"sendmail"}),e&&("string"==typeof e?this.path=e:"object"==typeof e&&(e.path&&(this.path=e.path),Array.isArray(e.args)&&(this.args=e.args),this.winbreak=["win","windows","dos","\r\n"].includes((e.newline||"").toString().toLowerCase())))}send(e,t){e.message.keepBcc=!0;let i,a,s,n=e.data.envelope||e.message.getEnvelope(),o=e.message.messageId();if([].concat(n.from||[]).concat(n.to||[]).some(e=>/^-/.test(e)))return t(new Error("Can not send mail. Invalid envelope addresses."));i=this.args?["-i"].concat(this.args).concat(n.to):["-i"].concat(n.from?["-f",n.from]:[]).concat(n.to);let r=i=>{if(!s)return s=!0,"function"==typeof t?i?t(i):t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:o,response:"Messages queued for delivery"}):void 0};try{a=this._spawn(this.path,i)}catch(e){return this.logger.error({err:e,tnx:"spawn",messageId:o},"Error occurred while spawning sendmail. %s",e.message),r(e)}if(!a)return r(new Error("sendmail was not found"));{a.on("error",e=>{this.logger.error({err:e,tnx:"spawn",messageId:o},"Error occurred when sending message %s. %s",o,e.message),r(e)}),a.once("exit",e=>{if(!e)return r();let t;t=127===e?new Error("Sendmail command not found, process exited with code "+e):new Error("Sendmail exited with code "+e),this.logger.error({err:t,tnx:"stdin",messageId:o},"Error sending message %s to sendmail. %s",o,t.message),r(t)}),a.once("close",r),a.stdin.on("error",e=>{this.logger.error({err:e,tnx:"stdin",messageId:o},"Error occurred when piping message %s to sendmail. %s",o,e.message),r(e)});let t=[].concat(n.to||[]);t.length>3&&t.push("...and "+t.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:o},"Sending message %s to <%s>",o,t.join(", "));let i=e.message.createReadStream();i.once("error",e=>{this.logger.error({err:e,tnx:"stdin",messageId:o},"Error occurred when generating message %s. %s",o,e.message),a.kill("SIGINT"),r(e)}),i.pipe(a.stdin)}}};var Lt=class{constructor(e){e=e||{},this.options=e||{},this.name="StreamTransport",this.version=U.version,this.logger=D.getLogger(this.options,{component:this.options.component||"stream-transport"}),this.winbreak=["win","windows","dos","\r\n"].includes((e.newline||"").toString().toLowerCase())}send(e,t){e.message.keepBcc=!0;let i=e.data.envelope||e.message.getEnvelope(),a=e.message.messageId(),s=[].concat(i.to||[]);s.length>3&&s.push("...and "+s.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:a},"Sending message %s to <%s> using %s line breaks",a,s.join(", "),this.winbreak?"<CR><LF>":"<LF>"),setImmediate(()=>{let i;try{i=e.message.createReadStream()}catch(e){return this.logger.error({err:e,tnx:"send",messageId:a},"Creating send stream failed for %s. %s",a,e.message),t(e)}if(!this.options.buffer)return i.once("error",e=>{this.logger.error({err:e,tnx:"send",messageId:a},"Failed creating message for %s. %s",a,e.message)}),t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:a,message:i});let s=[],n=0;i.on("readable",()=>{let e;for(;null!==(e=i.read());)s.push(e),n+=e.length}),i.once("error",e=>(this.logger.error({err:e,tnx:"send",messageId:a},"Failed creating message for %s. %s",a,e.message),t(e))),i.on("end",()=>t(null,{envelope:e.data.envelope||e.message.getEnvelope(),messageId:a,message:Buffer.concat(s,n)}))})}};var Ht=class{constructor(e){e=e||{},this.options=e||{},this.name="JSONTransport",this.version=U.version,this.logger=D.getLogger(this.options,{component:this.options.component||"json-transport"})}send(e,t){e.message.keepBcc=!0;let i=e.data.envelope||e.message.getEnvelope(),a=e.message.messageId(),s=[].concat(i.to||[]);s.length>3&&s.push("...and "+s.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:a},"Composing JSON structure of %s to <%s>",a,s.join(", ")),setImmediate(()=>{e.normalize((e,s)=>e?(this.logger.error({err:e,tnx:"send",messageId:a},"Failed building JSON structure for %s. %s",a,e.message),t(e)):(delete s.envelope,delete s.normalizedHeaders,t(null,{envelope:i,messageId:a,message:this.options.skipEncoding?s:JSON.stringify(s)})))})}};var Ot=class extends t{constructor(e){super(),e=e||{},this.options=e||{},this.ses=this.options.SES,this.name="SESTransport",this.version=U.version,this.logger=D.getLogger(this.options,{component:this.options.component||"ses-transport"}),this.maxConnections=Number(this.options.maxConnections)||1/0,this.connections=0,this.sendingRate=Number(this.options.sendingRate)||1/0,this.sendingRateTTL=null,this.rateInterval=1e3,this.rateMessages=[],this.pending=[],this.idling=!0,setImmediate(()=>{this.idling&&this.emit("idle")})}send(e,t){return this.connections>=this.maxConnections?(this.idling=!1,this.pending.push({mail:e,callback:t})):this._checkSendingRate()?void this._send(e,(...e)=>{setImmediate(()=>t(...e)),this._sent()}):(this.idling=!1,this.pending.push({mail:e,callback:t}))}_checkRatedQueue(){if(this.connections>=this.maxConnections||!this._checkSendingRate())return;if(!this.pending.length)return void(this.idling||(this.idling=!0,this.emit("idle")));let e=this.pending.shift();this._send(e.mail,(...t)=>{setImmediate(()=>e.callback(...t)),this._sent()})}_checkSendingRate(){clearTimeout(this.sendingRateTTL);let e=Date.now(),t=!1;for(let i=this.rateMessages.length-1;i>=0;i--)this.rateMessages[i].ts>=e-this.rateInterval&&(!t||this.rateMessages[i].ts<t)&&(t=this.rateMessages[i].ts),this.rateMessages[i].ts<e-this.rateInterval&&!this.rateMessages[i].pending&&this.rateMessages.splice(i,1);if(this.rateMessages.length<this.sendingRate)return!0;let i=Math.max(t+1001,e+20);this.sendingRateTTL=setTimeout(()=>this._checkRatedQueue(),e-i);try{this.sendingRateTTL.unref()}catch(e){}return!1}_sent(){this.connections--,this._checkRatedQueue()}isIdle(){return this.idling}_send(e,t){let i={ts:Date.now(),pending:!0};this.connections++,this.rateMessages.push(i);let a=e.data.envelope||e.message.getEnvelope(),s=e.message.messageId(),n=[].concat(a.to||[]);n.length>3&&n.push("...and "+n.splice(2).length+" more"),this.logger.info({tnx:"send",messageId:s},"Sending message %s to <%s>",s,n.join(", "));setImmediate(()=>(t=>{e.data._dkim||(e.data._dkim={}),e.data._dkim.skipFields&&"string"==typeof e.data._dkim.skipFields?e.data._dkim.skipFields+=":date:message-id":e.data._dkim.skipFields="date:message-id";let i=e.message.createReadStream(),a=i.pipe(new ce),s=[],n=0;a.on("readable",()=>{let e;for(;null!==(e=a.read());)s.push(e),n+=e.length}),i.once("error",e=>a.emit("error",e)),a.once("error",e=>{t(e)}),a.once("end",()=>t(null,Buffer.concat(s,n)))})((n,o)=>{if(n)return this.logger.error({err:n,tnx:"send",messageId:s},"Failed creating message for %s. %s",s,n.message),i.pending=!1,t(n);let r={RawMessage:{Data:o},Source:a.from,Destinations:a.to};Object.keys(e.data.ses||{}).forEach(t=>{r[t]=e.data.ses[t]});let p=(this.ses.aws?this.ses.ses:this.ses)||{},c=this.ses.aws||{};var l;l=(e,n)=>{let l;!e&&n||(n="us-east-1"),l="function"==typeof p.send&&c.SendRawEmailCommand?p.send(new c.SendRawEmailCommand(r)):p.sendRawEmail(r).promise(),l.then(e=>{"us-east-1"===n&&(n="email"),i.pending=!1,t(null,{envelope:{from:a.from,to:a.to},messageId:"<"+e.MessageId+(/@/.test(e.MessageId)?"":"@"+n+".amazonses.com")+">",response:e.MessageId,raw:o})}).catch(e=>{this.logger.error({err:e,tnx:"send"},"Send error for %s: %s",s,e.message),i.pending=!1,t(e)})},p.config&&"function"==typeof p.config.region?p.config.region().then(e=>l(null,e)).catch(e=>l(e)):l(null,p.config&&p.config.region||"us-east-1")}))}verify(e){let t,i=(this.ses.aws?this.ses.ses:this.ses)||{},a=this.ses.aws||{};const s={RawMessage:{Data:"From: invalid@invalid\r\nTo: invalid@invalid\r\n Subject: Invalid\r\n\r\nInvalid"},Source:"invalid@invalid",Destinations:["invalid@invalid"]};e||(t=new Promise((t,i)=>{e=D.callbackPromise(t,i)}));const n=t=>t&&"InvalidParameterValue"!==(t.code||t.Code)?e(t):e(null,!0);return"function"==typeof i.send&&a.SendRawEmailCommand?(s.RawMessage.Data=Buffer.from(s.RawMessage.Data),i.send(new a.SendRawEmailCommand(s),n)):i.sendRawEmail(s,n),t}};const zt=(process.env.ETHEREAL_API||"https://api.nodemailer.com").replace(/\/+$/,""),Nt=(process.env.ETHEREAL_WEB||"https://ethereal.email").replace(/\/+$/,""),Mt=["true","yes","y","1"].includes((process.env.ETHEREAL_CACHE||"yes").toString().trim().toLowerCase());let Bt=!1;var qt={createTransport:function(e,t){let i,a,s;return("object"==typeof e&&"function"!=typeof e.send||"string"==typeof e&&/^(smtps?|direct):/i.test(e))&&(a=(i="string"==typeof e?e:e.url)?D.parseConnectionUrl(i):e,e=a.pool?new At(a):a.sendmail?new It(a):a.streamTransport?new Lt(a):a.jsonTransport?new Ht(a):a.SES?new Ot(a):new Ct(a)),s=new Ce(e,a,t),s},createTestAccount:function(e,t){let i;if(t||"function"!=typeof e||(t=e,e=!1),t||(i=new Promise((e,i)=>{t=D.callbackPromise(e,i)})),Mt&&Bt)return setImmediate(()=>t(null,Bt)),i;let a=[],s=0,n=R((e=e||zt)+"/user",{contentType:"application/json",method:"POST",body:Buffer.from(JSON.stringify({requestor:U.name,version:U.version}))});return n.on("readable",()=>{let e;for(;null!==(e=n.read());)a.push(e),s+=e.length}),n.once("error",e=>t(e)),n.once("end",()=>{let e,i,n=Buffer.concat(a,s);try{e=JSON.parse(n.toString())}catch(e){i=e}return i?t(i):"success"!==e.status||e.error?t(new Error(e.error||"Request failed")):(delete e.status,Bt=e,void t(null,Bt))}),i},getTestMessageUrl:function(e){if(!e||!e.response)return!1;let t=new Map;return e.response.replace(/\[([^\]]+)\]$/,(e,i)=>{i.replace(/\b([A-Z0-9]+)=([^\s]+)/g,(e,i,a)=>{t.set(i,a)})}),!(!t.has("STATUS")||!t.has("MSGID"))&&(Bt.web||Nt)+"/message/"+t.get("MSGID")}};module.exports=qt;
