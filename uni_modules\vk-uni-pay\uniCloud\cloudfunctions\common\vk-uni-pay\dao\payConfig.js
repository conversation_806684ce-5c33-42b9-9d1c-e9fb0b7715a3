/**
 * 请勿修改此处代码，因为插件更新后此处代码会被覆盖。
 * 作者：VK
 * 发布于：2021-07-06
 */
const dbName = {
	payConfig: "vk-pay-config", // 数据库表名 - 商户支付配置表
};

const db = uniCloud.database();
const _ = db.command;
var dao = {};

/**
 * 获取 - 商户支付配置
 * let payConfig = await dao.payConfig.findById(pid);
 */
dao.findById = async (pid) => {
	let res = await db.collection(dbName.payConfig).doc(pid).get();
	if (res.data && res.data.length > 0) {
		return res.data[0];
	} else {
		return null;
	}
};

module.exports = dao;
