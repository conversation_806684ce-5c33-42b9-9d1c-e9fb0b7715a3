<template>
	<view class="page-content">
		<t-navbar :title="title"></t-navbar>
		<view class="content" v-if="info" v-html="info.content"></view>
		<view class="time" v-if="info">协议更新时间：{{updateTime}}</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	export default {
		data() {
			return {
				info: null
			}
		},
		computed: {
			title() {
				if (!this.info) return '详情信息'
				return this.info.title
			},
			updateTime() {
				if (!this.info) return
				return dayjs(this.info.update_time).format('YYYY-MM-DD HH:mm:ss')
			},
		},
		onLoad(options) {
			this.getData(options.type)
		},
		methods: {
			async getData(type) {
				try {
					let data = await vk.callFunction({
						url: 'client/public/pub/agreement',
						title: '请求中...',
						data: {
							type,
						},
					});
					this.info = data.data
				} catch (error) {
					console.log(error);
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.page-content {
		padding: 30rpx;
		background-color: #fff;
	}

	.time {
		font-size: 24rpx;
		color: #8a8a8a;
		margin-top: 20rpx;
		text-align: end;
	}

	.content {
		font-size: 28rpx;
	}
</style>