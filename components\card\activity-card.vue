<template>
  <view class="activity-card" v-if="activity" :class="[animationClass]" :style="customStyle">
    <view @click="toDetail" class="card-content">
      <!-- Card Header -->
      <view class="card-header">
        <view class="card-title">
          <text class="title-text">{{ activity.name }}</text>
          <view class="status-icons">
            <image v-if="activity.club_id" class="status-icon" :src="`${iconUrl}theme_club.svg`" mode="aspectFill" />
          </view>
        </view>

        <view class="distance-info" v-if="hasDistance">
          <text class="distance-label">距您</text>
          <text class="distance-value">{{ formattedDistance }}km</text>
        </view>
      </view>

      <!-- Activity Info -->
      <view class="activity-info">
        <view class="info-item">
          <image class="info-icon" :src="`${iconUrl}location-line.svg`" mode="heightFix" />
          <text class="info-text">地点：{{ siteName }}</text>
        </view>

        <view class="info-item">
          <image class="info-icon" :src="`${iconUrl}time-line.svg`" mode="heightFix" />
          <text class="info-text">时间：{{ timeArea }}</text>
          <wd-tag v-if="formattedDateEasy" :type="formattedDateEasy.type" custom-style="margin-left:30rpx;flex-shrink:0">{{
            formattedDateEasy.text
          }}</wd-tag>
        </view>

        <view class="info-grid">
          <view class="info-item">
            <image class="info-icon" :src="`${iconUrl}group-3-line.svg`" mode="heightFix" />
            <text class="info-text">
              人数：<text :class="{ full: activity.join_list?.length === activity.max_num }">{{ participantCount }}</text>
            </text>
          </view>

          <view class="info-item">
            <image class="info-icon" :src="`${iconUrl}currency-cny-line.svg`" mode="heightFix" />
            <text class="info-text">花费：{{ costType }}</text>
          </view>
        </view>
      </view>

      <!-- Activity Tags -->
      <view class="activity-tags" v-if="hasActiveTags">
        <wd-tag v-if="levelText" type="primary">{{ levelText }}</wd-tag>
        <wd-tag v-if="minNumOpenText" type="primary">{{ minNumOpenText }}</wd-tag>
        <wd-tag v-if="helpJoinText" type="primary">{{ helpJoinText }}</wd-tag>
        <wd-tag v-if="applyText" type="primary">{{ applyText }}</wd-tag>
      </view>
    </view>

    <!-- Card Footer -->
    <view class="card-footer" v-if="displayParticipants.length > 0">
      <view class="participant-avatars">
        <image
          v-for="(participant, index) in displayParticipants"
          :key="participant.user_info._id || index"
          class="avatar"
          :src="participant.user_info.avatar"
          mode="aspectFill"
        />
      </view>

      <!-- <view class="action-buttons" v-if="!isActivityEnded">
        <wd-button v-if="activity.my_join?.state === 'applying'" type="danger" @click="handleExit" class="action-btn"> 取消申请 </wd-button>
        <wd-button v-else-if="activity.my_join?.state === 'joined'" type="danger" @click="handleExit" class="action-btn"> 退出活动 </wd-button>
      </view> -->
    </view>
  </view>
</template>

<script>
import dayjs from "dayjs";

export default {
  name: "ActivityCard",

  props: {
    value: {
      type: Object,
      required: true,
      default: () => ({}),
    },
    customStyle: {
      type: Object,
      default: () => ({}),
    },
    animateIndex: {
      type: Number,
      default: 0,
    },
    animateNumber: {
      type: Number,
      default: 20,
    },
  },

  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      userInfo: vk.getVuex("$user.userInfo"),
    };
  },

  computed: {
    // 活动数据别名，提高可读性
    activity() {
      return this.value || null;
    },

    // 动画类名
    animationClass() {
      const index = this.animateIndex + 1;
      const number = this.animateNumber;
      const result = index % number;

      if (result === 0) {
        return `animation${number}`;
      } else {
        return `animation${result + 1}`;
      }
    },

    // 距离信息
    hasDistance() {
      return this.activity.distance !== undefined && this.activity.distance !== null;
    },

    formattedDistance() {
      return this.activity.distance?.toFixed(2) || "0.00";
    },

    // 费用类型
    costType() {
      if (!this.activity.cost_type) return "无";

      switch (this.activity.cost_type) {
        case "average":
          return `${this.activity.cost}/AA`;
        case "fixed":
          return `${this.activity.cost}/人`;
        case "none":
          return "免费";
        default:
          return "无";
      }
    },

    // 水平要求标签
    levelText() {
      if (!this.activity.level) return "";
      return `${this.activity.level.toFixed(1)}+`;
    },

    // 最少人数标签
    minNumOpenText() {
      if (!this.activity.min_num) return "";
      return `${this.activity.min_num}人开`;
    },

    // 代接标签
    helpJoinText() {
      return this.activity.help_join ? "可代接" : "";
    },

    // 审核标签
    applyText() {
      return this.activity.apply ? "需审核" : "";
    },

    // 是否有活跃标签
    hasActiveTags() {
      return this.levelText || this.minNumOpenText || this.helpJoinText || this.applyText;
    },

    // 场地名称
    siteName() {
      if (!this.activity.site_info && !this.activity.club_site) {
        return "无场地信息";
      }

      if (this.activity.club_id) {
        return `${this.activity.club_info?.name || ""} ${this.activity.club_site?.name || ""}`.trim();
      } else {
        return this.activity.site_info?.name || "无";
      }
    },

    // 时间区域
    timeArea() {
      if (!this.activity) return "无";

      const startDate = dayjs(this.activity.start).format("YYYY-MM-DD");
      const endDate = dayjs(this.activity.end).format("YYYY-MM-DD");
      const startTime = dayjs(this.activity.start).format("HH:mm");
      const endTime = dayjs(this.activity.end).format("HH:mm");
      const startDay = dayjs(this.activity.start).format("MM-DD");
      const endDay = dayjs(this.activity.end).format("MM-DD");

      if (startDate === endDate) {
        return `${startDay} ${startTime}~${endTime}`;
      } else {
        return `${startDay} ${startTime} ~ ${endDay} ${endTime}`;
      }
    },

    // 格式化简易日期 今天明天后天
    formattedDateEasy() {
      if (!this.activity) return null;
      let now = dayjs().format("YYYY-MM-DD");
      let startDay = dayjs(this.activity.start).format("YYYY-MM-DD");
      let day = dayjs(startDay).diff(now, "day");

      if (day === 0) return { type: "danger", text: "今天" };
      else if (day === 1) return { type: "warning", text: "明天" };
      else if (day === 2) return { type: "primary", text: "后天" };
      return null;
    },

    // 参与人数
    participantCount() {
      const joinedCount = this.activity.join_list?.length || 0;
      const maxCount = this.activity.max_num || "无限制";
      return `${joinedCount}/${maxCount}`;
    },

    // 显示的参与者（最多7个）
    displayParticipants() {
      return this.activity.join_list?.slice(0, 7) || [];
    },

    // 活动是否已结束
    isActivityEnded() {
      return dayjs(this.activity.end).isBefore(dayjs());
    },
  },
  mounted() {},

  methods: {
    // 跳转到活动详情
    toDetail() {
      if (!this.activity._id) {
        console.error("Activity ID is missing");
        return;
      }
      vk.navigateTo(`/pages/activity/detail?id=${this.activity._id}`);
    },

    // 加入活动
    async joinActivity() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/join_state/kh/join",
          title: "请求中...",
          data: {
            id: this.activity._id,
            type: "myself",
          },
        });

        vk.toast(data.msg, "none", true, () => {
          uni.$emit("activity-refresh");
        });

        // 如果不是创建者，请求订阅消息
        if (this.activity.creator_id !== this.userInfo._id) {
          this.requestSubscribeMessage();
        }
      } catch (error) {
        console.error("Failed to join activity:", error);
        vk.toast("加入活动失败");
      }
    },

    // 请求订阅消息
    requestSubscribeMessage() {
      const tmplIds = this.activity.apply
        ? ["kunnNz3xL48JH06YDvtvU4dcu1iLcueEO5T5mmYlvas", "E8nAcoeuMnAMhwWwnoMFrV9IXZ-Uc2A2J-zEKjqf8Gk"]
        : ["E8nAcoeuMnAMhwWwnoMFrV9IXZ-Uc2A2J-zEKjqf8Gk"];

      uni.requestSubscribeMessage({
        tmplIds,
        success(res) {
          console.log("Subscribe message success:", res);
        },
        fail(error) {
          console.error("Subscribe message failed:", error);
        },
      });
    },

    // 退出活动
    async handleExit() {
      const isJoined = this.activity.my_join?.state === "joined";
      const actionText = isJoined ? "退出" : "取消申请";
      const confirmText = `是否要${actionText}活动？`;

      try {
        uni.showModal({
          title: "提示",
          content: confirmText,
          success: async (res) => {
            if (res.confirm) {
              await this.exitActivity();
            }
          },
        });
      } catch (error) {
        console.error("Failed to show exit modal:", error);
      }
    },

    // 执行退出活动
    async exitActivity() {
      try {
        const data = await vk.callFunction({
          url: "client/activity/join_state/kh/exit",
          title: "请求中...",
          data: {
            id: this.activity.my_join._id,
            activity_id: this.activity._id,
          },
        });

        vk.toast(data.msg, "success", true, () => {
          uni.$emit("activity-refresh");
        });
      } catch (error) {
        console.error("Failed to exit activity:", error);
        vk.toast("操作失败");
      }
    },
  },
};
</script>

<style scoped lang="scss">
@import "@/static/scss/animation.scss";

.activity-card {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
  border-radius: 28rpx;
  background: #fff;
  margin-bottom: 30rpx;
  opacity: 0;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
  }
}

.card-content {
  flex: 1;
}

// Card Header
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  border-bottom: 2rpx solid #f0f0f0;
  padding-bottom: 20rpx;
  margin-bottom: 20rpx;
}

.card-title {
  display: flex;
  align-items: flex-start;
  flex: 1;

  .title-text {
    flex: 1;
    font-size: 40rpx;
    color: #333;
    font-family: usic, -apple-system, BlinkMacSystemFont, sans-serif;
    font-weight: bold;
    line-height: 1.3;
    margin-right: 20rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 12rpx;

  .status-icon {
    width: 36rpx;
    height: 36rpx;
    flex-shrink: 0;
  }
}

.distance-info {
  display: flex;
  align-items: center;
  font-size: 26rpx;
  color: #000;
  font-weight: 500;
  margin-left: 20rpx;
  flex-shrink: 0;

  .distance-label {
    margin-right: 8rpx;
  }

  .distance-value {
    font-weight: bold;
  }
}

// Activity Info
.activity-info {
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;

  &:last-child {
    margin-bottom: 0;
  }

  .info-icon {
    width: 40rpx;
    height: 40rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
  }

  .info-text {
    font-size: 28rpx;
    color: #333;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;

    .full {
      color: #ff3030;
    }
  }
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20rpx;
  margin-top: 16rpx;

  .info-item {
    margin-bottom: 0;
  }
}

// Activity Tags
.activity-tags {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 20rpx;
}

// Card Footer
.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 2rpx solid #f0f0f0;
}

.participant-avatars {
  display: flex;
  align-items: center;

  .avatar {
    width: 68rpx;
    height: 68rpx;
    border-radius: 50%;
    border: 4rpx solid #fff;
    margin-right: -20rpx;

    &:last-child {
      margin-right: 0;
    }

    &:first-child {
      z-index: 7;
    }

    &:nth-child(2) {
      z-index: 6;
    }

    &:nth-child(3) {
      z-index: 5;
    }

    &:nth-child(4) {
      z-index: 4;
    }

    &:nth-child(5) {
      z-index: 3;
    }

    &:nth-child(6) {
      z-index: 2;
    }

    &:nth-child(7) {
      z-index: 1;
    }
  }
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16rpx;

  .action-btn {
    min-width: 140rpx;
    height: 64rpx;
    font-size: 26rpx;
  }
}

// Responsive Design
@media screen and (max-width: 750rpx) {
  .activity-card {
    padding: 24rpx;
    margin-bottom: 20rpx;
  }

  .card-title .title-text {
    font-size: 36rpx;
  }

  .info-item .info-text {
    font-size: 26rpx;
  }

  .distance-info {
    font-size: 24rpx;
  }

  .participant-avatars .avatar {
    width: 60rpx;
    height: 60rpx;
    background-color: #fff;
  }

  .action-buttons .action-btn {
    min-width: 120rpx;
    height: 60rpx;
    font-size: 24rpx;
  }
}

// Animation Classes
@for $i from 1 to 99 {
  .animation#{$i} {
    animation: fade-in-right 0.8s forwards #{$i * 0.2s};
  }
}

// Custom animations
@keyframes fade-in-right {
  from {
    opacity: 0;
    transform: translateX(30rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
