<template>
  <view>
    <t-navbar nav-style="custom" :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 200})` }" :placeholder="false">
      <view v-if="scrollTop > 200" class="nav-title truncate">{{ info.name }}</view>
    </t-navbar>
    <template v-if="info">
      <swiper class="cover" :autoplay="true" :interval="5000" :duration="1000" circular>
        <swiper-item v-for="image in info.image" :key="image">
          <view class="cover-item">
            <image class="image" :src="image" mode="aspectFill" @click="previewImages"></image>
          </view>
        </swiper-item>
        <swiper-item v-if="info.image.length == 0" :key="image">
          <view class="cover-item">
            <image class="image" :src="`${iconUrl}no-image.svg`" mode="aspectFill"></image>
          </view>
        </swiper-item>
      </swiper>
      <view class="content" v-if="info">
        <view class="like-button" @click="recommendInfo">
          <image class="icon" :class="[info.recommend ? '' : 'unlike']" :src="`${iconUrl}theme_thumb-up.svg`"> </image>
          <view class="text">{{ recommendNum }}</view>
        </view>
        <view class="explore-title">店铺详情</view>
        <view class="name">{{ info.name }}</view>
        <view class="content-text" v-if="info.intro">
          <text>{{ info.intro }}</text>
        </view>
        <view class="model">
          <view class="card-box">
            <map
              class="map"
              :style="[mapStyle]"
              :latitude="info.location.coordinates[1]"
              :longitude="info.location.coordinates[0]"
              :markers="markers"
              :enable-scroll="false"
              :enable-zoom="false"
              @markertap="navigateLocation"
              @click="navigateLocation"
            ></map>
            <t-card
              :style="{ width: '48%', height: '330rpx' }"
              v-if="contactValue"
              :value="contactValue.value"
              :icon="contactValue.icon"
              :text="contactValue.text"
              @click="contactClick"
            >
              <template v-slot:label>
                <picker mode="selector" :range="contactInfo" range-key="name" @change="contactSelect">
                  <view class="card-label">
                    <view>{{ contactValue.name }}</view>
                    <image :src="`${iconUrl}arrow-down.svg`" mode="widthFix"> </image>
                  </view>
                </picker>
              </template>
            </t-card>
          </view>
          <view v-if="info.address" style="margin-bottom: 20rpx">
            <t-title>地址详情</t-title>
            <view class="content-text" @click="navigateLocation">
              <text>{{ info.address || "暂无地址详情" }}</text>
            </view>
          </view>
          <view class="card-info">
            <t-title>营业时间</t-title>
            <view class="text">{{ info.open_time || "暂无信息" }}</view>
          </view>
          <view class="card-info">
            <t-title>价格参考</t-title>
            <view class="text">{{ info.price || "暂无信息" }}</view>
          </view>
        </view>
        <view class="edit-text" v-if="isEdit">该信息有误/不完整？<text class="theme-text" @click="editInfo">点击编辑信息</text> </view>
      </view>
    </template>
  </view>
</template>

<script>
export default {
  data() {
    return {
      imgUrl: vk.getVuex("$app.config.staticUrl.image"),
      iconUrl: vk.getVuex("$app.config.staticUrl.icon"),
      scrollTop: 0,
      id: null,
      info: null,
      userInfo: vk.getVuex("$user.userInfo"),

      markers: [],
      rowStyle: {
        padding: "20rpx 10rpx",
        backgroundColor: "#fff",
        flexDirection: "row",
        alignItems: "center",
      },

      backStyle: {
        padding: "20rpx 10rpx",
        backgroundColor: "#fff",
      },
      contactValue: null,
      contactInfo: [],
    };
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onShareAppMessage(e) {
    if (!this.info) return;
    let image = this.info.image.length > 0 ? this.info.image[0] : "";
    return {
      title: `${this.userInfo.nickname}给您推荐穿线店铺${this.info.name}`,
      path: `/pages/explore/string/detail?id=${this.info._id}`,
      imageUrl: image,
    };
  },
  computed: {
    mapStyle() {
      return {
        width: this.contactValue ? "48%" : "100%",
        height: "320rpx",
      };
    },
    isEdit() {
      if (!this.info) return false;
      if (!this.info.admin_id) return true;
      if (this.info.admin_id == this.userInfo._id) return true;
      return false;
    },
    recommendNum() {
      if (!this.info) return;
      let value = this.info.recommend_num;
      if (value > 10000) {
        value = (value / 10000).toFixed(2) + "w";
      } else if (value > 1000 && value < 10000) {
        value = (value / 1000).toFixed(2) + "k";
      } else {
        return value;
      }
    },
  },
  async onLoad(options) {
    if (!options.id) {
      vk.toast("获取信息失败!", "none", true, () => {
        vk.navigateBack();
      });
      return;
    }
    let localIcon = await uni.getImageInfo({
      src: `${this.imgUrl}marker/marker-local.png`,
    });
    this.localIcon = localIcon.path;
    this.id = options.id;
    this.getData();
  },
  methods: {
    handleContactInfo() {
      if (!this.info.contact_info || this.info.contact_info.length == 0) return;
      this.contactInfo = this.info.contact_info.map((item) => {
        switch (item.type) {
          case "phone":
            item.name = "电话";
            item.icon = `${this.iconUrl}call-white.svg`;
            item.text = "拨打电话";
            break;
          case "wechat":
            item.name = "微信号";
            item.icon = `${this.iconUrl}copy-white.svg`;
            item.text = "复制";
            break;
          case "public":
            item.name = "公众号";
            item.icon = `${this.iconUrl}copy-white.svg`;
            item.text = "复制";
            break;
          case "miniproject":
            item.name = "小程序";
            item.icon = `${this.iconUrl}copy-white.svg`;
            item.text = "复制";
            break;
          default:
            break;
        }
        return item;
      });
      console.log(this.contactInfo);
      this.contactValue = this.contactInfo[0];
    },
    // 选择联系方式
    contactSelect(e) {
      let index = e.detail.value;
      this.contactValue = this.contactInfo[index];
    },
    // 预览图片
    previewImages() {
      uni.previewImage({
        urls: this.info.image,
      });
    },
    contactClick() {
      if (this.info.contact_way == "phone") {
        console.log("拨打电话", this.info.contact_text);
        uni.makePhoneCall({
          phoneNumber: this.info.contact_text.toString(),
        });
      } else {
        console.log("复制文本");
        this.copyContact();
      }
    },
    async editInfo() {
      vk.navigateTo("/pages/public/submitInfo?type=string&id=" + this.id);
    },
    async getData() {
      let data = await vk.callFunction({
        url: "client/public/info/string/pub/get",
        title: "请求中...",
        data: {
          id: this.id,
        },
      });
      this.info = data.data;
      this.handleContactInfo();
      this.markers = [
        {
          id: 1,
          latitude: data.data.location.coordinates[1],
          longitude: data.data.location.coordinates[0],
          iconPath: this.localIcon,
          width: 30,
          height: 30,
        },
      ];
    },
    async recommendInfo() {
      let res = await vk.callFunction({
        url: "client/public/info/kh/recommend",
        title: "请求中...",
        data: {
          id: this.info._id,
          type: "string",
        },
      });
      this.info.recommend = res.data;
      this.info.recommend_num = res.num;
      uni.$emit("refresh-string");
    },
    navigateLocation() {
      let lat = this.info.location.coordinates[1];
      let lon = this.info.location.coordinates[0];
      uni.openLocation({
        name: this.info.name,
        address: this.info.address,
        latitude: lat,
        longitude: lon,
      });
    },
    copyContact() {
      let text = this.info.contact_text.toString();
      uni.setClipboardData({
        data: text,
        success: () => {
          vk.toast("复制成功", "success");
        },
        fail: (res) => {
          console.log("error", res);
        },
      });
    },
  },
};
</script>

<style scoped lang="scss">
.nav-title {
  width: 300rpx;
  position: absolute;
  left: 0;
  right: 0;
  margin: 0 auto;
  text-align: center;
}

.cover {
  width: 100%;
  height: 600rpx;
  position: fixed;
  top: 0;

  .cover-item {
    width: 100%;
    height: 100%;
  }

  .image {
    width: 100%;
    height: 100%;
  }
}

.content {
  width: 100%;
  min-height: calc(100vh - 600rpx);
  position: absolute;
  top: 560rpx;
  background-color: #fff;
  border-radius: 48rpx 0 0 0;
  padding: 30rpx 40rpx;
  box-sizing: border-box;

  .card-label {
    display: flex;
    align-items: center;

    image {
      width: 36rpx;
      margin-left: 20rpx;
    }
  }

  .like-button {
    width: 100rpx;
    height: 100rpx;
    position: absolute;
    right: 40rpx;
    top: -50rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    background-color: #fff;
    border-radius: 50%;
    z-index: 5;

    .unlike {
      filter: grayscale(1);
    }

    .icon {
      width: 48rpx;
      height: 48rpx;
      flex-shrink: 0;
    }

    .text {
      font-size: 24rpx;
      color: #ccc;
    }
  }

  .content-text {
    font-size: 28rpx;
    color: #8a8a8a;
    display: flex;
    margin-top: 10rpx;

    .label {
      flex-shrink: 0;
    }
  }

  .explore-title {
    font-family: usic;
    font-size: 32rpx;
    color: $primary-color;
    margin-bottom: 10rpx;
  }

  .name {
    font-size: 40rpx;
    font-weight: bold;
    color: #000;
    margin-bottom: 20rpx;
  }

  .model {
    .card-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 20rpx 0;
    }
    .card-info {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 20rpx;
      .text {
        margin-left: 20rpx;
        font-size: 28rpx;
        color: #333;
      }
    }
    .map {
      border-radius: 12px;
      overflow: hidden;
      flex-shrink: 0;
    }
  }
}

.edit-text {
  margin: 80rpx 40rpx 40rpx;
  text-align: center;
  font-size: 24rpx;
  color: #8a8a8a;

  .theme-text {
    color: $primary-color;
  }
}
</style>
