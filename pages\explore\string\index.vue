<template>
  <view class="page-content">
    <t-navbar title="穿线店铺信息"></t-navbar>
    <view class="header" :style="{ top: headerTop }">
      <view class="filter">
        <view class="filter-block" @click="sortList">
          <image class="icon" :src="`${iconUrl}transfer-2-fill.svg`"></image>
          <text class="text">{{ filters[filterIndex].title }}排序</text>
        </view>
        <view class="base-search">
          <wd-search placeholder="请输入店铺名称" hide-cancel @search="confirmSearch"></wd-search>
        </view>
      </view>
      <view class="add-btn" @click="vk.navigateTo('/pages/public/submitInfo?type=string')">
        <wd-icon name="add" size="20px" color="#fff"></wd-icon>
      </view>
    </view>
    <view class="placeholder" :style="{ height: '56px' }"></view>
    <template>
      <t-empty v-if="info.nodata && info.total == 0" data="data">
        <view class="empty-text"
          >附近暂无穿线店铺信息,<text class="theme" @click="vk.navigateTo('/pages/public/submitInfo?type=string')">去新增</text></view
        >
      </t-empty>
      <template v-else>
        <view v-for="(item, index) in info.data" :key="item._id">
          <string-card :animate-index="index" :value="item"></string-card>
        </view>
        <wd-divider v-if="info.page !== 1 && info.nodata == true">没有更多了</wd-divider>
      </template>
    </template>
  </view>
</template>

<script>
import StringCard from "@/components/card/string-card.vue";
import { getLocation } from "@/utils/location";
export default {
  components: {
    StringCard,
  },
  data() {
    return {
      iconUrl: vk.getVuex("$app.config.staticUrl.mingCuteIcon"),
      filters: [
        {
          type: "distance",
          title: "距离",
          sort: "asc",
        },
        {
          type: "recommend_num",
          title: "推荐",
          sort: "desc",
        },
      ],
      filterIndex: 0,
      system: uni.getSystemInfoSync(),
      locationInfo: vk.getVuex("$app.locationInfo"),
      userInfo: vk.getVuex("$user.userInfo"),
      scrollTop: 0,
      scrollup: true,
      info: {
        data: [],
        page: 1,
        total: 0,
        nodata: false,
      },
      searchValue: "",
    };
  },
  computed: {
    headerTop() {
      return `${this.system.statusBarHeight + 44}px`;
    },
  },
  onShareAppMessage(e) {
    let userName = this.userInfo.nickname || "";
    return {
      title: `${userName}给您分享穿线店铺列表`,
      path: `/pages/explore/string/index`,
    };
  },
  async onLoad() {
    uni.$on("refresh-string", () => {
      this.onRefresh();
    });

    this.locationInfo = await getLocation();
    this.getString();
  },
  onUnload() {
    uni.$off("refresh-string");
  },
  onReachBottom() {
    if (this.info.nodata) return;
    this.info.page += 1;
    this.getString();
  },
  onPageScroll(e) {
    this.scrollup = e.scrollTop > this.scrollTop;
    this.scrollTop = e.scrollTop;
  },
  methods: {
    sortList() {
      if (this.filters.length == this.filterIndex + 1) {
        this.filterIndex = 0;
      } else {
        this.filterIndex += 1;
      }
      this.onRefresh();
    },
    confirmSearch({ value }) {
      this.searchValue = value;
      this.onRefresh();
    },
    onRefresh() {
      this.info = {
        data: [],
        page: 1,
        total: 0,
        nodata: false,
      };
      this.getString();
    },
    async getString() {
      let data = await vk.callFunction({
        url: "client/public/info/string/pub/list",
        title: "请求中...",
        data: {
          name: this.searchValue,
          pageIndex: this.info.page,
          location: this.locationInfo,
          sort: [{ name: this.filters[this.filterIndex].type, type: this.filters[this.filterIndex].sort }],
        },
      });
      this.info.total = data.total;
      this.info.data.push(...data.rows);
      if (data.rows.length < 20) this.info.nodata = true;
    },
  },
};
</script>

<style scoped lang="scss">
.page-content {
  padding: 30rpx;
  box-sizing: border-box;
}

.add-btn {
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background-color: $primary-color;

  image {
    width: 24px;
    height: 24px;
  }
}

.header {
  width: 100%;
  z-index: 9;
  left: 0;
  height: 56px;
  padding: 0 30rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  position: fixed;
  background-color: #fff;
}

.filter {
  flex: 1;
  display: flex;
  align-items: center;

  &-block {
    display: flex;
    align-items: center;
    width: 170rpx;
    height: 30px;
    margin-right: 20rpx;

    .icon {
      width: 15px;
      height: 15px;
      margin-right: 10rpx;
      flex-shrink: 0;
    }

    .text {
      font-size: 28rpx;
      font-weight: bold;
      white-space: nowrap;
    }

    .multiline {
      @include multiline(1);
    }
  }
}
</style>
