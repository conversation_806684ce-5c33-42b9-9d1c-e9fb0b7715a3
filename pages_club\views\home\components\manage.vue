<template>
	<view style="padding-bottom: 100rpx;">
		<image class="background" :src="info.cover" mode="aspectFill"></image>
		<view class="content-top">
			<view class="tips">欢迎，{{userInfo.nickname}}</view>
			<view class="club-data">
				<view v-for="item in club_data" :key="item.label" class="data-block">
					<view class="data-title">{{ item.label }}</view>
					<view class="data-value">{{ item.value }}</view>
				</view>
			</view>
		</view>

		<view class="action-list">
			<view class="action-title">管理俱乐部</view>
			<view v-for="item in action_list" :key="item.title" class="action-block" @click="toPages(item.url)">
				<view class="action-icon">
					<image :src="item.icon" mode="widthFix" />
				</view>
				<view class="action-text">
					<view class="action-name">{{ item.title }}</view>
					<view class="action-desc">{{ item.desc }}</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
	export default {
		data() {
			return {
				info: null,
				id: null,
				iconUrl: vk.getVuex('$app.config.staticUrl.icon'),
				system: uni.getSystemInfoSync(),
				userInfo: vk.getVuex('$user.userInfo'),
				club_data: [],
			}
		},
		watch: {
			"$store.state.$club.info": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.info = newVal
				}
			},
			"$store.state.$club.id": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.id = newVal
					this.getCount()
				}
			}
		},
		computed: {
			action_list() {
				return [{
						icon: `${this.iconUrl}tennis.svg`,
						title: '发起活动',
						desc: '发起俱乐部活动',
						url: '/pages_club/views/activity/start'
					}, {
						icon: `${this.iconUrl}user-group.svg`,
						title: '会员管理',
						desc: '管理俱乐部的成员情况',
						url: '/pages_club/views/member/list'
					},
					{
						icon: `${this.iconUrl}tennis-site.svg`,
						title: '场地管理',
						desc: '场地以及排班信息管理',
						url: '/pages_club/views/site/list'
					},
					{
						icon: `${this.iconUrl}log.svg`,
						title: '活动管理',
						desc: '场地以及排班信息管理',
						url: '/pages_club/views/activity/index'
					},
					{
						icon: `${this.iconUrl}vip.svg`,
						title: '会员卡管理',
						desc: '管理会员卡片类型',
						url: '/pages_club/views/card/index'
					},
					{
						icon: `${this.iconUrl}log.svg`,
						title: '订单管理',
						desc: '管理俱乐部中的支付订单',
						url: '/pages_club/views/order/index'
					},
					{
						icon: `${this.iconUrl}setting.svg`,
						title: '信息管理',
						desc: '管理俱乐部logo和宣传标语等信息',
						url: '/pages_club/views/setting/index'
					}
				]
			},
		},
		async mounted() {
			this.id = vk.getVuex('$club.id')
			this.info = await vk.vuex.getters('$club/getInfo');
			this.getCount()
		},
		methods: {
			async getCount() {
				let data = await vk.callFunction({
					url: 'client/club/kh/statistics',
					title: '请求中...',
					data: {
						club_id: this.id
					},
				});
				this.club_data = [{
						label: '成员数',
						value: data.data.user_num
					},
					{
						label: '活动数',
						value: data.data.activity_num
					},
					{
						label: '场地数',
						value: data.data.site_num
					},
					{
						label: '会员卡',
						value: data.data.card_num
					},
				]
			},
			toPages(url) {
				vk.navigateTo(url)
			}
		}
	}
</script>
<style scoped lang="scss">
	.background {
		width: 100%;
		height: 500rpx;
	}

	.content-top {
		width: 90%;
		margin: -100rpx auto 30rpx;
		min-height: 200rpx;
		background-color: #fff;
		position: relative;
		z-index: 9;
		border-radius: 12px;

		.tips {
			font-size: 32rpx;
			color: #333;
			text-align: center;
			padding: 30rpx 0;
		}
	}


	.club {
		&-data {
			width: 100%;
			border-radius: 24rpx;
			position: relative;
			display: grid;
			grid-template-columns: 33% 33% 33%;
			padding: 20rpx 0;
			box-sizing: border-box;
			gap: 20rpx 0;

			.data-block {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				position: relative;
				height: 120rpx;

				.data-title {
					font-size: 28rpx;
					color: #333;
				}

				.data-value {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
					margin-top: 20rpx;
					font-family: 'Times New Roman', Times, serif;
				}

				&::after {
					content: '';
					position: absolute;
					height: 40rpx;
					width: 2rpx;
					top: 0;
					bottom: 0;
					right: 0;
					margin: auto 0;
					background-color: #ccc;
				}

				&:nth-child(3n) {
					&::after {
						content: none;
					}
				}

				&:last-child {
					&::after {
						content: none;
					}
				}
			}
		}
	}

	.action {
		&-list {
			margin: 0 40rpx;
			background-color: #fff;
			border-radius: 12px;
		}

		&-block {
			display: flex;
			align-items: center;
			padding: 30rpx;
			box-sizing: border-box;
			border-bottom: 2rpx solid #f8f8f8;
		}

		&-icon {
			margin-right: 30rpx;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 80rpx;
			height: 80rpx;
			border-radius: 18rpx;

			image {
				width: 48rpx;
				height: 48rpx;
			}
		}

		&-text {
			flex: 1;
		}

		&-title {
			font-size: 40rpx;
			color: #333;
			font-weight: bold;
			display: none;
		}

		&-name {
			font-size: 32rpx;
			color: #333;
		}

		&-desc {
			font-size: 28rpx;
			color: #e1e1e1;
		}
	}
</style>