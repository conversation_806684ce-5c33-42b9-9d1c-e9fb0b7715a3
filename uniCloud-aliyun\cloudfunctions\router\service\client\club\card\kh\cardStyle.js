'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/kh/cardStyle 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = {
			code: 0,
			msg: ""
		}; // 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.select({
			dbName: "card-style-data",
			getCount: false,
			pageIndex: 1,
			pageSize: 999,
			sortArr: [{ name: "_add_time", type: "desc" }],
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}