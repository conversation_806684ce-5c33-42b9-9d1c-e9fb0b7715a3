'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url script/data/pub/newSite 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let key = 'fef8da3adafbcfe9ab7108a53906945b'
		let ary = []
		let base = {
			image: [],
			name: "",
			intro: "",
			location: "",
			address: "",
			address_name: "",
			contact_info: [],
			site_indoor: "",
			site_outdoor: "",
			cost_info: [],
			open_time: "",
			service: [],
			open: true,
			recommend_num: 0,
			admin_id: '',
		}
		let result = null
		const city = [
			"010", "022", "0311", "0315", "0335", "0310", "0319", "0312", "0313", "0314", "0317", "0316", "0318",
			"0351", "0352", "0353", "0355", "0356", "0349", "0354", "0359", "0350", "0357", "0358", "0471", "0472",
			"0473", "0476", "0475", "0477", "0470", "0478", "0474", "0482", "0479", "0483", "024", "0411", "0412",
			"0413", "0414", "0415", "0416", "0417", "0418", "0419", "0427", "0410", "0421", "0429", "0431", "0432",
			"0434", "0437", "0435", "0439", "0438", "0436", "1433", "0451", "0452", "0467", "0468", "0469", "0459",
			"0458", "0454", "0464", "0453", "0456", "0455", "0457", "021", "025", "0510", "0516", "0519", "0512",
			"0513", "0518", "0517", "0515", "0514", "0511", "0523", "0527", "0571", "0574", "0577", "0573", "0572",
			"0575", "0579", "0570", "0580", "0576", "0578", "0551", "0553", "0552", "0554", "0555", "0561", "0562",
			"0556", "0559", "0550", "1558", "0557", "0564", "0558", "0566", "0563", "0591", "0592", "0594", "0598",
			"0595", "0596", "0599", "0597", "0593", "0791", "0798", "0799", "0792", "0790", "0701", "0797", "0796",
			"0795", "0794", "0793", "0531", "0532", "0533", "0632", "0546", "0535", "0536", "0537", "0538", "0631",
			"0633", "0539", "0534", "0635", "0543", "0530", "0371", "0378", "0379", "0375", "0372", "0392", "0373",
			"0391", "0393", "0374", "0395", "0398", "0377", "0370", "0376", "0394", "0396", "1391", "027", "0714",
			"0719", "0717", "0710", "0711", "0724", "0712", "0716", "0713", "0715", "0722", "0718", "0728", "2728",
			"1728", "1719", "0731", "0733", "0732", "0734", "0739", "0730", "0736", "0744", "0737", "0735", "0746",
			"0745", "0738", "0743", "020", "0751", "0755", "0756", "0754", "0757", "0750", "0759", "0668", "0758",
			"0752", "0753", "0660", "0762", "0662", "0763", "0769", "0760", "0768", "0663", "0766", "0771", "0772",
			"0773", "0774", "0779", "0770", "0777", "1755", "0775", "0776", "1774", "0778", "1772", "1771", "0898",
			"0899", "2898", "0805", "1897", "1894", "1893", "1898", "0807", "0806", "1892", "0804", "1896", "0802",
			"0803", "2802", "0809", "0801", "1899", "023", "028", "0813", "0812", "0830", "0838", "0816", "0839",
			"0825", "1832", "0833", "0817", "1833", "0831", "0826", "0818", "0835", "0827", "0832", "0837", "0836",
			"0834", "0851", "0858", "0852", "0853", "0857", "0856", "0859", "0855", "0854", "0871", "0874", "0877",
			"0875", "0870", "0888", "0879", "0883", "0878", "0873", "0876", "0691", "0872", "0692", "0886", "0887",
			"0891", "0892", "0895", "0894", "0893", "0896", "0897", "029", "0919", "0917", "0910", "0913", "0911",
			"0916", "0912", "0915", "0914", "0931", "1937", "0935", "0943", "0938", "1935", "0936", "0933", "0937",
			"0934", "0932", "2935", "0930", "0941", "0971", "0972", "0970", "0973", "0974", "0975", "0976", "0977",
			"0951", "0952", "0953", "0954", "1953", "0991", "0990", "0995", "0902", "0994", "0909", "0996", "0997",
			"0908", "0998", "0903", "0999", "0901", "0906", "0993", "1997", "1998", "1994", "1906", "1996", "1909",
			"1999", "1903", "0992", "2940", "1886", "1852", "1853"
		]
		const pages = Array.from({ length: 100 }, (_, index) => index + 1);
		for (const code of city) {
			for (const index of pages) {
				await new Promise(resolve => setTimeout(resolve, index * 300));
				let result = await vk.request({
					url: `https://restapi.amap.com/v5/place/text?key=${key}&region=${code}&keywords=网球场&types=080103&page_size=25&page_num=${index}`,
					method: "GET",
					header: {
						"content-type": "application/json; charset=utf-8",
					},
				});
				const { pois } = result
				console.log('pois', pois);
				if (!pois || pois.length == 0) break
				pois.forEach(item => {
					let obj = vk.pubfn.deepClone(base);
					let location = item.location.split(',')
					obj.location = new db.Geo.Point(Number(location[0]), Number(location[1]))
					obj.address = `${item.pname}${item.cityname}${item.adname}${item.address}`
					obj.address_name = item.name
					obj.name = item.name
					ary.push(obj)
				})
			}
		}

		await vk.baseDao.adds({
			dbName: "info-site-data",
			dataJson: ary
		});

		console.log("数据======>", ary.length, ary);
		// 业务逻辑结束-----------------------------------------------------------
		// return res;
	}
}