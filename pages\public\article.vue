<template>
	<view class="page-content">
		<t-navbar :title="info.title"></t-navbar>
		<view class="content" v-if="info" v-html="info.content"></view>
		<view class="time" v-if="info">{{updateTime}}</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	export default {
		data() {
			return {
				info: null
			}
		},
		computed: {
			updateTime() {
				if (!this.info) return
				return dayjs(this.info._add_time).format('YYYY-MM-DD HH:mm:ss')
			},
		},
		onLoad(options) {
			this.getData(options.id)
		},
		methods: {
			async getData(type) {
				try {
					let data = await vk.callFunction({
						url: 'client/public/pub/ariticle',
						title: '请求中...',
						data: {
							id,
						},
					});
					this.info = data.data
				} catch (error) {
					console.log(error);
				}
			},
		}
	}
</script>

<style scoped lang="scss">
	.page-content {
		padding: 30rpx;
		background-color: #fff;
	}

	.time {
		font-size: 24rpx;
		color: #8a8a8a;
		margin-top: 20rpx;
		text-align: end;
	}

	.content {
		font-size: 28rpx;
	}
</style>