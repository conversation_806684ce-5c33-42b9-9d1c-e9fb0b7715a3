<template>
  <t-modal :value="value" title="更新会员卡" @close="close" confirmText="确认更改" @confirm="save" @cancel="close">
    <view v-if="cardId" class="edit-card-value">
      <t-group title="更改前" :customStyle="customStyle">
        <template v-if="info.type === 'date'">
          <t-picker label="开始时间" mode="date" marginTop="0" disabled v-model="info.start"></t-picker>
        </template>
        <t-input :label="label" v-model="origin.value" disabled :unit="unit"></t-input>
      </t-group>

      <!-- 会员卡日期 -->
      <t-group title="更改后" :customStyle="customStyle">
        <template v-if="info.type === 'date'">
          <t-picker label="开始时间" mode="date" marginTop="0" v-model="startTime" placeholder="请选择开始时间"></t-picker>
        </template>
        <t-input :label="label" v-model="changeValue" :unit="unit"></t-input>
      </t-group>

      <t-input label="编辑原因" textarea v-model="text" placeholder="请输入编辑原因" :customStyle="customStyle"></t-input>
    </view>
  </t-modal>
</template>

<script>
import dayjs from "dayjs";
export default {
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    cardId: {
      type: String,
    },
  },
  data() {
    return {
      startTime: "",
      text: "",
      unit: "",
      label: "",
      info: null,

      changeValue: "",
      origin: {
        value: "",
      },
      customStyle: {
        // boxShadow: "0 0 16rpx rgba(0, 0, 0, 0.06)",
        border:"2rpx solid #f8f8f8"
      },
    };
  },
  mounted() {
    this.getCardInfo();
  },
  watch: {
    value(val) {
      // 向父组件发送更新事件
      this.$emit("input", val);
      if (val) this.getCardInfo();
    },
  },
  methods: {
    close() {
      this.$emit("input", false);
    },
    async getCardInfo() {
      if (!this.cardId) return;
      let data = await vk.callFunction({
        url: "client/club/card/kh/userCardDetail",
        title: "请求中...",
        data: {
          id: this.cardId,
        },
      });
      this.info = data.data;
      this.handleValue();
    },
    handleValue() {
      switch (this.info.type) {
        case "times":
          this.unit = "次";
          this.label = "次数";
          this.origin.value = this.info.value;
          break;
        case "date":
          this.unit = "天";
          this.label = "有效天数";
          this.startTime = this.info.start;
          this.origin.value = dayjs(this.info.end).diff(dayjs(this.info.start), "day");
          break;
        case "day":
          this.unit = "天";
          this.label = "有效天数";
          this.origin.value = this.info.value;
          break;
        case "save":
          this.unit = "元";
          this.label = "余额";
          this.origin.value = this.info.value;
          break;
      }
    },
    // 更改会员卡余额
    async save() {
      if (!this.cardId) return;
      if (!this.changeValue) return vk.toast("请输入更改后的值");
      if (!this.text) return vk.toast("请输入编辑原因");
      try {
        let data = {
          id: this.cardId,
          type: this.info.type,
          value: this.changeValue,
          reason: this.text,
        };
        if (this.info.type === "date") {
          if (!this.startTime) return vk.toast("请选择开始时间");
          data.start = this.startTime;
        }
        await vk.callFunction({
          url: "client/club/card/kh/editCardValue",
          title: "请求中...",
          data,
        });
        vk.toast("更改成功");
        this.close();
      } catch (err) {
        vk.toast(err.msg);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-card-value {
  padding: 30rpx;
  height: 50vh;
  box-sizing: border-box;
  overflow: scroll;
}
.button-box {
  height: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
</style>
