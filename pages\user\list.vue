<template>
  <view class="page-container">
    <t-navbar :title="navTitle"></t-navbar>
    <view class="content">
      <view class="list">
        <view class="block" v-for="(item, index) in list" :key="index">
          <template v-if="item">
            <view class="info" @click="viewUser(item._id)">
              <image :src="item.avatar" mode="aspectFill"></image>
              <text>{{ item.nickname }}</text>
            </view>
            <template v-if="type == 'follow' && user_id == userInfo._id">
              <wd-button type="info" size="medium" @click="unFollow(item._id, index)">取消关注</wd-button>
            </template>
            <view v-if="item._id == userInfo._id" class="self-tag">我</view>
          </template>
          <template v-else>
            <view class="info">
              <image style="background-color: #f8f8f8"></image>
              <text>用户已注销</text>
            </view>
          </template>
        </view>
      </view>
      <t-empty v-if="total == 0 && page == 1" data="data" :tip="noDataText"></t-empty>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      navTitle: "用户列表",
      user_id: null,
      list: [],
      page: 1,
      type: null,
      noMore: false,
      total: 0,
      noDataText: "暂无数据",
    };
  },
  onReachBottom() {
    if (this.noMore) return;
    this.page += 1;
    this.getData();
  },
  onLoad(options) {
    this.user_id = options.user_id;
    this.type = options.type;
    this.getData();
  },
  methods: {
    // 查看用户详情
    viewUser(id) {
      console.log(id);
      if (!id || id == this.userInfo._id) return;
      vk.navigateTo(`/pages/user/detail?id=${id}`);
    },
    // 取消关注
    async unFollow(id, index) {
      try {
        let data = await vk.callFunction({
          url: "client/user/kh/follow",
          title: "请求中...",
          data: {
            target_user_id: id,
          },
        });
        vk.toast(data.msg);
        this.list.splice(index, 1);
        this.total -= 1;
        vk.setVuex("$status.user_reload", true);
      } catch (error) {
        console.log(error);
      }
    },
    // 判断获取数据
    getData() {
      switch (this.type) {
        case "fans":
          this.navTitle = "粉丝列表";
          this.noDataText = "暂无粉丝用户";
          this.getList();
          break;
        case "follow":
          this.navTitle = "关注列表";
          this.noDataText = "还未关注任何用户";
          this.getList();
          break;
      }
    },
    // 获取粉丝或关注列表
    async getList() {
      try {
        let data = await vk.callFunction({
          url: "client/user/pub/list",
          title: "请求中...",
          data: {
            pageIndex: this.page,
            user_id: this.user_id,
            type: this.type,
          },
        });
        this.list.push(...data.rows);
        this.noMore = !data.hasMore;
        this.total = data.total;
      } catch (error) {
        console.log(error);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.page-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.content {
  padding: 30rpx;
}

.list {
  .block {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 30rpx;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 24rpx;
    margin-bottom: 20rpx;

    .info {
      display: flex;
      align-items: center;
      flex: 1;

      image {
        width: 80rpx;
        height: 80rpx;
        border-radius: 50%;
      }

      text {
        font-size: 32rpx;
        margin-left: 20rpx;
        flex: 1;
        @include multiline(1);
      }
    }

    .self-tag {
      font-size: 28rpx;
      color: #0171bc;
      font-weight: bold;
    }
  }
}
</style>
