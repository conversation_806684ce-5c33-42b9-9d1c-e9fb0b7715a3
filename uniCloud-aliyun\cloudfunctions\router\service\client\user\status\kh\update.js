'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/status/kh/update 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, _id, location, address_name, accept_distance, tags, contact, open, apply } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = 'player-status-data'
		let dataJson = {
			user_id: uid,
			address_name,
			accept_distance,
			tags,
			contact,
			open,
			apply
		}
		if (location && location.type !== 'Point') {
			location = new db.Geo.Point(location.longitude, location.latitude)
			dataJson.location = location
		}
		await vk.baseDao.updateById({
			dbName,
			id: _id,
			dataJson,
			getUpdateData: false
		});
		res.msg = "保存成功！"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}