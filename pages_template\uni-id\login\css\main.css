.content {
	display: flex;
	flex-direction: column;
	justify-content: center;
}

/* 头部 logo 开始 */
.header {
	width: 160rpx;
	height: 160rpx;
	box-shadow: 0rpx 0rpx 60rpx 0rpx rgba(0, 0, 0, 0.1);
	border-radius: 50%;
	margin-top: 40rpx;
	margin-bottom: 40rpx;
	margin-left: auto;
	margin-right: auto;
}

.header.weixin-one-click-login {
	margin-top: 200rpx;
	transform: scale(1.4);
}

.header .logo {
	width: 160rpx;
	height: 160rpx;
	border-radius: 50%;
	border: 0;
}

/* 头部 logo 结束 */

/* 表单主体 开始 */
.form-view {
	display: flex;
	flex-direction: column;
	padding-left: 70rpx;
	padding-right: 70rpx;
}

.form-view .form-item {
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	align-items: center;
	color: #333333;
	padding: 24rpx 32rpx;
	margin: 26rpx 0;
	background-color: #ffffff;
}

.form-view .form-border {
	border: none;
	border-radius: 2.5rem;
	-webkit-box-shadow: 0 0 60rpx 0 rgba(43, 86, 112, 0.1);
	box-shadow: 0 0 60rpx 0 rgba(43, 86, 112, 0.1);
}

.form-view .form-input {
	flex: 1;
	text-align: left;
	font-size: 28rpx;
	padding-right: 10rpx;
	margin-left: 20rpx;
	width: 100%;
	min-height: 70rpx;
}

/* 表单主体 结束 */
/* 登录按钮 */
.login-btn {
	margin-top: 60rpx;
	width: 80%;
	height: 100rpx;
	margin-left: auto;
	margin-right: auto;
}

.login-btn .btn{
	height: 76rpx;
	line-height: 76rpx;
	font-size: 30rpx;
}

.login-btn .btn.success{
	background-color: #19be6b;
	color: #ffffff;
}

.login-btn .btn.circle{
	border-radius: 100rpx
}

.login-btn .btn.hover{
	opacity: 0.7;
}


/* 底部 */
.footer {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;
	font-size: 28rpx;
	margin-top: 80rpx;
	color: rgba(0, 0, 0, 0.7);
	text-align: center;
	height: 40rpx;
	line-height: 40rpx;
}

.footer .center-line {
	font-size: 24rpx;
	margin-left: 15rpx;
	margin-right: 15rpx;
}

.footer .footer-checkbox{
	transform:scale(0.7);
}

.login-icon-view{
	width: 100%;
	text-align: center;
	margin-top: 80rpx;
	display: flex;
	justify-content: center;
}
.login-icon-item{
	width: 120rpx;
}
.tips{
	margin-top: 20rpx;
	font-size: 28rpx;
}
