<template>
  <view class="draw-page">
    <!-- 轮次选择 -->
    <scroll-view class="round-scroll" scroll-x :scroll-into-view="'round-' + currentRound" :show-scrollbar="false">
      <view class="round-list">
        <view
          v-for="item in rounds"
          :key="item.value"
          :id="'round-' + item.value"
          class="round-item"
          :class="{ active: currentRound === item.value }"
          @click="handleRoundChange(item.value)"
        >
          {{ item.label }}
        </view>
      </view>
    </scroll-view>

    <!-- 签表内容 -->
    <scroll-view class="draw-content" scroll-y refresher-enabled :refresher-triggered="isRefreshing" @refresherrefresh="handleRefresh">
      <view class="draw-tree">
        <!-- 决赛 -->
        <view class="final-round" v-if="currentRound === 'final'">
          <view class="match-card">
            <view
              class="player-box"
              :class="{ winner: finalMatch && finalMatch.score && finalMatch.score.winner === (finalMatch.playerA && finalMatch.playerA.user_id) }"
            >
              <image
                class="avatar"
                :src="(finalMatch && finalMatch.playerA && finalMatch.playerA.avatar) || '/static/images/default-avatar.png'"
                mode="aspectFill"
              ></image>
              <text class="name">{{ (finalMatch && finalMatch.playerA && finalMatch.playerA.name) || "待定" }}</text>
              <text v-if="finalMatch && finalMatch.score" class="score">{{ getPlayerScore(finalMatch.score, "A") }}</text>
            </view>
            <view class="vs">VS</view>
            <view
              class="player-box"
              :class="{ winner: finalMatch && finalMatch.score && finalMatch.score.winner === (finalMatch.playerB && finalMatch.playerB.user_id) }"
            >
              <image
                class="avatar"
                :src="(finalMatch && finalMatch.playerB && finalMatch.playerB.avatar) || '/static/images/default-avatar.png'"
                mode="aspectFill"
              ></image>
              <text class="name">{{ (finalMatch && finalMatch.playerB && finalMatch.playerB.name) || "待定" }}</text>
              <text v-if="finalMatch && finalMatch.score" class="score">{{ getPlayerScore(finalMatch.score, "B") }}</text>
            </view>
          </view>
        </view>

        <!-- 其他轮次 -->
        <view class="match-round" v-else>
          <view class="match-group" v-for="(group, groupIndex) in matchGroups" :key="groupIndex">
            <view class="match-card" v-for="match in group" :key="match._id" @click="goToMatch(match)">
              <view class="player-box" :class="{ winner: match && match.score && match.score.winner === (match.playerA && match.playerA.user_id) }">
                <image
                  class="avatar"
                  :src="(match && match.playerA && match.playerA.avatar) || '/static/images/default-avatar.png'"
                  mode="aspectFill"
                ></image>
                <text class="name">{{ (match && match.playerA && match.playerA.name) || "待定" }}</text>
                <text v-if="match && match.score" class="score">{{ getPlayerScore(match.score, "A") }}</text>
              </view>
              <view class="vs">VS</view>
              <view class="player-box" :class="{ winner: match && match.score && match.score.winner === (match.playerB && match.playerB.user_id) }">
                <image
                  class="avatar"
                  :src="(match && match.playerB && match.playerB.avatar) || '/static/images/default-avatar.png'"
                  mode="aspectFill"
                ></image>
                <text class="name">{{ (match && match.playerB && match.playerB.name) || "待定" }}</text>
                <text v-if="match && match.score" class="score">{{ getPlayerScore(match.score, "B") }}</text>
              </view>
              <view class="match-info">
                <text class="time">{{ formatTime(match.start_time) }}</text>
                <text class="court">{{ match.court }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      rounds: [
        { label: "初赛", value: "preliminary" },
        { label: "16强", value: "round_16" },
        { label: "8强", value: "quarter_final" },
        { label: "4强", value: "semi_final" },
        { label: "决赛", value: "final" },
      ],
      currentRound: "preliminary",
      matchList: [],
      finalMatch: null,
      isRefreshing: false,
      match_id: "",
    };
  },
  computed: {
    matchGroups() {
      if (!this.matchList.length) return [];

      const groupSize =
        {
          preliminary: 4,
          round_16: 4,
          quarter_final: 2,
          semi_final: 2,
        }[this.currentRound] || 1;

      const groups = [];
      for (let i = 0; i < this.matchList.length; i += groupSize) {
        groups.push(this.matchList.slice(i, i + groupSize));
      }
      return groups;
    },
  },
  onLoad(options) {
    if (options.id) {
      this.match_id = options.id;
      this.loadDrawData();
    }
  },
  methods: {
    formatTime(timestamp) {
      if (!timestamp) return "";
      const date = new Date(timestamp);
      return `${String(date.getHours()).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
    },
    handleRoundChange(round) {
      this.currentRound = round;
      this.loadDrawData();
    },
    async loadDrawData() {
      try {
        const res = await vk.callFunction({
          url: "client/tour/draw/kh/getList",
          data: {
            match_id: this.match_id,
            round: this.currentRound,
          },
        });

        if (this.currentRound === "final") {
          this.finalMatch = res.data[0];
        } else {
          this.matchList = res.data;
        }
      } catch (e) {
        vk.toast(e.message || "加载失败");
      } finally {
        uni.hideLoading();
      }
    },
    async handleRefresh() {
      this.isRefreshing = true;
      await this.loadDrawData();
    },
    getPlayerScore(score, player) {
      if (!score || !score.sets) return "";
      return score.sets.map((set) => set[`player${player}`]).join(":");
    },
    goToMatch(match) {
      if (!match || !match._id) return;
      uni.navigateTo({
        url: `/pages_tour/views/match/detail?id=${match._id}`,
      });
    },
  },
};
</script>

<style lang="scss">
.draw-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .round-scroll {
    background-color: #fff;
    height: 100rpx;
    white-space: nowrap;
    border-bottom: 1rpx solid #eee;

    .round-list {
      display: flex;
      padding: 0 20rpx;

      .round-item {
        padding: 0 30rpx;
        height: 100rpx;
        line-height: 100rpx;
        font-size: 28rpx;
        color: #666;
        position: relative;

        &.active {
          color: #2979ff;
          font-weight: bold;

          &::after {
            content: "";
            position: absolute;
            left: 50%;
            bottom: 0;
            transform: translateX(-50%);
            width: 40rpx;
            height: 4rpx;
            background-color: #2979ff;
            border-radius: 2rpx;
          }
        }
      }
    }
  }

  .draw-content {
    flex: 1;
    padding: 30rpx;

    .draw-tree {
      .final-round {
        display: flex;
        justify-content: center;
        margin-bottom: 40rpx;

        .match-card {
          width: 600rpx;
        }
      }

      .match-round {
        .match-group {
          margin-bottom: 40rpx;

          &:last-child {
            margin-bottom: 0;
          }
        }
      }

      .match-card {
        background-color: #fff;
        border-radius: 16rpx;
        padding: 30rpx;
        margin-bottom: 20rpx;
        box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);

        &:last-child {
          margin-bottom: 0;
        }

        .player-box {
          display: flex;
          align-items: center;
          padding: 20rpx 0;

          &.winner {
            .name,
            .score {
              color: #2979ff;
              font-weight: bold;
            }
          }

          .avatar {
            width: 80rpx;
            height: 80rpx;
            border-radius: 50%;
            margin-right: 20rpx;
            background-color: #fff;
          }

          .name {
            flex: 1;
            font-size: 28rpx;
            color: #333;
          }

          .score {
            font-size: 32rpx;
            color: #666;
            margin-left: 20rpx;
          }
        }

        .vs {
          text-align: center;
          font-size: 24rpx;
          color: #999;
          padding: 10rpx 0;
        }

        .match-info {
          display: flex;
          justify-content: space-between;
          margin-top: 20rpx;
          padding-top: 20rpx;
          border-top: 1rpx solid #eee;
          font-size: 24rpx;
          color: #999;

          .time {
            margin-right: 20rpx;
          }
        }
      }
    }
  }
}
</style>
