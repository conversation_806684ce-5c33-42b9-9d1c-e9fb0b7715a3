'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/activity/kh/record 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, type, location, pageIndex } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (location) {
			location = _.geoNear({
				geometry: new db.Geo.Point(location.longitude, location.latitude),
				distanceMultiplier: 0.001,
				distanceField: "distance",
			})
		}
		let records = await vk.baseDao.select({
			dbName: "activity-record-data",
			getCount: true,
			pageIndex,
			pageSize: 10,
			// 主表where条件
			whereJson: {
				user_id: uid,
				type,
			},
			// 主表字段显示规则
			fieldJson: { activity_id: true },
			// 主表排序规则
			sortArr: [{ name: "_add_time", type: "desc" }],
		});
		let whereJson = {}
		res = records
		if (records.rows.length > 0) {
			whereJson._id = _.in(records.rows.map(item => item.activity_id))
		} else {
			return res
		}
		if (location) whereJson.location = location
		res.rows = await vk.baseDao.selects({
			dbName: "activity-data",
			getCount: false,
			getMain: true,
			pageIndex,
			pageSize: 10,
			whereJson,
			sortArr: [{ name: "_add_time", type: "desc" }],
			foreignDB: [{
					dbName: "club-data",
					localKey: "club_id",
					foreignKey: "_id",
					as: "club_info",
					limit: 1
				},
				{
					dbName: "club-site-data",
					localKey: "site",
					foreignKey: "_id",
					as: "club_site",
					limit: 1
				},
				{
					dbName: "activity-join-data",
					localKey: "_id",
					foreignKey: "activity_id",
					as: "join_list",
					limit: 1000,
					fieldJson: { user_id: true, user_info: true, },
					whereJson: { state: 'joined' },
					foreignDB: [{
						dbName: "uni-id-users",
						localKey: "user_id",
						foreignKey: "_id",
						as: "user_info",
						limit: 1,
					}]
				}, {
					dbName: "activity-join-data",
					localKey: "_id",
					foreignKey: "activity_id",
					as: "my_join",
					limit: 1,
					fieldJson: { state: true, },
					whereJson: { user_id: uid, type: 'myself' },
				}
			],
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}