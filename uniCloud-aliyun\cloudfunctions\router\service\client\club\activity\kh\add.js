const formRules = require("../../util/formRules.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/activity/kh/add 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let {
			name,
			desc,
			coach,
			card,
			site,
			club_id,
			max_num,
			min_num,
			apply,
			open,
			level,
			type,
		} = data;
		let formRulesRes = await formRules.activity_add(event);
		if (formRulesRes.code !== 0) {
			return formRulesRes;
		}
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let id = await vk.baseDao.add({
			dbName: "club-activity-data",
			dataJson: {
				name,
				desc,
				coach,
				site,
				card,
				club_id,
				max_num,
				min_num,
				apply,
				open,
				level,
				type,
				creator_id: userInfo._id,
			}
		});
		res.msg = "新增成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}