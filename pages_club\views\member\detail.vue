<template>
  <view>
    <t-navbar nav-style="custom" :custom-style="{ 'background-color': `rgba(255,255,255,${scrollTop / 300})` }" :placeholder="false">
      <view v-if="scrollTop > 350" class="user-small-info">
        <image class="avatar" :src="user_info.avatar" mode="aspectFill" />
        <view class="name">
          {{ user_info.nickname }}
        </view>
      </view>
    </t-navbar>
    <image class="user-cover" :src="user_info.cover || user_info.avatar" mode="aspectFill" />
    <view class="user-content" v-if="user_info">
      <image class="setting" src="/static/images/setting.svg" mode="heightFix" @click="actionVisible = true"></image>
      <view class="user-name">
        <text>{{ user_info.nickname }}</text>
        <text v-if="user_info.remark.name" style="color: #8a8a8a">({{ user_info.remark.name }})</text>
      </view>
      <view class="user-remark">备注信息：{{ user_info.remark.text || "无" }}</view>
      <view class="content" v-if="user_info.card">
        <view class="top-title">
          <text class="text">会员卡</text>
        </view>
        <view v-for="item in user_info.card" :key="item._id" @click="selectCard(item._id)">
          <vip-card :data="item.card_info" :customStyle="{ transform: 'scale(0.9)', 'transform-origin': 'left' }"></vip-card>
        </view>
      </view>
      <view class="content" v-if="user_info.card">
        <view class="top-title">
          <text class="text">行为日志</text>
          <text class="more">查看更多</text>
        </view>
        <view class="footmark">
          <view class="block">
            <view class="text">参与了“俱乐部接龙活动”</view>
            <view class="time">2025-01-24 14:23:33</view>
          </view>
          <view class="block">
            <view class="text">购买了场地卡</view>
            <view class="time">2025-01-24 12:23:33</view>
          </view>
        </view>
      </view>
    </view>
    <t-actionsheet v-model="actionVisible" :action="action" @click="actionClick"></t-actionsheet>

    <t-actionsheet v-model="cardVisible" :action="cardAction" @click="cardActionClick"></t-actionsheet>
    <t-modal v-model="editRemark.visible" :title="editRemark.title" @confirm="editConfirm">
      <t-input labelWidth="0" maxlength="8" :textarea="editRemark.textarea" v-model="editRemark.text"></t-input>
    </t-modal>
    <edit-card-value v-model="editCardVisible" :card-id="selectCardId"></edit-card-value>
  </view>
</template>
<script>
import vipCard from "@/components/card/vip-card.vue";
import editCardValue from "@/components/business/editCardValue/index.vue";
export default {
  components: { vipCard, editCardValue },
  data() {
    return {
      club_id: null,
      user_id: null,
      user_info: null,
      scrollTop: 0,

      editRemark: {
        visible: false,
        title: "编辑备注名称",
        text: "",
        textarea: false,
      },

      actionVisible: false,
      action: [
        {
          label: "编辑备注名称",
          value: "remark_name",
        },
        {
          label: "编辑备注信息",
          value: "remark_info",
        },
        {
          label: "添加会员卡",
          value: "add_card",
        },
        {
          label: "踢出成员",
          value: "out",
          color: "red",
        },
      ],

      // 卡片操作
      cardVisible: false,
      editCard: null,
      editCardVisible: false,
      selectCardId: null,
      cardAction: [
        {
          label: "编辑会员卡",
          value: "edit",
        },
        {
          label: "删除会员卡",
          value: "delete",
          color: "red",
        },
      ],
    };
  },
  onPageScroll(e) {
    this.scrollTop = e.scrollTop;
  },
  onLoad(options) {
    this.club_id = vk.getVuex("$club.id");
    this.user_id = options.id;
    this.getInfo();
  },
  onUnload() {
    uni.$off("vip-card-select");
  },
  methods: {
    selectCard(id) {
      this.selectCardId = id;
      this.cardVisible = true;

      // uni.navigateTo({
      //   url: "/pages_club/views/member/editCard?id=" + id,
      // });
    },
    cardActionClick(type) {
      console.log("类型", type);

      switch (type) {
        case "edit":
          this.editCardVisible = true;
        case "delete":
          // this.deleteCard();
          break;
      }
      this.cardVisible = false;
    },
    // 获取用户信息
    async getInfo() {
      let data = await vk.callFunction({
        url: "client/club/member/kh/get",
        title: "请求中...",
        data: {
          user_id: this.user_id,
          club_id: this.club_id,
        },
      });
      this.user_info = data.data;
    },
    actionClick(type) {
      switch (type) {
        case "remark_name":
          this.$set(this, "editRemark", {
            visible: true,
            textarea: false,
            title: "编辑备注名称",
            text: "",
          });
          break;
        case "remark_info":
          this.$set(this, "editRemark", {
            visible: true,
            textarea: true,
            title: "编辑备注信息",
            text: "",
          });
          break;
        case "add_card":
          uni.$once("vip-card-select", async (data) => {
            await this.addCard(data);
          });
          uni.navigateTo({
            url: "/pages/public/select/vipCard?type=club&club_id=" + this.club_id,
          });
          break;
        case "out":
          this.kickOut();
          break;
      }
      this.actionVisible = false;
    },
    editConfirm() {
      console.log(this.editRemark.text);
    },
    // 添加会员卡
    async addCard(data) {
      try {
        let res = await vk.callFunction({
          url: "client/club/member/kh/addCard",
          title: "请求中...",
          data: {
            user_id: this.user_id,
            club_id: this.club_id,
            card_id: data._id,
          },
        });
        vk.toast("添加成功");
        // 刷新信息
        this.getInfo();
      } catch (err) {}
    },
    // 踢出成员
    kickOut() {
      uni.showModal({
        content: "是否要把该成员踢出俱乐部",
        success: (res) => {
          if (res.confirm) {
          }
        },
      });
    },
  },
};
</script>
<style scoped lang="scss">
.user {
  &-cover {
    width: 100%;
    height: 800rpx;
    position: fixed;
    top: 0;
    left: 0;
  }

  &-content {
    width: 100%;
    min-height: calc(100vh - 700rpx);
    position: absolute;
    top: 700rpx;
    background-color: #fff;
    border-radius: 68rpx 0 0 0;
    padding: 30rpx 60rpx;
    box-sizing: border-box;

    .setting {
      position: absolute;
      right: 60rpx;
      top: 40rpx;
      height: 48rpx;
    }

    .content {
      margin-top: 40rpx;
    }

    .top-title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .text {
        font-size: 32rpx;
        font-weight: bold;
        color: #333;
      }

      .more {
        font-size: 28rpx;
      }
    }

    .type {
      background-color: #f8f8f8;
      padding: 30rpx;
      box-sizing: border-box;
      border-radius: 28rpx;
      margin-top: 30rpx;

      &-block {
        display: flex;
        align-items: center;
        font-size: 32rpx;
        margin-top: 20rpx;

        .label {
          margin-right: 30rpx;
        }
      }
    }
  }

  &-name {
    font-size: 48rpx;
    font-weight: bold;
    color: #333;
    padding-right: 50rpx;

    .setting {
      height: 40rpx;
    }
  }

  &-remark {
    font-size: 28rpx;
    margin-top: 30rpx;
    color: #8a8a8a;
  }

  &-small-info {
    width: 40vw;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    left: 0;
    right: 0;
    bottom: 14rpx;
    margin: 0 auto;

    .avatar {
      width: 60rpx;
      height: 60rpx;
      border-radius: 50%;
      margin-right: 20rpx;
      flex-shrink: 0;
      background-color: #fff;
    }

    .name {
      flex: 1;
      font-size: 28rpx;
      color: #333;
    }
  }
}

.footmark {
  font-size: 28rpx;

  .block {
    margin-top: 20rpx;
    padding: 30rpx;
    box-sizing: border-box;
    background-color: #f8f8f8;
    border-radius: 12rpx;

    .time {
      color: #8a8a8a;
      text-align: right;
      margin-top: 20rpx;
    }
  }
}
</style>
