'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/site/pub/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, club_id, pageIndex,ids } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		const whereJson = {club_id}
		if(ids) whereJson._id = _.in(ids)
		res = await vk.baseDao.select({
			dbName: "club-site-data",
			getCount: true,
			pageIndex,
			pageSize: 20,
			where<PERSON><PERSON>,
			field<PERSON>son: {},
			sortArr: [{ "name": "_add_time", "type": "desc" }],
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}