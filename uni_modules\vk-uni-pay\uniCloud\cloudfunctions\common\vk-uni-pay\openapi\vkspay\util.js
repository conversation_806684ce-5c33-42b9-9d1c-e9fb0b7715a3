/**
 * 支付宝工具包
 * 请勿修改此处代码，因为插件更新后此处代码会被覆盖。
 * 作者：VK
 * 发布于：2021-07-06
 */
const crypto = require("crypto");

var util = {};

/**
 * 签名
 */
util.md5 = function(text) {
	return crypto.createHash('md5').update(text).digest('hex');
};

/**
 * 对象属性排序
 */
util.objectKeySort = function(obj) {
	let keys = Object.keys(obj).sort();
	let newObject = {};
	for (let i in keys) {
		newObject[keys[i]] = (obj[keys[i]]);
	}
	return newObject;
};
/**
 * 对象转url参数
 */
util.jsonToUrlString = function(json) {
	let str = "";
	for (let key in json) {
		if (json.hasOwnProperty(key)) {
			let value = json[key];
			if (str !== "") {
				str += "&";
			}
			str += key + "=" + value;
		}
	}
	return str;
};

/**
 * url参数转对象
 */
util.urlStringToJson = function(str) {
	let json = {};
	if (str != "" && str != undefined && str != null) {
		let arr = str.split("&");
		for (let i = 0; i < arr.length; i++) {
			let arrstr = arr[i].split("=");
			let k = arrstr[0];
			let v = arrstr[1];
			json[k] = v;
		}
	}
	return json;
};

/**
 * 保留小数
 * @param {Number} val 原值
 * @param {Number} precision 精度
 */
util.toDecimal = function(val, precision = 0) {
	if (typeof val === "string") val = Number(val);
	return parseFloat(val.toFixed(precision));
};

module.exports = util;
