@mixin hide-slide-down($second) {
	animation: hide-slide-down $second forwards;
}

@mixin show-slide-down($second) {
	animation: show-slide-down $second forwards;
}

@mixin hide-opacity($second) {
	animation: hide-opacity $second forwards;
}

@mixin show-opacity($second) {
	animation: show-opacity $second forwards;
}

@mixin scale-out-center($second) {
	animation: scale-out-center $second forwards;
}

@mixin scale-in-center($second) {
	animation: scale-in-center $second forwards;
}

@mixin scale-out-vertical($second) {
	animation: scale-out-vertical $second forwards;
}

@mixin scale-in-ver-center($second) {
	animation: scale-in-ver-center $second forwards;
}
@keyframes scale-out-vertical {
  0% {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scaleY(0);
            transform: scaleY(0);
    opacity: 1;
  }
}

@keyframes scale-in-ver-center {
  0% {
    -webkit-transform: scaleY(0);
            transform: scaleY(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: scaleY(1);
            transform: scaleY(1);
    opacity: 1;
  }
}

@keyframes scale-out-center {
  0% {
    -webkit-transform: scale(1);
            transform: scale(1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(0);
            transform: scale(0);
    opacity: 1;
  }
}

@keyframes hide-slide-down {
	from {
		transform: translateY(0);
	}

	to {
		transform: translateY(100vh);
	}
}

@keyframes show-slide-down {
	from {
		transform: translateY(100vh);
	}

	to {
		transform: translateY(0);
	}
}

@keyframes hide-opacity {
	0% {
		opacity: 1;
	}

	80% {
		transform: scale(1);
	}

	100% {
		opacity: 0;
		transform: scale(0);
	}
}

@keyframes show-opacity {
	0% {
		opacity: 0;
		transform: scale(1);
	}

	100% {
		opacity: 1;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2023-2-16 13:10:59
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-in-center
 * ----------------------------------------
 */
@-webkit-keyframes scale-in-center {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}
}
@keyframes scale-in-center {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2023-2-16 12:59:6
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-out-bck-center
 * ----------------------------------------
 */
@-webkit-keyframes slide-out-bck-center {
	0% {
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		opacity: 1;
	}
	100% {
		-webkit-transform: translateZ(-1100px);
		transform: translateZ(-1100px);
		opacity: 0;
	}
}
@keyframes slide-out-bck-center {
	0% {
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		opacity: 1;
	}
	100% {
		-webkit-transform: translateZ(-1100px);
		transform: translateZ(-1100px);
		opacity: 0;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2023-2-16 12:58:24
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-in-fwd-center
 * ----------------------------------------
 */
@-webkit-keyframes slide-in-fwd-center {
	0% {
		-webkit-transform: translateZ(-1400px);
		transform: translateZ(-1400px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		opacity: 1;
	}
}
@keyframes slide-in-fwd-center {
	0% {
		-webkit-transform: translateZ(-1400px);
		transform: translateZ(-1400px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateZ(0);
		transform: translateZ(0);
		opacity: 1;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2022-7-9 19:54:8
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation text-focus-in
 * ----------------------------------------
 */
@-webkit-keyframes text-focus-in {
	0% {
		-webkit-filter: blur(12px);
		filter: blur(12px);
		opacity: 0;
	}
	100% {
		-webkit-filter: blur(0px);
		filter: blur(0px);
		opacity: 1;
	}
}
@keyframes text-focus-in {
	0% {
		-webkit-filter: blur(12px);
		filter: blur(12px);
		opacity: 0;
	}
	100% {
		-webkit-filter: blur(0px);
		filter: blur(0px);
		opacity: 1;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2023-1-11 11:6:11
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation fade-in
 * ----------------------------------------
 */
@-webkit-keyframes fade-in {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}
@keyframes fade-in {
	0% {
		opacity: 0;
	}
	100% {
		opacity: 1;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2022-7-8 16:59:31
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation fade-in-bottom
 * ----------------------------------------
 */
@-webkit-keyframes fade-in-bottom {
	0% {
		-webkit-transform: translateY(50px);
		transform: translateY(50px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		opacity: 1;
	}
}
@keyframes fade-in-bottom {
	0% {
		-webkit-transform: translateY(50px);
		transform: translateY(50px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		opacity: 1;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2022-7-8 16:59:31
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation fade-in-right
 * ----------------------------------------
 */
@-webkit-keyframes fade-in-right {
	0% {
		-webkit-transform: translateX(50px);
		transform: translateX(50px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateX(0);
		transform: translateX(0);
		opacity: 1;
	}
}
@keyframes fade-in-right {
	0% {
		-webkit-transform: translateX(50px);
		transform: translateX(50px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateX(0);
		transform: translateX(0);
		opacity: 1;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2022-12-14 11:5:51
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation fade-in-left
 * ----------------------------------------
 */
@-webkit-keyframes fade-in-left {
  0% {
    -webkit-transform: translateX(-50px);
            transform: translateX(-50px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 1;
  }
}
@keyframes fade-in-left {
  0% {
    -webkit-transform: translateX(-50px);
            transform: translateX(-50px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
    opacity: 1;
  }
}
/* ----------------------------------------------
 * Generated by Animista on 2022-7-8 14:50:21
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-in-bottom
 * ----------------------------------------
 */
@-webkit-keyframes slide-in-bottom {
	0% {
		-webkit-transform: translateY(1000px);
		transform: translateY(1000px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		opacity: 1;
	}
}
@keyframes slide-in-bottom {
	0% {
		-webkit-transform: translateY(1000px);
		transform: translateY(1000px);
		opacity: 0;
	}
	100% {
		-webkit-transform: translateY(0);
		transform: translateY(0);
		opacity: 1;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2022-7-8 10:47:39
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation shadow-drop-center
 * ----------------------------------------
 */
@-webkit-keyframes shadow-drop-center {
	0% {
		-webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
		box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
	}
	100% {
		-webkit-box-shadow: 0 0 20rpx 0rpx rgba(0, 0, 0, 0.35);
		box-shadow: 0 0 20rpx 0rpx rgba(0, 0, 0, 0.35);
	}
}
@keyframes shadow-drop-center {
	0% {
		-webkit-box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
		box-shadow: 0 0 0 0 rgba(0, 0, 0, 0);
	}
	100% {
		-webkit-box-shadow: 0 0 20rpx 0rpx rgba(0, 0, 0, 0.35);
		box-shadow: 0 0 20rpx 0rpx rgba(0, 0, 0, 0.35);
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2022-7-8 10:18:36
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation tracking-in-expand
 * ----------------------------------------
 */
@-webkit-keyframes tracking-in-expand {
	0% {
		letter-spacing: -0.5em;
		opacity: 0;
	}
	40% {
		opacity: 0.6;
	}
	100% {
		opacity: 1;
	}
}
@keyframes tracking-in-expand {
	0% {
		letter-spacing: -0.5em;
		opacity: 0;
	}
	40% {
		opacity: 0.6;
	}
	100% {
		opacity: 1;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2023-1-10 21:16:36
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-in-top
 * ----------------------------------------
 */
@-webkit-keyframes scale-in-top {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
		-webkit-transform-origin: 50% 0%;
		transform-origin: 50% 0%;
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-transform-origin: 50% 0%;
		transform-origin: 50% 0%;
		opacity: 1;
	}
}
@keyframes scale-in-top {
	0% {
		-webkit-transform: scale(0);
		transform: scale(0);
		-webkit-transform-origin: 50% 0%;
		transform-origin: 50% 0%;
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(1);
		transform: scale(1);
		-webkit-transform-origin: 50% 0%;
		transform-origin: 50% 0%;
		opacity: 1;
	}
}

/* ----------------------------------------------
 * Generated by Animista on 2022-7-8 10:15:7
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-in-hor-left
 * ----------------------------------------
 */
@-webkit-keyframes scale-in-hor-left {
	0% {
		-webkit-transform: scaleX(0);
		transform: scaleX(0);
		-webkit-transform-origin: 0% 0%;
		transform-origin: 0% 0%;
		opacity: 1;
	}
	100% {
		-webkit-transform: scaleX(1);
		transform: scaleX(1);
		-webkit-transform-origin: 0% 0%;
		transform-origin: 0% 0%;
		opacity: 1;
	}
}
@keyframes scale-in-hor-left {
	0% {
		-webkit-transform: scaleX(0);
		transform: scaleX(0);
		-webkit-transform-origin: 0% 0%;
		transform-origin: 0% 0%;
		opacity: 1;
	}
	100% {
		-webkit-transform: scaleX(1);
		transform: scaleX(1);
		-webkit-transform-origin: 0% 0%;
		transform-origin: 0% 0%;
		opacity: 1;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2022-7-7 11:30:27
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation shake-horizontal
 * ----------------------------------------
 */
@-webkit-keyframes shake-horizontal {
	0%,
	100% {
		-webkit-transform: translateX(0);
		transform: translateX(0);
	}
	10%,
	30%,
	50%,
	70% {
		-webkit-transform: translateX(-10rpx);
		transform: translateX(-10rpx);
	}
	20%,
	40%,
	60% {
		-webkit-transform: translateX(10rpx);
		transform: translateX(10rpx);
	}
	80% {
		-webkit-transform: translateX(8rpx);
		transform: translateX(8rpx);
	}
	90% {
		-webkit-transform: translateX(-8rpx);
		transform: translateX(-8rpx);
	}
}
@keyframes shake-horizontal {
	0%,
	100% {
		-webkit-transform: translateX(0);
		transform: translateX(0);
	}
	10%,
	30%,
	50%,
	70% {
		-webkit-transform: translateX(-10rpx);
		transform: translateX(-10rpx);
	}
	20%,
	40%,
	60% {
		-webkit-transform: translateX(10rpx);
		transform: translateX(10rpx);
	}
	80% {
		-webkit-transform: translateX(8rpx);
		transform: translateX(8rpx);
	}
	90% {
		-webkit-transform: translateX(-8rpx);
		transform: translateX(-8rpx);
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2022-9-30 14:32:56
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation flip-out-hor-top
 * ----------------------------------------
 */
@-webkit-keyframes flip-out-hor-top {
	0% {
		-webkit-transform: rotateX(0);
		transform: rotateX(0);
		opacity: 1;
	}
	100% {
		-webkit-transform: rotateX(70deg);
		transform: rotateX(70deg);
		opacity: 0;
	}
}
@keyframes flip-out-hor-top {
	0% {
		-webkit-transform: rotateX(0);
		transform: rotateX(0);
		opacity: 1;
	}
	100% {
		-webkit-transform: rotateX(70deg);
		transform: rotateX(70deg);
		opacity: 0;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2022-9-30 14:36:34
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-out-vertical
 * ----------------------------------------
 */
@-webkit-keyframes scale-out-vertical {
	0% {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
		opacity: 1;
	}
	100% {
		-webkit-transform: scaleY(0);
		transform: scaleY(0);
		opacity: 1;
	}
}
@keyframes scale-out-vertical {
	0% {
		-webkit-transform: scaleY(1);
		transform: scaleY(1);
		opacity: 1;
	}
	100% {
		-webkit-transform: scaleY(0);
		transform: scaleY(0);
		opacity: 1;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2022-9-30 14:40:34
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-out-hor-left
 * ----------------------------------------
 */
@-webkit-keyframes scale-out-hor-left {
	0% {
		-webkit-transform: scaleX(1);
		transform: scaleX(1);
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		opacity: 1;
	}
	100% {
		-webkit-transform: scaleX(0);
		transform: scaleX(0);
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		opacity: 1;
	}
}
@keyframes scale-out-hor-left {
	0% {
		-webkit-transform: scaleX(1);
		transform: scaleX(1);
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		opacity: 1;
	}
	100% {
		-webkit-transform: scaleX(0);
		transform: scaleX(0);
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		opacity: 1;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2022-9-30 14:42:11
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation scale-out-center
 * ----------------------------------------
 */
@-webkit-keyframes scale-out-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1;
	}
}
@keyframes scale-out-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 1;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2022-9-30 14:44:2
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-out-bck-center
 * ----------------------------------------
 */
@-webkit-keyframes slide-out-bck-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 0;
	}
}
@keyframes slide-out-bck-center {
	0% {
		-webkit-transform: scale(1);
		transform: scale(1);
		opacity: 1;
	}
	100% {
		-webkit-transform: scale(0);
		transform: scale(0);
		opacity: 0;
	}
}
/* ----------------------------------------------
 * Generated by Animista on 2025-3-11 10:38:49
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-in-top
 * ----------------------------------------
 */
@-webkit-keyframes slide-in-top {
  0% {
    -webkit-transform: translateY(-1000px);
            transform: translateY(-1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
  }
}
@keyframes slide-in-top {
  0% {
    -webkit-transform: translateY(-1000px);
            transform: translateY(-1000px);
    opacity: 0;
  }
  100% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
  }
}
/* ----------------------------------------------
 * Generated by Animista on 2025-3-11 10:39:19
 * Licensed under FreeBSD License.
 * See http://animista.net/license for more info. 
 * w: http://animista.net, t: @cssanimista
 * ---------------------------------------------- */

/**
 * ----------------------------------------
 * animation slide-out-top
 * ----------------------------------------
 */
@-webkit-keyframes slide-out-top {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateY(-1000px);
            transform: translateY(-1000px);
    opacity: 0;
  }
}
@keyframes slide-out-top {
  0% {
    -webkit-transform: translateY(0);
            transform: translateY(0);
    opacity: 1;
  }
  100% {
    -webkit-transform: translateY(-1000px);
            transform: translateY(-1000px);
    opacity: 0;
  }
}
