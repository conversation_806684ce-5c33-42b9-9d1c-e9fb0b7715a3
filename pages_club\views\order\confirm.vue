<template>
	<view class="page-content">
		<t-navbar title="订单信息"></t-navbar>
		<view class="info">
			<view class="info-title">订单详情</view>
			<view class="info-list">
				<view class="info-block">
					<image src="/static/images/full.png" mode="aspectFill"></image>
					<view>
						<view>室外场</view>
						<view v-for="item in list">
							<text>{{item}}</text>
							<text></text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import { splitTime } from '@/utils/time'
	export default {
		data() {
			return {
				list: [],
				id: null,
				club_config: null,
				time_line: null,
			}
		},
		watch: {
			"$store.state.$club.id": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.id = newVal
					this.init()
				}
			}
		},
		onLoad() {
			let data = vk.getStorageSync('orderConfirm')
			// vk.removeStorageSync('orderConfirm')
			this.id = vk.getVuex('$club.id')
			// 获取俱乐部配置信息
			this.getConfig()
			this.handleOrder(data)
		},
		methods: {
			async handleOrder(data) {
				// 计算总价
				let total_price = data.data.reduce((acc, item) => acc += item.price, 0)
				// 优化信息显示
				// 处理场地以及时间分类
				const site_ary = data.data.reduce((acc, item) => {
					// 如果映射中还没有这个site_id，则创建一个新数组
					if (!acc[item.site_id]) {
						acc[item.site_id] = [];
					}
					// 将当前项添加到对应site_id的数组中
					acc[item.site_id].push(item);
					return acc;
				}, {});
				// 请求场地数据
				let site = await vk.callFunction({
					url: 'client/club/site/pub/list',
					title: '请求中...',
					data: {
						club_id: this.id,
						ids: Object.keys(site_ary)
					},
				});
				console.log(site);

				const booked_ary = {}
				Object.keys(site_ary).forEach(id => {
					let data = site_ary[id].sort((a, b) => a.timestamp - b.timestamp)
					let time_line = vk.pubfn.deepClone(this.time_line)
					booked_ary[id] = []
					let obj = {
						start_time: null,
						end_time: null,
						price: 0,
					}
					time_line = time_line.forEach(item => {
						let info = data.find(items => item.timestamp == items.timestamp)
						if (info) {
							if (!obj.start_time) obj.start_time = item.time
							obj.price += info.price
						} else {
							if (obj.start_time) {
								obj.end_time = item.time
								booked_ary[id].push(obj)
								obj = {
									start_time: null,
									end_time: null,
									price: 0
								}
							}
						}
					})
				})


			},
			getConfig() {
				this.club_config = {
					open: "09:00",
					close: "22:00",
					time_type: 'half',
				}
				this.time_line = splitTime(this.club_config.open, this.club_config.close, false, this.club_config
					.time_type)
			},
		}
	}
</script>

<style lang="scss" scoped>
	.page-content {
		padding: 30rpx;
		box-sizing: border-box;
	}

	.info {
		background-color: #fff;
		padding: 30rpx;
		width: 100%;
		box-sizing: border-box;
		border-radius: 12rpx;

		&-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #000;
		}
	}
</style>