<template>
  <view class="page-content">
    <t-navbar title="水平测验"></t-navbar>
    <view class="content-box">
      <view class="top">
        <view class="title">能力分析图</view>
        <!-- <view class="comment">已有<text class="comment-num">{{commentNum}}</text>人评价</view> -->
      </view>
      <t-capacity-canvas :data="scoreData" />
      <view class="footer">
        <!-- <t-button width="160" height="68" size="24" :customStyle="{marginRight:'20rpx'}">查看评价</t-button> -->
        <wd-button type="primary" @click="toTest">{{ checkText }}</wd-button>
      </view>
    </view>
    <view class="content-box">
      <view class="top">
        <view class="title">NTRP评级</view>
      </view>
      <view class="text" v-html="tips"></view>
      <wd-button @click="vk.toast('功能暂未开放')" block custom-style="margin:0 auto">高水平认证</wd-button>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      commentNum: 0,
      scoreData: null,
      tips: `<h4>1.0 - 1.5：初学者</h4><p> &nbsp; &nbsp;1.0：刚刚开始接触网球，对基本规则和击球技术一无所知。</p><p> &nbsp; &nbsp;1.5：有了一些击球经验，但技术非常不稳定，无法持续对打。</p><p><br></p><h4>2.0 - 2.5：初级水平</h4><p> &nbsp; &nbsp;2.0：知道基本的规则和计分方法，能够进行简单的对打，但技术不稳定，缺乏控制。</p><p> &nbsp; &nbsp;2.5：能够进行短时间的对打，但击球方向和深度控制不佳，发球和接发球技术较弱。</p><p><br></p><h4>3.0 - 3.5：中级水平</h4><p> &nbsp; &nbsp;3.0：能够稳定地进行中等速度的对打，掌握基本的正手、反手、发球和截击技术，但缺乏战术意识。</p><p> &nbsp; &nbsp;3.5：能够稳定地进行中等速度的对打，并开始运用一些简单的战术，如改变击球方向和深度。发球和接发球技术有所提高。</p><p><br></p><h4>4.0 - 4.5：高级水平</h4><p> &nbsp; &nbsp;4.0：能够稳定地进行中高速的对打，掌握多种击球技术，并能够运用战术。发球和接发球技术较好，能够在比赛中保持一定的稳定性。</p><p> &nbsp; &nbsp;4.5：技术全面，能够稳定地进行高速对打，并能够运用复杂的战术。发球和接发球技术出色，能够在比赛中保持高水平的稳定性。</p><p><br></p><h4>5.0 - 5.5：准职业水平</h4><p> &nbsp; &nbsp;5.0：技术非常全面，能够在比赛中运用各种战术，发球和接发球技术非常出色。能够在高水平的比赛中保持稳定性，并具备一定的比赛经验。</p><p> &nbsp; &nbsp;5.5：接近职业水平，技术非常全面，能够在高强度的比赛中保持高水平的稳定性，并具备丰富的比赛经验。</p><p><br></p><h4>6.0 - 7.0：职业水平</h4><p> &nbsp; &nbsp;6.0：职业球员，具备参加国际比赛的能力，技术全面，战术意识强，比赛经验丰富。</p><p> &nbsp; &nbsp;6.5 - 7.0：顶级职业球员，具备参加大满贯赛事的能力，技术、战术和比赛经验都达到世界顶尖水平。</p><p><br></p>`,
    };
  },
  computed: {
    checkText() {
      return this.userInfo.level ? "重新测评" : "开始测评";
    },
  },
  onLoad() {
    this.getLevelScore();
  },
  methods: {
    toTest() {
      uni.$off("level-test-over");
      uni.$once("level-test-over", () => {
        this.getLevelScore();
      });
      vk.navigateTo("/pages/public/checkLevel");
    },
    async getLevelScore() {
      let { comment_num, data } = await vk.callFunction({
        url: "client/user/level/kh/base",
      });
      this.commentNum = comment_num;
      this.scoreData = data;
    },
  },
};
</script>

<style scoped lang="scss">
.page-content {
  padding-bottom: 30rpx;
}

.content-box {
  background-color: #fff;
  border-radius: 16rpx;
  margin: 30rpx;
  padding: 30rpx;

  .top {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .comment {
      font-size: 24rpx;

      &-num {
        color: $primary-color;
        margin: 0 10rpx;
      }
    }

    .title {
      font-size: 36rpx;
      font-weight: bold;
    }
  }

  .text {
    margin-top: 40rpx;
  }

  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
  }
}
</style>
