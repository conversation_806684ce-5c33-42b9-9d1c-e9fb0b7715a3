'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url admin/examine/string/sys/update 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, params, update } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let dataJson = {}
		for (let i in update) {
			let key = update[i];
			if (vk.pubfn.isNotNull(params[key])) dataJson[key] = params[key];
		}

		await vk.baseDao.updateById({
			dbName: "info-string-data",
			id: params.info_id,
			dataJson,
			getUpdateData: false
		});

		let options = [
			{ "label": "图片", "value": "image" },
			{ "label": "店铺名", "value": "name" },
			{ "label": "简介", "value": "intro" },
			{ "label": "经纬度", "value": "location.coordinates" },
			{ "label": "地址", "value": "address" },
			{ "label": "参考价格", "value": "price" },
			{ "label": "联系方式", "value": "contact_info" },
			{ "label": "营业时间", "value": "open_time" }
		]

		let content = []
		if (params.creator_id) {
			// 获取文本
			for (const key in dataJson) {
				let info = options.find(item => item.value == key)
				content.push(info.label)
			}
			let text = `维护了${content.join('、')}信息`
			console.log(text);

			// 添加历史更新记录
			await vk.baseDao.add({
				dbName: "info-edit-history",
				dataJson: {
					info_id: params.info_id,
					user_id: params.creator_id,
					text,
					update_time: params._add_time
				}
			});

			let notice_title = `更新商店信息已审核通过`
			let notice_text = `您提交更新商店“${params.name}”中的"${content.join('、')}"信息已审核通过，十分感谢您对本平台的支持`
			await vk.baseDao.add({
				dbName: "user-news-data",
				dataJson: {
					accept_user: params.creator_id,
					title: notice_title,
					text: notice_text,
					read_state: false,
					type: "info",
					link: `/pages/explore/string/detail?id=${params.info_id}`,
				}
			})
		}

		// 添加完后删除相关信息
		await vk.baseDao.deleteById({
			dbName: "pending-audit-data",
			id: params._id
		});
		res.msg = "更新成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}