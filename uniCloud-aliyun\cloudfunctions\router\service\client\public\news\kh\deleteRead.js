'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/news/kh/deleteRead 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		await vk.baseDao.del({
			dbName: "user-news-data",
			whereJson: {
				accept_user: uid,
				read_state: true
			}
		});
		res.msg = "删除成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}