<template>
  <view class="t-title" :style="[customStyle, titleStyle]">
    <slot></slot>
  </view>
</template>

<script>
export default {
  props: {
    style: {
      type: String,
    },
    size: {
      type: String,
    },
    customStyle: {
      type: Object,
      default: () => {},
    },
  },
  mounted() {},
  computed: {
    titleStyle() {
      return {
        fontFamily: this.style || "usic",
        fontSize: this.size || "40rpx",
      };
    },
  },
};
</script>

<style scoped lang="scss">
.t-title {
  flex-shrink: 0;
}
</style>
