{"uni-id-users": {"data": [{"_id": "test_001", "username": "test_001", "register_date": 1596416400000, "register_ip": "*******", "nickname": "测试用户", "balance": 0}], "index": [{"IndexName": "username", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "username", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "mobile", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "mobile", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "email", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "email", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "wx_openid.app-plus", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "wx_openid.app-plus", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "wx_openid.mp-weixin", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "wx_openid.mp-weixin", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "wx_unionid", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "wx_unionid", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "ali_openid", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "ali_openid", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "my_invite_code", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "my_invite_code", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "inviter_uid", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "inviter_uid", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "invite_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "invite_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "role", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "role", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "register_date", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "register_date", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-pay-orders": {"data": [], "index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "out_trade_no", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "out_trade_no", "Direction": "1"}], "MgoIsUnique": true}}, {"IndexName": "user_order_success", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "user_order_success", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "transaction_id", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "transaction_id", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "total_fee", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "total_fee", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "pid", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "pid", "Direction": "1"}], "MgoIsUnique": false}}]}, "vk-pay-config": {"data": [], "index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}]}, "opendb-open-data": {"data": [], "index": [{"IndexName": "_add_time", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "_add_time", "Direction": "1"}], "MgoIsUnique": false}}, {"IndexName": "expired", "MgoKeySchema": {"MgoIndexKeys": [{"Name": "expired", "Direction": "1"}], "MgoIsUnique": false}}]}}