<template>
  <view class="page-content">
    <t-navbar :title="club_info.name" :placeholder="config.navStyle == 'title'" :navStyle="config.navStyle" :custom-style="navStyle"></t-navbar>
    <!-- <view class="header">
			<t-tabs v-model="current" :list="tabs"></t-tabs>
		</view> -->
    <view v-if="club_info">
      <scroll-view
        scroll-y
        :show-scrollbar="false"
        :refresher-enabled="pullEnable"
        :scroll-top="scrollTop"
        :refresher-triggered="triggered"
        :refresher-threshold="100"
        @refresherrefresh="refreshModel"
        :style="[contentStyle]"
        @scroll="scrollPage"
        @scrolltolower="loadMoreModel"
      >
        <base-page v-show="current == 0"></base-page>
        <t-empty v-show="current == 1" data="data" text="暂无内容"></t-empty>
        <shop-page v-show="current == 2" ref="shop" @detail="cardShow"></shop-page>
        <site-page v-show="current == 3"></site-page>
        <manage-page v-show="current == 4"></manage-page>
      </scroll-view>

      <t-bottom :placeholder="false">
        <view class="bottom-box">
          <template v-if="!join_info || join_info.state == 'applying'">
            <view style="flex: 1">
              <t-button color="#0171BC" width="100%" radius="12" v-if="!join_info" text-color="#fff" @click="joinClub">申请加入</t-button>
              <t-button color="#ff3030" v-else-if="join_info.state == 'applying'" width="100%" radius="12" text-color="#fff" @click="backout"
                >取消申请</t-button
              >
            </view>
          </template>
          <template v-else>
            <view class="tabbar" :class="{ unselect: current !== index }" v-for="(item, index) in page_config" @click="current = index">
              <image :src="item.icon" mode="heightFix"></image>
              <text>{{ item.name }}</text>
            </view>
          </template>
        </view>
      </t-bottom>

      <!-- 购买会员卡弹窗 -->
      <t-popup v-model="card_visible" title="购买确认">
        <view class="card-content">
          <view class="card-item">
            <text class="text">会员卡名称</text>
            <text class="text">{{ card_info.name }}</text>
          </view>
          <view class="card-item">
            <text class="text">会员卡{{ valueLabel }}</text>
            <text class="text">{{ cardValue }}</text>
          </view>
          <view class="card-item">
            <text class="text">所属俱乐部</text>
            <text class="text">{{ card_info.club_info.name }}</text>
          </view>
          <view class="card-item">
            <text class="text">会员卡价格</text>
            <view>
              <text class="price">{{ card_info.price }}</text>
              <text class="text">元</text>
            </view>
          </view>
        </view>
        <t-button width="auto" :customStyle="{ margin: '30rpx auto' }" @click="shopCard">购买</t-button>
      </t-popup>
    </view>
  </view>
</template>

<script>
import basePage from "@/pages_club/views/home/<USER>/base.vue";
import shopPage from "@/pages_club/views/home/<USER>/shop.vue";
import managePage from "@/pages_club/views/home/<USER>/manage.vue";
import sitePage from "@/pages_club/views/home/<USER>/site.vue";
import dayjs from "dayjs";
export default {
  components: {
    basePage,
    shopPage,
    managePage,
    sitePage,
  },
  data() {
    return {
      club_info: null,
      id: null,
      user_info: vk.getVuex("$user.userInfo"),
      system: uni.getSystemInfoSync(),

      scrollTop: 0,
      loaded: false,
      current: 0,
      join_info: null,

      pullEnable: false,
      triggered: false,

      // 购买信息
      card_visible: false,
      card_info: null,

      // 页面配置
      page_config: [
        {
          key: "home",
          pullEnable: false,
          loadMore: false,
          navStyle: "custom",
          scrollTop: 0,
          icon: "/static/images/i-mingcute-home-2-line.svg",
          name: "首页",
        },
        {
          key: "activity",
          pullEnable: true,
          loadMore: true,
          navStyle: "title",
          scrollTop: 0,
          icon: "/static/images/i-bx-tennis-ball.svg",
          name: "活动",
        },
        {
          key: "shop",
          pullEnable: true,
          loadMore: false,
          navStyle: "title",
          scrollTop: 0,
          icon: "/static/images/i-mingcute-bank-card-line.svg",
          name: "会员卡",
        },
        {
          key: "site",
          pullEnable: false,
          loadMore: false,
          navStyle: "title",
          scrollTop: 0,
          icon: "/static/images/i-game-icons-tennis-court.svg",
          name: "场地",
        },
        {
          key: "manage",
          pullEnable: false,
          loadMore: false,
          navStyle: "custom",
          scrollTop: 0,
          icon: "/static/images/i-mingcute-settings-3-line.svg",
          name: "管理",
        },
      ],
    };
  },
  async onLoad(options) {
    uni.$off("refresh-finish");
    uni.$on("refresh-finish", () => {
      this.pullEnable = true;
      this.triggered = false;
      console.log("执行重置");
    });
    this.id = options.id;
    vk.setVuex("$club.id", options.id);
    try {
      let info = await vk.vuex.getters("$club/getInfo");
      this.getJoinState();
      this.club_info = info;
    } catch (err) {
      console.log(err);
    } finally {
      this.pullEnable = this.config.pullEnable;
      this.triggered = false;
    }
  },
  onShareAppMessage(e) {
    return {
      title: `${this.user_info.nickname}邀请您加入${this.club_info.name}`,
      imageUrl: this.club_info.cover,
    };
  },
  watch: {
    current(index) {
      this.pullEnable = this.page_config[index].pullEnable;
      this.scrollTop = this.config.scrollTop;
      console.log("滚动", this.scrollTop);
    },
  },
  computed: {
    navStyle() {
      if (this.config.navStyle === "custom") {
        return { "background-color": `rgba(255,255,255,${this.config.scrollTop / 200})` };
      } else {
        return { "background-color": "#fff" };
      }
    },
    tabs() {
      let tabs = ["首页", "活动"];
      if (!this.join_info) return tabs;
      let state = this.join_info.state;
      if (state == "manage" || state == "admin" || state == "coach") {
        tabs.push(...["会员卡", "场地", "管理"]);
      } else if (state == "member") {
        tabs.push(...["会员卡", "场地"]);
      }
      return tabs;
    },
    contentStyle() {
      const { statusBarHeight, screenHeight } = this.system;
      console.log(statusBarHeight, screenHeight);
      let base = 80;
      if (this.config.navStyle !== "custom") base += 40 + statusBarHeight;
      return {
        height: `${screenHeight - base}px`,
      };
    },
    valueLabel() {
      if (!this.card_info) return;
      switch (this.card_info.type) {
        case "day":
        case "date":
          return "到期时间";
        case "times":
          return "次数";
        case "save":
          return "额度";
      }
    },
    cardValue() {
      if (!this.card_info) return;
      switch (this.card_info.type) {
        case "day":
          return dayjs().add(this.card_info.day, "day").format("YYYY-MM-DD");
        case "date":
          return dayjs(this.card_info.end).format("YYYY-MM-DD");
        case "times":
          return this.card_info.value + "次";
        case "save":
          return this.card_info.value + "元";
        case "forever":
          return "永久有效";
      }
    },
    config() {
      return this.page_config[this.current];
    },
  },
  methods: {
    refreshModel() {
      let config = this.config;
      if (this.triggered) return;
      if (!config.pullEnable) return;
      this.triggered = true;
      this.$refs[config.key].refresh();
    },
    loadMoreModel() {
      let config = this.config;
      if (!config.loadMore) return;
      this.$refs[config.key].loadMore();
    },
    // 购买会员卡
    async shopCard() {
      try {
        let info = this.card_info;
        let data = await vk.callFunction({
          url: "client/order/kh/create",
          title: "请求中...",
          data: {
            subject: `购买会员卡<${info.name}>`,
            total_fee: info.price * 100,
            type: "card",
            card_id: info._id,
            club_id: this.id,
          },
        });
        uni.requestPayment({
          provider: data.provider,
          orderInfo: data.orderInfo,
          timeStamp: data.orderInfo.timeStamp,
          nonceStr: data.orderInfo.nonceStr,
          package: data.orderInfo.package,
          signType: data.orderInfo.signType,
          paySign: data.orderInfo.paySign,
          success: (res) => {
            this.card_visible = false;
            vk.toast("支付成功！", "none", true, () => {
              this.$refs.shop.refresh();
            });
          },
          fail: (error) => {
            console.log("失败了", error);
          },
        });
      } catch (error) {}
    },
    // 显示会员卡购买
    cardShow(data) {
      this.card_info = data;
      this.card_visible = true;
    },
    scrollPage(e) {
      this.page_config[this.current].scrollTop = e.detail.scrollTop;
    },
    async joinClub() {
      let res = await vk.callFunction({
        url: "client/club/kh/join",
        title: "正在加入...",
        data: {
          club_id: this.id,
        },
      });
      vk.toast(res.msg, "none", true, () => {
        this.getJoinState();
      });
    },
    async backout() {
      let res = await vk.callFunction({
        url: "client/club/kh/backout",
        title: "请求中...",
        data: {
          club_id: this.id,
        },
      });
      vk.toast(res.msg, "none", true, () => {
        this.getJoinState();
      });
    },
    async refreshInfo() {
      if (this.triggered) return;
      this.triggered = true;
      try {
        await vk.vuex.getters("$club/refreshInfo");
      } catch (err) {
        console.log(err);
      } finally {
        this.triggered = false;
      }
    },
    getJoinState() {
      console.log("id", this.id);
      vk.callFunction({
        url: "client/club/kh/joinList",
        title: "请求中...",
        data: {
          club_id: this.id,
        },
      }).then((data) => {
        this.join_info = data.rows.length == 0 ? null : data.rows[0];
        this.loaded = true;
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.header {
  width: 100%;
  height: 30px;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 0 20rpx 4px;
}

// 会员卡购买弹窗样式
.card-content {
  height: 240rpx;
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  padding: 20rpx 0;

  .card-item {
    display: flex;
    justify-content: space-between;
  }

  .price {
    font-size: 36rpx;
    font-weight: bold;
    color: #ff3030;
  }

  .text {
    font-size: 28rpx;
    font-weight: bold;
    color: #333;
  }
}

.bottom-box {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.unselect {
  opacity: 0.4;
}

.tabbar {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  image {
    height: 48rpx;
  }
}
</style>
