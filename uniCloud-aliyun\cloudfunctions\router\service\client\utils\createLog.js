'use strict';
/**
 * 日志记录生成转换
 * @type {string} type key为仅记录键不记录文本 text为记录键与文本 bool为记录键与是否 time为记录键的时间 date为记录键与日期
 * 
 */

class Log {
	constructor() {
		this.keys = {
			update_club: [
				{ "key": "name", "label": "名称", "type": "text" },
				{ "key": "logo", "label": "标志", "type": "key" },
				{ "key": "cover", "label": "封面", "type": "key" },
				{ "key": "banner", "label": "轮播图", "type": "key" },
				{ "key": "desc", "label": "描述", "type": "text" },
				{ "key": "images", "label": "介绍图", "type": "key" },
				{ "key": "address_name", "label": "位置", "type": "text" },
				{ "key": "address", "label": "详细地址", "type": "text" },
				{ "key": "apply", "label": "加入审批", "type": "bool" },
				{ "key": "startTime", "label": "营业开始时间", "type": "time" },
				{ "key": "endTime", "label": "营业结束时间", "type": "time" }
			]
		}
	}

	async update_club(event, old) {
		const { util, data = {} } = event
		const { vk } = util
		const newData = data
		const oldData = old
		const changes = [];
		const changeKeys = []
		const fields = this.keys.update_club

		if (!fields) return null
		fields.forEach(field => {
			const { key, label, type } = field;
			const oldValue = oldData[key];
			const newValue = newData[key];
			// 值相同则跳过
			if (JSON.stringify(oldValue) === JSON.stringify(newValue)) return;

			switch (type) {
				case 'key':
					// 仅记录是否变更
					changes.push(`修改了${label}`);
					break;

				case 'text':
					// 记录文本变更
					changes.push(`${label}由"${oldValue || ''}"改为"${newValue || ''}"`)
					break;

				case 'bool':
					// 记录布尔值变更
					changes.push(`${label}由"${oldValue ? '是' : '否'}"改为"${newValue ? '是' : '否'}"`)
					break;

				case 'time':
					// 记录布尔值变更
					changes.push(`${label}"${oldValue?'由'+vk.pubfn.timeFormat(new Date(oldValue), "hh:mm:ss"):''}"变更为"${vk.pubfn.timeFormat(new Date(newValue), "hh:mm:ss")}"`)
					break;

				case 'date':
					// 记录布尔值变更
					changes.push(`${label}"${oldValue?'由'+vk.pubfn.timeFormat(new Date(oldValue), "yyyy-MM-dd hh:mm:ss"):''}"变更为"${vk.pubfn.timeFormat(new Date(newValue), "yyyy-MM-dd hh:mm:ss")}"`)
					break;
			}
			changeKeys.push(key)
		});
		if (changes.length === 0) return
		await vk.baseDao.add({
			dbName: "system-club-log",
			dataJson: {
				update_user: data.uid,
				update_time: new Date().valueOf(),
				content: changes,
				keys: changeKeys,
				type: "update_club"
			}
		});
	}
}

module.exports = new Log