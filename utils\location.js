export async function getLocation() {
  return new Promise((resolve, reject) => {
    uni.getLocation({
      type: "gcj02",
      success: (res) => {
        resolve(res);
      },
      fail: (err) => {
        resolve(null);
        console.log("获取定位失败", err);
      },
    });
  });
}

/**
 * 使用 Haversine 公式计算两个经纬度坐标之间的距离
 * @param {Object} pos1 - 第一个位置坐标
 * @param {number} pos1.latitude - 第一个位置的纬度
 * @param {number} pos1.longitude - 第一个位置的经度
 * @param {Object} pos2 - 第二个位置坐标
 * @param {number} pos2.latitude - 第二个位置的纬度
 * @param {number} pos2.longitude - 第二个位置的经度
 * @returns {number} 两点之间的距离（单位：公里），保留2位小数
 * @example
 * const pos1 = { latitude: 22.5431, longitude: 114.0579 };
 * const pos2 = { latitude: 22.5500, longitude: 114.0600 };
 * const distance = calculateDistance(pos1, pos2);
 * console.log(distance); // 输出: 1.23
 */
export function calculateDistance(pos1, pos2) {
  // 参数验证
  if (!pos1 || !pos2) {
    throw new Error("位置参数不能为空");
  }

  if (
    typeof pos1.latitude !== "number" ||
    typeof pos1.longitude !== "number" ||
    typeof pos2.latitude !== "number" ||
    typeof pos2.longitude !== "number"
  ) {
    throw new Error("经纬度必须为数字类型");
  }

  // 验证经纬度范围
  if (Math.abs(pos1.latitude) > 90 || Math.abs(pos2.latitude) > 90) {
    throw new Error("纬度值必须在 -90 到 90 之间");
  }

  if (Math.abs(pos1.longitude) > 180 || Math.abs(pos2.longitude) > 180) {
    throw new Error("经度值必须在 -180 到 180 之间");
  }

  // 处理相同坐标点的情况
  if (pos1.latitude === pos2.latitude && pos1.longitude === pos2.longitude) {
    return 0.0;
  }

  // 地球半径（公里）
  const EARTH_RADIUS = 6371;

  // 将角度转换为弧度
  const lat1Rad = (pos1.latitude * Math.PI) / 180;
  const lat2Rad = (pos2.latitude * Math.PI) / 180;
  const deltaLatRad = ((pos2.latitude - pos1.latitude) * Math.PI) / 180;
  const deltaLonRad = ((pos2.longitude - pos1.longitude) * Math.PI) / 180;

  // Haversine 公式计算
  const a =
    Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
    Math.cos(lat1Rad) * Math.cos(lat2Rad) * Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);

  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  // 计算距离并保留2位小数
  const distance = EARTH_RADIUS * c;
  return Math.round(distance * 100) / 100;
}
