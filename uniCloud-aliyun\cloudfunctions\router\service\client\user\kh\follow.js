'use strict';
module.exports = {
	/**
	 * 关注与取消关注
	 * @url client/user/kh/follow 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, target_user_id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		// 判断是否自己关注自己
		if (target_user_id == uid) return { code: -1, msg: "不能对自己操作关注" }
		// 查询是否已经关注
		const dbName = "user-follow-data"
		if (!target_user_id) return {
			code: -1,
			msg: "用户不存在",
		}
		let info = await vk.baseDao.findByWhereJson({
			dbName,
			whereJson: {
				target_user_id,
				user_id: uid
			}
		});
		// 如果已关注，则取消关注
		if (info) {
			await vk.baseDao.deleteById({
				dbName,
				id: info._id
			});
			res = {
				code: 0,
				msg: "已取消关注",
				data: false,
			}
		} else {
			await vk.baseDao.add({
				dbName,
				dataJson: {
					target_user_id,
					user_id: uid
				}
			});
			res = {
				code: 0,
				msg: "已关注",
				data: true,
			}
		}
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}