$primary-color: #0171bc; // 1,113,188
$primary-bg: rgba(1, 113, 188, 0.16); // 1,113,188
$success-color: #d3ef30;
$warning-color: #dd524d;

page {
  --wot-color-theme: #0171bc;
  --wot-tag-small-fs: 24rpx;
}

:deep(.wd-tag) {
  padding: 3px 6px !important;
  border-radius: 4px !important;
}

.base-search {
  flex: 1;
  --wot-search-input-height: 44px;
  :deep(.wd-search) {
    padding: 0 !important;
    .wd-search__input {
      border: none;
    }
    .wd-search__block {
      border-radius: 12rpx;
    }
    .wd-search__cover {
      height: 44px;
    }

    .wd-search__input-inner {
      color: #333;
      font-size: 28rpx;
    }

    .wd-search__placeholder {
      color: #999;
      font-size: 28rpx;
    }
  }
}

.page-content {
  min-height: 100vh;
  background-color: #f8f8f8;
  box-sizing: border-box;
}

.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@mixin multiline($line-count) {
  display: -webkit-box;
  -webkit-line-clamp: $line-count;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.flex {
  display: flex;
  align-items: center;
}

.grid-2 {
  display: grid;
  grid-template-columns: 50% 50%;
}

.empty-text {
  font-size: 28rpx;
  color: #8a8a8a;

  .theme {
    color: $primary-color;
  }
}
