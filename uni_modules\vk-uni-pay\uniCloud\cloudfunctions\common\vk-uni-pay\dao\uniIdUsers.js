/**
 * 请勿修改此处代码，因为插件更新后此处代码会被覆盖。
 * 作者：VK
 * 发布于：2021-07-06
 */
const dbName = {
	uniIdUsers: "uni-id-users", // 数据库表名 - uni-id用户表
};

const db = uniCloud.database();
const _ = db.command;
var dao = {};

/**
 * 获取 - 根据id获取用户信息
 * let userInfo = await dao.uniIdUsers.findById(user_id);
 */
dao.findById = async (id) => {
	let res = await db.collection(dbName.uniIdUsers).doc(id).get();
	if (res.data && res.data.length > 0) {
		return res.data[0];
	} else {
		return null;
	}
};

module.exports = dao;
