'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/activity/kh/delete 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, _id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (vk.pubfn.isNull(_id)) {
			return { code: -1, msg: 'id不能为空' };
		}
		let dbName = "club-activity-data";
		await vk.baseDao.deleteById({
			dbName,
			id: _id
		});
		res.msg = "删除成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}