const vkPay = require("vk-uni-pay");
'use strict';
module.exports = {
	/**
	 * 活动结算退款（平摊费用退款和申请加入退款）
	 * @url client/timerTask/pub/settlementRefund 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, token, now } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		if (token !== 'du6AVGV61YTCoqbvMUzF7QgY5Z1uExOd') return { code: -1, msg: "未知错误" }

		let lockId = "settlementRefund"; // 锁的id

		// 获得锁的实例
		const lockManage = vk.getReentrantLockManage({
			id: lockId,
			timeout: 600,
		});

		// 尝试获取锁
		let lock = await lockManage.lock();
		if (!lock) return { code: -1, msg: `已有退款任务执行中` };

		try {
			// 查找需要结算并且已结束的活动
			let activity_list = await vk.baseDao.select({
				dbName: "activity-data",
				getMain: true,
				pageIndex: 1,
				pageSize: 20,
				whereJson: {
					settlement: 0,
					end: _.lte(now),
				},
				sortArr: [{ name: "_add_time", type: "desc" }],
			});

			if (!activity_list || activity_list.length == 0) return { code: 0, msg: "没有要处理的数据" }
			let activity_ids = activity_list.map(item => item._id)
			// 查找活动相关的加入数据
			let join_list = await vk.baseDao.select({
				dbName: "activity-join-data",
				getMain: true,
				pageIndex: 1,
				pageSize: 1000,
				whereJson: {
					activity_id: _.in(activity_ids),
				},
			});

			// 处理平摊费用退款
			let refund_list = []
			for (const item of activity_list) {
				// 如果类型是无需费用，直接中断
				if (item.cost_type === 'none') continue
				// 获得该活动相关的所有记录
				let joins = join_list.filter(items => item._id === items.activity_id)
				// 获取已加入的信息和申请中的信息
				let joined_list = joins.filter(items => items.state === 'joined')
				let applying_list = joins.filter(items => items.state === 'applying')
				// 已加入活动的人数
				let join_num = joined_list.length
				// 应退费用
				let total_fee = 0
				// 判断当前活动的支付方案
				let refund_obj = {
					activity_id: item._id,
					out_trade_no: '', // 订单编号
					balance: 0, // 订单应剩的费用
					joined_refund: 0, // 平摊应退的费用
					applying_refund: 0, // 申请应退的费用
				}
				// 如果类型是平摊费用，计算需退还的费用
				if (item.cost_type === 'average') {
					// 计算活动实际每人要花费的费用
					let prePay = Math.floor(item.cost / item.min_num * 100) // 活动每人预缴费用(元)
					let realPay = Math.floor(item.cost / join_num * 100) // 活动每人应缴费用(元)
					let shouldRefund = prePay - realPay // 活动每人应退款费用
					for (const join of joined_list) {
						if (!join.out_trade_no) continue
						// 寻找数组中相应的订单编号
						let refund_index = refund_list.findIndex(items => items.out_trade_no === join.out_trade_no)
						if (refund_index !== -1) {
							refund_list[refund_index].balance += realPay
							refund_list[refund_index].joined_refund += shouldRefund
						} else {
							let obj = vk.pubfn.deepClone(refund_obj);
							obj.out_trade_no = join.out_trade_no
							obj.balance = realPay
							obj.joined_refund = shouldRefund
							refund_list.push(obj)
						}
					}

					// 处理还在申请中的退款
					for (const join of applying_list) {
						if (!join.out_trade_no) continue
						// 寻找数组中相应的订单编号
						let refund_index = refund_list.findIndex(items => items.out_trade_no === join.out_trade_no)
						if (refund_index !== -1) {
							refund_list[refund_index].applying_refund += prePay
						} else {
							let obj = vk.pubfn.deepClone(refund_obj);
							obj.out_trade_no = join.out_trade_no
							obj.applying_refund = prePay
							refund_list.push(obj)
						}
					}

				} else if (item.cost_type === 'fixed') { // 固定费用时只需要处理申请退款
					// 处理还在申请中的退款
					for (const join of applying_list) {
						if (!join.out_trade_no) continue
						// 寻找数组中相应的订单编号
						let refund_index = refund_list.findIndex(items => items.out_trade_no === join.out_trade_no)
						if (refund_index !== -1) {
							refund_list[refund_index].applying_refund += item.cost
						} else {
							let obj = vk.pubfn.deepClone(refund_obj);
							obj.out_trade_no = join.out_trade_no
							obj.applying_refund = item.cost
							refund_list.push(obj)
						}
					}
				}
			}
			console.log("最终处理的要退款的订单：", refund_list);
			// 提取商户订单编码请求信息
			let pay_orders_no = refund_list.map(item => item.out_trade_no)
			let payOrders = await vk.baseDao.select({
				dbName: "vk-pay-orders",
				getMain: true,
				pageIndex: 1,
				pageSize: 1000,
				whereJson: {
					out_trade_no: _.in(pay_orders_no),
					user_order_success: true,
				},
				fieldJson: { total_fee: true, refund_fee: true, out_trade_no: true, },
				sortArr: [{ name: "_id", type: "desc" }],
			});
			const activity_error_refund = []
			for (const item of refund_list) {
				let order_info = payOrders.find(items => item.out_trade_no === items.out_trade_no)
				// 订单剩余费用
				let already_refund = order_info.refund_fee || 0 // 已退金额
				let order_balance = order_info.total_fee - already_refund
				// 如果订单余额与应剩余额一致证明已处理退款完成，直接中断
				if (order_balance === item.balance) continue
				// 判断处理退款后是否等于应剩余额
				let refund_after = order_info.total_fee - item.joined_refund - item.applying_refund - already_refund
				// 与预想结果一致，执行退款
				if (refund_after === item.balance) {
					let refund_fee = item.joined_refund + item.applying_refund
					if (refund_fee <= 0) continue
					let refund_type = []
					if (item.joined_refund) refund_type.push("活动预缴多余费用")
					if (item.applying_refund) refund_type.push("活动申请未响应")
					let refund_desc = refund_type.join('、') + '退款'

					await vkPay.refund({
						out_trade_no: item.out_trade_no,
						refund_desc,
						refund_fee
					});
				} else {
					// 与预想结果不一致，记录信息
					await vk.baseDao.add({
						dbName: "refund-order-error",
						dataJson: {
							...item,
							refund_after
						}
					});
					// 把失败的订单中的活动id存入,如果没存的话
					if (!activity_error_refund.includes(item.activity_id)) activity_error_refund.push(item.activity_id)
				}
			}
			// 判断活动退款执行状态是否存在失败的
			const refund_success_activity = activity_ids.filter(id => !activity_error_refund.includes(id));
			// 批量更改相应执行成功活动的结算状态
			await vk.baseDao.update({
				dbName: "activity-data",
				whereJson: {
					_id: _.in(refund_success_activity)
				},
				dataJson: {
					settlement: 1
				}
			});

		} finally {
			// 归还锁
			await lock.unlock();
		}



		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}