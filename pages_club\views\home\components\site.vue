<template>
	<view class="swiper-page" v-if="showPage">
		<view class="content-top">
			<view class="selected-text" v-if="calendar">
				<text>{{calendar[selectDate].date}} {{calendar[selectDate].cn}}</text>
				<t-tag :color="tagColor.color" :textColor="tagColor.text" :text="calendar[selectDate].holiday?'节假日':'工作日'"></t-tag>
			</view>
			<scroll-view class="date" enable-flex scroll-x :show-scrollbar="false">
				<view class="date-fixed" v-if="calendar">
					<view v-for="(days, index) in calendar" :key="index" class="date-day" @click="changeDate(index)">
						<text>{{ days.num }}</text>
						<text>{{ days.cn }}</text>
					</view>
					<view class="selected" :style="{ left: `${selectDate * 50}px` }" />
				</view>
			</scroll-view>
		</view>
		<view class="content-main">
			<view class="site-list">
				<view class="table-tip">
					<text>场地</text>
					<text>时间</text>
				</view>
				<view class="block" v-for="(item,index) in site_list">{{item.name}}</view>
			</view>
			<view class="main" :style="{width:site_list.length*95+150+'rpx'}">
				<view class="time-line">
					<view class="block" v-for="item in time_line">{{item.time}}</view>
				</view>
				<view class="box">
					<!-- 时间线的长度 -->
					<view class="reverse-box" v-for="(item,siteIndex) in reverse_list">
						<!-- 场地的长度 -->
						<view class="reverse-block block" :class="{'reverse-close':!items.price||items.disabled,'reverse-select':items.select}" v-for="(items,timeIndex) in item" @click="selectTime(siteIndex,timeIndex)">
							<text v-if="items.disabled">已预定</text>
							<text v-else>{{items.price?`¥${items.price}`:'未开放'}}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="content-bottom" v-if="totalPrice>0">
			<view class="price">
				<text class="unit">¥</text>
				<text class="value">{{totalPrice}}</text>
				<text class="unit">元</text>
			</view>
			<t-button radius="68" @click="confirmOrder">结算</t-button>
		</view>
	</view>
</template>

<script>
	import dayjs from 'dayjs'
	import { splitTime } from '@/utils/time'
	export default {
		data() {
			return {
				id: null,
				showPage: false,
				club_config: {
					open: "09:00",
					close: "22:00",
					time_type: 'half',
				},
				site_list: [],
				// 日期相关
				calendar: null,
				selectDate: 0,
				// 时间线
				time_line: [],

				// 预订信息
				reverse_list: [],

				// 场地配置
				site_config: [
						{
						day: 'workday',
						start_time: '09:00',
						end_time: '18:00',
						price: 20,
						site_id: "660fe7ef213929f86632f8c4"
					}, {
						day: [3],
						start_time: '18:00',
						end_time: '20:00',
						price: 40,
						site_id: "660fe7ef213929f86632f8c4"
					}, {
						day: 'workday',
						start_time: '20:00',
						end_time: '22:00',
						price: 40,
						site_id: "660fe7ef213929f86632f8c4"
					}, {
						day: 'holiday',
						start_time: '09:00',
						end_time: '18:00',
						price: 40,
						site_id: "660fe7ef213929f86632f8c4"
					}, {
						day: 'holiday',
						start_time: '20:00',
						end_time: '22:00',
						price: 60,
						site_id: "660fe7ef213929f86632f8c4"
					}, {
						day: 'all',
						start_time: '09:00',
						end_time: '22:00',
						price: 150,
						site_id: "65f7f08221821b6d2beb466a"
					}, {
						day: 'workday',
						start_time: '09:00',
						end_time: '22:00',
						price: 80,
						site_id: "65e68fe0e0ec199b18fcbb72"
					},
				],

				// 已预定数据模拟
				booked_data: [{
					site_id: "65e68fe0e0ec199b18fcbb72",
					date: "2025-01-20",
					data: [{
						start_time: "09:00",
						end_time: "12:00"
					}, {
						start_time: "15:00",
						end_time: "17:00"
					}]
				}],
			}
		},
		computed: {
			totalPrice() {
				let total = 0
				this.reverse_list.forEach(item => {
					item.forEach(items => {
						if (items.select) total += items.price
					})
				})
				return total
			},
			tagColor() {
				if (!this.calendar) return
				return this.calendar[this.selectDate].holiday ? {
					color: vk.getVuex('$app.color.secondary'),
					text: "#fff"
				} : null
			}
		},
		watch: {
			"$store.state.$club.id": {
				deep: true, // 如果是对象，需要深度监听设置为 true
				handler: function(newVal, oldVal) {
					this.id = newVal
					this.init()
				}
			}
		},
		mounted() {
			this.id = vk.getVuex('$club.id')
			this.init()
		},
		methods: {
			async init() {
				// 初始化日历
				vk.vuex.dispatch('$date/getDays', {
					start: dayjs().format('YYYY-MM-DD'),
					num: 30
				});
				this.calendar = vk.getVuex('$date.dayList')
				await this.getData()
				this.showPage = true
				this.time_line = splitTime(this.club_config.open, this.club_config.close, false, this.club_config
					.time_type)
				this.siteOpenTime()
			},
			// 生成订单
			confirmOrder() {
				let ary = []
				this.reverse_list.forEach((item, index) => {
					let data = item.filter(items => items.select)
					if (data.length > 0) {
						ary.push(...data)
					}
				})
				let obj = {
					date: this.calendar[this.selectDate],
					data: ary
				}
				vk.setStorageSync('orderConfirm', obj)
				vk.navigateTo('/pages_club/views/order/confirm')
			},
			// 切换日期
			changeDate(index) {
				this.selectDate = index
				this.reverse_list = this.reverse_list.map(item => {
					item = item.map(items => {
						items.select = false
						return items
					})
					return item
				})
				this.dateTimeData()
			},
			// 选中时间
			selectTime(siteIndex, timeIndex) {
				let info = this.reverse_list[siteIndex][timeIndex]
				if (info.price && !info.disabled) info.select = !info.select
			},
			// 判断当天球场开放情况
			dateTimeData() {
				let day = this.calendar[this.selectDate]
				this.reverse_list = this.reverse_list.map((site, index) => {
					// 判断是否改场地的配置
					let config = this.site_config.filter(item => this.site_list[index]._id == item.site_id)
					config = config.filter(item => {
						const isHoliday = day.holiday;
						const isCurrentDay = item.day.includes(day.day);
						const isWorkday = item.day === 'workday' && !isHoliday;
						const isAll = item.day === 'all';

						return isHoliday && item.day === 'holiday' ||
							isCurrentDay ||
							isWorkday ||
							isAll;
					})

					let all_time_config = []
					config.forEach(item => {
						let times = splitTime(item.start_time, item.end_time, true)
						times = times.map(items => {
							items.price = item.price
							return items
						})
						all_time_config.push(...times)
					})
					// 处理预订信息
					let booked = this.booked_data.find(item => this.site_list[index]._id == item.site_id)
					let all_booked_times = []
					if (booked) {
						booked.data.forEach(item => {
							let times = splitTime(item.start_time, item.end_time, true)
							all_booked_times.push(...times)
						})
					}

					// 处理配置时间
					site.map(item => {
						// 设置价格
						let info = all_time_config.find(items => item.timestamp == items.timestamp)
						item.price = info ? info.price : null
						// 设置是否不可预订
						let unbooked = all_booked_times.find(items => item.timestamp == items.timestamp)
						item.disabled = unbooked ? true : false
						return item
					})
					return site
				})
			},
			// 处理营业时间
			siteOpenTime() {
				let all_time_data = []
				this.site_list.forEach(site => {
					let ary = []
					this.time_line.forEach((time, index) => {
						time = { ...time, select: false, disabled: false, site_id: site._id }
						if (index + 1 != this.time_line.length) ary.push(time)
					})
					all_time_data.push(ary)
				})
				this.reverse_list = all_time_data
				this.dateTimeData()
			},
			async getData() {
				let data = await vk.callFunction({
					url: 'client/club/site/pub/list',
					title: '请求中...',
					data: {
						club_id: this.id,
					},
				});
				console.log(data);
				this.site_list = data.rows
			},
		},
	}
</script>

<style scoped lang="scss">
	.swiper-page {
		width: 100%;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		background-color: #fff;
	}

	.content {
		&-top {
			padding: 30rpx;
			flex-shrink: 0;

			.selected-text {
				font-size: 28rpx;
				display: flex;
				align-items: center;

				text {
					margin-right: 20rpx;
				}
			}

		}

		&-bottom {
			padding: 30rpx;
			flex-shrink: 0;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.price {
				margin-left: 30rpx;

				.unit {
					margin-right: 10rpx;
					font-size: 32rpx;
					font-weight: bold;
					color: $warning-color;
				}

				.value {
					font-size: 40rpx;
					font-weight: bold;
					color: $warning-color;
				}
			}
		}

		&-main {
			flex: 1;
			background-color: #fff;
			overflow: scroll;
			position: relative;

			&::-webkit-scrollbar {
				display: none;
			}

			.table-tip {
				position: sticky;
				top: 0;
				left: 0;
				z-index: 10;
				background-color: #fff;
				width: 75px;
				height: 44px;

				&::after {
					content: '';
					position: absolute;
					left: 14px;
					top: 22px;
					width: 50px;
					height: 2px;
					background-color: #eee;
					transform: rotate(30deg);
				}

				text {
					font-size: 12px;

					&:first-child {
						position: absolute;
						right: 8px;
						top: 5px;
					}

					&:last-child {
						position: absolute;
						left: 8px;
						bottom: 5px;
					}
				}
			}

			.block {
				flex-shrink: 0;
				width: 75px;
				border: 1px solid transparent;
				text-align: center;
				box-sizing: border-box;
			}

			.site-list {
				display: flex;
				align-items: center;
				float: left;
				position: sticky;
				z-index: 9;
				top: 0;
				background-color: #fff;
				width: 100%;
				padding: 10rpx 0;

				.block {
					padding: 0 20rpx;
					width: 85px;
					// height: 44px;
					background-color: #fff;
					font-size: 12px;
					font-weight: bold;
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;
					@include multiline(2);

					&::after {
						content: "";
						position: absolute;
						width: 1px;
						height: 20px;
						background-color: #eee;
						right: 0;
						bottom: 0;
						top: 0;
						margin: auto 0;
					}

				}
			}

			.main {
				display: flex;
				flex: 1;


				.time-line {
					width: 75px;
					height: 100%;
					flex-shrink: 0;
					position: sticky;
					z-index: 8;
					left: 0;
					background-color: #fff;

					.block {
						width: 75px;
						text-align: right;
						padding-right: 5px;
						box-sizing: border-box;
						font-size: 14px;
						height: 44px;
						position: relative;
						font-weight: bold;

						&::after {
							content: "";
							position: absolute;
							width: 1px;
							height: 24px;
							background-color: #eee;
							right: 20px;
							bottom: 0;
						}

						&:last-child {
							&::after {
								content: none;
							}
						}
					}
				}

				.box {
					padding-top: 9px;
					display: flex;

					.reverse-box {
						display: flex;
						align-items: center;
						flex-direction: column;
					}

					.reverse-close {
						filter: grayscale(1);
						color: #8a8a8a;
					}

					.reverse-select {
						background-color: $primary-color !important;
						color: #fff;
					}

					.reverse-block {
						border: 1px solid $primary-color;
						background-color: $primary-bg;
						height: 34px;
						border-radius: 6px;
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 12px;
						margin: 5px;
					}
				}
			}
		}
	}

	.date {
		margin-top: 20rpx;
		width: 100%;
		height: 54px;
		position: relative;
		display: flex;
		margin-bottom: 5px;


		&-fixed {
			display: flex;
			align-items: center;
			justify-content: space-between;
			position: relative;
			box-sizing: border-box;
		}

		&-day {
			width: 50px;
			height: 54px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			position: relative;
			z-index: 1;
			flex: 1;
			text-align: center;

			text {
				&:first-child {
					font-size: 18px;
					color: #000;
					font-weight: bold;
				}

				&:nth-child(2) {
					font-size: 12px;
					color: #333;
				}
			}
		}

		.selected {
			position: absolute;
			width: 50px;
			height: 54px;
			background-color: #0171BC;
			z-index: 0;
			top: 0;
			border-radius: 14rpx;
			transition: left 0.3s;
		}
	}
</style>