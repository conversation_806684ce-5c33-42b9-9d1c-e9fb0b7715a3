'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/member/kh/add 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, club_id, name, avatar, text } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let user_id = club_id + new Date().valueOf()
		// 添加成员
		await vk.baseDao.add({
			dbName: "club-user-data",
			dataJson: {
				user_id,
				club_id,
				state: "custom",
				nickname: name,
			}
		});
		// 添加备注信息
		await vk.baseDao.add({
			dbName: "club-user-remark",
			dataJson: {
				user_id,
				club_id,
				name,
				text
			}
		});
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}