'use strict';
module.exports = {
	/**
	 * 获取我编辑的未审核记录
	 * @url client/public/info/kh/editRecord 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, id, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------

		let info = await vk.baseDao.findByWhereJson({
			dbName: "pending-audit-data",
			whereJson: {
				creator_id: uid,
				info_id: id,
				type,
			},
		});

		res.data = info

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}