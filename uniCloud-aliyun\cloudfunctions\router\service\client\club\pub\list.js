'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/pub/list 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, name, pageIndex } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let whereJson = {}
		if (name) whereJson.name = new RegExp(name)
		res = await vk.baseDao.selects({
			dbName: "club-data",
			getCount: false,
			pageIndex,
			pageSize: 20,
			whereJson,
			sortArr: [{ name: "distance", type: "desc" }],
			foreignDB: [{
				dbName: "club-user-data",
				localKey: "_id",
				foreignKey: "club_id",
				as: "club_user",
				limit: 100,
				whereJson: { state: _.neq('custom') },
				fieldJson: { user_id: true, club_id: true },
				foreignDB: [{
					dbName: "uni-id-users",
					localKey: "user_id",
					foreignKey: "_id",
					as: "userInfo",
					limit: 1,
					fieldJson: { nickname: true, avatar: true },
				}]
			}]
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}