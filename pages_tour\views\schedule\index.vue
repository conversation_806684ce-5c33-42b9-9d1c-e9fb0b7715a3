<template>
  <view class="schedule-page">
    <!-- 日期选择 -->
    <scroll-view 
      class="date-scroll" 
      scroll-x 
      :scroll-into-view="'date-' + currentDate" 
      :show-scrollbar="false"
    >
      <view class="date-list">
        <view 
          v-for="item in dateList" 
          :key="item.date"
          :id="'date-' + item.date"
          class="date-item"
          :class="{'active': currentDate === item.date}"
          @click="handleDateChange(item.date)"
        >
          <text class="day">{{item.day}}</text>
          <text class="date">{{item.date}}</text>
        </view>
      </view>
    </scroll-view>

    <!-- 赛程列表 -->
    <scroll-view 
      class="schedule-list"
      scroll-y 
      refresher-enabled 
      :refresher-triggered="isRefreshing"
      @refresherrefresh="handleRefresh"
      @scrolltolower="handleLoadMore"
    >
      <block v-if="scheduleList.length > 0">
        <view 
          class="schedule-group" 
          v-for="group in groupedSchedule" 
          :key="group.time"
        >
          <view class="time-header">{{group.time}}</view>
          <view 
            class="schedule-item"
            v-for="item in group.matches"
            :key="item._id"
            @click="goToMatch(item)"
          >
            <view class="match-info">
              <view class="court">{{item.court}}</view>
              <view class="type">{{getMatchType(item.type)}}</view>
            </view>
            <view class="player-vs">
              <view class="player player-a">
                <image 
                  class="avatar" 
                  :src="item.playerA.avatar || '/static/images/default-avatar.png'" 
                  mode="aspectFill"
                ></image>
                <text class="name">{{item.playerA.name}}</text>
              </view>
              <view class="vs">VS</view>
              <view class="player player-b">
                <image 
                  class="avatar" 
                  :src="item.playerB.avatar || '/static/images/default-avatar.png'" 
                  mode="aspectFill"
                ></image>
                <text class="name">{{item.playerB.name}}</text>
              </view>
            </view>
            <view class="match-status" :class="item.status">
              {{getStatusText(item.status)}}
            </view>
          </view>
        </view>
      </block>
      
      <view v-else class="empty-tip">
        <image src="/static/images/empty.png" mode="aspectFit"></image>
        <text>暂无赛程安排</text>
      </view>

      <uni-load-more :status="loadMoreStatus"></uni-load-more>
    </scroll-view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      dateList: [],
      currentDate: '',
      scheduleList: [],
      page: 1,
      pageSize: 20,
      isRefreshing: false,
      loadMoreStatus: 'more'
    }
  },
  computed: {
    groupedSchedule() {
      const groups = {}
      this.scheduleList.forEach(item => {
        const time = this.formatTime(item.start_time)
        if (!groups[time]) {
          groups[time] = {
            time,
            matches: []
          }
        }
        groups[time].matches.push(item)
      })
      return Object.values(groups).sort((a, b) => {
        return new Date('2000/01/01 ' + a.time) - new Date('2000/01/01 ' + b.time)
      })
    }
  },
  onLoad() {
    this.initDateList()
    this.loadScheduleList()
  },
  methods: {
    initDateList() {
      const today = new Date()
      const dateList = []
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(today)
        date.setDate(date.getDate() + i)
        
        dateList.push({
          date: this.formatDate(date),
          day: i === 0 ? '今天' : this.formatDay(date)
        })
      }
      
      this.dateList = dateList
      this.currentDate = dateList[0].date
    },
    formatDate(date) {
      return `${date.getMonth() + 1}-${date.getDate()}`
    },
    formatDay(date) {
      const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      return days[date.getDay()]
    },
    formatTime(timestamp) {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return `${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
    },
    async loadScheduleList(isRefresh = false) {
      if (isRefresh) {
        this.page = 1
      }
      
      try {
        const res = await vk.callFunction({
          url: 'client/tour/schedule/kh/getList',
          data: {
            match_id: this.match_id,
            date: this.currentDate
          }
        })
        this.scheduleList = res.data
      } catch (e) {
        vk.toast(e.message || '加载失败')
      } finally {
        this.isRefreshing = false
      }
    },
    handleDateChange(date) {
      this.currentDate = date
      this.loadScheduleList(true)
    },
    async handleRefresh() {
      this.isRefreshing = true
      await this.loadScheduleList(true)
    },
    handleLoadMore() {
      if (this.loadMoreStatus === 'more') {
        this.page++
        this.loadScheduleList()
      }
    },
    getMatchType(type) {
      const typeMap = {
        single: '单打',
        double: '双打'
      }
      return typeMap[type] || type
    },
    getStatusText(status) {
      const statusMap = {
        waiting: '待开始',
        in_progress: '进行中',
        completed: '已结束',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    },
    goToMatch(item) {
      uni.navigateTo({
        url: `/pages_tour/views/match/detail?id=${item.match_id}`
      })
    }
  }
}
</script>

<style lang="scss">
.schedule-page {
  min-height: 100vh;
  background-color: #f5f5f5;
  display: flex;
  flex-direction: column;

  .date-scroll {
    background-color: #fff;
    height: 160rpx;
    white-space: nowrap;
    border-bottom: 1rpx solid #eee;

    .date-list {
      display: flex;
      padding: 20rpx;

      .date-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 120rpx;
        height: 120rpx;
        margin-right: 20rpx;
        border-radius: 16rpx;
        background-color: #f8f8f8;
        
        &.active {
          background-color: #2979ff;
          
          .day, .date {
            color: #fff;
          }
        }

        .day {
          font-size: 28rpx;
          color: #666;
          margin-bottom: 8rpx;
        }

        .date {
          font-size: 32rpx;
          color: #333;
          font-weight: bold;
        }
      }
    }
  }

  .schedule-list {
    flex: 1;
    
    .schedule-group {
      margin-bottom: 20rpx;
      
      .time-header {
        padding: 20rpx 30rpx;
        font-size: 28rpx;
        color: #666;
        background-color: #f8f8f8;
      }
      
      .schedule-item {
        background-color: #fff;
        padding: 30rpx;
        margin-bottom: 2rpx;
        position: relative;
        
        .match-info {
          display: flex;
          align-items: center;
          margin-bottom: 20rpx;
          
          .court {
            font-size: 28rpx;
            color: #333;
            margin-right: 20rpx;
          }
          
          .type {
            font-size: 24rpx;
            color: #2979ff;
            background-color: rgba(41, 121, 255, 0.1);
            padding: 4rpx 12rpx;
            border-radius: 8rpx;
          }
        }
        
        .player-vs {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 40rpx;
          
          .player {
            display: flex;
            flex-direction: column;
            align-items: center;
            
            .avatar {
              width: 100rpx;
              height: 100rpx;
              border-radius: 50%;
              margin-bottom: 16rpx;
            }
            
            .name {
              font-size: 28rpx;
              color: #333;
            }
          }
          
          .vs {
            font-size: 36rpx;
            color: #999;
            font-weight: bold;
          }
        }
        
        .match-status {
          position: absolute;
          right: 30rpx;
          top: 30rpx;
          font-size: 24rpx;
          padding: 4rpx 12rpx;
          border-radius: 8rpx;
          
          &.waiting {
            color: #666;
            background-color: #f8f8f8;
          }
          
          &.in_progress {
            color: #2979ff;
            background-color: rgba(41, 121, 255, 0.1);
          }
          
          &.completed {
            color: #19be6b;
            background-color: rgba(25, 190, 107, 0.1);
          }
          
          &.cancelled {
            color: #999;
            background-color: rgba(153, 153, 153, 0.1);
          }
        }
      }
    }
    
    .empty-tip {
      padding: 100rpx 0;
      text-align: center;
      
      image {
        width: 200rpx;
        height: 200rpx;
        margin-bottom: 20rpx;
      }
      
      text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }
}
</style> 