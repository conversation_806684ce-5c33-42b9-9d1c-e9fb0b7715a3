<template>
  <view v-if="info">
    <image class="cover" :src="info.image" mode="aspectFill"></image>
    <view class="skip" @click="skip">跳过 {{ interval }}S</view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      userInfo: vk.getVuex("$user.userInfo"),
      info: null,
      interval: null,
      url: "/pages/index/index",
      needLogin: false,
      userInfo: null,
      pageInterval: null,
    };
  },
  watch: {
    "$store.state.$user.userInfo": {
      deep: true, // 如果是对象，需要深度监听设置为 true
      handler: function (newVal, oldVal) {
        this.userInfo = newVal;
      },
    },
  },
  async onLoad(options) {
    await this.getWelcome();
    this.judgeOptions(options);
  },
  methods: {
    async getWelcome() {
      try {
        let data = await vk.callFunction({
          url: "client/public/pub/system",
          data: {
            type: "welcome",
          },
        });
        this.info = data.data;
        console.log(data.data, "data.data");
        this.interval = data.data.second ? data.data.second : 3;
        this.pageInterval = setInterval(() => {
          if (this.interval <= 0) {
            this.skip();
          } else {
            this.interval -= 1;
          }
        }, 1000);
      } catch (error) {
        console.log("error", error);
        this.skip();
      }
    },
    skip() {
      if (this.needLogin) {
        let interval = setInterval(() => {
          if (this.userInfo) {
            if (this.userInfo.unLogin) {
              uni.setStorageSync("next_url", this.url);
              vk.reLaunch("/pages/login/login");
              clearInterval(interval);
              clearInterval(this.pageInterval);
            } else {
              vk.redirectTo(this.url);
              clearInterval(interval);
              clearInterval(this.pageInterval);
            }
          }
        }, 100);
      } else {
        vk.redirectTo(this.url);
        clearInterval(this.pageInterval);
      }
    },
    // 处理启动页判断
    judgeOptions(info) {
      let needLogin = false;
      let url = "";
      switch (info.type) {
        case "club_join":
          url = "/pages_club/views/home/<USER>" + info.id;
          break;
        case "club_card_sell":
          url = "/pages_club/views/card/sell?id=" + info.id;
          break;
        case "activity":
          url = "/pages/activity/detail?id=" + info.id;
          break;
        case "string":
          url = "/pages/explore/string/detail?id=" + info.id;
          break;
        case "string_fill":
          url = "/pages/public/submitInfo?type=string";
          break;
        case "string_list":
          url = "/pages/explore/string/index";
          break;
        case "site":
          url = "/pages/explore/site/detail?id=" + info.id;
          break;
        case "site_fill":
          url = "/pages/public/submitInfo?type=site";
          break;
        case "site_list":
          url = "/pages/explore/site/index";
          break;
        case "start":
          url = "/pages/activity/start";
          break;
        default:
          url = "/pages/index/index";
          break;
      }
      this.url = url;
      this.needLogin = needLogin;
    },
  },
};
</script>

<style>
.cover {
  width: 100vw;
  height: 100vh;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
}

.skip {
  position: absolute;
  z-index: 2;
  right: 40rpx;
  bottom: 60rpx;
  width: 140rpx;
  height: 68rpx;
  border-radius: 68rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(4px);
  font-size: 28rpx;
  color: #fff;
}
</style>
