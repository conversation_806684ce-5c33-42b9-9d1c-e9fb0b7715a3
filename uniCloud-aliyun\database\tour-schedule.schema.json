{"bsonType": "object", "required": ["match_id", "start_time", "court", "status"], "permission": {"read": true, "create": "auth.role == 'admin'", "update": "auth.role == 'admin'", "delete": "auth.role == 'admin'"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "match_id": {"bsonType": "string", "description": "关联的比赛ID"}, "start_time": {"bsonType": "timestamp", "description": "开始时间"}, "end_time": {"bsonType": "timestamp", "description": "结束时间"}, "court": {"bsonType": "string", "description": "场地编号"}, "round": {"bsonType": "string", "description": "比赛轮次", "enum": ["preliminary", "round_16", "quarter_final", "semi_final", "final"]}, "playerA": {"bsonType": "object", "description": "A方选手信息", "properties": {"user_id": {"bsonType": "string", "description": "选手ID"}, "name": {"bsonType": "string", "description": "选手姓名"}, "avatar": {"bsonType": "string", "description": "选手头像"}}}, "playerB": {"bsonType": "object", "description": "B方选手信息", "properties": {"user_id": {"bsonType": "string", "description": "选手ID"}, "name": {"bsonType": "string", "description": "选手姓名"}, "avatar": {"bsonType": "string", "description": "选手头像"}}}, "score": {"bsonType": "object", "description": "比分信息", "properties": {"sets": {"bsonType": "array", "description": "每局比分", "items": {"bsonType": "object", "properties": {"playerA": {"bsonType": "int", "description": "A方得分"}, "playerB": {"bsonType": "int", "description": "B方得分"}}}}, "winner": {"bsonType": "string", "description": "获胜方ID"}}}, "status": {"bsonType": "string", "description": "比赛状态", "enum": ["waiting", "in_progress", "completed", "cancelled"]}, "create_date": {"bsonType": "timestamp", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}}}