'use strict';
/**
 * 表单验证
 */
class createOrder {
	constructor() {

	}
	async create(data) {
		try {
			const { uid, out_trade_no, type } = data
			let order_info = {
				out_trade_no,
				user_id: uid,
				type,
				status: 0,
			}
			switch (data.type) {
				case "card":
					order_info.club_id = data.club_id
					order_info.card_id = data.card_id
					break;
				case "activity":
					order_info.activity_id = data.activity_id
					order_info.join_type = data.join_type
					order_info.join_name = data.join_name
					break;
			}
			return await this.createOrderInfo(data, order_info)
		} catch (error) {
			return { code: -1, msg: error }
		}
	}
	/**
	 * 卡片购买验证
	 */
	async createOrderInfo(data, order_info) {
		const { uid, club_id, out_trade_no } = data
		try {
			// 添加订单记录
			let order_id = await vk.baseDao.add({
				dbName: "order-pay-data",
				dataJson: order_info
			});
			await vk.baseDao.add({
				dbName: "order-pay-record",
				dataJson: {
					user_id: uid,
					order_id,
					out_trade_no,
				}
			});
			return { code: 0, msg: '添加成功' }
		} catch (error) {
			return { code: -1, msg: error }
		}

	}
}
module.exports = new createOrder