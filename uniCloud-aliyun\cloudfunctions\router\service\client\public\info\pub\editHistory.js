'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/pub/editHistory 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, id, pageIndex = 1, pageSize = 10 } = data;
		let res = { code: 0, msg: "" };
		if (!id) {
			return {
				code: -1,
				msg: "信息id不能为空"
			}
		}
		// 业务逻辑开始-----------------------------------------------------------
		res = await vk.baseDao.selects({
			dbName: "info-edit-history",
			getCount: false,
			pageIndex,
			pageSize,
			// 主表where条件
			whereJson: {
				info_id: id
			},
			// 主表排序规则
			sortArr: [{ name: "_add_time", type: "desc" }],
			// 副表列表
			foreignDB: [{
				dbName: "uni-id-users",
				localKey: "user_id",
				foreignKey: "_id",
				as: "user_info",
				limit: 1
			}]
		});



		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}