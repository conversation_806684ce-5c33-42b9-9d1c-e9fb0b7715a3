'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/user/level/kh/base 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, user_id } = data;
		let res = { code: 0, msg: "" };
		user_id = user_id ? user_id : uid
		// 业务逻辑开始-----------------------------------------------------------
		res.comment_num = await vk.baseDao.count({
			dbName: "level-score-data",
			whereJson: {
				type: "comment",
				user_id,
			}
		});
		res.data = ""
		let info = await vk.baseDao.select({
			dbName: "level-score-data",
			getOne: true,
			getMain: true,
			whereJson: {
				type: "init",
				user_id,
			}
		});
		if (info) res.data = info.data
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}