'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/site/kh/detail 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, id } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let info = await vk.baseDao.findById({
			dbName: "club-site-data",
			id: id,
		});
		res.info = info
		res.msg = '获取成功'
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}