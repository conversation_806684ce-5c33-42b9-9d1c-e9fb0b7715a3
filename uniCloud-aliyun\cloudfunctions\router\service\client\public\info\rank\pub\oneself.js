'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/public/info/rank/pub/oneself 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _, $ } = util;
		let { uid, type } = data;
		let res = { code: 0, msg: "" };
		// 业务逻辑开始-----------------------------------------------------------
		let dbName = 'info-edit-history'
		if (!uid) {
			res.num = 0
			return res
		}
		switch (type) {
			case 1:
				break;
			default:
				dbName = 'info-edit-history'
				break;
		}
		res.num = await vk.baseDao.count({
			dbName, // 表名
			whereJson: { // 条件
				user_id: uid
			},
		});

		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}