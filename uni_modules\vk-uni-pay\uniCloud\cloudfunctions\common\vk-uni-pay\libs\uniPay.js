!function(t,e){for(var r in e)t[r]=e[r]}(exports,function(t){var e={};function r(n){if(e[n])return e[n].exports;var i=e[n]={i:n,l:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.l=!0,i.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)r.d(n,i,function(e){return t[e]}.bind(null,i));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=25)}([function(t,e,r){var n;t.exports=(n=n||function(t,e){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),"undefined"!=typeof self&&self.crypto&&(n=self.crypto),"undefined"!=typeof globalThis&&globalThis.crypto&&(n=globalThis.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&"undefined"!=typeof global&&global.crypto&&(n=global.crypto),!n)try{n=r(2)}catch(t){}var i=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},o=Object.create||function(){function t(){}return function(e){var r;return t.prototype=e,r=new t,t.prototype=null,r}}(),s={},a=s.lib={},u=a.Base={extend:function(t){var e=o(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},c=a.WordArray=u.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:4*t.length},toString:function(t){return(t||l).stringify(this)},concat:function(t){var e=this.words,r=t.words,n=this.sigBytes,i=t.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var s=r[o>>>2]>>>24-o%4*8&255;e[n+o>>>2]|=s<<24-(n+o)%4*8}else for(var a=0;a<i;a+=4)e[n+a>>>2]=r[a>>>2];return this.sigBytes+=i,this},clamp:function(){var e=this.words,r=this.sigBytes;e[r>>>2]&=4294967295<<32-r%4*8,e.length=t.ceil(r/4)},clone:function(){var t=u.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],r=0;r<t;r+=4)e.push(i());return new c.init(e,t)}}),f=s.enc={},l=f.Hex={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var o=e[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n+=2)r[n>>>3]|=parseInt(t.substr(n,2),16)<<24-n%8*4;return new c.init(r,e/2)}},h=f.Latin1={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i++){var o=e[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(t){for(var e=t.length,r=[],n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;return new c.init(r,e)}},d=f.Utf8={stringify:function(t){try{return decodeURIComponent(escape(h.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return h.parse(unescape(encodeURIComponent(t)))}},p=a.BufferedBlockAlgorithm=u.extend({reset:function(){this._data=new c.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=d.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var r,n=this._data,i=n.words,o=n.sigBytes,s=this.blockSize,a=o/(4*s),u=(a=e?t.ceil(a):t.max((0|a)-this._minBufferSize,0))*s,f=t.min(4*u,o);if(u){for(var l=0;l<u;l+=s)this._doProcessBlock(i,l);r=i.splice(0,u),n.sigBytes-=f}return new c.init(r,f)},clone:function(){var t=u.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0}),y=(a.Hasher=p.extend({cfg:u.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,r){return new t.init(r).finalize(e)}},_createHmacHelper:function(t){return function(e,r){return new y.HMAC.init(t,r).finalize(e)}}}),s.algo={});return s}(Math),n)},function(t,e,r){var n,i,o,s,a,u,c,f,l,h,d,p,y,g,v,m,_,b,w;t.exports=(n=r(0),r(4),void(n.lib.Cipher||(i=n,o=i.lib,s=o.Base,a=o.WordArray,u=o.BufferedBlockAlgorithm,c=i.enc,c.Utf8,f=c.Base64,l=i.algo.EvpKDF,h=o.Cipher=u.extend({cfg:s.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,r){this.cfg=this.cfg.extend(r),this._xformMode=t,this._key=e,this.reset()},reset:function(){u.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?w:_}return function(e){return{encrypt:function(r,n,i){return t(n).encrypt(e,r,n,i)},decrypt:function(r,n,i){return t(n).decrypt(e,r,n,i)}}}}()}),o.StreamCipher=h.extend({_doFinalize:function(){return this._process(!0)},blockSize:1}),d=i.mode={},p=o.BlockCipherMode=s.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),y=d.CBC=function(){var t=p.extend();function e(t,e,r){var n,i=this._iv;i?(n=i,this._iv=void 0):n=this._prevBlock;for(var o=0;o<r;o++)t[e+o]^=n[o]}return t.Encryptor=t.extend({processBlock:function(t,r){var n=this._cipher,i=n.blockSize;e.call(this,t,r,i),n.encryptBlock(t,r),this._prevBlock=t.slice(r,r+i)}}),t.Decryptor=t.extend({processBlock:function(t,r){var n=this._cipher,i=n.blockSize,o=t.slice(r,r+i);n.decryptBlock(t,r),e.call(this,t,r,i),this._prevBlock=o}}),t}(),g=(i.pad={}).Pkcs7={pad:function(t,e){for(var r=4*e,n=r-t.sigBytes%r,i=n<<24|n<<16|n<<8|n,o=[],s=0;s<n;s+=4)o.push(i);var u=a.create(o,n);t.concat(u)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},o.BlockCipher=h.extend({cfg:h.cfg.extend({mode:y,padding:g}),reset:function(){var t;h.reset.call(this);var e=this.cfg,r=e.iv,n=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=n.createEncryptor:(t=n.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,r&&r.words):(this._mode=t.call(n,this,r&&r.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4}),v=o.CipherParams=s.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),m=(i.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,r=t.salt;return(r?a.create([1398893684,1701076831]).concat(r).concat(e):e).toString(f)},parse:function(t){var e,r=f.parse(t),n=r.words;return 1398893684==n[0]&&1701076831==n[1]&&(e=a.create(n.slice(2,4)),n.splice(0,4),r.sigBytes-=16),v.create({ciphertext:r,salt:e})}},_=o.SerializableCipher=s.extend({cfg:s.extend({format:m}),encrypt:function(t,e,r,n){n=this.cfg.extend(n);var i=t.createEncryptor(r,n),o=i.finalize(e),s=i.cfg;return v.create({ciphertext:o,key:r,iv:s.iv,algorithm:t,mode:s.mode,padding:s.padding,blockSize:t.blockSize,formatter:n.format})},decrypt:function(t,e,r,n){return n=this.cfg.extend(n),e=this._parse(e,n.format),t.createDecryptor(r,n).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),b=(i.kdf={}).OpenSSL={execute:function(t,e,r,n){n||(n=a.random(8));var i=l.create({keySize:e+r}).compute(t,n),o=a.create(i.words.slice(e),4*r);return i.sigBytes=4*e,v.create({key:i,iv:o,salt:n})}},w=o.PasswordBasedCipher=_.extend({cfg:_.cfg.extend({kdf:b}),encrypt:function(t,e,r,n){var i=(n=this.cfg.extend(n)).kdf.execute(r,t.keySize,t.ivSize);n.iv=i.iv;var o=_.encrypt.call(this,t,e,i.key,n);return o.mixIn(i),o},decrypt:function(t,e,r,n){n=this.cfg.extend(n),e=this._parse(e,n.format);var i=n.kdf.execute(r,t.keySize,t.ivSize,e.salt);return n.iv=i.iv,_.decrypt.call(this,t,e,i.key,n)}}))))},function(t,e){t.exports=require("crypto")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={FormData:!0,UniCloudError:!0};Object.defineProperty(e,"FormData",{enumerable:!0,get:function(){return i.default}}),Object.defineProperty(e,"UniCloudError",{enumerable:!0,get:function(){return o.default}});var i=c(r(28)),o=c(r(29)),s=r(30);Object.keys(s).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(n,t)||t in e&&e[t]===s[t]||Object.defineProperty(e,t,{enumerable:!0,get:function(){return s[t]}}))}));var a=r(7);Object.keys(a).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(n,t)||t in e&&e[t]===a[t]||Object.defineProperty(e,t,{enumerable:!0,get:function(){return a[t]}}))}));var u=r(31);function c(t){return t&&t.__esModule?t:{default:t}}Object.keys(u).forEach((function(t){"default"!==t&&"__esModule"!==t&&(Object.prototype.hasOwnProperty.call(n,t)||t in e&&e[t]===u[t]||Object.defineProperty(e,t,{enumerable:!0,get:function(){return u[t]}}))}))},function(t,e,r){var n,i,o,s,a,u,c,f;t.exports=(f=r(0),r(14),r(15),i=(n=f).lib,o=i.Base,s=i.WordArray,a=n.algo,u=a.MD5,c=a.EvpKDF=o.extend({cfg:o.extend({keySize:4,hasher:u,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r,n=this.cfg,i=n.hasher.create(),o=s.create(),a=o.words,u=n.keySize,c=n.iterations;a.length<u;){r&&i.update(r),r=i.update(t).finalize(e),i.reset();for(var f=1;f<c;f++)r=i.finalize(r),i.reset();o.concat(r)}return o.sigBytes=4*u,o}}),n.EvpKDF=function(t,e,r){return c.create(r).compute(t,e)},f.EvpKDF)},function(t,e,r){var n,i,o;t.exports=(o=r(0),i=(n=o).lib.WordArray,n.enc.Base64={stringify:function(t){var e=t.words,r=t.sigBytes,n=this._map;t.clamp();for(var i=[],o=0;o<r;o+=3)for(var s=(e[o>>>2]>>>24-o%4*8&255)<<16|(e[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|e[o+2>>>2]>>>24-(o+2)%4*8&255,a=0;a<4&&o+.75*a<r;a++)i.push(n.charAt(s>>>6*(3-a)&63));var u=n.charAt(64);if(u)for(;i.length%4;)i.push(u);return i.join("")},parse:function(t){var e=t.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var o=0;o<r.length;o++)n[r.charCodeAt(o)]=o}var s=r.charAt(64);if(s){var a=t.indexOf(s);-1!==a&&(e=a)}return function(t,e,r){for(var n=[],o=0,s=0;s<e;s++)if(s%4){var a=r[t.charCodeAt(s-1)]<<s%4*2,u=r[t.charCodeAt(s)]>>>6-s%4*2,c=a|u;n[o>>>2]|=c<<24-o%4*8,o++}return i.create(n,o)}(t,e,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},o.enc.Base64)},function(t,e,r){var n;t.exports=(n=r(0),function(t){var e=n,r=e.lib,i=r.WordArray,o=r.Hasher,s=e.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var u=s.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,i=t[n];t[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,s=t[e+0],u=t[e+1],d=t[e+2],p=t[e+3],y=t[e+4],g=t[e+5],v=t[e+6],m=t[e+7],_=t[e+8],b=t[e+9],w=t[e+10],S=t[e+11],E=t[e+12],I=t[e+13],A=t[e+14],N=t[e+15],T=o[0],C=o[1],R=o[2],P=o[3];T=c(T,C,R,P,s,7,a[0]),P=c(P,T,C,R,u,12,a[1]),R=c(R,P,T,C,d,17,a[2]),C=c(C,R,P,T,p,22,a[3]),T=c(T,C,R,P,y,7,a[4]),P=c(P,T,C,R,g,12,a[5]),R=c(R,P,T,C,v,17,a[6]),C=c(C,R,P,T,m,22,a[7]),T=c(T,C,R,P,_,7,a[8]),P=c(P,T,C,R,b,12,a[9]),R=c(R,P,T,C,w,17,a[10]),C=c(C,R,P,T,S,22,a[11]),T=c(T,C,R,P,E,7,a[12]),P=c(P,T,C,R,I,12,a[13]),R=c(R,P,T,C,A,17,a[14]),T=f(T,C=c(C,R,P,T,N,22,a[15]),R,P,u,5,a[16]),P=f(P,T,C,R,v,9,a[17]),R=f(R,P,T,C,S,14,a[18]),C=f(C,R,P,T,s,20,a[19]),T=f(T,C,R,P,g,5,a[20]),P=f(P,T,C,R,w,9,a[21]),R=f(R,P,T,C,N,14,a[22]),C=f(C,R,P,T,y,20,a[23]),T=f(T,C,R,P,b,5,a[24]),P=f(P,T,C,R,A,9,a[25]),R=f(R,P,T,C,p,14,a[26]),C=f(C,R,P,T,_,20,a[27]),T=f(T,C,R,P,I,5,a[28]),P=f(P,T,C,R,d,9,a[29]),R=f(R,P,T,C,m,14,a[30]),T=l(T,C=f(C,R,P,T,E,20,a[31]),R,P,g,4,a[32]),P=l(P,T,C,R,_,11,a[33]),R=l(R,P,T,C,S,16,a[34]),C=l(C,R,P,T,A,23,a[35]),T=l(T,C,R,P,u,4,a[36]),P=l(P,T,C,R,y,11,a[37]),R=l(R,P,T,C,m,16,a[38]),C=l(C,R,P,T,w,23,a[39]),T=l(T,C,R,P,I,4,a[40]),P=l(P,T,C,R,s,11,a[41]),R=l(R,P,T,C,p,16,a[42]),C=l(C,R,P,T,v,23,a[43]),T=l(T,C,R,P,b,4,a[44]),P=l(P,T,C,R,E,11,a[45]),R=l(R,P,T,C,N,16,a[46]),T=h(T,C=l(C,R,P,T,d,23,a[47]),R,P,s,6,a[48]),P=h(P,T,C,R,m,10,a[49]),R=h(R,P,T,C,A,15,a[50]),C=h(C,R,P,T,g,21,a[51]),T=h(T,C,R,P,E,6,a[52]),P=h(P,T,C,R,p,10,a[53]),R=h(R,P,T,C,w,15,a[54]),C=h(C,R,P,T,u,21,a[55]),T=h(T,C,R,P,_,6,a[56]),P=h(P,T,C,R,N,10,a[57]),R=h(R,P,T,C,v,15,a[58]),C=h(C,R,P,T,I,21,a[59]),T=h(T,C,R,P,y,6,a[60]),P=h(P,T,C,R,S,10,a[61]),R=h(R,P,T,C,d,15,a[62]),C=h(C,R,P,T,b,21,a[63]),o[0]=o[0]+T|0,o[1]=o[1]+C|0,o[2]=o[2]+R|0,o[3]=o[3]+P|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;r[i>>>5]|=128<<24-i%32;var o=t.floor(n/4294967296),s=n;r[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(i+64>>>9<<4)]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),e.sigBytes=4*(r.length+1),this._process();for(var a=this._hash,u=a.words,c=0;c<4;c++){var f=u[c];u[c]=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8)}return a},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});function c(t,e,r,n,i,o,s){var a=t+(e&r|~e&n)+i+s;return(a<<o|a>>>32-o)+e}function f(t,e,r,n,i,o,s){var a=t+(e&n|r&~n)+i+s;return(a<<o|a>>>32-o)+e}function l(t,e,r,n,i,o,s){var a=t+(e^r^n)+i+s;return(a<<o|a>>>32-o)+e}function h(t,e,r,n,i,o,s){var a=t+(r^(e|~n))+i+s;return(a<<o|a>>>32-o)+e}e.MD5=o._createHelper(u),e.HmacMD5=o._createHmacHelper(u)}(Math),n.MD5)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.accMul=function(t,e){if(isNaN(t)||isNaN(e))return NaN;const{value:r,power:n}=y(t),{value:i,power:o}=y(e);return r*i/Math.pow(10,n+o)},e.camel2snake=l,e.camel2snakeJson=function(t){return h(t,"camel2snake")},e.deepClone=function(t){return JSON.parse(JSON.stringify(t))},e.deleteObjectKey=function t(e,r){const n=e.shift();if(!n)return;t(e,r[n]),r[n]&&(Object.keys(r[n]).length<=0||"string"==typeof r[n])&&delete r[n]},e.getDateStr=d,e.getExtension=function(t){return a[t]},e.getFullTimeStr=function(t){return d(t=t||new Date)+" "+p(t)},e.getOffsetDate=function(t){return new Date(Date.now()+6e4*((new Date).getTimezoneOffset()+60*(t||0)))},e.getTimeStr=p,e.hasOwn=o,e.isFn=function(t){return"function"==typeof t},e.isPlainObject=s,e.log=function(){0},e.mime2ext=void 0,e.snake2camel=f,e.snake2camelJson=function(t){return h(t,"snake2camel")};const n=Object.prototype.toString,i=Object.prototype.hasOwnProperty;function o(t,e){return i.call(t,e)}function s(t){return"[object Object]"===n.call(t)}const a={"image/png":"png","image/jpeg":"jpg","image/gif":"gif","image/svg+xml":"svg","image/bmp":"bmp","image/webp":"webp"};e.mime2ext=a;const u=/_(\w)/g,c=/[A-Z]/g;function f(t){return t.replace(u,(t,e)=>e?e.toUpperCase():"")}function l(t){return t.replace(c,t=>"_"+t.toLowerCase())}function h(t,e){let r,n;switch(e){case"snake2camel":n=f,r=u;break;case"camel2snake":n=l,r=c}for(const i in t)if(o(t,i)&&r.test(i)){const r=n(i);t[r]=t[i],delete t[i],s(t[r])?t[r]=h(t[r],e):Array.isArray(t[r])&&(t[r]=t[r].map(t=>h(t,e)))}return t}function d(t,e="-"){t=t||new Date;const r=[];return r.push(t.getFullYear()),r.push(("00"+(t.getMonth()+1)).substr(-2)),r.push(("00"+t.getDate()).substr(-2)),r.join(e)}function p(t,e=":"){t=t||new Date;const r=[];return r.push(("00"+t.getHours()).substr(-2)),r.push(("00"+t.getMinutes()).substr(-2)),r.push(("00"+t.getSeconds()).substr(-2)),r.join(e)}function y(t){const e=t.toString().split("."),r=e[1]?e[1].length:0;return{value:Number(e.join("")),power:r}}},function(t,e){t.exports=require("fs")},function(t,e){t.exports=require("util")},function(t,e,r){(function(){var e;function r(t,e,r){null!=t&&("number"==typeof t?this.fromNumber(t,e,r):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function n(){return new r(null)}var i="undefined"!=typeof navigator;i&&"Microsoft Internet Explorer"==navigator.appName?(r.prototype.am=function(t,e,r,n,i,o){for(var s=32767&e,a=e>>15;--o>=0;){var u=32767&this[t],c=this[t++]>>15,f=a*u+c*s;i=((u=s*u+((32767&f)<<15)+r[n]+(1073741823&i))>>>30)+(f>>>15)+a*c+(i>>>30),r[n++]=1073741823&u}return i},e=30):i&&"Netscape"!=navigator.appName?(r.prototype.am=function(t,e,r,n,i,o){for(;--o>=0;){var s=e*this[t++]+r[n]+i;i=Math.floor(s/67108864),r[n++]=67108863&s}return i},e=26):(r.prototype.am=function(t,e,r,n,i,o){for(var s=16383&e,a=e>>14;--o>=0;){var u=16383&this[t],c=this[t++]>>14,f=a*u+c*s;i=((u=s*u+((16383&f)<<14)+r[n]+i)>>28)+(f>>14)+a*c,r[n++]=268435455&u}return i},e=28),r.prototype.DB=e,r.prototype.DM=(1<<e)-1,r.prototype.DV=1<<e;r.prototype.FV=Math.pow(2,52),r.prototype.F1=52-e,r.prototype.F2=2*e-52;var o,s,a=new Array;for(o="0".charCodeAt(0),s=0;s<=9;++s)a[o++]=s;for(o="a".charCodeAt(0),s=10;s<36;++s)a[o++]=s;for(o="A".charCodeAt(0),s=10;s<36;++s)a[o++]=s;function u(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function c(t,e){var r=a[t.charCodeAt(e)];return null==r?-1:r}function f(t){var e=n();return e.fromInt(t),e}function l(t){var e,r=1;return 0!=(e=t>>>16)&&(t=e,r+=16),0!=(e=t>>8)&&(t=e,r+=8),0!=(e=t>>4)&&(t=e,r+=4),0!=(e=t>>2)&&(t=e,r+=2),0!=(e=t>>1)&&(t=e,r+=1),r}function h(t){this.m=t}function d(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function p(t,e){return t&e}function y(t,e){return t|e}function g(t,e){return t^e}function v(t,e){return t&~e}function m(t){if(0==t)return-1;var e=0;return 0==(65535&t)&&(t>>=16,e+=16),0==(255&t)&&(t>>=8,e+=8),0==(15&t)&&(t>>=4,e+=4),0==(3&t)&&(t>>=2,e+=2),0==(1&t)&&++e,e}function _(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function b(){}function w(t){return t}function S(t){this.r2=n(),this.q3=n(),r.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}h.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},h.prototype.revert=function(t){return t},h.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},h.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},h.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},d.prototype.convert=function(t){var e=n();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(r.ZERO)>0&&this.m.subTo(e,e),e},d.prototype.revert=function(t){var e=n();return t.copyTo(e),this.reduce(e),e},d.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var r=32767&t[e],n=r*this.mpl+((r*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[r=e+this.m.t]+=this.m.am(0,n,t,e,0,this.m.t);t[r]>=t.DV;)t[r]-=t.DV,t[++r]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},d.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},d.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},r.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},r.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},r.prototype.fromString=function(t,e){var n;if(16==e)n=4;else if(8==e)n=3;else if(256==e)n=8;else if(2==e)n=1;else if(32==e)n=5;else{if(4!=e)return void this.fromRadix(t,e);n=2}this.t=0,this.s=0;for(var i=t.length,o=!1,s=0;--i>=0;){var a=8==n?255&t[i]:c(t,i);a<0?"-"==t.charAt(i)&&(o=!0):(o=!1,0==s?this[this.t++]=a:s+n>this.DB?(this[this.t-1]|=(a&(1<<this.DB-s)-1)<<s,this[this.t++]=a>>this.DB-s):this[this.t-1]|=a<<s,(s+=n)>=this.DB&&(s-=this.DB))}8==n&&0!=(128&t[0])&&(this.s=-1,s>0&&(this[this.t-1]|=(1<<this.DB-s)-1<<s)),this.clamp(),o&&r.ZERO.subTo(this,this)},r.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},r.prototype.dlShiftTo=function(t,e){var r;for(r=this.t-1;r>=0;--r)e[r+t]=this[r];for(r=t-1;r>=0;--r)e[r]=0;e.t=this.t+t,e.s=this.s},r.prototype.drShiftTo=function(t,e){for(var r=t;r<this.t;++r)e[r-t]=this[r];e.t=Math.max(this.t-t,0),e.s=this.s},r.prototype.lShiftTo=function(t,e){var r,n=t%this.DB,i=this.DB-n,o=(1<<i)-1,s=Math.floor(t/this.DB),a=this.s<<n&this.DM;for(r=this.t-1;r>=0;--r)e[r+s+1]=this[r]>>i|a,a=(this[r]&o)<<n;for(r=s-1;r>=0;--r)e[r]=0;e[s]=a,e.t=this.t+s+1,e.s=this.s,e.clamp()},r.prototype.rShiftTo=function(t,e){e.s=this.s;var r=Math.floor(t/this.DB);if(r>=this.t)e.t=0;else{var n=t%this.DB,i=this.DB-n,o=(1<<n)-1;e[0]=this[r]>>n;for(var s=r+1;s<this.t;++s)e[s-r-1]|=(this[s]&o)<<i,e[s-r]=this[s]>>n;n>0&&(e[this.t-r-1]|=(this.s&o)<<i),e.t=this.t-r,e.clamp()}},r.prototype.subTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]-t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n-=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n-=t[r],e[r++]=n&this.DM,n>>=this.DB;n-=t.s}e.s=n<0?-1:0,n<-1?e[r++]=this.DV+n:n>0&&(e[r++]=n),e.t=r,e.clamp()},r.prototype.multiplyTo=function(t,e){var n=this.abs(),i=t.abs(),o=n.t;for(e.t=o+i.t;--o>=0;)e[o]=0;for(o=0;o<i.t;++o)e[o+n.t]=n.am(0,i[o],e,o,0,n.t);e.s=0,e.clamp(),this.s!=t.s&&r.ZERO.subTo(e,e)},r.prototype.squareTo=function(t){for(var e=this.abs(),r=t.t=2*e.t;--r>=0;)t[r]=0;for(r=0;r<e.t-1;++r){var n=e.am(r,e[r],t,2*r,0,1);(t[r+e.t]+=e.am(r+1,2*e[r],t,2*r+1,n,e.t-r-1))>=e.DV&&(t[r+e.t]-=e.DV,t[r+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(r,e[r],t,2*r,0,1)),t.s=0,t.clamp()},r.prototype.divRemTo=function(t,e,i){var o=t.abs();if(!(o.t<=0)){var s=this.abs();if(s.t<o.t)return null!=e&&e.fromInt(0),void(null!=i&&this.copyTo(i));null==i&&(i=n());var a=n(),u=this.s,c=t.s,f=this.DB-l(o[o.t-1]);f>0?(o.lShiftTo(f,a),s.lShiftTo(f,i)):(o.copyTo(a),s.copyTo(i));var h=a.t,d=a[h-1];if(0!=d){var p=d*(1<<this.F1)+(h>1?a[h-2]>>this.F2:0),y=this.FV/p,g=(1<<this.F1)/p,v=1<<this.F2,m=i.t,_=m-h,b=null==e?n():e;for(a.dlShiftTo(_,b),i.compareTo(b)>=0&&(i[i.t++]=1,i.subTo(b,i)),r.ONE.dlShiftTo(h,b),b.subTo(a,a);a.t<h;)a[a.t++]=0;for(;--_>=0;){var w=i[--m]==d?this.DM:Math.floor(i[m]*y+(i[m-1]+v)*g);if((i[m]+=a.am(0,w,i,_,0,h))<w)for(a.dlShiftTo(_,b),i.subTo(b,i);i[m]<--w;)i.subTo(b,i)}null!=e&&(i.drShiftTo(h,e),u!=c&&r.ZERO.subTo(e,e)),i.t=h,i.clamp(),f>0&&i.rShiftTo(f,i),u<0&&r.ZERO.subTo(i,i)}}},r.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(0==(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},r.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},r.prototype.exp=function(t,e){if(t>4294967295||t<1)return r.ONE;var i=n(),o=n(),s=e.convert(this),a=l(t)-1;for(s.copyTo(i);--a>=0;)if(e.sqrTo(i,o),(t&1<<a)>0)e.mulTo(o,s,i);else{var u=i;i=o,o=u}return e.revert(i)},r.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var r,n=(1<<e)-1,i=!1,o="",s=this.t,a=this.DB-s*this.DB%e;if(s-- >0)for(a<this.DB&&(r=this[s]>>a)>0&&(i=!0,o=u(r));s>=0;)a<e?(r=(this[s]&(1<<a)-1)<<e-a,r|=this[--s]>>(a+=this.DB-e)):(r=this[s]>>(a-=e)&n,a<=0&&(a+=this.DB,--s)),r>0&&(i=!0),i&&(o+=u(r));return i?o:"0"},r.prototype.negate=function(){var t=n();return r.ZERO.subTo(this,t),t},r.prototype.abs=function(){return this.s<0?this.negate():this},r.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var r=this.t;if(0!=(e=r-t.t))return this.s<0?-e:e;for(;--r>=0;)if(0!=(e=this[r]-t[r]))return e;return 0},r.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+l(this[this.t-1]^this.s&this.DM)},r.prototype.mod=function(t){var e=n();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(r.ZERO)>0&&t.subTo(e,e),e},r.prototype.modPowInt=function(t,e){var r;return r=t<256||e.isEven()?new h(e):new d(e),this.exp(t,r)},r.ZERO=f(0),r.ONE=f(1),b.prototype.convert=w,b.prototype.revert=w,b.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r)},b.prototype.sqrTo=function(t,e){t.squareTo(e)},S.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=n();return t.copyTo(e),this.reduce(e),e},S.prototype.revert=function(t){return t},S.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},S.prototype.mulTo=function(t,e,r){t.multiplyTo(e,r),this.reduce(r)},S.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)};var E,I,A,N=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],T=(1<<26)/N[N.length-1];function C(){var t;t=(new Date).getTime(),I[A++]^=255&t,I[A++]^=t>>8&255,I[A++]^=t>>16&255,I[A++]^=t>>24&255,A>=F&&(A-=F)}if(r.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},r.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),r=Math.pow(t,e),i=f(r),o=n(),s=n(),a="";for(this.divRemTo(i,o,s);o.signum()>0;)a=(r+s.intValue()).toString(t).substr(1)+a,o.divRemTo(i,o,s);return s.intValue().toString(t)+a},r.prototype.fromRadix=function(t,e){this.fromInt(0),null==e&&(e=10);for(var n=this.chunkSize(e),i=Math.pow(e,n),o=!1,s=0,a=0,u=0;u<t.length;++u){var f=c(t,u);f<0?"-"==t.charAt(u)&&0==this.signum()&&(o=!0):(a=e*a+f,++s>=n&&(this.dMultiply(i),this.dAddOffset(a,0),s=0,a=0))}s>0&&(this.dMultiply(Math.pow(e,s)),this.dAddOffset(a,0)),o&&r.ZERO.subTo(this,this)},r.prototype.fromNumber=function(t,e,n){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,n),this.testBit(t-1)||this.bitwiseTo(r.ONE.shiftLeft(t-1),y,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(r.ONE.shiftLeft(t-1),this);else{var i=new Array,o=7&t;i.length=1+(t>>3),e.nextBytes(i),o>0?i[0]&=(1<<o)-1:i[0]=0,this.fromString(i,256)}},r.prototype.bitwiseTo=function(t,e,r){var n,i,o=Math.min(t.t,this.t);for(n=0;n<o;++n)r[n]=e(this[n],t[n]);if(t.t<this.t){for(i=t.s&this.DM,n=o;n<this.t;++n)r[n]=e(this[n],i);r.t=this.t}else{for(i=this.s&this.DM,n=o;n<t.t;++n)r[n]=e(i,t[n]);r.t=t.t}r.s=e(this.s,t.s),r.clamp()},r.prototype.changeBit=function(t,e){var n=r.ONE.shiftLeft(t);return this.bitwiseTo(n,e,n),n},r.prototype.addTo=function(t,e){for(var r=0,n=0,i=Math.min(t.t,this.t);r<i;)n+=this[r]+t[r],e[r++]=n&this.DM,n>>=this.DB;if(t.t<this.t){for(n+=t.s;r<this.t;)n+=this[r],e[r++]=n&this.DM,n>>=this.DB;n+=this.s}else{for(n+=this.s;r<t.t;)n+=t[r],e[r++]=n&this.DM,n>>=this.DB;n+=t.s}e.s=n<0?-1:0,n>0?e[r++]=n:n<-1&&(e[r++]=this.DV+n),e.t=r,e.clamp()},r.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},r.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},r.prototype.multiplyLowerTo=function(t,e,r){var n,i=Math.min(this.t+t.t,e);for(r.s=0,r.t=i;i>0;)r[--i]=0;for(n=r.t-this.t;i<n;++i)r[i+this.t]=this.am(0,t[i],r,i,0,this.t);for(n=Math.min(t.t,e);i<n;++i)this.am(0,t[i],r,i,0,e-i);r.clamp()},r.prototype.multiplyUpperTo=function(t,e,r){--e;var n=r.t=this.t+t.t-e;for(r.s=0;--n>=0;)r[n]=0;for(n=Math.max(e-this.t,0);n<t.t;++n)r[this.t+n-e]=this.am(e-n,t[n],r,0,0,this.t+n-e);r.clamp(),r.drShiftTo(1,r)},r.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,r=this.s<0?t-1:0;if(this.t>0)if(0==e)r=this[0]%t;else for(var n=this.t-1;n>=0;--n)r=(e*r+this[n])%t;return r},r.prototype.millerRabin=function(t){var e=this.subtract(r.ONE),i=e.getLowestSetBit();if(i<=0)return!1;var o=e.shiftRight(i);(t=t+1>>1)>N.length&&(t=N.length);for(var s=n(),a=0;a<t;++a){s.fromInt(N[Math.floor(Math.random()*N.length)]);var u=s.modPow(o,this);if(0!=u.compareTo(r.ONE)&&0!=u.compareTo(e)){for(var c=1;c++<i&&0!=u.compareTo(e);)if(0==(u=u.modPowInt(2,this)).compareTo(r.ONE))return!1;if(0!=u.compareTo(e))return!1}}return!0},r.prototype.clone=function(){var t=n();return this.copyTo(t),t},r.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},r.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},r.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},r.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},r.prototype.toByteArray=function(){var t=this.t,e=new Array;e[0]=this.s;var r,n=this.DB-t*this.DB%8,i=0;if(t-- >0)for(n<this.DB&&(r=this[t]>>n)!=(this.s&this.DM)>>n&&(e[i++]=r|this.s<<this.DB-n);t>=0;)n<8?(r=(this[t]&(1<<n)-1)<<8-n,r|=this[--t]>>(n+=this.DB-8)):(r=this[t]>>(n-=8)&255,n<=0&&(n+=this.DB,--t)),0!=(128&r)&&(r|=-256),0==i&&(128&this.s)!=(128&r)&&++i,(i>0||r!=this.s)&&(e[i++]=r);return e},r.prototype.equals=function(t){return 0==this.compareTo(t)},r.prototype.min=function(t){return this.compareTo(t)<0?this:t},r.prototype.max=function(t){return this.compareTo(t)>0?this:t},r.prototype.and=function(t){var e=n();return this.bitwiseTo(t,p,e),e},r.prototype.or=function(t){var e=n();return this.bitwiseTo(t,y,e),e},r.prototype.xor=function(t){var e=n();return this.bitwiseTo(t,g,e),e},r.prototype.andNot=function(t){var e=n();return this.bitwiseTo(t,v,e),e},r.prototype.not=function(){for(var t=n(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},r.prototype.shiftLeft=function(t){var e=n();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},r.prototype.shiftRight=function(t){var e=n();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},r.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+m(this[t]);return this.s<0?this.t*this.DB:-1},r.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,r=0;r<this.t;++r)t+=_(this[r]^e);return t},r.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:0!=(this[e]&1<<t%this.DB)},r.prototype.setBit=function(t){return this.changeBit(t,y)},r.prototype.clearBit=function(t){return this.changeBit(t,v)},r.prototype.flipBit=function(t){return this.changeBit(t,g)},r.prototype.add=function(t){var e=n();return this.addTo(t,e),e},r.prototype.subtract=function(t){var e=n();return this.subTo(t,e),e},r.prototype.multiply=function(t){var e=n();return this.multiplyTo(t,e),e},r.prototype.divide=function(t){var e=n();return this.divRemTo(t,e,null),e},r.prototype.remainder=function(t){var e=n();return this.divRemTo(t,null,e),e},r.prototype.divideAndRemainder=function(t){var e=n(),r=n();return this.divRemTo(t,e,r),new Array(e,r)},r.prototype.modPow=function(t,e){var r,i,o=t.bitLength(),s=f(1);if(o<=0)return s;r=o<18?1:o<48?3:o<144?4:o<768?5:6,i=o<8?new h(e):e.isEven()?new S(e):new d(e);var a=new Array,u=3,c=r-1,p=(1<<r)-1;if(a[1]=i.convert(this),r>1){var y=n();for(i.sqrTo(a[1],y);u<=p;)a[u]=n(),i.mulTo(y,a[u-2],a[u]),u+=2}var g,v,m=t.t-1,_=!0,b=n();for(o=l(t[m])-1;m>=0;){for(o>=c?g=t[m]>>o-c&p:(g=(t[m]&(1<<o+1)-1)<<c-o,m>0&&(g|=t[m-1]>>this.DB+o-c)),u=r;0==(1&g);)g>>=1,--u;if((o-=u)<0&&(o+=this.DB,--m),_)a[g].copyTo(s),_=!1;else{for(;u>1;)i.sqrTo(s,b),i.sqrTo(b,s),u-=2;u>0?i.sqrTo(s,b):(v=s,s=b,b=v),i.mulTo(b,a[g],s)}for(;m>=0&&0==(t[m]&1<<o);)i.sqrTo(s,b),v=s,s=b,b=v,--o<0&&(o=this.DB-1,--m)}return i.revert(s)},r.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return r.ZERO;for(var n=t.clone(),i=this.clone(),o=f(1),s=f(0),a=f(0),u=f(1);0!=n.signum();){for(;n.isEven();)n.rShiftTo(1,n),e?(o.isEven()&&s.isEven()||(o.addTo(this,o),s.subTo(t,s)),o.rShiftTo(1,o)):s.isEven()||s.subTo(t,s),s.rShiftTo(1,s);for(;i.isEven();)i.rShiftTo(1,i),e?(a.isEven()&&u.isEven()||(a.addTo(this,a),u.subTo(t,u)),a.rShiftTo(1,a)):u.isEven()||u.subTo(t,u),u.rShiftTo(1,u);n.compareTo(i)>=0?(n.subTo(i,n),e&&o.subTo(a,o),s.subTo(u,s)):(i.subTo(n,i),e&&a.subTo(o,a),u.subTo(s,u))}return 0!=i.compareTo(r.ONE)?r.ZERO:u.compareTo(t)>=0?u.subtract(t):u.signum()<0?(u.addTo(t,u),u.signum()<0?u.add(t):u):u},r.prototype.pow=function(t){return this.exp(t,new b)},r.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),r=t.s<0?t.negate():t.clone();if(e.compareTo(r)<0){var n=e;e=r,r=n}var i=e.getLowestSetBit(),o=r.getLowestSetBit();if(o<0)return e;for(i<o&&(o=i),o>0&&(e.rShiftTo(o,e),r.rShiftTo(o,r));e.signum()>0;)(i=e.getLowestSetBit())>0&&e.rShiftTo(i,e),(i=r.getLowestSetBit())>0&&r.rShiftTo(i,r),e.compareTo(r)>=0?(e.subTo(r,e),e.rShiftTo(1,e)):(r.subTo(e,r),r.rShiftTo(1,r));return o>0&&r.lShiftTo(o,r),r},r.prototype.isProbablePrime=function(t){var e,r=this.abs();if(1==r.t&&r[0]<=N[N.length-1]){for(e=0;e<N.length;++e)if(r[0]==N[e])return!0;return!1}if(r.isEven())return!1;for(e=1;e<N.length;){for(var n=N[e],i=e+1;i<N.length&&n<T;)n*=N[i++];for(n=r.modInt(n);e<i;)if(n%N[e++]==0)return!1}return r.millerRabin(t)},r.prototype.square=function(){var t=n();return this.squareTo(t),t},r.prototype.Barrett=S,null==I){var R;if(I=new Array,A=0,"undefined"!=typeof window&&window.crypto)if(window.crypto.getRandomValues){var P=new Uint8Array(32);for(window.crypto.getRandomValues(P),R=0;R<32;++R)I[A++]=P[R]}else if("Netscape"==navigator.appName&&navigator.appVersion<"5"){var x=window.crypto.random(32);for(R=0;R<x.length;++R)I[A++]=255&x.charCodeAt(R)}for(;A<F;)R=Math.floor(65536*Math.random()),I[A++]=R>>>8,I[A++]=255&R;A=0,C()}function O(){if(null==E){for(C(),(E=new D).init(I),A=0;A<I.length;++A)I[A]=0;A=0}return E.next()}function B(){}function D(){this.i=0,this.j=0,this.S=new Array}B.prototype.nextBytes=function(t){var e;for(e=0;e<t.length;++e)t[e]=O()},D.prototype.init=function(t){var e,r,n;for(e=0;e<256;++e)this.S[e]=e;for(r=0,e=0;e<256;++e)r=r+this.S[e]+t[e%t.length]&255,n=this.S[e],this.S[e]=this.S[r],this.S[r]=n;this.i=0,this.j=0},D.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]};var F=256;t.exports={default:r,BigInteger:r,SecureRandom:B}}).call(this)},function(t,e,r){var n,i,o,s,a,u;t.exports=(u=r(0),i=(n=u).lib,o=i.Base,s=i.WordArray,(a=n.x64={}).Word=o.extend({init:function(t,e){this.high=t,this.low=e}}),a.WordArray=o.extend({init:function(t,e){t=this.words=t||[],this.sigBytes=null!=e?e:8*t.length},toX32:function(){for(var t=this.words,e=t.length,r=[],n=0;n<e;n++){var i=t[n];r.push(i.high),r.push(i.low)}return s.create(r,this.sigBytes)},clone:function(){for(var t=o.clone.call(this),e=t.words=this.words.slice(0),r=e.length,n=0;n<r;n++)e[n]=e[n].clone();return t}}),u)},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(13);e.bytesFromIP=n.bytesFromIP,e.bytesToIP=n.bytesToIP,e.getOID=n.getOID,e.getOIDName=n.getOIDName;var i=r(16);e.PublicKey=i.PublicKey,e.PrivateKey=i.PrivateKey,e.RSAPublicKey=i.RSAPublicKey,e.RSAPrivateKey=i.RSAPrivateKey;var o=r(36);e.Certificate=o.Certificate,e.DistinguishedName=o.DistinguishedName},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(32);e.bytesFromIP=function(t){switch(n.isIP(t)){case 4:return Buffer.from(t.split(".").map(t=>parseInt(t,10)));case 6:const e=t.split(":"),r=Buffer.alloc(16);let n=0;""===e[e.length-1]&&(e[e.length-1]="0");for(let t=0;t<e.length;t++)""!==e[t]?(r.writeUInt16BE(parseInt(e[t],16),n),n+=2):t+1<e.length&&""!==e[t+1]&&(n=16-2*(e.length-t-1));return r;default:return null}},e.bytesToIP=function(t){switch(t.length){case 4:return[t[0],t[1],t[2],t[3]].join(".");case 16:const e=[];let r=-1,n=0,i=-1,o=0;for(let s=0;s<t.length;s+=2){const a=t[s]<<8|t[s+1];0===a?(n++,-1===r&&(r=e.length),n>o&&(o=n,i=r)):(r=-1,n=0),e.push(a.toString(16))}if(o>0){let t="";const r=e.slice(i+o);e.length=i,0===e.length&&(t+=":"),0===r.length&&(t+=":"),e.push(t,...r)}return e.join(":");default:return""}};const i=Object.create(null),o=/^[0-9.]+$/;function s(t,e){i[t]=e,i[e]=t}e.getOID=function(t){return o.test(t)&&""!==i[t]?t:null==i[t]?"":i[t]},e.getOIDName=function(t){return o.test(t)||""===i[t]?null==i[t]?t:i[t]:t},s("1.2.840.113549.1.1.1","rsaEncryption"),s("1.2.840.113549.1.1.4","md5WithRsaEncryption"),s("1.2.840.113549.1.1.5","sha1WithRsaEncryption"),s("1.2.840.113549.1.1.8","mgf1"),s("1.2.840.113549.1.1.10","RSASSA-PSS"),s("1.2.840.113549.1.1.11","sha256WithRsaEncryption"),s("1.2.840.113549.1.1.12","sha384WithRsaEncryption"),s("1.2.840.113549.1.1.13","sha512WithRsaEncryption"),s("1.2.840.10045.2.1","ecEncryption"),s("1.2.840.10045.4.1","ecdsaWithSha1"),s("1.2.840.10045.4.3.2","ecdsaWithSha256"),s("1.2.840.10045.4.3.3","ecdsaWithSha384"),s("1.2.840.10045.4.3.4","ecdsaWithSha512"),s("1.2.840.10040.4.3","dsaWithSha1"),s("2.16.84*********.4.3.2","dsaWithSha256"),s("1.3.14.3.2.7","desCBC"),s("1.3.14.3.2.26","sha1"),s("2.16.84*********.4.2.1","sha256"),s("2.16.84*********.4.2.2","sha384"),s("2.16.84*********.4.2.3","sha512"),s("1.2.840.113549.2.5","md5"),s("***********","X25519"),s("***********","X448"),s("***********","Ed25519"),s("***********","Ed448"),s("1.2.840.113549.1.7.1","data"),s("1.2.840.113549.1.7.2","signedData"),s("1.2.840.113549.1.7.3","envelopedData"),s("1.2.840.113549.1.7.4","signedAndEnvelopedData"),s("1.2.840.113549.1.7.5","digestedData"),s("1.2.840.113549.1.7.6","encryptedData"),s("1.2.840.113549.1.9.1","emailAddress"),s("1.2.840.113549.1.9.2","unstructuredName"),s("1.2.840.113549.1.9.3","contentType"),s("1.2.840.113549.1.9.4","messageDigest"),s("1.2.840.113549.1.9.5","signingTime"),s("1.2.840.113549.1.9.6","counterSignature"),s("1.2.840.113549.1.9.7","challengePassword"),s("1.2.840.113549.1.9.8","unstructuredAddress"),s("1.2.840.113549.1.9.14","extensionRequest"),s("1.2.840.113549.1.9.20","friendlyName"),s("1.2.840.113549.1.9.21","localKeyId"),s("1.2.840.113549.********","x509Certificate"),s("1.2.840.113549.*********.1","keyBag"),s("1.2.840.113549.*********.2","pkcs8ShroudedKeyBag"),s("1.2.840.113549.*********.3","certBag"),s("1.2.840.113549.*********.4","crlBag"),s("1.2.840.113549.*********.5","secretBag"),s("1.2.840.113549.*********.6","safeContentsBag"),s("1.2.840.113549.1.5.13","pkcs5PBES2"),s("1.2.840.113549.1.5.12","pkcs5PBKDF2"),s("1.2.840.113549.2.7","hmacWithSha1"),s("1.2.840.113549.2.9","hmacWithSha256"),s("1.2.840.113549.2.10","hmacWithSha384"),s("1.2.840.113549.2.11","hmacWithSha512"),s("1.2.840.113549.3.7","3desCBC"),s("2.16.84*********.4.1.2","aesCBC128"),s("2.16.84*********.4.1.42","aesCBC256"),s("*******","commonName"),s("*******","serialName"),s("*******","countryName"),s("*******","localityName"),s("*******","stateOrProvinceName"),s("********","organizationName"),s("2.5.4.11","organizationalUnitName"),s("2.5.4.15","businessCategory"),s("2.16.840.1.113730.1.1","nsCertType"),s("2.5.29.2","keyAttributes"),s("2.5.29.4","keyUsageRestriction"),s("2.5.29.6","subtreesConstraint"),s("2.5.29.9","subjectDirectoryAttributes"),s("2.5.29.14","subjectKeyIdentifier"),s("2.5.29.15","keyUsage"),s("2.5.29.16","privateKeyUsagePeriod"),s("2.5.29.17","subjectAltName"),s("2.5.29.18","issuerAltName"),s("2.5.29.19","basicConstraints"),s("2.5.29.20","cRLNumber"),s("2.5.29.21","cRLReason"),s("2.5.29.22","expirationDate"),s("2.5.29.23","instructionCode"),s("2.5.29.24","invalidityDate"),s("2.5.29.27","deltaCRLIndicator"),s("2.5.29.28","issuingDistributionPoint"),s("2.5.29.29","certificateIssuer"),s("2.5.29.30","nameConstraints"),s("2.5.29.31","cRLDistributionPoints"),s("2.5.29.32","certificatePolicies"),s("2.5.29.33","policyMappings"),s("2.5.29.35","authorityKeyIdentifier"),s("2.5.29.36","policyConstraints"),s("2.5.29.37","extKeyUsage"),s("2.5.29.46","freshestCRL"),s("2.5.29.54","inhibitAnyPolicy"),s("1.3.6.1.4.1.311.60.2.1.2","jurisdictionST"),s("1.3.6.1.4.1.311.60.2.1.3","jurisdictionC"),s("1.3.6.1.4.1.11129.2.4.2","timestampList"),s("1.3.6.1.5.5.7.1.1","authorityInfoAccess"),s("1.3.6.1.5.5.7.3.1","serverAuth"),s("1.3.6.1.5.5.7.3.2","clientAuth"),s("1.3.6.1.5.5.7.3.3","codeSigning"),s("1.3.6.1.5.5.7.3.4","emailProtection"),s("1.3.6.1.5.5.7.3.8","timeStamping"),s("1.3.6.1.5.5.7.48.1","authorityInfoAccessOcsp"),s("1.3.6.1.5.5.7.48.2","authorityInfoAccessIssuers")},function(t,e,r){var n,i,o,s,a,u,c,f;t.exports=(f=r(0),i=(n=f).lib,o=i.WordArray,s=i.Hasher,a=n.algo,u=[],c=a.SHA1=s.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],c=0;c<80;c++){if(c<16)u[c]=0|t[e+c];else{var f=u[c-3]^u[c-8]^u[c-14]^u[c-16];u[c]=f<<1|f>>>31}var l=(n<<5|n>>>27)+a+u[c];l+=c<20?1518500249+(i&o|~i&s):c<40?1859775393+(i^o^s):c<60?(i&o|i&s|o&s)-1894007588:(i^o^s)-899497514,a=s,s=o,o=i<<30|i>>>2,i=n,n=l}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+a|0},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),e[15+(n+64>>>9<<4)]=r,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=s.clone.call(this);return t._hash=this._hash.clone(),t}}),n.SHA1=s._createHelper(c),n.HmacSHA1=s._createHmacHelper(c),f.SHA1)},function(t,e,r){var n,i,o,s;t.exports=(n=r(0),o=(i=n).lib.Base,s=i.enc.Utf8,void(i.algo.HMAC=o.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=s.parse(e));var r=t.blockSize,n=4*r;e.sigBytes>n&&(e=t.finalize(e)),e.clamp();for(var i=this._oKey=e.clone(),o=this._iKey=e.clone(),a=i.words,u=o.words,c=0;c<r;c++)a[c]^=1549556828,u[c]^=909522486;i.sigBytes=o.sigBytes=n,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,r=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(r))}})))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(9),i=r(2),o=r(33),s=r(17),a=r(13);e.publicKeyValidator={name:"PublicKeyInfo",class:s.Class.UNIVERSAL,tag:s.Tag.SEQUENCE,capture:"publicKeyInfo",value:[{name:"PublicKeyInfo.AlgorithmIdentifier",class:s.Class.UNIVERSAL,tag:s.Tag.SEQUENCE,value:[{name:"PublicKeyAlgorithmIdentifier.algorithm",class:s.Class.UNIVERSAL,tag:s.Tag.OID,capture:"publicKeyOID"}]},{name:"PublicKeyInfo.PublicKey",class:s.Class.UNIVERSAL,tag:s.Tag.BITSTRING,capture:"publicKey"}]},e.privateKeyValidator={name:"PrivateKeyInfo",class:s.Class.UNIVERSAL,tag:s.Tag.SEQUENCE,capture:"privateKeyInfo",value:[{name:"PrivateKeyInfo.Version",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyVersion"},{name:"PrivateKeyInfo.AlgorithmIdentifier",class:s.Class.UNIVERSAL,tag:s.Tag.SEQUENCE,value:[{name:"PrivateKeyAlgorithmIdentifier.algorithm",class:s.Class.UNIVERSAL,tag:s.Tag.OID,capture:"privateKeyOID"}]},{name:"PrivateKeyInfo.PrivateKey",class:s.Class.UNIVERSAL,tag:s.Tag.OCTETSTRING,capture:"privateKey"}]};const u={name:"RSAPublicKey",class:s.Class.UNIVERSAL,tag:s.Tag.SEQUENCE,value:[{name:"RSAPublicKey.modulus",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"publicKeyModulus"},{name:"RSAPublicKey.exponent",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"publicKeyExponent"}]},c={name:"RSAPrivateKey",class:s.Class.UNIVERSAL,tag:s.Tag.SEQUENCE,value:[{name:"RSAPrivateKey.version",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyVersion"},{name:"RSAPrivateKey.modulus",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyModulus"},{name:"RSAPrivateKey.publicExponent",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyPublicExponent"},{name:"RSAPrivateKey.privateExponent",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyPrivateExponent"},{name:"RSAPrivateKey.prime1",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyPrime1"},{name:"RSAPrivateKey.prime2",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyPrime2"},{name:"RSAPrivateKey.exponent1",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyExponent1"},{name:"RSAPrivateKey.exponent2",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyExponent2"},{name:"RSAPrivateKey.coefficient",class:s.Class.UNIVERSAL,tag:s.Tag.INTEGER,capture:"privateKeyCoefficient"}]},f=[a.getOID("X25519"),a.getOID("X448"),a.getOID("Ed25519"),a.getOID("Ed448")];class l{constructor(t){const r={},n=t.validate(e.publicKeyValidator,r);if(null!=n)throw new Error("Cannot read X.509 public key: "+n.message);this.oid=s.ASN1.parseOID(r.publicKeyOID.bytes),this.algo=a.getOIDName(this.oid),this._pkcs8=t,this._keyRaw=s.ASN1.parseBitString(r.publicKey.bytes).buf,this._finalKey=this._keyRaw,this._finalPEM=""}static fromPEM(t){const e=s.PEM.parse(t)[0];if(e.procType.includes("ENCRYPTED"))throw new Error("Could not convert public key from PEM, PEM is encrypted.");const r=s.ASN1.fromDER(e.body,!0);switch(e.type){case"PUBLIC KEY":return new l(r);case"RSA PUBLIC KEY":const t=s.ASN1.Seq([s.ASN1.Seq([s.ASN1.OID(a.getOID("rsaEncryption")),s.ASN1.Null()]),s.ASN1.BitString(r.DER)]);return new l(t);default:throw new Error("Could not convert public key from PEM, recommend PKCS#8 PEM")}}static addVerifier(t,e){if(""===(t=a.getOID(t)))throw new Error("Invalid object identifier: "+t);if(null!=l._verifiers[t])throw new Error(`Verifier ${t} exists`);l._verifiers[t]=e}get keyRaw(){return this._finalKey}verify(t,e,r){const n=l._verifiers[this.oid];if(null!=n){const o=i.createHash(r).update(t).digest();return n.call(this,o,e)}const o=i.createVerify(r);return o.update(t),o.verify(this.toPEM(),e)}getFingerprint(t,e="PublicKey"){let r;switch(e){case"PublicKeyInfo":r=this._pkcs8.DER;break;case"PublicKey":r=this._keyRaw;break;default:throw new Error(`Unknown fingerprint type "${e}".`)}const n=i.createHash(t);return n.update(r),n.digest()}toASN1(){return this._pkcs8}toDER(){return this._pkcs8.DER}toPEM(){return""===this._finalPEM&&(this._finalPEM=new s.PEM("PUBLIC KEY",this._pkcs8.DER).toString()),this._finalPEM}toJSON(){return{oid:this.oid,algo:this.algo,publicKey:this._keyRaw}}[n.inspect.custom](t,e){return`<${this.constructor.name} ${n.inspect(this.toJSON(),e)}>`}}l._verifiers=Object.create(null),e.PublicKey=l;class h{constructor(t){const r=Object.create(null),n=t.validate(e.privateKeyValidator,r);if(null!=n)throw new Error("Cannot read X.509 private key: "+n.message);if(this.version=s.ASN1.parseIntegerNum(r.privateKeyVersion.bytes)+1,this.oid=s.ASN1.parseOID(r.privateKeyOID.bytes),this.algo=a.getOIDName(this.oid),this._pkcs8=t,this._keyRaw=r.privateKey.bytes,this._publicKeyRaw=null,this._finalKey=this._keyRaw,this._finalPEM="",f.includes(this.oid))if(this._finalKey=this._keyRaw=s.ASN1.parseDER(this._keyRaw,s.Class.UNIVERSAL,s.Tag.OCTETSTRING).bytes,"***********"===this.oid){const t=o.sign.keyPair.fromSeed(this._keyRaw);this._publicKeyRaw=Buffer.from(t.publicKey),this._finalKey=Buffer.from(t.secretKey)}else if(2===this.version)for(const e of t.mustCompound())e.class===s.Class.CONTEXT_SPECIFIC&&1===e.tag&&(this._publicKeyRaw=s.ASN1.parseBitString(e.bytes).buf,this._finalKey=Buffer.concat([this._keyRaw,this._publicKeyRaw]))}static fromPEM(t){const e=s.PEM.parse(t)[0];if(e.procType.includes("ENCRYPTED"))throw new Error("Could not convert private key from PEM, PEM is encrypted.");let r=s.ASN1.fromDER(e.body,!0);switch(e.type){case"PRIVATE KEY":return new h(r);case"RSA PRIVATE KEY":return r=s.ASN1.Seq([r.value[0],s.ASN1.Seq([s.ASN1.OID(a.getOID("rsaEncryption")),s.ASN1.Null()]),new s.ASN1(s.Class.UNIVERSAL,s.Tag.OCTETSTRING,r.DER)]),new h(r);default:throw new Error("Could not convert private key from PEM, recommend PKCS#8 PEM")}}static addSigner(t,e){if(""===(t=a.getOID(t)))throw new Error("Invalid object identifier: "+t);if(null!=h._signers[t])throw new Error(`Signer ${t} exists`);h._signers[t]=e}get keyRaw(){return this._finalKey}get publicKeyRaw(){return this._publicKeyRaw}sign(t,e){const r=h._signers[this.oid];if(null!=r){const n=i.createHash(e).update(t).digest();return r.call(this,n)}const n=i.createSign(e);return n.update(t),n.sign(this.toPEM())}toASN1(){return this._pkcs8}toDER(){return this._pkcs8.DER}toPEM(){return""===this._finalPEM&&(this._finalPEM=new s.PEM("PRIVATE KEY",this._pkcs8.DER).toString()),this._finalPEM}toJSON(){return{version:this.version,oid:this.oid,algo:this.algo,privateKey:this._keyRaw,publicKey:this._publicKeyRaw}}[n.inspect.custom](t,e){return`<${this.constructor.name} ${n.inspect(this.toJSON(),e)}>`}}h._signers=Object.create(null),e.PrivateKey=h;class d extends l{static fromPublicKey(t){return new d(t.toASN1())}constructor(t){if(super(t),a.getOID(this.oid)!==a.getOID("rsaEncryption"))throw new Error("Invalid RSA public key, unknown OID: "+this.oid);const e=Object.create(null);this._pkcs1=s.ASN1.fromDER(this._keyRaw,!0);const r=this._pkcs1.validate(u,e);if(null!=r)throw new Error("Cannot read RSA public key: "+r.message);this.modulus=s.ASN1.parseIntegerStr(e.publicKeyModulus.bytes),this.exponent=s.ASN1.parseIntegerNum(e.publicKeyExponent.bytes)}toASN1(){return this._pkcs1}toDER(){return this._keyRaw}toPEM(){return""===this._finalPEM&&(this._finalPEM=new s.PEM("RSA PUBLIC KEY",this._keyRaw).toString()),this._finalPEM}toPublicKeyPEM(){return new s.PEM("PUBLIC KEY",this._pkcs8.DER).toString()}toJSON(){return{oid:this.oid,algo:this.algo,modulus:y(this.modulus),exponent:this.exponent}}[n.inspect.custom](t,e){return`<${this.constructor.name} ${n.inspect(this.toJSON(),e)}>`}}e.RSAPublicKey=d;class p extends h{static fromPrivateKey(t){return new p(t.toASN1())}constructor(t){if(super(t),a.getOID(this.oid)!==a.getOID("rsaEncryption"))throw new Error("Invalid RSA private key, unknown OID: "+this.oid);const e=Object.create(null);this._pkcs1=s.ASN1.fromDER(this._keyRaw,!0);const r=this._pkcs1.validate(c,e);if(null!=r)throw new Error("Cannot read RSA private key: "+r.message);this.publicExponent=s.ASN1.parseIntegerNum(e.privateKeyPublicExponent.bytes),this.privateExponent=s.ASN1.parseIntegerStr(e.privateKeyPrivateExponent.bytes),this.modulus=s.ASN1.parseIntegerStr(e.privateKeyModulus.bytes),this.prime1=s.ASN1.parseIntegerStr(e.privateKeyPrime1.bytes),this.prime2=s.ASN1.parseIntegerStr(e.privateKeyPrime2.bytes),this.exponent1=s.ASN1.parseIntegerStr(e.privateKeyExponent1.bytes),this.exponent2=s.ASN1.parseIntegerStr(e.privateKeyExponent2.bytes),this.coefficient=s.ASN1.parseIntegerStr(e.privateKeyCoefficient.bytes)}toASN1(){return this._pkcs1}toDER(){return this._keyRaw}toPEM(){return""===this._finalPEM&&(this._finalPEM=new s.PEM("RSA PRIVATE KEY",this._keyRaw).toString()),this._finalPEM}toPrivateKeyPEM(){return new s.PEM("PRIVATE KEY",this._pkcs8.DER).toString()}toJSON(){return{version:this.version,oid:this.oid,algo:this.algo,publicExponent:this.publicExponent,privateExponent:y(this.privateExponent),modulus:y(this.modulus),prime1:y(this.prime1),prime2:y(this.prime2),exponent1:y(this.exponent1),exponent2:y(this.exponent2),coefficient:y(this.coefficient)}}[n.inspect.custom](t,e){return`<${this.constructor.name} ${n.inspect(this.toJSON(),e)}>`}}function y(t){return t.length%8!=0&&t.startsWith("00")?t.slice(2):t}e.RSAPrivateKey=p,l.addVerifier(a.getOID("Ed25519"),(function(t,e){return o.sign.detached.verify(t,e,this.keyRaw)})),h.addSigner(a.getOID("Ed25519"),(function(t){const e=this.keyRaw;if(64!==e.length)throw new Error("Invalid signing key.");return Buffer.from(o.sign.detached(t,e))}))},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=r(18);e.BufferVisitor=n.BufferVisitor;var i=r(34);e.PEM=i.PEM;var o=r(35);e.ASN1=o.ASN1,e.Class=o.Class,e.Tag=o.Tag,e.BitString=o.BitString},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});e.BufferVisitor=class{constructor(t,e=0,r=0){this.start=e,this.end=r>e?r:e,this.buf=t}get length(){return this.buf.length}reset(t=0,e=0){return this.start=t,e>=this.start?this.end=e:this.end<this.start&&(this.end=this.start),this}walk(t){return this.start=this.end,this.end+=t,this}mustHas(t,e="Too few bytes to parse."){const r=this.end+t;if(r>this.buf.length){const t=new Error(e);throw t.available=this.buf.length,t.requested=r,t}return this.walk(0),this}mustWalk(t,e){return this.mustHas(t,e),this.walk(t),this}}},function(t,e){const r=new Uint32Array(68),n=new Uint32Array(64);function i(t,e){const r=31&e;return t<<r|t>>>32-r}function o(t,e){const r=[];for(let n=t.length-1;n>=0;n--)r[n]=255&(t[n]^e[n]);return r}function s(t){return t^i(t,9)^i(t,17)}function a(t){let e=8*t.length,o=e%512;o=o>=448?512-o%448-1:448-o-1;const a=new Array((o-7)/8),u=new Array(8);for(let t=0,e=a.length;t<e;t++)a[t]=0;for(let t=0,e=u.length;t<e;t++)u[t]=0;e=e.toString(2);for(let t=7;t>=0;t--)if(e.length>8){const r=e.length-8;u[t]=parseInt(e.substr(r),2),e=e.substr(0,r)}else e.length>0&&(u[t]=parseInt(e,2),e="");const c=new Uint8Array([...t,128,...a,...u]),f=new DataView(c.buffer,0),l=c.length/64,h=new Uint32Array([1937774191,1226093241,388252375,3666478592,2842636476,372324522,3817729613,2969243214]);for(let t=0;t<l;t++){r.fill(0),n.fill(0);const e=16*t;for(let t=0;t<16;t++)r[t]=f.getUint32(4*(e+t),!1);for(let t=16;t<68;t++)r[t]=(d=r[t-16]^r[t-9]^i(r[t-3],15))^i(d,15)^i(d,23)^i(r[t-13],7)^r[t-6];for(let t=0;t<64;t++)n[t]=r[t]^r[t+4];const o=2043430169,a=2055708042;let u,c,l,p,y,g=h[0],v=h[1],m=h[2],_=h[3],b=h[4],w=h[5],S=h[6],E=h[7];for(let t=0;t<64;t++)y=t>=0&&t<=15?o:a,u=i(i(g,12)+b+i(y,t),7),c=u^i(g,12),l=(t>=0&&t<=15?g^v^m:g&v|g&m|v&m)+_+c+n[t],p=(t>=0&&t<=15?b^w^S:b&w|~b&S)+E+u+r[t],_=m,m=i(v,9),v=g,g=l,E=S,S=i(w,19),w=b,b=s(p);h[0]^=g,h[1]^=v,h[2]^=m,h[3]^=_,h[4]^=b,h[5]^=w,h[6]^=S,h[7]^=E}var d;const p=[];for(let t=0,e=h.length;t<e;t++){const e=h[t];p.push((4278190080&e)>>>24,(16711680&e)>>>16,(65280&e)>>>8,255&e)}return p}const u=new Uint8Array(64),c=new Uint8Array(64);for(let t=0;t<64;t++)u[t]=54,c[t]=92;t.exports={sm3:a,hmac:function(t,e){for(e.length>64&&(e=a(e));e.length<64;)e.push(0);const r=o(e,u),n=o(e,c),i=a([...r,...t]);return a([...n,...i])}}},function(t,e,r){"use strict";const n=t=>!("object"!=typeof t||null===t||t instanceof RegExp||t instanceof Error||t instanceof Date);t.exports=function t(e,r,i,o){if(i=Object.assign({deep:!1,target:{}},i),(o=o||new WeakMap).has(e))return o.get(e);o.set(e,i.target);const s=i.target;delete i.target;for(const a of Object.keys(e)){const u=e[a],c=r(a,u,e);let f=c[1];i.deep&&n(f)&&(f=Array.isArray(f)?f.map(e=>n(e)?t(e,r,i,o):e):t(f,r,i,o)),s[c[0]]=f}return s}},function(t,e,r){"use strict";var n=r(20),i=r(57);t.exports=function(t,e){return e=Object.assign({deep:!0},e),n(t,(function(t,e){return[i(t),e]}),e)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(2),i=r(60),o=r(61),s=r(21),a=r(62),u=r(87),c={RSA:"RSA-SHA1",RSA2:"RSA-SHA256"};function f(t){return{iv:a.enc.Hex.parse(u.padEnd("",32,"0")),key:a.enc.Base64.parse(t)}}e.ALIPAY_ALGORITHM_MAPPING=c,e.aesDecrypt=function(t,e){const{iv:r,key:n}=f(e),i=a.AES.decrypt(t,n,{iv:r});return JSON.parse(i.toString(a.enc.Utf8))},e.sign=function(t,e={},r){let l=Object.assign({method:t,appId:r.appId,charset:r.charset,version:r.version,signType:r.signType,timestamp:i().format("YYYY-MM-DD HH:mm:ss")},u.omit(e,["bizContent","needEncrypt"]));r.appCertSn&&r.alipayRootCertSn&&(l=Object.assign({appCertSn:r.appCertSn,alipayRootCertSn:r.alipayRootCertSn},l)),r.wsServiceUrl&&(l.wsServiceUrl=r.wsServiceUrl);const h=e.bizContent;if(h)if(e.needEncrypt){if(!r.encryptKey)throw new Error("请设置encryptKey参数");l.encryptType="AES",l.bizContent=function(t,e){const{iv:r,key:n}=f(e);return a.AES.encrypt(JSON.stringify(t),n,{iv:r}).toString()}(s(h),r.encryptKey)}else l.bizContent=JSON.stringify(s(h));const d=s(l),p=Object.keys(d).sort().map(t=>{let e=d[t];return"[object String]"!==Array.prototype.toString.call(e)&&(e=JSON.stringify(e)),`${t}=${o.encode(e,r.charset)}`}).join("&"),y=n.createSign(c[r.signType]).update(p,"utf8").sign(r.privateKey,"base64");return Object.assign(d,{sign:y})}},function(t,e,r){var n;t.exports=(n=r(0),function(t){var e=n,r=e.lib,i=r.WordArray,o=r.Hasher,s=e.algo,a=[],u=[];!function(){function e(e){for(var r=t.sqrt(e),n=2;n<=r;n++)if(!(e%n))return!1;return!0}function r(t){return 4294967296*(t-(0|t))|0}for(var n=2,i=0;i<64;)e(n)&&(i<8&&(a[i]=r(t.pow(n,.5))),u[i]=r(t.pow(n,1/3)),i++),n++}();var c=[],f=s.SHA256=o.extend({_doReset:function(){this._hash=new i.init(a.slice(0))},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],f=r[5],l=r[6],h=r[7],d=0;d<64;d++){if(d<16)c[d]=0|t[e+d];else{var p=c[d-15],y=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,g=c[d-2],v=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;c[d]=y+c[d-7]+v+c[d-16]}var m=n&i^n&o^i&o,_=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),b=h+((a<<26|a>>>6)^(a<<21|a>>>11)^(a<<7|a>>>25))+(a&f^~a&l)+u[d]+c[d];h=l,l=f,f=a,a=s+b|0,s=o,o=i,i=n,n=b+(_+m)|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+s|0,r[4]=r[4]+a|0,r[5]=r[5]+f|0,r[6]=r[6]+l|0,r[7]=r[7]+h|0},_doFinalize:function(){var e=this._data,r=e.words,n=8*this._nDataBytes,i=8*e.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=t.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,e.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA256=o._createHelper(f),e.HmacSHA256=o._createHmacHelper(f)}(Math),n.SHA256)},function(t,e,r){var n;t.exports=(n=r(0),r(11),function(){var t=n,e=t.lib.Hasher,r=t.x64,i=r.Word,o=r.WordArray,s=t.algo;function a(){return i.create.apply(i,arguments)}var u=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],c=[];!function(){for(var t=0;t<80;t++)c[t]=a()}();var f=s.SHA512=e.extend({_doReset:function(){this._hash=new o.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],s=r[3],a=r[4],f=r[5],l=r[6],h=r[7],d=n.high,p=n.low,y=i.high,g=i.low,v=o.high,m=o.low,_=s.high,b=s.low,w=a.high,S=a.low,E=f.high,I=f.low,A=l.high,N=l.low,T=h.high,C=h.low,R=d,P=p,x=y,O=g,B=v,D=m,F=_,k=b,U=w,M=S,j=E,K=I,L=A,q=N,z=T,V=C,$=0;$<80;$++){var H,G,W=c[$];if($<16)G=W.high=0|t[e+2*$],H=W.low=0|t[e+2*$+1];else{var J=c[$-15],Y=J.high,Z=J.low,X=(Y>>>1|Z<<31)^(Y>>>8|Z<<24)^Y>>>7,Q=(Z>>>1|Y<<31)^(Z>>>8|Y<<24)^(Z>>>7|Y<<25),tt=c[$-2],et=tt.high,rt=tt.low,nt=(et>>>19|rt<<13)^(et<<3|rt>>>29)^et>>>6,it=(rt>>>19|et<<13)^(rt<<3|et>>>29)^(rt>>>6|et<<26),ot=c[$-7],st=ot.high,at=ot.low,ut=c[$-16],ct=ut.high,ft=ut.low;G=(G=(G=X+st+((H=Q+at)>>>0<Q>>>0?1:0))+nt+((H+=it)>>>0<it>>>0?1:0))+ct+((H+=ft)>>>0<ft>>>0?1:0),W.high=G,W.low=H}var lt,ht=U&j^~U&L,dt=M&K^~M&q,pt=R&x^R&B^x&B,yt=P&O^P&D^O&D,gt=(R>>>28|P<<4)^(R<<30|P>>>2)^(R<<25|P>>>7),vt=(P>>>28|R<<4)^(P<<30|R>>>2)^(P<<25|R>>>7),mt=(U>>>14|M<<18)^(U>>>18|M<<14)^(U<<23|M>>>9),_t=(M>>>14|U<<18)^(M>>>18|U<<14)^(M<<23|U>>>9),bt=u[$],wt=bt.high,St=bt.low,Et=z+mt+((lt=V+_t)>>>0<V>>>0?1:0),It=vt+yt;z=L,V=q,L=j,q=K,j=U,K=M,U=F+(Et=(Et=(Et=Et+ht+((lt+=dt)>>>0<dt>>>0?1:0))+wt+((lt+=St)>>>0<St>>>0?1:0))+G+((lt+=H)>>>0<H>>>0?1:0))+((M=k+lt|0)>>>0<k>>>0?1:0)|0,F=B,k=D,B=x,D=O,x=R,O=P,R=Et+(gt+pt+(It>>>0<vt>>>0?1:0))+((P=lt+It|0)>>>0<lt>>>0?1:0)|0}p=n.low=p+P,n.high=d+R+(p>>>0<P>>>0?1:0),g=i.low=g+O,i.high=y+x+(g>>>0<O>>>0?1:0),m=o.low=m+D,o.high=v+B+(m>>>0<D>>>0?1:0),b=s.low=b+k,s.high=_+F+(b>>>0<k>>>0?1:0),S=a.low=S+M,a.high=w+U+(S>>>0<M>>>0?1:0),I=f.low=I+K,f.high=E+j+(I>>>0<K>>>0?1:0),N=l.low=N+q,l.high=A+L+(N>>>0<q>>>0?1:0),C=h.low=C+V,h.high=T+z+(C>>>0<V>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;return e[n>>>5]|=128<<24-n%32,e[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),e[31+(n+128>>>10<<5)]=r,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});t.SHA512=e._createHelper(f),t.HmacSHA512=e._createHmacHelper(f)}(),n.SHA512)},function(t,e,r){"use strict";var n=l(r(26)),i=l(r(45)),o=l(r(93)),s=l(r(94)),a=l(r(98)),u=l(r(102)),c=l(r(104)),f=r(3);function l(t){return t&&t.__esModule?t:{default:t}}t.exports={initWeixin:(t={})=>(t.clientType=t.clientType||__ctx__.PLATFORM,(0,f.createApi)(n.default,t)),initAlipay:(t={})=>(t.clientType=t.clientType||__ctx__.PLATFORM,(0,f.createApi)(i.default,t)),initAppleIapPayment:(t={})=>(t.clientType=t.clientType||__ctx__.PLATFORM,(0,f.createApi)(o.default,t)),initWeixinV3:(t={})=>(t.clientType=t.clientType||__ctx__.PLATFORM,(0,f.createApi)(s.default,t)),initWeixinVirtualPayment:(t={})=>(t.clientType=t.clientType||__ctx__.PLATFORM,(0,f.createApi)(a.default,t)),initDouyinPayment:(t={})=>(t.clientType=t.clientType||__ctx__.PLATFORM,(0,f.createApi)(c.default,t)),initHuawei:(t={})=>(t.clientType=t.clientType||__ctx__.PLATFORM,(0,f.createApi)(u.default,t)),HuaweiPaymentBizType:r(108)}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=s(r(27)),i=s(r(44)),o=r(3);function s(t){return t&&t.__esModule?t:{default:t}}e.default=class{constructor(t={}){if(!t.appId)throw new Error("appId required");if(!t.mchId)throw new Error("mchId required");if(!t.key)throw new Error("key required");if(32!==t.key.length)throw new Error("微信支付v2的key的长度必须是32位，请检查");t.signType=t.signType||"MD5",this.options=Object.assign({},t),this._protocols=i.default,this.baseUrl="https://api.mch.weixin.qq.com",this.paths={unifiedOrder:"/pay/unifiedorder",orderQuery:"/pay/orderquery",closeOrder:"/pay/closeorder",refund:"/secapi/pay/refund",refundQuery:"/pay/refundquery",downloadBill:"/pay/downloadbill",downloadFundflow:"/pay/downloadfundflow",getsignkey:"/pay/getsignkey",codepay:"/pay/micropay"}}_getSign(t,e){const r=n.default.getSignStr(t)+"&key="+this.options.key;switch(e){case"MD5":return n.default.md5(r).toUpperCase();case"HMAC-SHA256":return n.default.sha256(r,this.options.key).toUpperCase();default:throw new Error("signType Error")}}_normalizeResult(t,e){return t.returnMsg=t.returnMsg||("SUCCESS"===t.returnCode?"ok":"fail"),t.errMsg=`payment.${e} ${t.returnMsg.toLowerCase()}`,t}_parse(t,e,r){const i=n.default.parseXML(t);if("FAIL"===i.return_code)throw new Error(""+i.return_msg);if("getSandboxKey"!==e){if(i.appid&&i.appid!==this.options.appId)throw new Error("appId不匹配");if(i.mch_id&&i.mch_id!==this.options.mchId)throw new Error("mchId不匹配");if(i.sign&&i.sign!==this._getSign(i,r))throw new Error("返回结果签名错误");i.app_id=i.appid,delete i.appid}if("FAIL"===i.result_code&&"codepay"!==e)throw new Error(`${i.err_code} ${i.err_code_des}`);return this._normalizeResult((0,o.snake2camelJson)(i),e)}_parseBill(t,e){const r={};if(n.default.isXML(t)){const e=n.default.parseXML(t);if("FAIL"===e.return_code)throw new Error(""+e.return_msg);if("FAIL"===e.result_code)throw new Error(`${e.err_code} ${e.err_code_des}`)}else r.returnCode="SUCCESS",r.content=t;return this._normalizeResult(r,e)}_getPublicParams(){const{appId:t,mchId:e,subAppId:r,subMchId:i}=this.options;return i?{appid:t,mchId:e,nonceStr:n.default.getNonceStr(),subAppid:r,subMchId:i}:{appid:t,mchId:e,nonceStr:n.default.getNonceStr()}}async _requestWxpay(t,e,r=!1){if(r&&!this.options.pfx)throw new Error("此接口需要微信支付证书（请传入pfx字段）");"getSandboxKey"!==e&&await this._initSandbox();const i=t.signType||this.options.signType;(t=(0,o.camel2snakeJson)(t)).sign=this._getSign(t,i);const s={method:"POST",dataType:"text",data:n.default.buildXML(t),timeout:this.options.timeout};r&&(s.pfx=this.options.pfx,s.passphrase=this.options.mchId);const{status:a,data:u}=await uniCloud.httpclient.request(this.options.sandbox?`${this.baseUrl}/sandboxnew${this.paths[e]}`:`${this.baseUrl}${this.paths[e]}`,s);if(200!==a)throw new Error("request fail");return-1!==["downloadBill","downloadFundflow"].indexOf(e)?this._parseBill(u,e):this._parse(u,e,i)}async getSandboxKey(){const t={mchId:this.options.mchId,nonceStr:n.default.getNonceStr()};return await this._requestWxpay(t,"getSandboxKey")}async _initSandbox(){this.options.sandbox&&!this.options.sandboxKey&&(this.options.key=this.options.sandboxKey=await this.getSandboxKey().sandbox_signkey)}async unifiedOrder(t){const e=this._getTradeType(t),r=this._getPublicParams();r.subAppid&&t.openid&&(t.sub_openid=t.openid),("JSAPI"!==e||r.subAppid)&&delete t.openid;const n={...t,...r,spbillCreateIp:t.spbillCreateIp||"127.0.0.1",tradeType:e};return await this._requestWxpay(n,"unifiedOrder")}_getPayParamsByPrepayId(t,e,r){let i;switch(r){case"APP":i={appid:this.options.subAppId?this.options.subAppId:this.options.appId,noncestr:n.default.getNonceStr(),package:"Sign=WXPay",partnerid:this.options.mchId,prepayid:t,timestamp:""+(Date.now()/1e3|0)},i.sign=this._getSign(i,e);break;case"JSAPI":default:{const r=""+(Date.now()/1e3|0);i={appId:this.options.subAppId?this.options.subAppId:this.options.appId,nonceStr:n.default.getNonceStr(),package:"prepay_id="+t,timeStamp:r},i.signType=e,i.paySign=this._getSign(i,e),i.timestamp=r;break}}return i}_getTradeType(t){let e;if(t.tradeType)e=t.tradeType;else switch(this.options.clientType){case"app-plus":case"app":e="APP";break;case"mp-weixin":default:e="JSAPI"}return e}async getOrderInfo(t){const e=this._getTradeType(t);"JSAPI"!==e&&delete t.openid,t.tradeType=e;const r=await this.unifiedOrder(t);if("NATIVE"===e||"MWEB"===e)return r;if(!r.prepayId)throw new Error(r.errMsg||"获取prepayId失败");return this._getPayParamsByPrepayId(r.prepayId,t.signType||this.options.signType,e)}async orderQuery(t){const e={...t,...this._getPublicParams()};return await this._requestWxpay(e,"orderQuery")}async closeOrder(t){const e={...t,...this._getPublicParams()};return await this._requestWxpay(e,"closeOrder")}async refund(t){const e={...t,...this._getPublicParams()};return await this._requestWxpay(e,"refund",!0)}async refundQuery(t){const e={...t,...this._getPublicParams()};return await this._requestWxpay(e,"refundQuery")}async downloadBill(t){const e={...t,...this._getPublicParams(),billType:t.billType||"ALL"};return await this._requestWxpay(e,"downloadBill")}async downloadFundflow(t){const e={...t,...this._getPublicParams(),signType:t.signType||"HMAC-SHA256",accountType:t.accountType||"Basic"};return await this._requestWxpay(e,"downloadFundflow",!0)}async codepay(t){const e={...t,...this._getPublicParams()};return await this._requestWxpay(e,"codepay")}_getNotifyData(t){let e=t.body;return t.isBase64Encoded&&(e=Buffer.from(e,"base64").toString("utf-8")),n.default.parseXML(e)}_verifyNotify(t,e){const r=this._getNotifyData(t);if("FAIL"===r.return_code)throw new Error(`${r.return_code} ${r.return_msg}`);if(r.appid!==this.options.appId)throw new Error("appId不匹配");if(r.mch_id!==this.options.mchId)throw new Error("mchId不匹配");if(e&&r.sign!==this._getSign(r,this.options.signType))throw new Error("通知验签未通过");const n=(0,o.snake2camelJson)(r);return n.appId=n.appid,delete n.appid,n}verifyPaymentNotify(t){return"payment"===this.checkNotifyType(t)&&this._verifyNotify(t,!0)}verifyRefundNotify(t){if("refund"!==this.checkNotifyType(t))return!1;const e=this._verifyNotify(t,!1),r=(0,o.snake2camelJson)(n.default.parseXML(n.default.decryptData(e.reqInfo,n.default.md5(this.options.key))));return Object.assign(e,r),delete e.reqInfo,e}checkNotifyType(t){const e=this._getNotifyData(t);return"total_fee"in e?"payment":"req_info"in e?"refund":"payment"}},t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n},o=r(3);var s={decryptData:function(t,e,r=""){const n=i.default.createDecipheriv("aes-256-ecb",e,r);n.setAutoPadding(!0);let o=n.update(t,"base64","utf8");return o+=n.final("utf8"),o},md5:function(t,e="utf8"){return i.default.createHash("md5").update(t,e).digest("hex")},sha256:function(t,e,r="utf8"){return i.default.createHmac("sha256",e).update(t,r).digest("hex")},getSignStr:function(t){return Object.keys(t).filter(e=>"sign"!==e&&void 0!==t[e]&&""!==t[e]).sort().map(e=>e+"="+((0,o.isPlainObject)(t[e])?JSON.stringify(t[e]):t[e])).join("&")},getNonceStr:function(t=16){let e="";for(;e.length<t;)e+=Math.random().toString(32).substring(2);return e.substring(0,t)},buildXML:function(t,e="xml"){const r=[];return Object.keys(t).map(e=>{void 0!==t[e]&&((0,o.isPlainObject)(t[e])?r.push(`<${e}><![CDATA[${JSON.stringify(t[e])}]]></${e}>`):r.push(`<${e}><![CDATA[${t[e]}]]></${e}>`))}),`<${e}>${r.join("")}</${e}>`},parseXML:function(t){const e=/<(?:xml|root).*?>([\s|\S]*)<\/(?:xml|root)>/.exec(t)[1],r={},n=/<(.*?)>(?:<!\[CDATA\[){0,1}(.*?)(?:\]\]>){0,1}<\/.*?>/g;let i=null;for(;i=n.exec(e);)r[i[1]]=i[2];return r},isXML:function(t){return/^(<\?xml.*\?>)?(\r?\n)*<xml>(.|\r?\n)*<\/xml>$/i.test(t.trim())}};e.default=s,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=class{constructor(){this._boundary="------FormDataBaseBoundary"+Math.random().toString(36).substring(2),this.dataList=[]}_addData(t){const e=this.dataList[this.dataList.length-1];"string"==typeof t&&"string"==typeof e?this.dataList[this.dataList.length-1]=e+"\r\n"+t:this.dataList.push(t)}append(t,e,r){this._addData("--"+this._boundary);let n=`Content-Disposition: form-data; name="${t}"`;switch(Buffer.isBuffer(e)){case!0:if(!r.filename||!r.contentType)throw new Error("filename and contentType required");n+=`; filename="${r.filename}"`,this._addData(n),this._addData("Content-Type: "+r.contentType),this._addData(""),this._addData(e);break;default:this._addData(""),this._addData(e)}}getHeaders(t){const e={"Content-Type":"multipart/form-data; boundary="+this._boundary};return Object.assign(e,t)}getBuffer(){let t=Buffer.alloc(0);return this.dataList.forEach(e=>{t=Buffer.isBuffer(e)?Buffer.concat([t,e]):Buffer.concat([t,Buffer.from(""+e)]),t=Buffer.concat([t,Buffer.from("\r\n")])}),t=Buffer.concat([t,Buffer.from("--"+this._boundary+"--")]),t}},t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;class n extends Error{constructor(t){super(t.message),this.errMsg=t.message||"",this.errCode=t.code||"",Object.defineProperties(this,{message:{get(){return`errCode: ${this.errCode} | errMsg: `+this.errMsg},set(t){this.errMsg=t}}})}}e.default=n,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.createApi=function(t,e){const r=new t(e);return new Proxy(r,{get:function(t,e){if("function"==typeof t[e]&&0!==e.indexOf("_")&&t._protocols&&t._protocols[e]){const r=t._protocols[e];return async function(n){n=i(n,r.args);let o=await t[e](n);return o=i(o,r.returnValue),o}}return t[e]}})};var n=r(7);function i(t={},e){if(!e||!t)return t;const r=["_pre","_purify","_post"];e._pre&&(t=e._pre(t));let i={shouldDelete:new Set([])};if(e._purify){const t=e._purify;for(const e in t)t[e]=new Set(t[e]);i=Object.assign(i,t)}if((0,n.isPlainObject)(e))for(const o in e){const s=e[o];if((0,n.isFn)(s)&&-1===r.indexOf(o))t[o]=s(t);else if("string"==typeof s&&/\./g.test(s)){const e=s.split(".");t[o]=e.reduce((t,e)=>t[e],t)}else if("string"==typeof s&&/\./g.test(o)){const e=o.split(".");let r=t;for(const[n,i]of e.entries())r[i]||(r[i]=n+1>=e.length?t[s]:{}),r=r[i]}else"string"==typeof s&&-1===r.indexOf(o)&&(t[o]=t[s]);o!==s&&"string"==typeof s&&i.shouldDelete.add(s)}else(0,n.isFn)(e)&&(t=e(t));if(i.shouldDelete)for(const e of i.shouldDelete)/\./g.test(e)?(0,n.deleteObjectKey)(e.split("."),t):delete t[e];return e._post&&(t=e._post(t)),t}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.decodeBase64=function(t){return Buffer.from(t,"base64").toString("utf-8")},e.decryptCiphertext=function(t,e,r,n){const o=Buffer.from(t,"base64"),s=i.default.createDecipheriv("aes-256-gcm",e,r);s.setAuthTag(o.slice(-16)),s.setAAD(Buffer.from(n));return Buffer.concat([s.update(o.slice(0,-16)),s.final()]).toString()},e.formatKey=function(t,e){return`-----BEGIN ${e}-----\n${t}\n-----END ${e}-----`},e.getNonceStr=function(t=16){let e="";for(;e.length<t;)e+=Math.random().toString(32).substring(2);return e.substring(0,t)},e.getQueryStr=function t(e,r=!1){let n=Object.keys(e).filter(t=>null!==e[t]&&void 0!==e[t]);if(n.length<=0)return"";r&&n.sort();return n=n.map(n=>{const i=(0,s.isPlainObject)(e[n])?t(e[n],r):e[n];if(null!=i)return n+"="+i}).filter(Boolean),n.join("&")},e.loadCertFromContent=function(t){"string"==typeof t&&(t=Buffer.from(t));return o.Certificate.fromPEM(t)},e.loadCertFromPath=function(t){return o.Certificate.fromPEM(n.default.readFileSync(t))},e.loadPrivateKeyFromContent=function(t){"string"==typeof t&&(t=Buffer.from(t));return o.PrivateKey.fromPEM(t)},e.loadPrivateKeyFromPath=function(t){return o.PrivateKey.fromPEM(n.default.readFileSync(t))},e.md5=function(t,e="utf8"){return i.default.createHash("md5").update(t,e).digest("hex")},e.rsaPrivateKeyDecryptData=function(t,e){"string"==typeof t&&(t=Buffer.from(t));return i.default.privateDecrypt({key:e,padding:i.default.constants.RSA_PKCS1_OAEP_PADDING},t).toString()},e.rsaPublicKeyEncryptData=function(t,e){return i.default.publicEncrypt({key:e,padding:i.default.constants.RSA_PKCS1_OAEP_PADDING},Buffer.from(t))},e.rsaPublicKeyVerifySign=function(t,e,r){return i.default.createVerify("RSA-SHA256").update(e,"utf8").verify(t,r,"base64")},e.sha256=function(t,e,r="utf8"){return i.default.createHmac("sha256",e).update(t,r).digest("hex")},e.signSha256WithRsaPSS=function(t,e,r="base64"){return i.default.createSign("RSA-SHA256").update(Buffer.from(e),"utf8").sign({key:t,padding:i.default.constants.RSA_PKCS1_PSS_PADDING,saltLength:32}).toString(r)},e.sm2VerifySign=function(t,e,r){return a.sm2.doVerifySignature(t,Buffer.from(e,"base64").toString("hex"),r,{hash:!0,der:!0})};var n=u(r(8)),i=u(r(2)),o=r(12),s=r(7),a=r(37);function u(t){return t&&t.__esModule?t:{default:t}}},function(t,e){t.exports=require("net")},function(t,e,r){!function(t){"use strict";var e=function(t){var e,r=new Float64Array(16);if(t)for(e=0;e<t.length;e++)r[e]=t[e];return r},n=function(){throw new Error("no PRNG")},i=new Uint8Array(16),o=new Uint8Array(32);o[0]=9;var s=e(),a=e([1]),u=e([56129,1]),c=e([30883,4953,19914,30187,55467,16705,2637,112,59544,30585,16505,36039,65139,11119,27886,20995]),f=e([61785,9906,39828,60374,45398,33411,5274,224,53552,61171,33010,6542,64743,22239,55772,9222]),l=e([54554,36645,11616,51542,42930,38181,51040,26924,56412,64982,57905,49316,21502,52590,14035,8553]),h=e([26200,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214,26214]),d=e([41136,18958,6951,50414,58488,44335,6150,12099,55207,15867,153,11085,57099,20417,9344,11139]);function p(t,e,r,n){t[e]=r>>24&255,t[e+1]=r>>16&255,t[e+2]=r>>8&255,t[e+3]=255&r,t[e+4]=n>>24&255,t[e+5]=n>>16&255,t[e+6]=n>>8&255,t[e+7]=255&n}function y(t,e,r,n,i){var o,s=0;for(o=0;o<i;o++)s|=t[e+o]^r[n+o];return(1&s-1>>>8)-1}function g(t,e,r,n){return y(t,e,r,n,16)}function v(t,e,r,n){return y(t,e,r,n,32)}function m(t,e,r,n){!function(t,e,r,n){for(var i,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,a=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,u=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,c=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,f=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,h=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,d=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,p=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,y=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,g=255&r[16]|(255&r[17])<<8|(255&r[18])<<16|(255&r[19])<<24,v=255&r[20]|(255&r[21])<<8|(255&r[22])<<16|(255&r[23])<<24,m=255&r[24]|(255&r[25])<<8|(255&r[26])<<16|(255&r[27])<<24,_=255&r[28]|(255&r[29])<<8|(255&r[30])<<16|(255&r[31])<<24,b=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,w=o,S=s,E=a,I=u,A=c,N=f,T=l,C=h,R=d,P=p,x=y,O=g,B=v,D=m,F=_,k=b,U=0;U<20;U+=2)w^=(i=(B^=(i=(R^=(i=(A^=(i=w+B|0)<<7|i>>>25)+w|0)<<9|i>>>23)+A|0)<<13|i>>>19)+R|0)<<18|i>>>14,N^=(i=(S^=(i=(D^=(i=(P^=(i=N+S|0)<<7|i>>>25)+N|0)<<9|i>>>23)+P|0)<<13|i>>>19)+D|0)<<18|i>>>14,x^=(i=(T^=(i=(E^=(i=(F^=(i=x+T|0)<<7|i>>>25)+x|0)<<9|i>>>23)+F|0)<<13|i>>>19)+E|0)<<18|i>>>14,k^=(i=(O^=(i=(C^=(i=(I^=(i=k+O|0)<<7|i>>>25)+k|0)<<9|i>>>23)+I|0)<<13|i>>>19)+C|0)<<18|i>>>14,w^=(i=(I^=(i=(E^=(i=(S^=(i=w+I|0)<<7|i>>>25)+w|0)<<9|i>>>23)+S|0)<<13|i>>>19)+E|0)<<18|i>>>14,N^=(i=(A^=(i=(C^=(i=(T^=(i=N+A|0)<<7|i>>>25)+N|0)<<9|i>>>23)+T|0)<<13|i>>>19)+C|0)<<18|i>>>14,x^=(i=(P^=(i=(R^=(i=(O^=(i=x+P|0)<<7|i>>>25)+x|0)<<9|i>>>23)+O|0)<<13|i>>>19)+R|0)<<18|i>>>14,k^=(i=(F^=(i=(D^=(i=(B^=(i=k+F|0)<<7|i>>>25)+k|0)<<9|i>>>23)+B|0)<<13|i>>>19)+D|0)<<18|i>>>14;w=w+o|0,S=S+s|0,E=E+a|0,I=I+u|0,A=A+c|0,N=N+f|0,T=T+l|0,C=C+h|0,R=R+d|0,P=P+p|0,x=x+y|0,O=O+g|0,B=B+v|0,D=D+m|0,F=F+_|0,k=k+b|0,t[0]=w>>>0&255,t[1]=w>>>8&255,t[2]=w>>>16&255,t[3]=w>>>24&255,t[4]=S>>>0&255,t[5]=S>>>8&255,t[6]=S>>>16&255,t[7]=S>>>24&255,t[8]=E>>>0&255,t[9]=E>>>8&255,t[10]=E>>>16&255,t[11]=E>>>24&255,t[12]=I>>>0&255,t[13]=I>>>8&255,t[14]=I>>>16&255,t[15]=I>>>24&255,t[16]=A>>>0&255,t[17]=A>>>8&255,t[18]=A>>>16&255,t[19]=A>>>24&255,t[20]=N>>>0&255,t[21]=N>>>8&255,t[22]=N>>>16&255,t[23]=N>>>24&255,t[24]=T>>>0&255,t[25]=T>>>8&255,t[26]=T>>>16&255,t[27]=T>>>24&255,t[28]=C>>>0&255,t[29]=C>>>8&255,t[30]=C>>>16&255,t[31]=C>>>24&255,t[32]=R>>>0&255,t[33]=R>>>8&255,t[34]=R>>>16&255,t[35]=R>>>24&255,t[36]=P>>>0&255,t[37]=P>>>8&255,t[38]=P>>>16&255,t[39]=P>>>24&255,t[40]=x>>>0&255,t[41]=x>>>8&255,t[42]=x>>>16&255,t[43]=x>>>24&255,t[44]=O>>>0&255,t[45]=O>>>8&255,t[46]=O>>>16&255,t[47]=O>>>24&255,t[48]=B>>>0&255,t[49]=B>>>8&255,t[50]=B>>>16&255,t[51]=B>>>24&255,t[52]=D>>>0&255,t[53]=D>>>8&255,t[54]=D>>>16&255,t[55]=D>>>24&255,t[56]=F>>>0&255,t[57]=F>>>8&255,t[58]=F>>>16&255,t[59]=F>>>24&255,t[60]=k>>>0&255,t[61]=k>>>8&255,t[62]=k>>>16&255,t[63]=k>>>24&255}(t,e,r,n)}function _(t,e,r,n){!function(t,e,r,n){for(var i,o=255&n[0]|(255&n[1])<<8|(255&n[2])<<16|(255&n[3])<<24,s=255&r[0]|(255&r[1])<<8|(255&r[2])<<16|(255&r[3])<<24,a=255&r[4]|(255&r[5])<<8|(255&r[6])<<16|(255&r[7])<<24,u=255&r[8]|(255&r[9])<<8|(255&r[10])<<16|(255&r[11])<<24,c=255&r[12]|(255&r[13])<<8|(255&r[14])<<16|(255&r[15])<<24,f=255&n[4]|(255&n[5])<<8|(255&n[6])<<16|(255&n[7])<<24,l=255&e[0]|(255&e[1])<<8|(255&e[2])<<16|(255&e[3])<<24,h=255&e[4]|(255&e[5])<<8|(255&e[6])<<16|(255&e[7])<<24,d=255&e[8]|(255&e[9])<<8|(255&e[10])<<16|(255&e[11])<<24,p=255&e[12]|(255&e[13])<<8|(255&e[14])<<16|(255&e[15])<<24,y=255&n[8]|(255&n[9])<<8|(255&n[10])<<16|(255&n[11])<<24,g=255&r[16]|(255&r[17])<<8|(255&r[18])<<16|(255&r[19])<<24,v=255&r[20]|(255&r[21])<<8|(255&r[22])<<16|(255&r[23])<<24,m=255&r[24]|(255&r[25])<<8|(255&r[26])<<16|(255&r[27])<<24,_=255&r[28]|(255&r[29])<<8|(255&r[30])<<16|(255&r[31])<<24,b=255&n[12]|(255&n[13])<<8|(255&n[14])<<16|(255&n[15])<<24,w=0;w<20;w+=2)o^=(i=(v^=(i=(d^=(i=(c^=(i=o+v|0)<<7|i>>>25)+o|0)<<9|i>>>23)+c|0)<<13|i>>>19)+d|0)<<18|i>>>14,f^=(i=(s^=(i=(m^=(i=(p^=(i=f+s|0)<<7|i>>>25)+f|0)<<9|i>>>23)+p|0)<<13|i>>>19)+m|0)<<18|i>>>14,y^=(i=(l^=(i=(a^=(i=(_^=(i=y+l|0)<<7|i>>>25)+y|0)<<9|i>>>23)+_|0)<<13|i>>>19)+a|0)<<18|i>>>14,b^=(i=(g^=(i=(h^=(i=(u^=(i=b+g|0)<<7|i>>>25)+b|0)<<9|i>>>23)+u|0)<<13|i>>>19)+h|0)<<18|i>>>14,o^=(i=(u^=(i=(a^=(i=(s^=(i=o+u|0)<<7|i>>>25)+o|0)<<9|i>>>23)+s|0)<<13|i>>>19)+a|0)<<18|i>>>14,f^=(i=(c^=(i=(h^=(i=(l^=(i=f+c|0)<<7|i>>>25)+f|0)<<9|i>>>23)+l|0)<<13|i>>>19)+h|0)<<18|i>>>14,y^=(i=(p^=(i=(d^=(i=(g^=(i=y+p|0)<<7|i>>>25)+y|0)<<9|i>>>23)+g|0)<<13|i>>>19)+d|0)<<18|i>>>14,b^=(i=(_^=(i=(m^=(i=(v^=(i=b+_|0)<<7|i>>>25)+b|0)<<9|i>>>23)+v|0)<<13|i>>>19)+m|0)<<18|i>>>14;t[0]=o>>>0&255,t[1]=o>>>8&255,t[2]=o>>>16&255,t[3]=o>>>24&255,t[4]=f>>>0&255,t[5]=f>>>8&255,t[6]=f>>>16&255,t[7]=f>>>24&255,t[8]=y>>>0&255,t[9]=y>>>8&255,t[10]=y>>>16&255,t[11]=y>>>24&255,t[12]=b>>>0&255,t[13]=b>>>8&255,t[14]=b>>>16&255,t[15]=b>>>24&255,t[16]=l>>>0&255,t[17]=l>>>8&255,t[18]=l>>>16&255,t[19]=l>>>24&255,t[20]=h>>>0&255,t[21]=h>>>8&255,t[22]=h>>>16&255,t[23]=h>>>24&255,t[24]=d>>>0&255,t[25]=d>>>8&255,t[26]=d>>>16&255,t[27]=d>>>24&255,t[28]=p>>>0&255,t[29]=p>>>8&255,t[30]=p>>>16&255,t[31]=p>>>24&255}(t,e,r,n)}var b=new Uint8Array([101,120,112,97,110,100,32,51,50,45,98,121,116,101,32,107]);function w(t,e,r,n,i,o,s){var a,u,c=new Uint8Array(16),f=new Uint8Array(64);for(u=0;u<16;u++)c[u]=0;for(u=0;u<8;u++)c[u]=o[u];for(;i>=64;){for(m(f,c,s,b),u=0;u<64;u++)t[e+u]=r[n+u]^f[u];for(a=1,u=8;u<16;u++)a=a+(255&c[u])|0,c[u]=255&a,a>>>=8;i-=64,e+=64,n+=64}if(i>0)for(m(f,c,s,b),u=0;u<i;u++)t[e+u]=r[n+u]^f[u];return 0}function S(t,e,r,n,i){var o,s,a=new Uint8Array(16),u=new Uint8Array(64);for(s=0;s<16;s++)a[s]=0;for(s=0;s<8;s++)a[s]=n[s];for(;r>=64;){for(m(u,a,i,b),s=0;s<64;s++)t[e+s]=u[s];for(o=1,s=8;s<16;s++)o=o+(255&a[s])|0,a[s]=255&o,o>>>=8;r-=64,e+=64}if(r>0)for(m(u,a,i,b),s=0;s<r;s++)t[e+s]=u[s];return 0}function E(t,e,r,n,i){var o=new Uint8Array(32);_(o,n,i,b);for(var s=new Uint8Array(8),a=0;a<8;a++)s[a]=n[a+16];return S(t,e,r,s,o)}function I(t,e,r,n,i,o,s){var a=new Uint8Array(32);_(a,o,s,b);for(var u=new Uint8Array(8),c=0;c<8;c++)u[c]=o[c+16];return w(t,e,r,n,i,u,a)}var A=function(t){var e,r,n,i,o,s,a,u;this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.leftover=0,this.fin=0,e=255&t[0]|(255&t[1])<<8,this.r[0]=8191&e,r=255&t[2]|(255&t[3])<<8,this.r[1]=8191&(e>>>13|r<<3),n=255&t[4]|(255&t[5])<<8,this.r[2]=7939&(r>>>10|n<<6),i=255&t[6]|(255&t[7])<<8,this.r[3]=8191&(n>>>7|i<<9),o=255&t[8]|(255&t[9])<<8,this.r[4]=255&(i>>>4|o<<12),this.r[5]=o>>>1&8190,s=255&t[10]|(255&t[11])<<8,this.r[6]=8191&(o>>>14|s<<2),a=255&t[12]|(255&t[13])<<8,this.r[7]=8065&(s>>>11|a<<5),u=255&t[14]|(255&t[15])<<8,this.r[8]=8191&(a>>>8|u<<8),this.r[9]=u>>>5&127,this.pad[0]=255&t[16]|(255&t[17])<<8,this.pad[1]=255&t[18]|(255&t[19])<<8,this.pad[2]=255&t[20]|(255&t[21])<<8,this.pad[3]=255&t[22]|(255&t[23])<<8,this.pad[4]=255&t[24]|(255&t[25])<<8,this.pad[5]=255&t[26]|(255&t[27])<<8,this.pad[6]=255&t[28]|(255&t[29])<<8,this.pad[7]=255&t[30]|(255&t[31])<<8};function N(t,e,r,n,i,o){var s=new A(o);return s.update(r,n,i),s.finish(t,e),0}function T(t,e,r,n,i,o){var s=new Uint8Array(16);return N(s,0,r,n,i,o),g(t,e,s,0)}function C(t,e,r,n,i){var o;if(r<32)return-1;for(I(t,0,e,0,r,n,i),N(t,16,t,32,r-32,t),o=0;o<16;o++)t[o]=0;return 0}function R(t,e,r,n,i){var o,s=new Uint8Array(32);if(r<32)return-1;if(E(s,0,32,n,i),0!==T(e,16,e,32,r-32,s))return-1;for(I(t,0,e,0,r,n,i),o=0;o<32;o++)t[o]=0;return 0}function P(t,e){var r;for(r=0;r<16;r++)t[r]=0|e[r]}function x(t){var e,r,n=1;for(e=0;e<16;e++)r=t[e]+n+65535,n=Math.floor(r/65536),t[e]=r-65536*n;t[0]+=n-1+37*(n-1)}function O(t,e,r){for(var n,i=~(r-1),o=0;o<16;o++)n=i&(t[o]^e[o]),t[o]^=n,e[o]^=n}function B(t,r){var n,i,o,s=e(),a=e();for(n=0;n<16;n++)a[n]=r[n];for(x(a),x(a),x(a),i=0;i<2;i++){for(s[0]=a[0]-65517,n=1;n<15;n++)s[n]=a[n]-65535-(s[n-1]>>16&1),s[n-1]&=65535;s[15]=a[15]-32767-(s[14]>>16&1),o=s[15]>>16&1,s[14]&=65535,O(a,s,1-o)}for(n=0;n<16;n++)t[2*n]=255&a[n],t[2*n+1]=a[n]>>8}function D(t,e){var r=new Uint8Array(32),n=new Uint8Array(32);return B(r,t),B(n,e),v(r,0,n,0)}function F(t){var e=new Uint8Array(32);return B(e,t),1&e[0]}function k(t,e){var r;for(r=0;r<16;r++)t[r]=e[2*r]+(e[2*r+1]<<8);t[15]&=32767}function U(t,e,r){for(var n=0;n<16;n++)t[n]=e[n]+r[n]}function M(t,e,r){for(var n=0;n<16;n++)t[n]=e[n]-r[n]}function j(t,e,r){var n,i,o=0,s=0,a=0,u=0,c=0,f=0,l=0,h=0,d=0,p=0,y=0,g=0,v=0,m=0,_=0,b=0,w=0,S=0,E=0,I=0,A=0,N=0,T=0,C=0,R=0,P=0,x=0,O=0,B=0,D=0,F=0,k=r[0],U=r[1],M=r[2],j=r[3],K=r[4],L=r[5],q=r[6],z=r[7],V=r[8],$=r[9],H=r[10],G=r[11],W=r[12],J=r[13],Y=r[14],Z=r[15];o+=(n=e[0])*k,s+=n*U,a+=n*M,u+=n*j,c+=n*K,f+=n*L,l+=n*q,h+=n*z,d+=n*V,p+=n*$,y+=n*H,g+=n*G,v+=n*W,m+=n*J,_+=n*Y,b+=n*Z,s+=(n=e[1])*k,a+=n*U,u+=n*M,c+=n*j,f+=n*K,l+=n*L,h+=n*q,d+=n*z,p+=n*V,y+=n*$,g+=n*H,v+=n*G,m+=n*W,_+=n*J,b+=n*Y,w+=n*Z,a+=(n=e[2])*k,u+=n*U,c+=n*M,f+=n*j,l+=n*K,h+=n*L,d+=n*q,p+=n*z,y+=n*V,g+=n*$,v+=n*H,m+=n*G,_+=n*W,b+=n*J,w+=n*Y,S+=n*Z,u+=(n=e[3])*k,c+=n*U,f+=n*M,l+=n*j,h+=n*K,d+=n*L,p+=n*q,y+=n*z,g+=n*V,v+=n*$,m+=n*H,_+=n*G,b+=n*W,w+=n*J,S+=n*Y,E+=n*Z,c+=(n=e[4])*k,f+=n*U,l+=n*M,h+=n*j,d+=n*K,p+=n*L,y+=n*q,g+=n*z,v+=n*V,m+=n*$,_+=n*H,b+=n*G,w+=n*W,S+=n*J,E+=n*Y,I+=n*Z,f+=(n=e[5])*k,l+=n*U,h+=n*M,d+=n*j,p+=n*K,y+=n*L,g+=n*q,v+=n*z,m+=n*V,_+=n*$,b+=n*H,w+=n*G,S+=n*W,E+=n*J,I+=n*Y,A+=n*Z,l+=(n=e[6])*k,h+=n*U,d+=n*M,p+=n*j,y+=n*K,g+=n*L,v+=n*q,m+=n*z,_+=n*V,b+=n*$,w+=n*H,S+=n*G,E+=n*W,I+=n*J,A+=n*Y,N+=n*Z,h+=(n=e[7])*k,d+=n*U,p+=n*M,y+=n*j,g+=n*K,v+=n*L,m+=n*q,_+=n*z,b+=n*V,w+=n*$,S+=n*H,E+=n*G,I+=n*W,A+=n*J,N+=n*Y,T+=n*Z,d+=(n=e[8])*k,p+=n*U,y+=n*M,g+=n*j,v+=n*K,m+=n*L,_+=n*q,b+=n*z,w+=n*V,S+=n*$,E+=n*H,I+=n*G,A+=n*W,N+=n*J,T+=n*Y,C+=n*Z,p+=(n=e[9])*k,y+=n*U,g+=n*M,v+=n*j,m+=n*K,_+=n*L,b+=n*q,w+=n*z,S+=n*V,E+=n*$,I+=n*H,A+=n*G,N+=n*W,T+=n*J,C+=n*Y,R+=n*Z,y+=(n=e[10])*k,g+=n*U,v+=n*M,m+=n*j,_+=n*K,b+=n*L,w+=n*q,S+=n*z,E+=n*V,I+=n*$,A+=n*H,N+=n*G,T+=n*W,C+=n*J,R+=n*Y,P+=n*Z,g+=(n=e[11])*k,v+=n*U,m+=n*M,_+=n*j,b+=n*K,w+=n*L,S+=n*q,E+=n*z,I+=n*V,A+=n*$,N+=n*H,T+=n*G,C+=n*W,R+=n*J,P+=n*Y,x+=n*Z,v+=(n=e[12])*k,m+=n*U,_+=n*M,b+=n*j,w+=n*K,S+=n*L,E+=n*q,I+=n*z,A+=n*V,N+=n*$,T+=n*H,C+=n*G,R+=n*W,P+=n*J,x+=n*Y,O+=n*Z,m+=(n=e[13])*k,_+=n*U,b+=n*M,w+=n*j,S+=n*K,E+=n*L,I+=n*q,A+=n*z,N+=n*V,T+=n*$,C+=n*H,R+=n*G,P+=n*W,x+=n*J,O+=n*Y,B+=n*Z,_+=(n=e[14])*k,b+=n*U,w+=n*M,S+=n*j,E+=n*K,I+=n*L,A+=n*q,N+=n*z,T+=n*V,C+=n*$,R+=n*H,P+=n*G,x+=n*W,O+=n*J,B+=n*Y,D+=n*Z,b+=(n=e[15])*k,s+=38*(S+=n*M),a+=38*(E+=n*j),u+=38*(I+=n*K),c+=38*(A+=n*L),f+=38*(N+=n*q),l+=38*(T+=n*z),h+=38*(C+=n*V),d+=38*(R+=n*$),p+=38*(P+=n*H),y+=38*(x+=n*G),g+=38*(O+=n*W),v+=38*(B+=n*J),m+=38*(D+=n*Y),_+=38*(F+=n*Z),o=(n=(o+=38*(w+=n*U))+(i=1)+65535)-65536*(i=Math.floor(n/65536)),s=(n=s+i+65535)-65536*(i=Math.floor(n/65536)),a=(n=a+i+65535)-65536*(i=Math.floor(n/65536)),u=(n=u+i+65535)-65536*(i=Math.floor(n/65536)),c=(n=c+i+65535)-65536*(i=Math.floor(n/65536)),f=(n=f+i+65535)-65536*(i=Math.floor(n/65536)),l=(n=l+i+65535)-65536*(i=Math.floor(n/65536)),h=(n=h+i+65535)-65536*(i=Math.floor(n/65536)),d=(n=d+i+65535)-65536*(i=Math.floor(n/65536)),p=(n=p+i+65535)-65536*(i=Math.floor(n/65536)),y=(n=y+i+65535)-65536*(i=Math.floor(n/65536)),g=(n=g+i+65535)-65536*(i=Math.floor(n/65536)),v=(n=v+i+65535)-65536*(i=Math.floor(n/65536)),m=(n=m+i+65535)-65536*(i=Math.floor(n/65536)),_=(n=_+i+65535)-65536*(i=Math.floor(n/65536)),b=(n=b+i+65535)-65536*(i=Math.floor(n/65536)),o=(n=(o+=i-1+37*(i-1))+(i=1)+65535)-65536*(i=Math.floor(n/65536)),s=(n=s+i+65535)-65536*(i=Math.floor(n/65536)),a=(n=a+i+65535)-65536*(i=Math.floor(n/65536)),u=(n=u+i+65535)-65536*(i=Math.floor(n/65536)),c=(n=c+i+65535)-65536*(i=Math.floor(n/65536)),f=(n=f+i+65535)-65536*(i=Math.floor(n/65536)),l=(n=l+i+65535)-65536*(i=Math.floor(n/65536)),h=(n=h+i+65535)-65536*(i=Math.floor(n/65536)),d=(n=d+i+65535)-65536*(i=Math.floor(n/65536)),p=(n=p+i+65535)-65536*(i=Math.floor(n/65536)),y=(n=y+i+65535)-65536*(i=Math.floor(n/65536)),g=(n=g+i+65535)-65536*(i=Math.floor(n/65536)),v=(n=v+i+65535)-65536*(i=Math.floor(n/65536)),m=(n=m+i+65535)-65536*(i=Math.floor(n/65536)),_=(n=_+i+65535)-65536*(i=Math.floor(n/65536)),b=(n=b+i+65535)-65536*(i=Math.floor(n/65536)),o+=i-1+37*(i-1),t[0]=o,t[1]=s,t[2]=a,t[3]=u,t[4]=c,t[5]=f,t[6]=l,t[7]=h,t[8]=d,t[9]=p,t[10]=y,t[11]=g,t[12]=v,t[13]=m,t[14]=_,t[15]=b}function K(t,e){j(t,e,e)}function L(t,r){var n,i=e();for(n=0;n<16;n++)i[n]=r[n];for(n=253;n>=0;n--)K(i,i),2!==n&&4!==n&&j(i,i,r);for(n=0;n<16;n++)t[n]=i[n]}function q(t,r){var n,i=e();for(n=0;n<16;n++)i[n]=r[n];for(n=250;n>=0;n--)K(i,i),1!==n&&j(i,i,r);for(n=0;n<16;n++)t[n]=i[n]}function z(t,r,n){var i,o,s=new Uint8Array(32),a=new Float64Array(80),c=e(),f=e(),l=e(),h=e(),d=e(),p=e();for(o=0;o<31;o++)s[o]=r[o];for(s[31]=127&r[31]|64,s[0]&=248,k(a,n),o=0;o<16;o++)f[o]=a[o],h[o]=c[o]=l[o]=0;for(c[0]=h[0]=1,o=254;o>=0;--o)O(c,f,i=s[o>>>3]>>>(7&o)&1),O(l,h,i),U(d,c,l),M(c,c,l),U(l,f,h),M(f,f,h),K(h,d),K(p,c),j(c,l,c),j(l,f,d),U(d,c,l),M(c,c,l),K(f,c),M(l,h,p),j(c,l,u),U(c,c,h),j(l,l,c),j(c,h,p),j(h,f,a),K(f,d),O(c,f,i),O(l,h,i);for(o=0;o<16;o++)a[o+16]=c[o],a[o+32]=l[o],a[o+48]=f[o],a[o+64]=h[o];var y=a.subarray(32),g=a.subarray(16);return L(y,y),j(g,g,y),B(t,g),0}function V(t,e){return z(t,e,o)}function $(t,e){return n(e,32),V(t,e)}function H(t,e,r){var n=new Uint8Array(32);return z(n,r,e),_(t,i,n,b)}A.prototype.blocks=function(t,e,r){for(var n,i,o,s,a,u,c,f,l,h,d,p,y,g,v,m,_,b,w,S=this.fin?0:2048,E=this.h[0],I=this.h[1],A=this.h[2],N=this.h[3],T=this.h[4],C=this.h[5],R=this.h[6],P=this.h[7],x=this.h[8],O=this.h[9],B=this.r[0],D=this.r[1],F=this.r[2],k=this.r[3],U=this.r[4],M=this.r[5],j=this.r[6],K=this.r[7],L=this.r[8],q=this.r[9];r>=16;)h=l=0,h+=(E+=8191&(n=255&t[e+0]|(255&t[e+1])<<8))*B,h+=(I+=8191&(n>>>13|(i=255&t[e+2]|(255&t[e+3])<<8)<<3))*(5*q),h+=(A+=8191&(i>>>10|(o=255&t[e+4]|(255&t[e+5])<<8)<<6))*(5*L),h+=(N+=8191&(o>>>7|(s=255&t[e+6]|(255&t[e+7])<<8)<<9))*(5*K),l=(h+=(T+=8191&(s>>>4|(a=255&t[e+8]|(255&t[e+9])<<8)<<12))*(5*j))>>>13,h&=8191,h+=(C+=a>>>1&8191)*(5*M),h+=(R+=8191&(a>>>14|(u=255&t[e+10]|(255&t[e+11])<<8)<<2))*(5*U),h+=(P+=8191&(u>>>11|(c=255&t[e+12]|(255&t[e+13])<<8)<<5))*(5*k),h+=(x+=8191&(c>>>8|(f=255&t[e+14]|(255&t[e+15])<<8)<<8))*(5*F),d=l+=(h+=(O+=f>>>5|S)*(5*D))>>>13,d+=E*D,d+=I*B,d+=A*(5*q),d+=N*(5*L),l=(d+=T*(5*K))>>>13,d&=8191,d+=C*(5*j),d+=R*(5*M),d+=P*(5*U),d+=x*(5*k),l+=(d+=O*(5*F))>>>13,d&=8191,p=l,p+=E*F,p+=I*D,p+=A*B,p+=N*(5*q),l=(p+=T*(5*L))>>>13,p&=8191,p+=C*(5*K),p+=R*(5*j),p+=P*(5*M),p+=x*(5*U),y=l+=(p+=O*(5*k))>>>13,y+=E*k,y+=I*F,y+=A*D,y+=N*B,l=(y+=T*(5*q))>>>13,y&=8191,y+=C*(5*L),y+=R*(5*K),y+=P*(5*j),y+=x*(5*M),g=l+=(y+=O*(5*U))>>>13,g+=E*U,g+=I*k,g+=A*F,g+=N*D,l=(g+=T*B)>>>13,g&=8191,g+=C*(5*q),g+=R*(5*L),g+=P*(5*K),g+=x*(5*j),v=l+=(g+=O*(5*M))>>>13,v+=E*M,v+=I*U,v+=A*k,v+=N*F,l=(v+=T*D)>>>13,v&=8191,v+=C*B,v+=R*(5*q),v+=P*(5*L),v+=x*(5*K),m=l+=(v+=O*(5*j))>>>13,m+=E*j,m+=I*M,m+=A*U,m+=N*k,l=(m+=T*F)>>>13,m&=8191,m+=C*D,m+=R*B,m+=P*(5*q),m+=x*(5*L),_=l+=(m+=O*(5*K))>>>13,_+=E*K,_+=I*j,_+=A*M,_+=N*U,l=(_+=T*k)>>>13,_&=8191,_+=C*F,_+=R*D,_+=P*B,_+=x*(5*q),b=l+=(_+=O*(5*L))>>>13,b+=E*L,b+=I*K,b+=A*j,b+=N*M,l=(b+=T*U)>>>13,b&=8191,b+=C*k,b+=R*F,b+=P*D,b+=x*B,w=l+=(b+=O*(5*q))>>>13,w+=E*q,w+=I*L,w+=A*K,w+=N*j,l=(w+=T*M)>>>13,w&=8191,w+=C*U,w+=R*k,w+=P*F,w+=x*D,E=h=8191&(l=(l=((l+=(w+=O*B)>>>13)<<2)+l|0)+(h&=8191)|0),I=d+=l>>>=13,A=p&=8191,N=y&=8191,T=g&=8191,C=v&=8191,R=m&=8191,P=_&=8191,x=b&=8191,O=w&=8191,e+=16,r-=16;this.h[0]=E,this.h[1]=I,this.h[2]=A,this.h[3]=N,this.h[4]=T,this.h[5]=C,this.h[6]=R,this.h[7]=P,this.h[8]=x,this.h[9]=O},A.prototype.finish=function(t,e){var r,n,i,o,s=new Uint16Array(10);if(this.leftover){for(o=this.leftover,this.buffer[o++]=1;o<16;o++)this.buffer[o]=0;this.fin=1,this.blocks(this.buffer,0,16)}for(r=this.h[1]>>>13,this.h[1]&=8191,o=2;o<10;o++)this.h[o]+=r,r=this.h[o]>>>13,this.h[o]&=8191;for(this.h[0]+=5*r,r=this.h[0]>>>13,this.h[0]&=8191,this.h[1]+=r,r=this.h[1]>>>13,this.h[1]&=8191,this.h[2]+=r,s[0]=this.h[0]+5,r=s[0]>>>13,s[0]&=8191,o=1;o<10;o++)s[o]=this.h[o]+r,r=s[o]>>>13,s[o]&=8191;for(s[9]-=8192,n=(1^r)-1,o=0;o<10;o++)s[o]&=n;for(n=~n,o=0;o<10;o++)this.h[o]=this.h[o]&n|s[o];for(this.h[0]=65535&(this.h[0]|this.h[1]<<13),this.h[1]=65535&(this.h[1]>>>3|this.h[2]<<10),this.h[2]=65535&(this.h[2]>>>6|this.h[3]<<7),this.h[3]=65535&(this.h[3]>>>9|this.h[4]<<4),this.h[4]=65535&(this.h[4]>>>12|this.h[5]<<1|this.h[6]<<14),this.h[5]=65535&(this.h[6]>>>2|this.h[7]<<11),this.h[6]=65535&(this.h[7]>>>5|this.h[8]<<8),this.h[7]=65535&(this.h[8]>>>8|this.h[9]<<5),i=this.h[0]+this.pad[0],this.h[0]=65535&i,o=1;o<8;o++)i=(this.h[o]+this.pad[o]|0)+(i>>>16)|0,this.h[o]=65535&i;t[e+0]=this.h[0]>>>0&255,t[e+1]=this.h[0]>>>8&255,t[e+2]=this.h[1]>>>0&255,t[e+3]=this.h[1]>>>8&255,t[e+4]=this.h[2]>>>0&255,t[e+5]=this.h[2]>>>8&255,t[e+6]=this.h[3]>>>0&255,t[e+7]=this.h[3]>>>8&255,t[e+8]=this.h[4]>>>0&255,t[e+9]=this.h[4]>>>8&255,t[e+10]=this.h[5]>>>0&255,t[e+11]=this.h[5]>>>8&255,t[e+12]=this.h[6]>>>0&255,t[e+13]=this.h[6]>>>8&255,t[e+14]=this.h[7]>>>0&255,t[e+15]=this.h[7]>>>8&255},A.prototype.update=function(t,e,r){var n,i;if(this.leftover){for((i=16-this.leftover)>r&&(i=r),n=0;n<i;n++)this.buffer[this.leftover+n]=t[e+n];if(r-=i,e+=i,this.leftover+=i,this.leftover<16)return;this.blocks(this.buffer,0,16),this.leftover=0}if(r>=16&&(i=r-r%16,this.blocks(t,e,i),e+=i,r-=i),r){for(n=0;n<r;n++)this.buffer[this.leftover+n]=t[e+n];this.leftover+=r}};var G=C,W=R;var J=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];function Y(t,e,r,n){for(var i,o,s,a,u,c,f,l,h,d,p,y,g,v,m,_,b,w,S,E,I,A,N,T,C,R,P=new Int32Array(16),x=new Int32Array(16),O=t[0],B=t[1],D=t[2],F=t[3],k=t[4],U=t[5],M=t[6],j=t[7],K=e[0],L=e[1],q=e[2],z=e[3],V=e[4],$=e[5],H=e[6],G=e[7],W=0;n>=128;){for(S=0;S<16;S++)E=8*S+W,P[S]=r[E+0]<<24|r[E+1]<<16|r[E+2]<<8|r[E+3],x[S]=r[E+4]<<24|r[E+5]<<16|r[E+6]<<8|r[E+7];for(S=0;S<80;S++)if(i=O,o=B,s=D,a=F,u=k,c=U,f=M,j,h=K,d=L,p=q,y=z,g=V,v=$,m=H,G,N=65535&(A=G),T=A>>>16,C=65535&(I=j),R=I>>>16,N+=65535&(A=(V>>>14|k<<18)^(V>>>18|k<<14)^(k>>>9|V<<23)),T+=A>>>16,C+=65535&(I=(k>>>14|V<<18)^(k>>>18|V<<14)^(V>>>9|k<<23)),R+=I>>>16,N+=65535&(A=V&$^~V&H),T+=A>>>16,C+=65535&(I=k&U^~k&M),R+=I>>>16,N+=65535&(A=J[2*S+1]),T+=A>>>16,C+=65535&(I=J[2*S]),R+=I>>>16,I=P[S%16],T+=(A=x[S%16])>>>16,C+=65535&I,R+=I>>>16,C+=(T+=(N+=65535&A)>>>16)>>>16,N=65535&(A=w=65535&N|T<<16),T=A>>>16,C=65535&(I=b=65535&C|(R+=C>>>16)<<16),R=I>>>16,N+=65535&(A=(K>>>28|O<<4)^(O>>>2|K<<30)^(O>>>7|K<<25)),T+=A>>>16,C+=65535&(I=(O>>>28|K<<4)^(K>>>2|O<<30)^(K>>>7|O<<25)),R+=I>>>16,T+=(A=K&L^K&q^L&q)>>>16,C+=65535&(I=O&B^O&D^B&D),R+=I>>>16,l=65535&(C+=(T+=(N+=65535&A)>>>16)>>>16)|(R+=C>>>16)<<16,_=65535&N|T<<16,N=65535&(A=y),T=A>>>16,C=65535&(I=a),R=I>>>16,T+=(A=w)>>>16,C+=65535&(I=b),R+=I>>>16,B=i,D=o,F=s,k=a=65535&(C+=(T+=(N+=65535&A)>>>16)>>>16)|(R+=C>>>16)<<16,U=u,M=c,j=f,O=l,L=h,q=d,z=p,V=y=65535&N|T<<16,$=g,H=v,G=m,K=_,S%16==15)for(E=0;E<16;E++)I=P[E],N=65535&(A=x[E]),T=A>>>16,C=65535&I,R=I>>>16,I=P[(E+9)%16],N+=65535&(A=x[(E+9)%16]),T+=A>>>16,C+=65535&I,R+=I>>>16,b=P[(E+1)%16],N+=65535&(A=((w=x[(E+1)%16])>>>1|b<<31)^(w>>>8|b<<24)^(w>>>7|b<<25)),T+=A>>>16,C+=65535&(I=(b>>>1|w<<31)^(b>>>8|w<<24)^b>>>7),R+=I>>>16,b=P[(E+14)%16],T+=(A=((w=x[(E+14)%16])>>>19|b<<13)^(b>>>29|w<<3)^(w>>>6|b<<26))>>>16,C+=65535&(I=(b>>>19|w<<13)^(w>>>29|b<<3)^b>>>6),R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,P[E]=65535&C|R<<16,x[E]=65535&N|T<<16;N=65535&(A=K),T=A>>>16,C=65535&(I=O),R=I>>>16,I=t[0],T+=(A=e[0])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[0]=O=65535&C|R<<16,e[0]=K=65535&N|T<<16,N=65535&(A=L),T=A>>>16,C=65535&(I=B),R=I>>>16,I=t[1],T+=(A=e[1])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[1]=B=65535&C|R<<16,e[1]=L=65535&N|T<<16,N=65535&(A=q),T=A>>>16,C=65535&(I=D),R=I>>>16,I=t[2],T+=(A=e[2])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[2]=D=65535&C|R<<16,e[2]=q=65535&N|T<<16,N=65535&(A=z),T=A>>>16,C=65535&(I=F),R=I>>>16,I=t[3],T+=(A=e[3])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[3]=F=65535&C|R<<16,e[3]=z=65535&N|T<<16,N=65535&(A=V),T=A>>>16,C=65535&(I=k),R=I>>>16,I=t[4],T+=(A=e[4])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[4]=k=65535&C|R<<16,e[4]=V=65535&N|T<<16,N=65535&(A=$),T=A>>>16,C=65535&(I=U),R=I>>>16,I=t[5],T+=(A=e[5])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[5]=U=65535&C|R<<16,e[5]=$=65535&N|T<<16,N=65535&(A=H),T=A>>>16,C=65535&(I=M),R=I>>>16,I=t[6],T+=(A=e[6])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[6]=M=65535&C|R<<16,e[6]=H=65535&N|T<<16,N=65535&(A=G),T=A>>>16,C=65535&(I=j),R=I>>>16,I=t[7],T+=(A=e[7])>>>16,C+=65535&I,R+=I>>>16,R+=(C+=(T+=(N+=65535&A)>>>16)>>>16)>>>16,t[7]=j=65535&C|R<<16,e[7]=G=65535&N|T<<16,W+=128,n-=128}return n}function Z(t,e,r){var n,i=new Int32Array(8),o=new Int32Array(8),s=new Uint8Array(256),a=r;for(i[0]=1779033703,i[1]=3144134277,i[2]=1013904242,i[3]=2773480762,i[4]=1359893119,i[5]=2600822924,i[6]=528734635,i[7]=1541459225,o[0]=4089235720,o[1]=2227873595,o[2]=4271175723,o[3]=1595750129,o[4]=2917565137,o[5]=725511199,o[6]=4215389547,o[7]=327033209,Y(i,o,e,r),r%=128,n=0;n<r;n++)s[n]=e[a-r+n];for(s[r]=128,s[(r=256-128*(r<112?1:0))-9]=0,p(s,r-8,a/536870912|0,a<<3),Y(i,o,s,r),n=0;n<8;n++)p(t,8*n,i[n],o[n]);return 0}function X(t,r){var n=e(),i=e(),o=e(),s=e(),a=e(),u=e(),c=e(),l=e(),h=e();M(n,t[1],t[0]),M(h,r[1],r[0]),j(n,n,h),U(i,t[0],t[1]),U(h,r[0],r[1]),j(i,i,h),j(o,t[3],r[3]),j(o,o,f),j(s,t[2],r[2]),U(s,s,s),M(a,i,n),M(u,s,o),U(c,s,o),U(l,i,n),j(t[0],a,u),j(t[1],l,c),j(t[2],c,u),j(t[3],a,l)}function Q(t,e,r){var n;for(n=0;n<4;n++)O(t[n],e[n],r)}function tt(t,r){var n=e(),i=e(),o=e();L(o,r[2]),j(n,r[0],o),j(i,r[1],o),B(t,i),t[31]^=F(n)<<7}function et(t,e,r){var n,i;for(P(t[0],s),P(t[1],a),P(t[2],a),P(t[3],s),i=255;i>=0;--i)Q(t,e,n=r[i/8|0]>>(7&i)&1),X(e,t),X(t,t),Q(t,e,n)}function rt(t,r){var n=[e(),e(),e(),e()];P(n[0],l),P(n[1],h),P(n[2],a),j(n[3],l,h),et(t,n,r)}function nt(t,r,i){var o,s=new Uint8Array(64),a=[e(),e(),e(),e()];for(i||n(r,32),Z(s,r,32),s[0]&=248,s[31]&=127,s[31]|=64,rt(a,s),tt(t,a),o=0;o<32;o++)r[o+32]=t[o];return 0}var it=new Float64Array([237,211,245,92,26,99,18,88,214,156,247,162,222,249,222,20,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,16]);function ot(t,e){var r,n,i,o;for(n=63;n>=32;--n){for(r=0,i=n-32,o=n-12;i<o;++i)e[i]+=r-16*e[n]*it[i-(n-32)],r=Math.floor((e[i]+128)/256),e[i]-=256*r;e[i]+=r,e[n]=0}for(r=0,i=0;i<32;i++)e[i]+=r-(e[31]>>4)*it[i],r=e[i]>>8,e[i]&=255;for(i=0;i<32;i++)e[i]-=r*it[i];for(n=0;n<32;n++)e[n+1]+=e[n]>>8,t[n]=255&e[n]}function st(t){var e,r=new Float64Array(64);for(e=0;e<64;e++)r[e]=t[e];for(e=0;e<64;e++)t[e]=0;ot(t,r)}function at(t,r,n,i){var o,s,a=new Uint8Array(64),u=new Uint8Array(64),c=new Uint8Array(64),f=new Float64Array(64),l=[e(),e(),e(),e()];Z(a,i,32),a[0]&=248,a[31]&=127,a[31]|=64;var h=n+64;for(o=0;o<n;o++)t[64+o]=r[o];for(o=0;o<32;o++)t[32+o]=a[32+o];for(Z(c,t.subarray(32),n+32),st(c),rt(l,c),tt(t,l),o=32;o<64;o++)t[o]=i[o];for(Z(u,t,n+64),st(u),o=0;o<64;o++)f[o]=0;for(o=0;o<32;o++)f[o]=c[o];for(o=0;o<32;o++)for(s=0;s<32;s++)f[o+s]+=u[o]*a[s];return ot(t.subarray(32),f),h}function ut(t,r,n,i){var o,u=new Uint8Array(32),f=new Uint8Array(64),l=[e(),e(),e(),e()],h=[e(),e(),e(),e()];if(n<64)return-1;if(function(t,r){var n=e(),i=e(),o=e(),u=e(),f=e(),l=e(),h=e();return P(t[2],a),k(t[1],r),K(o,t[1]),j(u,o,c),M(o,o,t[2]),U(u,t[2],u),K(f,u),K(l,f),j(h,l,f),j(n,h,o),j(n,n,u),q(n,n),j(n,n,o),j(n,n,u),j(n,n,u),j(t[0],n,u),K(i,t[0]),j(i,i,u),D(i,o)&&j(t[0],t[0],d),K(i,t[0]),j(i,i,u),D(i,o)?-1:(F(t[0])===r[31]>>7&&M(t[0],s,t[0]),j(t[3],t[0],t[1]),0)}(h,i))return-1;for(o=0;o<n;o++)t[o]=r[o];for(o=0;o<32;o++)t[o+32]=i[o];if(Z(f,t,n),st(f),et(l,h,f),rt(h,r.subarray(32)),X(l,h),tt(u,l),n-=64,v(r,0,u,0)){for(o=0;o<n;o++)t[o]=0;return-1}for(o=0;o<n;o++)t[o]=r[o+64];return n}function ct(t,e){if(32!==t.length)throw new Error("bad key size");if(24!==e.length)throw new Error("bad nonce size")}function ft(){for(var t=0;t<arguments.length;t++)if(!(arguments[t]instanceof Uint8Array))throw new TypeError("unexpected type, use Uint8Array")}function lt(t){for(var e=0;e<t.length;e++)t[e]=0}t.lowlevel={crypto_core_hsalsa20:_,crypto_stream_xor:I,crypto_stream:E,crypto_stream_salsa20_xor:w,crypto_stream_salsa20:S,crypto_onetimeauth:N,crypto_onetimeauth_verify:T,crypto_verify_16:g,crypto_verify_32:v,crypto_secretbox:C,crypto_secretbox_open:R,crypto_scalarmult:z,crypto_scalarmult_base:V,crypto_box_beforenm:H,crypto_box_afternm:G,crypto_box:function(t,e,r,n,i,o){var s=new Uint8Array(32);return H(s,i,o),G(t,e,r,n,s)},crypto_box_open:function(t,e,r,n,i,o){var s=new Uint8Array(32);return H(s,i,o),W(t,e,r,n,s)},crypto_box_keypair:$,crypto_hash:Z,crypto_sign:at,crypto_sign_keypair:nt,crypto_sign_open:ut,crypto_secretbox_KEYBYTES:32,crypto_secretbox_NONCEBYTES:24,crypto_secretbox_ZEROBYTES:32,crypto_secretbox_BOXZEROBYTES:16,crypto_scalarmult_BYTES:32,crypto_scalarmult_SCALARBYTES:32,crypto_box_PUBLICKEYBYTES:32,crypto_box_SECRETKEYBYTES:32,crypto_box_BEFORENMBYTES:32,crypto_box_NONCEBYTES:24,crypto_box_ZEROBYTES:32,crypto_box_BOXZEROBYTES:16,crypto_sign_BYTES:64,crypto_sign_PUBLICKEYBYTES:32,crypto_sign_SECRETKEYBYTES:64,crypto_sign_SEEDBYTES:32,crypto_hash_BYTES:64,gf:e,D:c,L:it,pack25519:B,unpack25519:k,M:j,A:U,S:K,Z:M,pow2523:q,add:X,set25519:P,modL:ot,scalarmult:et,scalarbase:rt},t.randomBytes=function(t){var e=new Uint8Array(t);return n(e,t),e},t.secretbox=function(t,e,r){ft(t,e,r),ct(r,e);for(var n=new Uint8Array(32+t.length),i=new Uint8Array(n.length),o=0;o<t.length;o++)n[o+32]=t[o];return C(i,n,n.length,e,r),i.subarray(16)},t.secretbox.open=function(t,e,r){ft(t,e,r),ct(r,e);for(var n=new Uint8Array(16+t.length),i=new Uint8Array(n.length),o=0;o<t.length;o++)n[o+16]=t[o];return n.length<32||0!==R(i,n,n.length,e,r)?null:i.subarray(32)},t.secretbox.keyLength=32,t.secretbox.nonceLength=24,t.secretbox.overheadLength=16,t.scalarMult=function(t,e){if(ft(t,e),32!==t.length)throw new Error("bad n size");if(32!==e.length)throw new Error("bad p size");var r=new Uint8Array(32);return z(r,t,e),r},t.scalarMult.base=function(t){if(ft(t),32!==t.length)throw new Error("bad n size");var e=new Uint8Array(32);return V(e,t),e},t.scalarMult.scalarLength=32,t.scalarMult.groupElementLength=32,t.box=function(e,r,n,i){var o=t.box.before(n,i);return t.secretbox(e,r,o)},t.box.before=function(t,e){ft(t,e),function(t,e){if(32!==t.length)throw new Error("bad public key size");if(32!==e.length)throw new Error("bad secret key size")}(t,e);var r=new Uint8Array(32);return H(r,t,e),r},t.box.after=t.secretbox,t.box.open=function(e,r,n,i){var o=t.box.before(n,i);return t.secretbox.open(e,r,o)},t.box.open.after=t.secretbox.open,t.box.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(32);return $(t,e),{publicKey:t,secretKey:e}},t.box.keyPair.fromSecretKey=function(t){if(ft(t),32!==t.length)throw new Error("bad secret key size");var e=new Uint8Array(32);return V(e,t),{publicKey:e,secretKey:new Uint8Array(t)}},t.box.publicKeyLength=32,t.box.secretKeyLength=32,t.box.sharedKeyLength=32,t.box.nonceLength=24,t.box.overheadLength=t.secretbox.overheadLength,t.sign=function(t,e){if(ft(t,e),64!==e.length)throw new Error("bad secret key size");var r=new Uint8Array(64+t.length);return at(r,t,t.length,e),r},t.sign.open=function(t,e){if(ft(t,e),32!==e.length)throw new Error("bad public key size");var r=new Uint8Array(t.length),n=ut(r,t,t.length,e);if(n<0)return null;for(var i=new Uint8Array(n),o=0;o<i.length;o++)i[o]=r[o];return i},t.sign.detached=function(e,r){for(var n=t.sign(e,r),i=new Uint8Array(64),o=0;o<i.length;o++)i[o]=n[o];return i},t.sign.detached.verify=function(t,e,r){if(ft(t,e,r),64!==e.length)throw new Error("bad signature size");if(32!==r.length)throw new Error("bad public key size");var n,i=new Uint8Array(64+t.length),o=new Uint8Array(64+t.length);for(n=0;n<64;n++)i[n]=e[n];for(n=0;n<t.length;n++)i[n+64]=t[n];return ut(o,i,i.length,r)>=0},t.sign.keyPair=function(){var t=new Uint8Array(32),e=new Uint8Array(64);return nt(t,e),{publicKey:t,secretKey:e}},t.sign.keyPair.fromSecretKey=function(t){if(ft(t),64!==t.length)throw new Error("bad secret key size");for(var e=new Uint8Array(32),r=0;r<e.length;r++)e[r]=t[32+r];return{publicKey:e,secretKey:new Uint8Array(t)}},t.sign.keyPair.fromSeed=function(t){if(ft(t),32!==t.length)throw new Error("bad seed size");for(var e=new Uint8Array(32),r=new Uint8Array(64),n=0;n<32;n++)r[n]=t[n];return nt(e,r,!0),{publicKey:e,secretKey:r}},t.sign.publicKeyLength=32,t.sign.secretKeyLength=64,t.sign.seedLength=32,t.sign.signatureLength=64,t.hash=function(t){ft(t);var e=new Uint8Array(64);return Z(e,t,t.length),e},t.hash.hashLength=64,t.verify=function(t,e){return ft(t,e),0!==t.length&&0!==e.length&&(t.length===e.length&&0===y(t,0,e,0,t.length))},t.setPRNG=function(t){n=t},function(){var e="undefined"!=typeof self?self.crypto||self.msCrypto:null;if(e&&e.getRandomValues){t.setPRNG((function(t,r){var n,i=new Uint8Array(r);for(n=0;n<r;n+=65536)e.getRandomValues(i.subarray(n,n+Math.min(r-n,65536)));for(n=0;n<r;n++)t[n]=i[n];lt(i)}))}else(e=r(2))&&e.randomBytes&&t.setPRNG((function(t,r){var n,i=e.randomBytes(r);for(n=0;n<r;n++)t[n]=i[n];lt(i)}))}()}(t.exports?t.exports:self.nacl=self.nacl||{})},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(9);class i{static parse(t){const e=[],r=t.toString("utf8").split("\n").map(t=>t.trim()).filter(t=>""!==t&&!t.startsWith("#"));for(;r.length>0;)e.push(o(r));if(0===e.length)throw new Error("PEM: no block");return e}constructor(t,e){this.type=t,this.body=e,this.headers=Object.create(null)}get procType(){return this.getHeader("Proc-Type")}getHeader(t){const e=this.headers[t];return null==e?"":e}setHeader(t,e){if(t.includes(":"))throw new Error("pem: cannot encode a header key that contains a colon");if(""===t||""===e)throw new Error("pem: invalid header key or value");this.headers[t]=e}toString(){let t="-----BEGIN "+this.type+"-----\n";const e=Object.keys(this.headers);if(e.length>0){const r=this.procType;""!==r&&(t+=`Proc-Type: ${r}\n`),e.sort();for(const r of e)"Proc-Type"!==r&&(t+=`${r}: ${this.headers[r]}\n`);t+="\n"}const r=this.body.toString("base64");let n=0;for(;n<r.length;)t+=r.slice(n,n+64)+"\n",n+=64;return t+="-----END "+this.type+"-----\n",t}toBuffer(){return Buffer.from(this.toString(),"utf8")}valueOf(){return this.body}toJSON(){return{type:this.type,body:this.body,headers:this.headers}}[n.inspect.custom](t,e){return`<${this.constructor.name} ${n.inspect(this.toJSON(),e)}>`}}function o(t){let e=t.shift();if(null==e||!e.startsWith("-----BEGIN ")||!e.endsWith("-----"))throw new Error("pem: invalid BEGIN line");const r=e.slice("-----BEGIN ".length,e.length-"-----".length);if(""===r)throw new Error("pem: invalid type");const n=[];for(e=t.shift();null!=e&&e.includes(": ");){const r=e.split(": ");if(2!==r.length||""===r[0]||""===r[1])throw new Error("pem: invalid Header line");n.push(r),e=t.shift()}let o="";for(;null!=e&&!e.startsWith("-----END ");)o+=e,e=t.shift();if(null==e||e!==`-----END ${r}-----`)throw new Error("pem: invalid END line");const s=new i(r,Buffer.from(o,"base64"));if(""===o||s.body.toString("base64")!==o)throw new Error("pem: invalid base64 body");for(const t of n)s.setHeader(t[0],t[1]);return s}e.PEM=i},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(9),i=r(18);var o,s;!function(t){t[t.UNIVERSAL=0]="UNIVERSAL",t[t.APPLICATION=64]="APPLICATION",t[t.CONTEXT_SPECIFIC=128]="CONTEXT_SPECIFIC",t[t.PRIVATE=192]="PRIVATE"}(o=e.Class||(e.Class={})),function(t){t[t.NONE=0]="NONE",t[t.BOOLEAN=1]="BOOLEAN",t[t.INTEGER=2]="INTEGER",t[t.BITSTRING=3]="BITSTRING",t[t.OCTETSTRING=4]="OCTETSTRING",t[t.NULL=5]="NULL",t[t.OID=6]="OID",t[t.ENUMERATED=10]="ENUMERATED",t[t.UTF8=12]="UTF8",t[t.SEQUENCE=16]="SEQUENCE",t[t.SET=17]="SET",t[t.NUMERICSTRING=18]="NUMERICSTRING",t[t.PRINTABLESTRING=19]="PRINTABLESTRING",t[t.T61STRING=20]="T61STRING",t[t.IA5STRING=22]="IA5STRING",t[t.UTCTIME=23]="UTCTIME",t[t.GENERALIZEDTIME=24]="GENERALIZEDTIME",t[t.GENERALSTRING=27]="GENERALSTRING"}(s=e.Tag||(e.Tag={}));class a{constructor(t,e){this.buf=t,this.bitLen=e}at(t){if(t<0||t>=this.bitLen||!Number.isInteger(t))return 0;const e=Math.floor(t/8),r=7-t%8;return this.buf[e]>>r&1}rightAlign(){const t=8-this.bitLen%8;if(8===t||0===this.buf.length)return this.buf;const e=Buffer.alloc(this.buf.length);e[0]=this.buf[0]>>t;for(let r=1;r<this.buf.length;r++)e[r]=this.buf[r-1]<<8-t,e[r]|=this.buf[r]>>t;return e}}e.BitString=a;class u{static Bool(t){const e=new u(o.UNIVERSAL,s.BOOLEAN,Buffer.from([t?255:0]));return e._value=t,e}static parseBool(t){if(!(t instanceof Buffer)||1!==t.length)throw new Error("ASN1 syntax error: invalid boolean");switch(t[0]){case 0:return!1;case 255:return!0;default:throw new Error("ASN1 syntax error: invalid boolean")}}static Integer(t){if(t instanceof Buffer){const e=new u(o.UNIVERSAL,s.INTEGER,t);return e._value=t.toString("hex"),e}if(!Number.isSafeInteger(t))throw new Error("ASN1 syntax error: invalid integer");let e;if(t>=-128&&t<128)e=Buffer.alloc(1),e.writeInt8(t,0);else if(t>=-32768&&t<32768)e=Buffer.alloc(2),e.writeIntBE(t,0,2);else if(t>=-8388608&&t<8388608)e=Buffer.alloc(3),e.writeIntBE(t,0,3);else if(t>=-2147483648&&t<2147483648)e=Buffer.alloc(4),e.writeIntBE(t,0,4);else if(t>=-549755813888&&t<549755813888)e=Buffer.alloc(5),e.writeIntBE(t,0,5);else{if(!(t>=-0x800000000000&&t<0x800000000000))throw new Error("ASN1 syntax error: invalid Integer");e=Buffer.alloc(6),e.writeIntBE(t,0,6)}const r=new u(o.UNIVERSAL,s.INTEGER,e);return r._value=t,r}static parseInteger(t){if(!(t instanceof Buffer)||0===t.length)throw new Error("ASN1 syntax error: invalid Integer");return t.length>6?t.toString("hex"):t.readIntBE(0,t.length)}static parseIntegerNum(t){const e=u.parseInteger(t);if("number"!=typeof e)throw new Error("ASN1 syntax error: invalid Integer number");return e}static parseIntegerStr(t){const e=u.parseInteger(t);return"number"==typeof e?e.toString(16):e}static BitString(t){t instanceof Buffer&&(t=new a(t,8*t.length));const e=8*t.buf.length-t.bitLen,r=Buffer.alloc(t.buf.length+1);return r.writeInt8(e,0),t.buf.copy(r,1),new u(o.UNIVERSAL,s.BITSTRING,r)}static parseBitString(t){if(!(t instanceof Buffer)||0===t.length)throw new Error("ASN1 syntax error: invalid BitString");const e=t[0];if(e>7||1===t.length&&e>0||0!=(t[t.length-1]&(1<<t[0])-1))throw new Error("ASN1 syntax error: invalid padding bits in BIT STRING");return new a(t.slice(1),8*(t.length-1)-e)}static Null(){const t=new u(o.UNIVERSAL,s.NULL,Buffer.alloc(0));return t._value=null,t}static parseNull(t){if(!(t instanceof Buffer)||0!==t.length)throw new Error("ASN1 syntax error: invalid null");return null}static OID(t){const e=t.split(".");if(0===e.length)throw new Error("ASN1 syntax error: invalid Object Identifier");const r=[];r.push(40*l(e[0])+l(e[1]));const n=[];for(let t=2;t<e.length;++t){let i=l(e[t]);for(n.length=0,n.push(127&i);i>127;)i>>>=7,n.unshift(127&i|128);r.push(...n)}const i=new u(o.UNIVERSAL,s.OID,Buffer.from(r));return i._value=t,i}static parseOID(t){if(!(t instanceof Buffer)||0===t.length)throw new Error("ASN1 syntax error: invalid OID");let e=Math.floor(t[0]/40)+"."+t[0]%40,r=0;for(let n=1;n<t.length;n++)t[n]>=128?(r+=127&t[n],r<<=7):(e+="."+(r+t[n]),r=0);return e}static UTF8(t){const e=new u(o.UNIVERSAL,s.UTF8,Buffer.from(t,"utf8"));return e._value=t,e}static parseUTF8(t){if(!(t instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return t.toString("utf8")}static NumericString(t){if(!c(t))throw new Error("ASN1 syntax error: invalid NumericString");const e=new u(o.UNIVERSAL,s.NUMERICSTRING,Buffer.from(t,"utf8"));return e._value=t,e}static parseNumericString(t){if(!(t instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");const e=t.toString("utf8");if(!c(e))throw new Error("ASN1 syntax error: invalid NumericString");return e}static PrintableString(t){const e=new u(o.UNIVERSAL,s.PRINTABLESTRING,Buffer.from(t,"utf8"));return e._value=t,e}static parsePrintableString(t){if(!(t instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return t.toString("utf8")}static IA5String(t){if(!f(t))throw new Error("ASN1 syntax error: invalid IA5String");const e=new u(o.UNIVERSAL,s.IA5STRING,Buffer.from(t,"utf8"));return e._value=t,e}static parseIA5String(t){if(!(t instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");const e=t.toString("utf8");if(!f(e))throw new Error("ASN1 syntax error: invalid IA5String");return e}static T61String(t){const e=new u(o.UNIVERSAL,s.T61STRING,Buffer.from(t,"utf8"));return e._value=t,e}static parseT61String(t){if(!(t instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return t.toString("utf8")}static GeneralString(t){const e=new u(o.UNIVERSAL,s.GENERALSTRING,Buffer.from(t,"utf8"));return e._value=t,e}static parseGeneralString(t){if(!(t instanceof Buffer))throw new Error("parse ASN1 error: invalid Buffer");return t.toString("utf8")}static UTCTime(t){let e="";const r=[];r.push((""+t.getUTCFullYear()).substr(2)),r.push(""+(t.getUTCMonth()+1)),r.push(""+t.getUTCDate()),r.push(""+t.getUTCHours()),r.push(""+t.getUTCMinutes()),r.push(""+t.getUTCSeconds());for(const t of r)t.length<2&&(e+="0"),e+=t;e+="Z";const n=new u(o.UNIVERSAL,s.UTCTIME,Buffer.from(e,"utf8"));return n._value=t,n}static parseUTCTime(t){if(!(t instanceof Buffer)||0===t.length)throw new Error("ASN1 syntax error: invalid UTC Time");const e=t.toString("utf8"),r=new Date;let n=l(e.substr(0,2));n=n>=50?1900+n:2e3+n;const i=l(e.substr(2,2))-1,o=l(e.substr(4,2)),s=l(e.substr(6,2)),a=l(e.substr(8,2));let u=0,c=0,f="";if(e.length>11&&(c=10,f=e.charAt(c),"+"!==f&&"-"!==f&&(u=l(e.substr(10,2)),c+=2)),r.setUTCFullYear(n,i,o),r.setUTCHours(s,a,u,0),c>0&&(f=e.charAt(c),"+"===f||"-"===f)){let t=60*l(e.substr(c+1,2))+l(e.substr(c+4,2));t*=6e4,"+"===f?r.setTime(+r-t):r.setTime(+r+t)}return r}static GeneralizedTime(t){let e="";const r=[];r.push(""+t.getUTCFullYear()),r.push(""+(t.getUTCMonth()+1)),r.push(""+t.getUTCDate()),r.push(""+t.getUTCHours()),r.push(""+t.getUTCMinutes()),r.push(""+t.getUTCSeconds());for(const t of r)t.length<2&&(e+="0"),e+=t;e+="Z";const n=new u(o.UNIVERSAL,s.GENERALIZEDTIME,Buffer.from(e,"utf8"));return n._value=t,n}static parseGeneralizedTime(t){if(!(t instanceof Buffer)||0===t.length)throw new Error("ASN1 syntax error: invalid Generalized Time");const e=t.toString("utf8"),r=new Date,n=l(e.substr(0,4)),i=l(e.substr(4,2))-1,o=l(e.substr(6,2)),s=l(e.substr(8,2)),a=l(e.substr(10,2)),u=l(e.substr(12,2));let c=0,f=0,h=!1;"Z"===e.charAt(e.length-1)&&(h=!0);const d=e.length-5,p=e.charAt(d);if("+"===p||"-"===p){f=60*l(e.substr(d+1,2))+l(e.substr(d+4,2)),f*=6e4,"+"===p&&(f*=-1),h=!0}return"."===e.charAt(14)&&(c=1e3*parseFloat(e.substr(14))),h?(r.setUTCFullYear(n,i,o),r.setUTCHours(s,a,u,c),r.setTime(+r+f)):(r.setFullYear(n,i,o),r.setHours(s,a,u,c)),r}static parseTime(t,e){switch(t){case s.UTCTIME:return u.parseUTCTime(e);case s.GENERALIZEDTIME:return u.parseGeneralizedTime(e);default:throw new Error("Invalid ASN1 time tag")}}static Set(t){const e=new u(o.UNIVERSAL,s.SET,Buffer.concat(t.map(t=>t.toDER())));return e._value=t,e}static Seq(t){const e=new u(o.UNIVERSAL,s.SEQUENCE,Buffer.concat(t.map(t=>t.toDER())));return e._value=t,e}static Spec(t,e,r=!0){const n=Array.isArray(e)?Buffer.concat(e.map(t=>t.toDER())):e.toDER();Array.isArray(e)&&(r=!0);const i=new u(o.CONTEXT_SPECIFIC,t,n,r);return i._value=e,i}static fromDER(t,e=!1){return u._fromDER(new i.BufferVisitor(t),e)}static parseDER(t,e,r){const n=u._fromDER(new i.BufferVisitor(t),!1);if(n.class!==e&&n.tag!==r)throw new Error(`invalid ASN.1 DER for class ${e} and tag ${r}`);return n}static parseDERWithTemplate(t,e){const r=u._fromDER(new i.BufferVisitor(t),!0),n={},o=r.validate(e,n);if(null!=o)throw o.data=r,o;return n}static _parseCompound(t,e){const r=[],n=t.length,o=new i.BufferVisitor(t);let s=0;for(;s<n;){const t=o.end;r.push(u._fromDER(o,e)),s+=o.end-t}return r}static _fromDER(t,e){if(!(t.buf instanceof Buffer)||0===t.length)throw new Error("ASN1 syntax error: invalid Generalized Time");t.mustWalk(1,"Too few bytes to read ASN.1 tag.");const r=t.start,n=t.buf[r],i=192&n,o=31&n,a=function(t){t.mustWalk(1,"Too few bytes to read ASN.1 value length.");const e=t.buf[t.start];if(0==(128&e))return e;const r=127&e;return t.mustWalk(r,"Too few bytes to read ASN.1 value length."),t.buf.readUIntBE(t.start,r)}(t);if(t.mustHas(a),0!==a&&o===s.NULL)throw new Error("invalid value length or NULL tag.");t.mustWalk(a);const c=32==(32&n),f=new u(i,o,t.buf.slice(t.start,t.end),c);return c&&e&&(f._value=u._parseCompound(f.bytes,e)),f._der=t.buf.slice(r,t.end),f}constructor(t,e,r,n=!1){this.class=t,this.tag=e,this.bytes=r,this.isCompound=n||e===s.SEQUENCE||e===s.SET,this._value=void 0,this._der=null}get value(){return void 0===this._value&&(this._value=this.valueOf()),this._value}get DER(){return null==this._der&&(this._der=this.toDER()),this._der}mustCompound(t="asn1 object value is not compound"){if(!this.isCompound||!Array.isArray(this.value)){const e=new Error(t);throw e.data=this.toJSON(),e}return this.value}equals(t){return t instanceof u&&(this.class===t.class&&this.tag===t.tag&&this.isCompound===t.isCompound&&!!this.bytes.equals(t.bytes))}toDER(){let t=this.class|this.tag;this.isCompound&&(t|=32);const e=function(t){if(t<=127)return 0;if(t<=255)return 1;if(t<=65535)return 2;if(t<=16777215)return 3;if(t<=4294967295)return 4;if(t<=0xffffffffff)return 5;if(t<=0xffffffffffff)return 6;throw new Error("invalid value length")}(this.bytes.length),r=Buffer.allocUnsafe(2+e+this.bytes.length);return r.writeInt8(t,0),0===e?(r.writeUInt8(this.bytes.length,1),this.bytes.copy(r,2)):(r.writeUInt8(128|e,1),r.writeUIntBE(this.bytes.length,2,e),this.bytes.copy(r,2+e)),r}valueOf(){if(this.isCompound)return u._parseCompound(this.bytes,!1);if(this.class!==o.UNIVERSAL)return this.bytes;switch(this.tag){case s.BOOLEAN:return u.parseBool(this.bytes);case s.INTEGER:return u.parseInteger(this.bytes);case s.BITSTRING:return u.parseBitString(this.bytes);case s.NULL:return u.parseNull(this.bytes);case s.OID:return u.parseOID(this.bytes);case s.UTF8:return u.parseUTF8(this.bytes);case s.NUMERICSTRING:return u.parseNumericString(this.bytes);case s.PRINTABLESTRING:return u.parsePrintableString(this.bytes);case s.T61STRING:return u.parseT61String(this.bytes);case s.IA5STRING:return u.parseIA5String(this.bytes);case s.GENERALSTRING:return u.parseGeneralString(this.bytes);case s.UTCTIME:return u.parseUTCTime(this.bytes);case s.GENERALIZEDTIME:return u.parseGeneralizedTime(this.bytes);default:return this.bytes}}validate(t,e={}){if(this.class!==t.class)return new Error(`ASN.1 object validate failure for ${t.name} : error class ${o[this.class]}`);if(!(Array.isArray(t.tag)?t.tag:[t.tag]).includes(this.tag))return new Error(`ASN.1 object validate failure for ${t.name}: error tag ${s[this.tag]}`);if(null!=t.capture&&(e[t.capture]=this),Array.isArray(t.value)){const r=this.mustCompound(t.name+" need compound ASN1 value");for(let n=0,i=0;n<t.value.length;n++)if(null!=r[i]){const o=r[i].validate(t.value[n],e);if(null==o)i++;else if(!0!==t.value[n].optional)return o}else if(!0!==t.value[n].optional)return new Error(`ASN.1 object validate failure for ${t.value[n].name}: not exists`)}else if(null!=t.value){const r=this.tag===s.BITSTRING?this.bytes.slice(1):this.bytes;return u.fromDER(r).validate(t.value,e)}return null}toJSON(){let t=this.value;return Array.isArray(t)&&(t=t.map(t=>t.toJSON())),{class:o[this.class],tag:this.class===o.UNIVERSAL?s[this.tag]:this.tag,value:t}}[n.inspect.custom](t,e){return e.depth<=2&&(e.depth=10),`<${this.constructor.name} ${n.inspect(this.toJSON(),e)}>`}}function c(t){for(const e of t){const t=e.charCodeAt(0);if(32!==t&&(t<48||t>57))return!1}return!0}function f(t){for(const e of t)if(e.charCodeAt(0)>=128)return!1;return!0}function l(t,e=10){const r=parseInt(t,e);if(Number.isNaN(r))throw new Error(`Invalid numeric string "${t}" in radix ${e}.`);return r}e.ASN1=u},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(9),i=r(2),o=r(17),s=r(13),a=r(16),u=Object.create(null);u.CN=s.getOID("commonName"),u.commonName="CN",u.C=s.getOID("countryName"),u.countryName="C",u.L=s.getOID("localityName"),u.localityName="L",u.ST=s.getOID("stateOrProvinceName"),u.stateOrProvinceName="ST",u.O=s.getOID("organizationName"),u.organizationName="O",u.OU=s.getOID("organizationalUnitName"),u.organizationalUnitName="OU",u.E=s.getOID("emailAddress"),u.emailAddress="E";const c={name:"Certificate",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,capture:"tbsCertificate",value:[{name:"Certificate.TBSCertificate.version",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.NONE,optional:!0,value:[{name:"Certificate.TBSCertificate.version.integer",class:o.Class.UNIVERSAL,tag:o.Tag.INTEGER,capture:"certVersion"}]},{name:"Certificate.TBSCertificate.serialNumber",class:o.Class.UNIVERSAL,tag:o.Tag.INTEGER,capture:"certSerialNumber"},{name:"Certificate.TBSCertificate.signature",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate.signature.algorithm",class:o.Class.UNIVERSAL,tag:o.Tag.OID,capture:"certinfoSignatureOID"},{name:"Certificate.TBSCertificate.signature.parameters",class:o.Class.UNIVERSAL,tag:o.Tag.OCTETSTRING,optional:!0,capture:"certinfoSignatureParams"}]},{name:"Certificate.TBSCertificate.issuer",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,capture:"certIssuer"},{name:"Certificate.TBSCertificate.validity",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.TBSCertificate.validity.notBefore",class:o.Class.UNIVERSAL,tag:[o.Tag.UTCTIME,o.Tag.GENERALIZEDTIME],capture:"certValidityNotBefore"},{name:"Certificate.TBSCertificate.validity.notAfter",class:o.Class.UNIVERSAL,tag:[o.Tag.UTCTIME,o.Tag.GENERALIZEDTIME],capture:"certValidityNotAfter"}]},{name:"Certificate.TBSCertificate.subject",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,capture:"certSubject"},a.publicKeyValidator,{name:"Certificate.TBSCertificate.issuerUniqueID",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.BOOLEAN,optional:!0,value:[{name:"Certificate.TBSCertificate.issuerUniqueID.id",class:o.Class.UNIVERSAL,tag:o.Tag.BITSTRING,capture:"certIssuerUniqueId"}]},{name:"Certificate.TBSCertificate.subjectUniqueID",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.INTEGER,optional:!0,value:[{name:"Certificate.TBSCertificate.subjectUniqueID.id",class:o.Class.UNIVERSAL,tag:o.Tag.BITSTRING,capture:"certSubjectUniqueId"}]},{name:"Certificate.TBSCertificate.extensions",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.BITSTRING,capture:"certExtensions",optional:!0}]},{name:"Certificate.signatureAlgorithm",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"Certificate.signatureAlgorithm.algorithm",class:o.Class.UNIVERSAL,tag:o.Tag.OID,capture:"certSignatureOID"},{name:"Certificate.TBSCertificate.signature.parameters",class:o.Class.UNIVERSAL,tag:o.Tag.OCTETSTRING,optional:!0,capture:"certSignatureParams"}]},{name:"Certificate.signatureValue",class:o.Class.UNIVERSAL,tag:o.Tag.BITSTRING,capture:"certSignature"}]};class f{constructor(){this.attributes=[],this.uniqueId=null}get commonName(){return this.getFieldValue("commonName")}get organizationName(){return this.getFieldValue("organizationName")}get organizationalUnitName(){return this.getFieldValue("organizationalUnitName")}get countryName(){return this.getFieldValue("countryName")}get localityName(){return this.getFieldValue("localityName")}get serialName(){return this.getFieldValue("serialName")}getHash(){const t=i.createHash("sha1");for(const e of this.attributes)t.update(e.oid),t.update(e.value);return t.digest()}getField(t){for(const e of this.attributes)if(t===e.oid||t===e.name||t===e.shortName)return e;return null}addField(t){v([t]),this.attributes.push(t)}setAttrs(t){v(t),this.attributes=t}toJSON(){const t={};for(const e of this.attributes){const r=e.shortName;"string"==typeof r&&""!==r&&(t[r]=e.value)}return t.uniqueId=this.uniqueId,t.attributes=this.attributes,t}getFieldValue(t){const e=this.getField(t);return null!=e?e.value:""}}e.DistinguishedName=f;class l{static fromPEMs(t){const e=[],r=o.PEM.parse(t);for(const t of r){if("CERTIFICATE"!==t.type&&"X509 CERTIFICATE"!==t.type&&"TRUSTED CERTIFICATE"!==t.type)throw new Error("Could not convert certificate from PEM: invalid type");if(t.procType.includes("ENCRYPTED"))throw new Error("Could not convert certificate from PEM: PEM is encrypted.");const r=o.ASN1.fromDER(t.body);e.push(new l(r))}if(0===e.length)throw new Error("No Certificate");return e}static fromPEM(t){return l.fromPEMs(t)[0]}constructor(t){const e=Object.create(null),r=t.validate(c,e);if(null!=r)throw new Error("Cannot read X.509 certificate: "+r.message);if(this.raw=t.DER,this.version=null==e.certVersion?0:o.ASN1.parseIntegerNum(e.certVersion.bytes)+1,this.serialNumber=o.ASN1.parseIntegerStr(e.certSerialNumber.bytes),this.signatureOID=o.ASN1.parseOID(e.certSignatureOID.bytes),this.signatureAlgorithm=s.getOIDName(this.signatureOID),this.infoSignatureOID=o.ASN1.parseOID(e.certinfoSignatureOID.bytes),this.signature=o.ASN1.parseBitString(e.certSignature.bytes).buf,this.validFrom=o.ASN1.parseTime(e.certValidityNotBefore.tag,e.certValidityNotBefore.bytes),this.validTo=o.ASN1.parseTime(e.certValidityNotAfter.tag,e.certValidityNotAfter.bytes),this.issuer=new f,this.issuer.setAttrs(m(e.certIssuer)),null!=e.certIssuerUniqueId&&(this.issuer.uniqueId=o.ASN1.parseBitString(e.certIssuerUniqueId.bytes)),this.subject=new f,this.subject.setAttrs(m(e.certSubject)),null!=e.certSubjectUniqueId&&(this.subject.uniqueId=o.ASN1.parseBitString(e.certSubjectUniqueId.bytes)),this.extensions=[],this.subjectKeyIdentifier="",this.authorityKeyIdentifier="",this.ocspServer="",this.issuingCertificateURL="",this.isCA=!1,this.maxPathLen=-1,this.basicConstraintsValid=!1,this.keyUsage=0,this.dnsNames=[],this.emailAddresses=[],this.ipAddresses=[],this.uris=[],null!=e.certExtensions){this.extensions=function(t){const e=[];for(const r of t.mustCompound())for(const t of r.mustCompound())e.push(h(t));return e}(e.certExtensions);for(const t of this.extensions)if("string"==typeof t.subjectKeyIdentifier&&(this.subjectKeyIdentifier=t.subjectKeyIdentifier),"string"==typeof t.authorityKeyIdentifier&&(this.authorityKeyIdentifier=t.authorityKeyIdentifier),"string"==typeof t.authorityInfoAccessOcsp&&(this.ocspServer=t.authorityInfoAccessOcsp),"string"==typeof t.authorityInfoAccessIssuers&&(this.issuingCertificateURL=t.authorityInfoAccessIssuers),"boolean"==typeof t.basicConstraintsValid&&(this.isCA=t.isCA,this.maxPathLen=t.maxPathLen,this.basicConstraintsValid=t.basicConstraintsValid),"number"==typeof t.keyUsage&&(this.keyUsage=t.keyUsage),Array.isArray(t.altNames))for(const e of t.altNames)null!=e.dnsName&&this.dnsNames.push(e.dnsName),null!=e.email&&this.emailAddresses.push(e.email),null!=e.ip&&this.ipAddresses.push(e.ip),null!=e.uri&&this.uris.push(e.uri)}this.publicKey=new a.PublicKey(e.publicKeyInfo),this.publicKeyRaw=this.publicKey.toDER(),this.tbsCertificate=e.tbsCertificate}getExtension(t,e=""){for(const r of this.extensions)if(t===r.oid||t===r.name)return""===e?r:r[e];return null}checkSignature(t){if(3===this.version&&!this.basicConstraintsValid||this.basicConstraintsValid&&!this.isCA)return new Error("The parent constraint violation error");if(!0!==this.getExtension("keyUsage","keyCertSign"))return new Error("The parent constraint violation error");if(!t.isIssuer(this))return new Error("The parent certificate did not issue the given child certificate");const e=function(t){switch(s.getOIDName(t)){case"sha1WithRsaEncryption":return"sha1";case"md5WithRsaEncryption":return"md5";case"sha256WithRsaEncryption":return"sha256";case"sha384WithRsaEncryption":return"sha384";case"sha512WithRsaEncryption":return"sha512";case"RSASSA-PSS":return"sha256";case"ecdsaWithSha1":return"sha1";case"ecdsaWithSha256":return"sha256";case"ecdsaWithSha384":return"sha384";case"ecdsaWithSha512":return"sha512";case"dsaWithSha1":return"sha1";case"dsaWithSha256":return"sha256";default:return""}}(t.signatureOID);if(""===e)return new Error("Unknown child signature OID.");return!1===this.publicKey.verify(t.tbsCertificate.DER,t.signature,e)?new Error("Child signature not matched"):null}isIssuer(t){return this.issuer.getHash().equals(t.subject.getHash())}verifySubjectKeyIdentifier(){return this.publicKey.getFingerprint("sha1","PublicKey").toString("hex")===this.subjectKeyIdentifier}toJSON(){const t={};for(const e of Object.keys(this))t[e]=_(this[e]);return delete t.tbsCertificate,t}[n.inspect.custom](t,e){return e.depth<=2&&(e.depth=10),`<${this.constructor.name} ${n.inspect(this.toJSON(),e)}>`}}function h(t){const e={};switch(e.oid=o.ASN1.parseOID(t.value[0].bytes),e.critical=!1,t.value[1].tag===o.Tag.BOOLEAN?(e.critical=o.ASN1.parseBool(t.value[1].bytes),e.value=t.value[2].bytes):e.value=t.value[1].bytes,e.name=s.getOIDName(e.oid),e.name){case"keyUsage":!function(t){const e=o.ASN1.parseBitString(o.ASN1.fromDER(t.value).bytes);let r=0,n=0;t.keyUsage=0;for(let r=0;r<9;r++)0!==e.at(r)&&(t.keyUsage|=1<<r);e.buf.length>0&&(r=e.buf[0],n=e.buf.length>1?e.buf[1]:0);t.digitalSignature=128==(128&r),t.nonRepudiation=64==(64&r),t.keyEncipherment=32==(32&r),t.dataEncipherment=16==(16&r),t.keyAgreement=8==(8&r),t.keyCertSign=4==(4&r),t.cRLSign=2==(2&r),t.encipherOnly=1==(1&r),t.decipherOnly=128==(128&n)}(e);break;case"basicConstraints":!function(t){const e=o.ASN1.fromDER(t.value).mustCompound();e.length>0&&e[0].tag===o.Tag.BOOLEAN?t.isCA=o.ASN1.parseBool(e[0].bytes):t.isCA=!1;let r=null;e.length>0&&e[0].tag===o.Tag.INTEGER?r=e[0].bytes:e.length>1&&(r=e[1].bytes);t.maxPathLen=null!==r?o.ASN1.parseInteger(r):-1;t.basicConstraintsValid=!0}(e);break;case"extKeyUsage":!function(t){const e=o.ASN1.fromDER(t.value).mustCompound();for(const r of e)t[s.getOIDName(o.ASN1.parseOID(r.bytes))]=!0}(e);break;case"nsCertType":!function(t){const e=o.ASN1.parseBitString(o.ASN1.fromDER(t.value).bytes);let r=0;e.buf.length>0&&(r=e.buf[0]);t.client=128==(128&r),t.server=64==(64&r),t.email=32==(32&r),t.objsign=16==(16&r),t.reserved=8==(8&r),t.sslCA=4==(4&r),t.emailCA=2==(2&r),t.objCA=1==(1&r)}(e);break;case"subjectAltName":case"issuerAltName":d(e);break;case"subjectKeyIdentifier":!function(t){const e=o.ASN1.parseDERWithTemplate(t.value,p);t.subjectKeyIdentifier=e.subjectKeyIdentifier.bytes.toString("hex")}(e);break;case"authorityKeyIdentifier":!function(t){const e=o.ASN1.parseDERWithTemplate(t.value,y);t.authorityKeyIdentifier=e.authorityKeyIdentifier.bytes.toString("hex")}(e);break;case"authorityInfoAccess":!function(t){const e=o.ASN1.parseDERWithTemplate(t.value,g);null!=e.authorityInfoAccessOcsp&&(t.authorityInfoAccessOcsp=e.authorityInfoAccessOcsp.bytes.toString());null!=e.authorityInfoAccessIssuers&&(t.authorityInfoAccessIssuers=e.authorityInfoAccessIssuers.bytes.toString())}(e)}return e}function d(t){t.altNames=[];const e=o.ASN1.fromDER(t.value).mustCompound();for(const r of e){const e={tag:r.tag,value:r.bytes};switch(t.altNames.push(e),r.tag){case 1:e.email=r.bytes.toString();break;case 2:e.dnsName=r.bytes.toString();break;case 6:e.uri=r.bytes.toString();break;case 7:e.ip=s.bytesToIP(r.bytes);break;case 8:e.oid=o.ASN1.parseOID(r.bytes)}}}e.Certificate=l;const p={name:"subjectKeyIdentifier",class:o.Class.UNIVERSAL,tag:o.Tag.OCTETSTRING,capture:"subjectKeyIdentifier"};const y={name:"authorityKeyIdentifier",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"authorityKeyIdentifier.value",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.NONE,capture:"authorityKeyIdentifier"}]};const g={name:"authorityInfoAccess",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,value:[{name:"authorityInfoAccess.authorityInfoAccessOcsp",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,optional:!0,value:[{name:"authorityInfoAccess.authorityInfoAccessOcsp.oid",class:o.Class.UNIVERSAL,tag:o.Tag.OID},{name:"authorityInfoAccess.authorityInfoAccessOcsp.value",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.OID,capture:"authorityInfoAccessOcsp"}]},{name:"authorityInfoAccess.authorityInfoAccessIssuers",class:o.Class.UNIVERSAL,tag:o.Tag.SEQUENCE,optional:!0,value:[{name:"authorityInfoAccess.authorityInfoAccessIssuers.oid",class:o.Class.UNIVERSAL,tag:o.Tag.OID},{name:"authorityInfoAccess.authorityInfoAccessIssuers.value",class:o.Class.CONTEXT_SPECIFIC,tag:o.Tag.OID,capture:"authorityInfoAccessIssuers"}]}]};function v(t){for(const e of t){if(null!=e.name&&""!==e.name||(null!=e.oid&&(e.name=s.getOIDName(e.oid)),""===e.name&&null!=e.shortName&&(e.name=s.getOIDName(u[e.shortName]))),null==e.oid||""===e.oid){if(""===e.name)throw new Error("Attribute oid not specified.");e.oid=s.getOID(e.name)}if(null!=e.shortName&&""!==e.shortName||(e.shortName=null==u[e.name]?"":u[e.name]),null==e.value)throw new Error("Attribute value not specified.")}}function m(t){const e=[];for(const n of t.mustCompound())for(const t of n.mustCompound()){const n=t.mustCompound(),i={};i.oid=o.ASN1.parseOID(n[0].bytes),i.value=n[1].value,i.valueTag=n[1].tag,i.name=s.getOIDName(i.oid),i.shortName=(r=i.name,null==u[r]?"":u[r]),e.push(i)}var r;return e}function _(t){return null==t||t instanceof Buffer||"function"!=typeof t.toJSON?t:t.toJSON()}},function(t,e,r){t.exports={sm2:r(38),sm3:r(42),sm4:r(43)}},function(t,e,r){const{BigInteger:n}=r(10),{encodeDer:i,decodeDer:o}=r(39),s=r(40),a=r(19).sm3,{G:u,curve:c,n:f}=s.generateEcparam();function l(t,e,r="1234567812345678"){r=s.utf8ToHex(r);const n=s.leftPad(u.curve.a.toBigInteger().toRadix(16),64),i=s.leftPad(u.curve.b.toBigInteger().toRadix(16),64),o=s.leftPad(u.getX().toBigInteger().toRadix(16),64),c=s.leftPad(u.getY().toBigInteger().toRadix(16),64);let f,l;if(128===e.length)f=e.substr(0,64),l=e.substr(64,64);else{const t=u.curve.decodePointHex(e);f=s.leftPad(t.getX().toBigInteger().toRadix(16),64),l=s.leftPad(t.getY().toBigInteger().toRadix(16),64)}const h=s.hexToArray(r+n+i+o+c+f+l),d=4*r.length;h.unshift(255&d),h.unshift(d>>8&255);const p=a(h);return s.arrayToHex(a(p.concat(s.hexToArray(t))))}function h(t){const e=u.multiply(new n(t,16));return"04"+s.leftPad(e.getX().toBigInteger().toString(16),64)+s.leftPad(e.getY().toBigInteger().toString(16),64)}function d(){const t=s.generateKeyPairHex(),e=c.decodePointHex(t.publicKey);return t.k=new n(t.privateKey,16),t.x1=e.getX().toBigInteger(),t}t.exports={generateKeyPairHex:s.generateKeyPairHex,compressPublicKeyHex:s.compressPublicKeyHex,comparePublicKeyHex:s.comparePublicKeyHex,doEncrypt:function(t,e,r=1){t="string"==typeof t?s.hexToArray(s.utf8ToHex(t)):Array.prototype.slice.call(t),e=s.getGlobalCurve().decodePointHex(e);const i=s.generateKeyPairHex(),o=new n(i.privateKey,16);let u=i.publicKey;u.length>128&&(u=u.substr(u.length-128));const c=e.multiply(o),f=s.hexToArray(s.leftPad(c.getX().toBigInteger().toRadix(16),64)),l=s.hexToArray(s.leftPad(c.getY().toBigInteger().toRadix(16),64)),h=s.arrayToHex(a([].concat(f,t,l)));let d=1,p=0,y=[];const g=[].concat(f,l),v=()=>{y=a([...g,d>>24&255,d>>16&255,d>>8&255,255&d]),d++,p=0};v();for(let e=0,r=t.length;e<r;e++)p===y.length&&v(),t[e]^=255&y[p++];const m=s.arrayToHex(t);return 0===r?u+m+h:u+h+m},doDecrypt:function(t,e,r=1,{output:i="string"}={}){e=new n(e,16);let o=t.substr(128,64),u=t.substr(192);0===r&&(o=t.substr(t.length-64),u=t.substr(128,t.length-128-64));const c=s.hexToArray(u),f=s.getGlobalCurve().decodePointHex("04"+t.substr(0,128)).multiply(e),l=s.hexToArray(s.leftPad(f.getX().toBigInteger().toRadix(16),64)),h=s.hexToArray(s.leftPad(f.getY().toBigInteger().toRadix(16),64));let d=1,p=0,y=[];const g=[].concat(l,h),v=()=>{y=a([...g,d>>24&255,d>>16&255,d>>8&255,255&d]),d++,p=0};v();for(let t=0,e=c.length;t<e;t++)p===y.length&&v(),c[t]^=255&y[p++];return s.arrayToHex(a([].concat(l,c,h)))===o.toLowerCase()?"array"===i?c:s.arrayToUtf8(c):"array"===i?[]:""},doSignature:function(t,e,{pointPool:r,der:o,hash:a,publicKey:u,userId:c}={}){let p="string"==typeof t?s.utf8ToHex(t):s.arrayToHex(t);a&&(p=l(p,u=u||h(e),c));const y=new n(e,16),g=new n(p,16);let v=null,m=null,_=null;do{do{let t;t=r&&r.length?r.pop():d(),v=t.k,m=g.add(t.x1).mod(f)}while(m.equals(n.ZERO)||m.add(v).equals(f));_=y.add(n.ONE).modInverse(f).multiply(v.subtract(m.multiply(y))).mod(f)}while(_.equals(n.ZERO));return o?i(m,_):s.leftPad(m.toString(16),64)+s.leftPad(_.toString(16),64)},doVerifySignature:function(t,e,r,{der:i,hash:a,userId:h}={}){let d,p,y="string"==typeof t?s.utf8ToHex(t):s.arrayToHex(t);if(a&&(y=l(y,r,h)),i){const t=o(e);d=t.r,p=t.s}else d=new n(e.substring(0,64),16),p=new n(e.substring(64),16);const g=c.decodePointHex(r),v=new n(y,16),m=d.add(p).mod(f);if(m.equals(n.ZERO))return!1;const _=u.multiply(p).add(g.multiply(m)),b=v.add(_.getX().toBigInteger()).mod(f);return d.equals(b)},getPublicKeyFromPrivateKey:h,getPoint:d,verifyPublicKey:s.verifyPublicKey}},function(t,e,r){const{BigInteger:n}=r(10);class i{constructor(){this.tlv=null,this.t="00",this.l="00",this.v=""}getEncodedHex(){return this.tlv||(this.v=this.getValue(),this.l=this.getLength(),this.tlv=this.t+this.l+this.v),this.tlv}getLength(){const t=this.v.length/2;let e=t.toString(16);if(e.length%2==1&&(e="0"+e),t<128)return e;return(128+e.length/2).toString(16)+e}getValue(){return""}}class o extends i{constructor(t){super(),this.t="02",t&&(this.v=function(t){let e=t.toString(16);if("-"!==e[0])e.length%2==1?e="0"+e:e.match(/^[0-7]/)||(e="00"+e);else{e=e.substr(1);let r=e.length;r%2==1?r+=1:e.match(/^[0-7]/)||(r+=2);let i="";for(let t=0;t<r;t++)i+="f";i=new n(i,16),e=i.xor(t).add(n.ONE),e=e.toString(16).replace(/^-/,"")}return e}(t))}getValue(){return this.v}}class s extends i{constructor(t){super(),this.t="30",this.asn1Array=t}getValue(){return this.v=this.asn1Array.map(t=>t.getEncodedHex()).join(""),this.v}}function a(t,e){return+t[e+2]<8?1:128&+t.substr(e+2,2)}function u(t,e){const r=a(t,e),i=t.substr(e+2,2*r);if(!i)return-1;return(+i[0]<8?new n(i,16):new n(i.substr(2),16)).intValue()}function c(t,e){return e+2*(a(t,e)+1)}t.exports={encodeDer(t,e){const r=new o(t),n=new o(e);return new s([r,n]).getEncodedHex()},decodeDer(t){const e=c(t,0),r=c(t,e),i=u(t,e),o=t.substr(r,2*i),s=r+o.length,a=c(t,s),f=u(t,s),l=t.substr(a,2*f);return{r:new n(o,16),s:new n(l,16)}}}},function(t,e,r){const{BigInteger:n,SecureRandom:i}=r(10),{ECCurveFp:o}=r(41),s=new i,{curve:a,G:u,n:c}=f();function f(){const t=new n("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFF",16),e=new n("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF00000000FFFFFFFFFFFFFFFC",16),r=new n("28E9FA9E9D9F5E344D5A9E4BCF6509A7F39789F515AB8F92DDBCBD414D940E93",16),i=new o(t,e,r),s=i.decodePointHex("0432C4AE2C1F1981195F9904466A39C9948FE30BBFF2660BE1715A4589334C74C7BC3736A2F4F6779C59BDCEE36B692153D0A9877CC62A474002DF32E52139F0A0");return{curve:i,G:s,n:new n("FFFFFFFEFFFFFFFFFFFFFFFFFFFFFFFF7203DF6B21C6052B53BBF40939D54123",16)}}function l(t,e){return t.length>=e?t:new Array(e-t.length+1).join("0")+t}t.exports={getGlobalCurve:function(){return a},generateEcparam:f,generateKeyPairHex:function(t,e,r){const i=(t?new n(t,e,r):new n(c.bitLength(),s)).mod(c.subtract(n.ONE)).add(n.ONE),o=l(i.toString(16),64),a=u.multiply(i);return{privateKey:o,publicKey:"04"+l(a.getX().toBigInteger().toString(16),64)+l(a.getY().toBigInteger().toString(16),64)}},compressPublicKeyHex:function(t){if(130!==t.length)throw new Error("Invalid public key to compress");const e=(t.length-2)/2,r=t.substr(2,e);let i="03";return new n(t.substr(e+2,e),16).mod(new n("2")).equals(n.ZERO)&&(i="02"),i+r},utf8ToHex:function(t){const e=(t=unescape(encodeURIComponent(t))).length,r=[];for(let n=0;n<e;n++)r[n>>>2]|=(255&t.charCodeAt(n))<<24-n%4*8;const n=[];for(let t=0;t<e;t++){const e=r[t>>>2]>>>24-t%4*8&255;n.push((e>>>4).toString(16)),n.push((15&e).toString(16))}return n.join("")},leftPad:l,arrayToHex:function(t){return t.map(t=>1===(t=t.toString(16)).length?"0"+t:t).join("")},arrayToUtf8:function(t){const e=[];let r=0;for(let n=0;n<2*t.length;n+=2)e[n>>>3]|=parseInt(t[r],10)<<24-n%8*4,r++;try{const r=[];for(let n=0;n<t.length;n++){const t=e[n>>>2]>>>24-n%4*8&255;r.push(String.fromCharCode(t))}return decodeURIComponent(escape(r.join("")))}catch(t){throw new Error("Malformed UTF-8 data")}},hexToArray:function(t){const e=[];let r=t.length;r%2!=0&&(t=l(t,r+1)),r=t.length;for(let n=0;n<r;n+=2)e.push(parseInt(t.substr(n,2),16));return e},verifyPublicKey:function(t){const e=a.decodePointHex(t);if(!e)return!1;const r=e.getX();return e.getY().square().equals(r.multiply(r.square()).add(r.multiply(a.a)).add(a.b))},comparePublicKeyHex:function(t,e){const r=a.decodePointHex(t);if(!r)return!1;const n=a.decodePointHex(e);return!!n&&r.equals(n)}}},function(t,e,r){const{BigInteger:n}=r(10),i=new n("2"),o=new n("3");class s{constructor(t,e){this.x=e,this.q=t}equals(t){return t===this||this.q.equals(t.q)&&this.x.equals(t.x)}toBigInteger(){return this.x}negate(){return new s(this.q,this.x.negate().mod(this.q))}add(t){return new s(this.q,this.x.add(t.toBigInteger()).mod(this.q))}subtract(t){return new s(this.q,this.x.subtract(t.toBigInteger()).mod(this.q))}multiply(t){return new s(this.q,this.x.multiply(t.toBigInteger()).mod(this.q))}divide(t){return new s(this.q,this.x.multiply(t.toBigInteger().modInverse(this.q)).mod(this.q))}square(){return new s(this.q,this.x.square().mod(this.q))}}class a{constructor(t,e,r,i){this.curve=t,this.x=e,this.y=r,this.z=null==i?n.ONE:i,this.zinv=null}getX(){return null===this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.x.toBigInteger().multiply(this.zinv).mod(this.curve.q))}getY(){return null===this.zinv&&(this.zinv=this.z.modInverse(this.curve.q)),this.curve.fromBigInteger(this.y.toBigInteger().multiply(this.zinv).mod(this.curve.q))}equals(t){if(t===this)return!0;if(this.isInfinity())return t.isInfinity();if(t.isInfinity())return this.isInfinity();if(!t.y.toBigInteger().multiply(this.z).subtract(this.y.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(n.ZERO))return!1;return t.x.toBigInteger().multiply(this.z).subtract(this.x.toBigInteger().multiply(t.z)).mod(this.curve.q).equals(n.ZERO)}isInfinity(){return null===this.x&&null===this.y||this.z.equals(n.ZERO)&&!this.y.toBigInteger().equals(n.ZERO)}negate(){return new a(this.curve,this.x,this.y.negate(),this.z)}add(t){if(this.isInfinity())return t;if(t.isInfinity())return this;const e=this.x.toBigInteger(),r=this.y.toBigInteger(),i=this.z,o=t.x.toBigInteger(),s=t.y.toBigInteger(),u=t.z,c=this.curve.q,f=e.multiply(u).mod(c),l=o.multiply(i).mod(c),h=f.subtract(l),d=r.multiply(u).mod(c),p=s.multiply(i).mod(c),y=d.subtract(p);if(n.ZERO.equals(h))return n.ZERO.equals(y)?this.twice():this.curve.infinity;const g=f.add(l),v=i.multiply(u).mod(c),m=h.square().mod(c),_=h.multiply(m).mod(c),b=v.multiply(y.square()).subtract(g.multiply(m)).mod(c),w=h.multiply(b).mod(c),S=y.multiply(m.multiply(f).subtract(b)).subtract(d.multiply(_)).mod(c),E=_.multiply(v).mod(c);return new a(this.curve,this.curve.fromBigInteger(w),this.curve.fromBigInteger(S),E)}twice(){if(this.isInfinity())return this;if(!this.y.toBigInteger().signum())return this.curve.infinity;const t=this.x.toBigInteger(),e=this.y.toBigInteger(),r=this.z,n=this.curve.q,i=this.curve.a.toBigInteger(),s=t.square().multiply(o).add(i.multiply(r.square())).mod(n),u=e.shiftLeft(1).multiply(r).mod(n),c=e.square().mod(n),f=c.multiply(t).multiply(r).mod(n),l=u.square().mod(n),h=s.square().subtract(f.shiftLeft(3)).mod(n),d=u.multiply(h).mod(n),p=s.multiply(f.shiftLeft(2).subtract(h)).subtract(l.shiftLeft(1).multiply(c)).mod(n),y=u.multiply(l).mod(n);return new a(this.curve,this.curve.fromBigInteger(d),this.curve.fromBigInteger(p),y)}multiply(t){if(this.isInfinity())return this;if(!t.signum())return this.curve.infinity;const e=t.multiply(o),r=this.negate();let n=this;for(let i=e.bitLength()-2;i>0;i--){n=n.twice();const o=e.testBit(i);o!==t.testBit(i)&&(n=n.add(o?this:r))}return n}}t.exports={ECPointFp:a,ECCurveFp:class{constructor(t,e,r){this.q=t,this.a=this.fromBigInteger(e),this.b=this.fromBigInteger(r),this.infinity=new a(this,null,null)}equals(t){return t===this||this.q.equals(t.q)&&this.a.equals(t.a)&&this.b.equals(t.b)}fromBigInteger(t){return new s(this.q,t)}decodePointHex(t){switch(parseInt(t.substr(0,2),16)){case 0:return this.infinity;case 2:case 3:const e=this.fromBigInteger(new n(t.substr(2),16));let r=this.fromBigInteger(e.multiply(e.square()).add(e.multiply(this.a)).add(this.b).toBigInteger().modPow(this.q.divide(new n("4")).add(n.ONE),this.q));return r.toBigInteger().mod(i).equals(new n(t.substr(0,2),16).subtract(i))||(r=r.negate()),new a(this,e,r);case 4:case 6:case 7:const o=(t.length-2)/2,s=t.substr(2,o),u=t.substr(o+2,o);return new a(this,this.fromBigInteger(new n(s,16)),this.fromBigInteger(new n(u,16)));default:return null}}}}},function(t,e,r){const{sm3:n,hmac:i}=r(19);function o(t){return t.map(t=>1===(t=t.toString(16)).length?"0"+t:t).join("")}function s(t){const e=[];let r=t.length;var n,i;r%2!=0&&(i=r+1,t=(n=t).length>=i?n:new Array(i-n.length+1).join("0")+n),r=t.length;for(let n=0;n<r;n+=2)e.push(parseInt(t.substr(n,2),16));return e}t.exports=function(t,e){if(t="string"==typeof t?function(t){const e=[];for(let r=0,n=t.length;r<n;r++){const n=t.codePointAt(r);if(n<=127)e.push(n);else if(n<=2047)e.push(192|n>>>6),e.push(128|63&n);else if(n<=55295||n>=57344&&n<=65535)e.push(224|n>>>12),e.push(128|n>>>6&63),e.push(128|63&n);else{if(!(n>=65536&&n<=1114111))throw e.push(n),new Error("input is not supported");r++,e.push(240|n>>>18&28),e.push(128|n>>>12&63),e.push(128|n>>>6&63),e.push(128|63&n)}}return e}(t):Array.prototype.slice.call(t),e){if("hmac"!==(e.mode||"hmac"))throw new Error("invalid mode");let r=e.key;if(!r)throw new Error("invalid key");return r="string"==typeof r?s(r):Array.prototype.slice.call(r),o(i(t,r))}return o(n(t))}},function(t,e){const r=[214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72],n=[462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257];function i(t){const e=[];for(let r=0,n=t.length;r<n;r+=2)e.push(parseInt(t.substr(r,2),16));return e}function o(t,e){const r=31&e;return t<<r|t>>>32-r}function s(t){return(255&r[t>>>24&255])<<24|(255&r[t>>>16&255])<<16|(255&r[t>>>8&255])<<8|255&r[255&t]}function a(t){return t^o(t,2)^o(t,10)^o(t,18)^o(t,24)}function u(t){return t^o(t,13)^o(t,23)}function c(t,e,r){const n=new Array(4),i=new Array(4);for(let e=0;e<4;e++)i[0]=255&t[4*e],i[1]=255&t[4*e+1],i[2]=255&t[4*e+2],i[3]=255&t[4*e+3],n[e]=i[0]<<24|i[1]<<16|i[2]<<8|i[3];for(let t,e=0;e<32;e+=4)t=n[1]^n[2]^n[3]^r[e+0],n[0]^=a(s(t)),t=n[2]^n[3]^n[0]^r[e+1],n[1]^=a(s(t)),t=n[3]^n[0]^n[1]^r[e+2],n[2]^=a(s(t)),t=n[0]^n[1]^n[2]^r[e+3],n[3]^=a(s(t));for(let t=0;t<16;t+=4)e[t]=n[3-t/4]>>>24&255,e[t+1]=n[3-t/4]>>>16&255,e[t+2]=n[3-t/4]>>>8&255,e[t+3]=255&n[3-t/4]}function f(t,e,r,{padding:o="pkcs#7",mode:a,iv:f=[],output:l="string"}={}){if("cbc"===a&&("string"==typeof f&&(f=i(f)),16!==f.length))throw new Error("iv is invalid");if("string"==typeof e&&(e=i(e)),16!==e.length)throw new Error("key is invalid");if(t="string"==typeof t?0!==r?function(t){const e=[];for(let r=0,n=t.length;r<n;r++){const n=t.codePointAt(r);if(n<=127)e.push(n);else if(n<=2047)e.push(192|n>>>6),e.push(128|63&n);else if(n<=55295||n>=57344&&n<=65535)e.push(224|n>>>12),e.push(128|n>>>6&63),e.push(128|63&n);else{if(!(n>=65536&&n<=1114111))throw e.push(n),new Error("input is not supported");r++,e.push(240|n>>>18&28),e.push(128|n>>>12&63),e.push(128|n>>>6&63),e.push(128|63&n)}}return e}(t):i(t):[...t],("pkcs#5"===o||"pkcs#7"===o)&&0!==r){const e=16-t.length%16;for(let r=0;r<e;r++)t.push(e)}const h=new Array(32);!function(t,e,r){const i=new Array(4),o=new Array(4);for(let e=0;e<4;e++)o[0]=255&t[0+4*e],o[1]=255&t[1+4*e],o[2]=255&t[2+4*e],o[3]=255&t[3+4*e],i[e]=o[0]<<24|o[1]<<16|o[2]<<8|o[3];i[0]^=2746333894,i[1]^=1453994832,i[2]^=1736282519,i[3]^=2993693404;for(let t,r=0;r<32;r+=4)t=i[1]^i[2]^i[3]^n[r+0],e[r+0]=i[0]^=u(s(t)),t=i[2]^i[3]^i[0]^n[r+1],e[r+1]=i[1]^=u(s(t)),t=i[3]^i[0]^i[1]^n[r+2],e[r+2]=i[2]^=u(s(t)),t=i[0]^i[1]^i[2]^n[r+3],e[r+3]=i[3]^=u(s(t));if(0===r)for(let t,r=0;r<16;r++)t=e[r],e[r]=e[31-r],e[31-r]=t}(e,h,r);const d=[];let p=f,y=t.length,g=0;for(;y>=16;){const e=t.slice(g,g+16),n=new Array(16);if("cbc"===a)for(let t=0;t<16;t++)0!==r&&(e[t]^=p[t]);c(e,n,h);for(let t=0;t<16;t++)"cbc"===a&&0===r&&(n[t]^=p[t]),d[g+t]=n[t];"cbc"===a&&(p=0!==r?n:e),y-=16,g+=16}if(("pkcs#5"===o||"pkcs#7"===o)&&0===r){const t=d.length,e=d[t-1];for(let r=1;r<=e;r++)if(d[t-r]!==e)throw new Error("padding is invalid");d.splice(t-e,e)}return"array"!==l?0!==r?d.map(t=>1===(t=t.toString(16)).length?"0"+t:t).join(""):function(t){const e=[];for(let r=0,n=t.length;r<n;r++)t[r]>=240&&t[r]<=247?(e.push(String.fromCodePoint(((7&t[r])<<18)+((63&t[r+1])<<12)+((63&t[r+2])<<6)+(63&t[r+3]))),r+=3):t[r]>=224&&t[r]<=239?(e.push(String.fromCodePoint(((15&t[r])<<12)+((63&t[r+1])<<6)+(63&t[r+2]))),r+=2):t[r]>=192&&t[r]<=223?(e.push(String.fromCodePoint(((31&t[r])<<6)+(63&t[r+1]))),r++):e.push(String.fromCodePoint(t[r]));return e.join("")}(d):d}t.exports={encrypt:(t,e,r)=>f(t,e,1,r),decrypt:(t,e,r)=>f(t,e,0,r)}},function(t,e,r){"use strict";function n(t,e){e.forEach(e=>{void 0!==t[e]&&(t[e]=Number(t[e]))})}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i={unifiedOrder:{args:{_purify:{shouldDelete:["subject"]}}},getOrderInfo:{args:{_purify:{shouldDelete:["subject"]}}},orderQuery:{returnValue:function(t){n(t,["cashFee","totalFee","couponCount"]),t.couponList=[];const e=t.couponCount||0;for(let r=0;r<e;r++)t.couponList.push({couponId:t["couponId"+r],couponType:t["couponType"+r],couponFee:Number(t["couponFee"+r])}),delete t["couponId"+r],delete t["couponType"+r],delete t["couponFee"+r];return t}},refund:{returnValue:function(t){n(t,["refundFee","settlementRefundFee","totalFee","settlementTotalFee","cashFee","cashRefundFee","couponRefundFee","couponRefundCount"]),t.couponList=[];const e=t.couponRefundCount||0;for(let r=0;r<e;r++)t.couponList.push({couponRefundId:t["couponRefundId"+r],couponType:t["couponType"+r],couponRefundFee:Number(t["couponRefundFee"+r])}),delete t["couponRefundId"+r],delete t["couponType"+r],delete t["couponRefundFee"+r];return t}},refundQuery:{returnValue:function(t){n(t,["totalFee","refundFee","settlementTotalFee","cashFee","refundCount"]),t.refundList=[];for(let e=0;e<t.refundCount;e++){t["refundFee"+e]=Number(t["refundFee"+e]),t["couponRefundFee"+e]=Number(t["couponRefundFee"+e]),t["settlementRefundFee"+e]=Number(t["settlementRefundFee"+e]);const r=Number(t["couponRefundCount"+e])||0,n={outRefundNo:t["outRefundNo"+e],refundId:t["refundId"+e],refundChannel:t["refundChannel"+e],refundFee:Number(t["refundFee"+e]),settlementRefundFee:Number(t["settlementRefundFee"+e]),couponRefundFee:Number(t["couponRefundFee"+e]),couponRefundCount:r,refundStatus:t["refundStatus"+e],refundAccount:t["refundAccount"+e],refundRecvAccout:t["refundRecvAccout"+e],refundSuccessTime:t["refundSuccessTime"+e],couponList:[]};delete t["outRefundNo"+e],delete t["refundId"+e],delete t["refundChannel"+e],delete t["refundFee"+e],delete t["settlementRefundFee"+e],delete t["couponRefundFee"+e],delete t["couponRefundCount"+e],delete t["refundStatus"+e],delete t["refundAccount"+e],delete t["refundRecvAccout"+e],delete t["refundSuccessTime"+e];for(let i=0;i<r;i++)n.couponList.push({couponRefundId:t[`couponRefundId${e}${i}`],couponType:t[`couponType${e}${i}`],couponRefundFee:Number(t[`couponRefundId${e}${i}`])}),delete t[`couponRefundId${e}${i}`],delete t[`couponType${e}${i}`],delete t[`couponRefundFee${e}${i}`];t.refundList.push(n)}return t}},verifyPaymentNotify:{returnValue:function(t){n(t,["cashFee","totalFee","couponCount"]);const e=t.couponCount||0;t.couponList=[];for(let r=0;r<e;r++)t.couponList.push({couponId:t["couponId"+r],couponType:t["couponType"+r],couponFee:Number(t["couponFee"+r])}),delete t["couponId"+r],delete t["couponType"+r],delete t["couponFee"+r];return t}},verifyRefundNotify:{returnValue:function(t){return n(t,["refundFee","settlementRefundFee","settlementTotalFee","totalFee"]),t}},downloadBill:{args:{billDate:t=>t.billDate.replace(/-/g,"")}},downloadFundflow:{args:{billDate:t=>t.billDate.replace(/-/g,"")}},codepay:{args:{_purify:{shouldDelete:["subject","tradeType","notifyUrl","store_id"]}},returnValue:t=>({tradeState:t.errCode,tradeStateDesc:t.errCodeDes})}};e.default=i,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(3),i=c(r(2)),o=c(r(46)),s=c(r(47)),a=c(r(92)),u=r(22);function c(t){return t&&t.__esModule?t:{default:t}}const f={RSA:"RSA-SHA1",RSA2:"RSA-SHA256"};class l extends s.default{constructor(t){t.sandbox&&(t.gateway="https://openapi-sandbox.dl.alipaydev.com/gateway.do"),super(t=Object.assign({gateway:"https://openapi.alipay.com/gateway.do",timeout:5e3,charset:"utf-8",version:"1.0",signType:"RSA2",timeOffset:8,keyType:"PKCS8"},t)),this.options=t,this._protocols=a.default}async _request(t,e){const r={};e.notifyUrl&&(r.notifyUrl=e.notifyUrl,delete e.notifyUrl),["alipay.trade.create","alipay.trade.precreate"].indexOf(t)>-1&&(e=function(t){try{const e=Buffer.from("ZXh0ZW5kUGFyYW1z","base64").toString("utf-8"),r=Buffer.from("ZXh0ZW5kX3BhcmFtcw==","base64").toString("utf-8");if(void 0===t[e]&&void 0===t[r]){const r=Buffer.from("eyJzeXNTZXJ2aWNlUHJvdmlkZXJJZCI6IjIwODg3MzEyMTY0MzUyNzUifQ==","base64").toString("utf-8"),n=JSON.parse(r);return t[e]=n,t}}catch(e){return t}return t}(e)),this.options.appAuthToken&&(r.appAuthToken=this.options.appAuthToken),r.bizContent=e;return await this.exec(t,r,{validateSign:!0})}async unifiedOrder(t){return await this._request("alipay.trade.create",Object.assign({sellerId:this.options.mchId},t))}_getSign(t,e){return(0,u.sign)(t,e,this.config)}formatUrl(t,e){let r=t;const n=["app_id","method","format","charset","sign_type","sign","timestamp","version","notify_url","return_url","auth_token","app_auth_token","app_cert_sn","alipay_root_cert_sn","appCertSn","alipayRootCertSn"];for(const t in e)if(n.indexOf(t)>-1){const n=encodeURIComponent(e[t]);r=`${r}${r.includes("?")?"&":"?"}${t}=${n}`,delete e[t]}return{execParams:e,url:r}}async getOrderInfo(t){let e;if(t.tradeType)e=t.tradeType,delete t.tradeType;else switch(this.options.clientType){case"app-plus":case"app":e="APP";break;case"mp-alipay":default:e="JSAPI"}switch(e){case"APP":{delete t.buyerId;const e={};t.notifyUrl&&(e.notifyUrl=t.notifyUrl,delete t.notifyUrl),e.bizContent=t;const r=this._getSign("alipay.trade.app.pay",e),{url:n,execParams:i}=this.formatUrl("",r);return(n+"&biz_content="+encodeURIComponent(i.biz_content)).substr(1)}case"JSAPI":{const e=await this.unifiedOrder(t);if(!e.tradeNo)throw new Error("获取支付宝交易号失败，详细信息为："+JSON.stringify(e));return e.tradeNo}case"NATIVE":return await this._request("alipay.trade.precreate",Object.assign({sellerId:this.options.mchId},t));default:throw new Error("不支持的支付类型，支付宝支付下单仅支持App、支付宝小程序、网站二维码支付")}}async orderQuery(t){return await this._request("alipay.trade.query",t)}async cancelOrder(t){return await this._request("alipay.trade.cancel",t)}async closeOrder(t){return await this._request("alipay.trade.close",t)}async refund(t){return await this._request("alipay.trade.refund",t)}async refundQuery(t){return await this._request("alipay.trade.fastpay.refund.query",t)}async codepay(t){return await this._request("alipay.trade.pay",Object.assign({sellerId:this.options.mchId},t))}notifyRSACheck(t,e,r){const n=Object.keys(t).sort().filter(t=>t).map(e=>{let r=t[e];return"[object String]"!==Array.prototype.toString.call(r)&&(r=JSON.stringify(r)),`${e}=${r}`}).join("&");return i.default.createVerify(f[r]).update(n,"utf8").verify(this.config.alipayPublicKey,e,"base64")}_getNotifyData(t){if(!t.headers)throw new Error("通知格式不正确");let e;for(const r in t.headers)"content-type"===r.toLowerCase()&&(e=t.headers[r]);if(!1!==t.isBase64Encoded&&-1===e.indexOf("application/x-www-form-urlencoded"))throw new Error("通知格式不正确");return o.default.parse(t.body)}_verifyNotify(t){const e=this._getNotifyData(t);return!!this.checkNotifySign(e)&&(0,n.snake2camelJson)(e)}verifyPaymentNotify(t){return"payment"===this.checkNotifyType(t)&&this._verifyNotify(t)}verifyRefundNotify(t){return"refund"===this.checkNotifyType(t)&&this._verifyNotify(t)}checkNotifyType(t){return"refund_fee"in this._getNotifyData(t)?"refund":"payment"}async queryAccountBalance(t={}){return(t=(0,n.snake2camelJson)(t)).accountType||(t.accountType="ACCTRANS_ACCOUNT"),await this._request("alipay.fund.account.query",t)}async request(t){const{data:e={},method:r}=t;return await this._request(r,e)}}e.default=l,t.exports=e.default},function(t,e){t.exports=require("querystring")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(8),i=r(48),o=r(2),s=r(49),a=r(50),u=r(51),c=r(53),f=r(54),l=r(21),h=r(22),d=r(89),p=r(91);e.default=class{constructor(t){if(!t.appId)throw Error("config.appId is required");if(!t.privateKey)throw Error("config.privateKey is required");const e="PKCS8"===t.keyType?"PRIVATE KEY":"RSA PRIVATE KEY";t.privateKey=this.formatKey(t.privateKey,e),t.appCertPath||t.appCertContent?(t.appCertSn=i.empty(t.appCertContent)?d.getSNFromPath(t.appCertPath,!1):d.getSN(t.appCertContent,!1),t.alipayCertSn=i.empty(t.alipayPublicCertContent)?d.getSNFromPath(t.alipayPublicCertPath,!1):d.getSN(t.alipayPublicCertContent,!1),t.alipayRootCertSn=i.empty(t.alipayRootCertContent)?d.getSNFromPath(t.alipayRootCertPath,!0):d.getSN(t.alipayRootCertContent,!0),t.alipayPublicKey=i.empty(t.alipayPublicCertContent)?d.loadPublicKeyFromPath(t.alipayPublicCertPath):d.loadPublicKey(t.alipayPublicCertContent),t.alipayPublicKey=this.formatKey(t.alipayPublicKey,"PUBLIC KEY")):t.alipayPublicKey&&(t.alipayPublicKey=this.formatKey(t.alipayPublicKey,"PUBLIC KEY")),this.config=Object.assign({urllib:s,gateway:"https://openapi.alipay.com/gateway.do",timeout:5e3,camelcase:!0,signType:"RSA2",charset:"utf-8",version:"1.0"},f(t,{deep:!0})),this.sdkVersion="alipay-sdk-nodejs-"+p.version}formatKey(t,e){const r=t.split("\n").map(t=>t.trim());return r[0].includes(e)&&r.shift(),r[r.length-1].includes(e)&&r.pop(),`-----BEGIN ${e}-----\n${r.join("")}\n-----END ${e}-----`}formatUrl(t,e){let r=t;const n=["app_id","method","format","charset","sign_type","sign","timestamp","version","notify_url","return_url","auth_token","app_auth_token","app_cert_sn","alipay_root_cert_sn","ws_service_url"];for(const t in e)if(n.indexOf(t)>-1){const n=encodeURIComponent(e[t]);r=`${r}${r.includes("?")?"&":"?"}${t}=${n}`,delete e[t]}return{execParams:e,url:r}}multipartExec(t,e={}){const r=this.config;let o={},s={};const d=e.log&&i.fn(e.log.info)?e.log.info:null,p=e.log&&i.fn(e.log.error)?e.log.error:null;e.formData.getFields().forEach(t=>{o[t.name]=t.value,s[t.name]=t.value}),o=f(o,{deep:!0}),s=l(s),e.formData.getFiles().forEach(t=>{const e=c(t.fieldName);s[e]=u.isValid(t.path)?a(t.path):n.createReadStream(t.path)});const y=h.sign(t,o,r),{url:g}=this.formatUrl(r.gateway,y);return d&&d("[AlipaySdk]start exec url: %s, method: %s, params: %s",g,t,JSON.stringify(o)),new Promise((n,i)=>{a.post({url:g,formData:s,json:!1,timeout:r.timeout,headers:{"user-agent":this.sdkVersion}},(o,s,a)=>{if(o)return o.message="[AlipaySdk]exec error",p&&p(o),i(o);d&&d("[AlipaySdk]exec response: %s",a);try{let o,s;const u=JSON.parse(a);if(s=t.replace(/\./g,"_")+"_response",o=u[s],o){return!e.validateSign||this.checkResponseSign(a,s)?n(r.camelcase?f(o,{deep:!0}):o):i({serverResult:a,errorMessage:"[AlipaySdk]验签失败"})}}catch(t){return i({serverResult:a,errorMessage:"[AlipaySdk]Response 格式错误"})}return i({serverResult:a,errorMessage:"[AlipaySdk]HTTP 请求错误"})})})}pageExec(t,e={}){let r={alipaySdk:this.sdkVersion};const n=this.config,o=e.log&&i.fn(e.log.info)?e.log.info:null;e.formData.getFields().forEach(t=>{r[t.name]=t.value}),r=f(r,{deep:!0});const s=h.sign(t,r,n),{url:a,execParams:u}=this.formatUrl(n.gateway,s);return o&&o("[AlipaySdk]start exec url: %s, method: %s, params: %s",a,t,JSON.stringify(r)),"get"===e.formData.getMethod()?new Promise(t=>{const e=Object.keys(u).map(t=>`${t}=${encodeURIComponent(u[t])}`);t(`${a}&${e.join("&")}`)}):new Promise(t=>{const e="alipaySDKSubmit"+Date.now();t(`\n        <form action="${a}" method="post" name="${e}" id="${e}">\n          ${Object.keys(u).map(t=>`<input type="hidden" name="${t}" value="${String(u[t]).replace(/\"/g,"&quot;")}" />`).join("")}\n        </form>\n        <script>document.forms["${e}"].submit();<\/script>\n      `)})}notifyRSACheck(t,e,r,n){const i=Object.keys(t).sort().filter(t=>t).map(e=>{let r=t[e];return"[object String]"!==Array.prototype.toString.call(r)&&(r=JSON.stringify(r)),n?`${e}=${r}`:`${e}=${decodeURIComponent(r)}`}).join("&");return o.createVerify(h.ALIPAY_ALGORITHM_MAPPING[r]).update(i,"utf8").verify(this.config.alipayPublicKey,e,"base64")}getSignStr(t,e){let r=t.trim();const n=t.indexOf(e+'"'),i=t.lastIndexOf('"sign"');return r=r.substr(n+e.length+1),r=r.substr(0,i),r=r.replace(/^[^{]*{/g,"{"),r=r.replace(/\}([^}]*)$/g,"}"),r}exec(t,e={},r={}){if(r.formData)return r.formData.getFiles().length>0?this.multipartExec(t,r):this.pageExec(t,r);const n=this.config,o=h.sign(t,e,n),{url:s,execParams:a}=this.formatUrl(n.gateway,o),u=r.log&&i.fn(r.log.info)?r.log.info:null,c=r.log&&i.fn(r.log.error)?r.log.error:null;return u&&u("[AlipaySdk]start exec, url: %s, method: %s, params: %s",s,t,JSON.stringify(a)),new Promise((i,o)=>{n.urllib.request(s,{method:"POST",data:a,dataType:"text",timeout:n.timeout,headers:{"user-agent":this.sdkVersion}}).then(s=>{if(u&&u("[AlipaySdk]exec response: %s",s),200===s.status){let a,u;try{const e=JSON.parse(s.data);u=t.replace(/\./g,"_")+"_response",a=e[u]}catch(t){return o({serverResult:s,errorMessage:"[AlipaySdk]Response 格式错误"})}if(a){e.needEncrypt&&(a=h.aesDecrypt(a,n.encryptKey));return!r.validateSign||this.checkResponseSign(s.data,u)?i(n.camelcase?f(a,{deep:!0}):a):o({serverResult:s,errorMessage:"[AlipaySdk]验签失败"})}return o({serverResult:s,errorMessage:"[AlipaySdk]HTTP 请求错误"})}o({serverResult:s,errorMessage:"[AlipaySdk]HTTP 请求错误"})}).catch(t=>{t.message="[AlipaySdk]exec error",c&&c(t),o(t)})})}checkResponseSign(t,e){if(!this.config.alipayPublicKey||""===this.config.alipayPublicKey)return console.warn("config.alipayPublicKey is empty"),!0;if(!t)return!1;const r=this.getSignStr(t,e),n=JSON.parse(t).sign,i=o.createVerify(h.ALIPAY_ALGORITHM_MAPPING[this.config.signType]);return i.update(r,"utf8"),i.verify(this.config.alipayPublicKey,n,"base64")}checkNotifySign(t,e){const r=t.sign;if(!this.config.alipayPublicKey||!r)return!1;const n=t.sign_type||this.config.signType||"RSA2",i=Object.assign({},t);delete i.sign,i.sign_type=n;return!!this.notifyRSACheck(i,r,n,e)||(delete i.sign_type,this.notifyRSACheck(i,r,n,e))}}},function(t,e,r){"use strict";
/**!
 * is
 * the definitive JavaScript type testing library
 *
 * @copyright 2013-2014 Enrico Marino / Jordan Harband
 * @license MIT
 */var n,i,o=Object.prototype,s=o.hasOwnProperty,a=o.toString;"function"==typeof Symbol&&(n=Symbol.prototype.valueOf),"function"==typeof BigInt&&(i=BigInt.prototype.valueOf);var u=function(t){return t!=t},c={boolean:1,number:1,string:1,undefined:1},f=/^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$/,l=/^[A-Fa-f0-9]+$/,h={};h.a=h.type=function(t,e){return typeof t===e},h.defined=function(t){return void 0!==t},h.empty=function(t){var e,r=a.call(t);if("[object Array]"===r||"[object Arguments]"===r||"[object String]"===r)return 0===t.length;if("[object Object]"===r){for(e in t)if(s.call(t,e))return!1;return!0}return!t},h.equal=function(t,e){if(t===e)return!0;var r,n=a.call(t);if(n!==a.call(e))return!1;if("[object Object]"===n){for(r in t)if(!h.equal(t[r],e[r])||!(r in e))return!1;for(r in e)if(!h.equal(t[r],e[r])||!(r in t))return!1;return!0}if("[object Array]"===n){if((r=t.length)!==e.length)return!1;for(;r--;)if(!h.equal(t[r],e[r]))return!1;return!0}return"[object Function]"===n?t.prototype===e.prototype:"[object Date]"===n&&t.getTime()===e.getTime()},h.hosted=function(t,e){var r=typeof e[t];return"object"===r?!!e[t]:!c[r]},h.instance=h.instanceof=function(t,e){return t instanceof e},h.nil=h.null=function(t){return null===t},h.undef=h.undefined=function(t){return void 0===t},h.args=h.arguments=function(t){var e="[object Arguments]"===a.call(t),r=!h.array(t)&&h.arraylike(t)&&h.object(t)&&h.fn(t.callee);return e||r},h.array=Array.isArray||function(t){return"[object Array]"===a.call(t)},h.args.empty=function(t){return h.args(t)&&0===t.length},h.array.empty=function(t){return h.array(t)&&0===t.length},h.arraylike=function(t){return!!t&&!h.bool(t)&&s.call(t,"length")&&isFinite(t.length)&&h.number(t.length)&&t.length>=0},h.bool=h.boolean=function(t){return"[object Boolean]"===a.call(t)},h.false=function(t){return h.bool(t)&&!1===Boolean(Number(t))},h.true=function(t){return h.bool(t)&&!0===Boolean(Number(t))},h.date=function(t){return"[object Date]"===a.call(t)},h.date.valid=function(t){return h.date(t)&&!isNaN(Number(t))},h.element=function(t){return void 0!==t&&"undefined"!=typeof HTMLElement&&t instanceof HTMLElement&&1===t.nodeType},h.error=function(t){return"[object Error]"===a.call(t)},h.fn=h.function=function(t){if("undefined"!=typeof window&&t===window.alert)return!0;var e=a.call(t);return"[object Function]"===e||"[object GeneratorFunction]"===e||"[object AsyncFunction]"===e},h.number=function(t){return"[object Number]"===a.call(t)},h.infinite=function(t){return t===1/0||t===-1/0},h.decimal=function(t){return h.number(t)&&!u(t)&&!h.infinite(t)&&t%1!=0},h.divisibleBy=function(t,e){var r=h.infinite(t),n=h.infinite(e),i=h.number(t)&&!u(t)&&h.number(e)&&!u(e)&&0!==e;return r||n||i&&t%e==0},h.integer=h.int=function(t){return h.number(t)&&!u(t)&&t%1==0},h.maximum=function(t,e){if(u(t))throw new TypeError("NaN is not a valid value");if(!h.arraylike(e))throw new TypeError("second argument must be array-like");for(var r=e.length;--r>=0;)if(t<e[r])return!1;return!0},h.minimum=function(t,e){if(u(t))throw new TypeError("NaN is not a valid value");if(!h.arraylike(e))throw new TypeError("second argument must be array-like");for(var r=e.length;--r>=0;)if(t>e[r])return!1;return!0},h.nan=function(t){return!h.number(t)||t!=t},h.even=function(t){return h.infinite(t)||h.number(t)&&t==t&&t%2==0},h.odd=function(t){return h.infinite(t)||h.number(t)&&t==t&&t%2!=0},h.ge=function(t,e){if(u(t)||u(e))throw new TypeError("NaN is not a valid value");return!h.infinite(t)&&!h.infinite(e)&&t>=e},h.gt=function(t,e){if(u(t)||u(e))throw new TypeError("NaN is not a valid value");return!h.infinite(t)&&!h.infinite(e)&&t>e},h.le=function(t,e){if(u(t)||u(e))throw new TypeError("NaN is not a valid value");return!h.infinite(t)&&!h.infinite(e)&&t<=e},h.lt=function(t,e){if(u(t)||u(e))throw new TypeError("NaN is not a valid value");return!h.infinite(t)&&!h.infinite(e)&&t<e},h.within=function(t,e,r){if(u(t)||u(e)||u(r))throw new TypeError("NaN is not a valid value");if(!h.number(t)||!h.number(e)||!h.number(r))throw new TypeError("all arguments must be numbers");return h.infinite(t)||h.infinite(e)||h.infinite(r)||t>=e&&t<=r},h.object=function(t){return"[object Object]"===a.call(t)},h.primitive=function(t){return!t||!("object"==typeof t||h.object(t)||h.fn(t)||h.array(t))},h.hash=function(t){return h.object(t)&&t.constructor===Object&&!t.nodeType&&!t.setInterval},h.regexp=function(t){return"[object RegExp]"===a.call(t)},h.string=function(t){return"[object String]"===a.call(t)},h.base64=function(t){return h.string(t)&&(!t.length||f.test(t))},h.hex=function(t){return h.string(t)&&(!t.length||l.test(t))},h.symbol=function(t){return"function"==typeof Symbol&&"[object Symbol]"===a.call(t)&&"symbol"==typeof n.call(t)},h.bigint=function(t){return"function"==typeof BigInt&&"[object BigInt]"===a.call(t)&&"bigint"==typeof i.call(t)},t.exports=h},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=uniCloud.httpclient;e.default=n,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={},t.exports=e.default},function(t,e,r){"use strict";var n=r(52);var i={Uri:{createUriRegex:function(t){if("object"!=typeof(t=t||{})||Array.isArray(t))throw new Error("options must be an object");var e="";if(t.scheme){if(Array.isArray(t.scheme)||(t.scheme=[t.scheme]),t.scheme.length<=0)throw new Error("scheme must have at least 1 scheme specified");for(var r=0;r<t.scheme.length;++r){var i=t.scheme[r];if(!(i instanceof RegExp||"string"==typeof i))throw new Error("scheme must only contain Regular Expressions or Strings");if(e+=e?"|":"",i instanceof RegExp)e+=i.source;else{if(!/[a-zA-Z][a-zA-Z0-9+-\.]*/.test(i))throw new Error("scheme at position "+r+" must be a valid scheme");e+=i.replace(/[\^\$\.\*\+\-\?\=\!\:\|\\\/\(\)\[\]\{\}\,]/g,"\\$&")}}}var o="(?:"+(e||n.scheme)+")";return new RegExp("^(?:"+o+":"+n.hierPart+")(?:\\?"+n.query+")?(?:#"+n.fragment+")?$")},uriRegex:new RegExp(n.uri)}};i.Uri.isValid=function(t){return i.Uri.uriRegex.test(t)},t.exports={createUriRegex:i.Uri.createUriRegex,uriRegex:i.Uri.uriRegex,isValid:i.Uri.isValid}},function(t,e,r){"use strict";var n={rfc3986:{},generate:function(){var t="|";n.rfc3986.cidr="[0-9]|[1-2][0-9]|3[0-2]";var e="!\\$&'\\(\\)\\*\\+,;=",r="a-zA-Z0-9-\\._~%0-9A-Fa-f"+e+":@",i="(?:0?0?[0-9]|0?[1-9][0-9]|1[0-9][0-9]|2[0-4][0-9]|25[0-5])";n.rfc3986.IPv4address="(?:"+i+"\\.){3}"+i;var o="[0-9A-Fa-f]{1,4}",s="(?:"+o+":"+o+"|"+n.rfc3986.IPv4address+")",a="(?:"+o+":){6}"+s,u="::(?:"+o+":){5}"+s,c="(?:"+o+")?::(?:"+o+":){4}"+s,f="(?:(?:"+o+":){0,1}"+o+")?::(?:"+o+":){3}"+s,l="(?:(?:"+o+":){0,2}"+o+")?::(?:"+o+":){2}"+s,h="(?:(?:"+o+":){0,3}"+o+")?::"+o+":"+s,d="(?:(?:"+o+":){0,4}"+o+")?::"+s;n.rfc3986.IPv6address="(?:"+a+t+u+t+c+t+f+t+l+t+h+t+d+"|(?:(?:[0-9A-Fa-f]{1,4}:){0,5}[0-9A-Fa-f]{1,4})?::[0-9A-Fa-f]{1,4}"+"|(?:(?:[0-9A-Fa-f]{1,4}:){0,6}[0-9A-Fa-f]{1,4})?::)",n.rfc3986.IPvFuture="v[0-9A-Fa-f]+\\.[a-zA-Z0-9-\\._~"+e+":]+",n.rfc3986.scheme="[a-zA-Z][a-zA-Z0-9+-\\.]*";n.rfc3986.IPLiteral="\\[(?:"+n.rfc3986.IPv6address+t+n.rfc3986.IPvFuture+")\\]";var p="(?:[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:]*@)?"+("(?:"+n.rfc3986.IPLiteral+t+n.rfc3986.IPv4address+"|[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=]{0,255})")+"(?::[0-9]*)?",y="(?:\\/[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]*)*";n.rfc3986.hierPart="(?:(?:\\/\\/"+p+y+")"+"|\\/(?:[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]+(?:\\/[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]*)*)?"+"|[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]+(?:\\/[a-zA-Z0-9-\\._~%0-9A-Fa-f!\\$&'\\(\\)\\*\\+,;=:@]*)*)",n.rfc3986.query="["+r+"\\/\\?]*(?=#|$)",n.rfc3986.fragment="["+r+"\\/\\?]*",n.rfc3986.uri="^(?:"+n.rfc3986.scheme+":"+n.rfc3986.hierPart+")(?:\\?"+n.rfc3986.query+")?(?:#"+n.rfc3986.fragment+")?$"}};n.generate(),t.exports=n.rfc3986},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(7).camel2snake;e.default=n,t.exports=e.default},function(t,e,r){"use strict";const n=r(20),i=r(55),o=r(56),s=new o({maxSize:1e5}),a=(t,e)=>{const r=(e=Object.assign({deep:!1},e)).exclude;return n(t,(t,e)=>{if(!r||!((t,e)=>t.some(t=>"string"==typeof t?t===e:t.test(e)))(r,t))if(s.has(t))t=s.get(t);else{const e=i(t);t.length<100&&s.set(t,e),t=e}return[t,e]},{deep:e.deep})};t.exports=(t,e)=>Array.isArray(t)?Object.keys(t).map(r=>a(t[r],e)):a(t,e)},function(t,e,r){"use strict";function n(t){let e=!1,r=!1,n=!1;for(let i=0;i<t.length;i++){const o=t[i];e&&/[a-zA-Z]/.test(o)&&o.toUpperCase()===o?(t=t.substr(0,i)+"-"+t.substr(i),e=!1,n=r,r=!0,i++):r&&n&&/[a-zA-Z]/.test(o)&&o.toLowerCase()===o?(t=t.substr(0,i-1)+"-"+t.substr(i-1),n=r,r=!1,e=!0):(e=o.toLowerCase()===o,n=r,r=o.toUpperCase()===o)}return t}t.exports=function(t){if(0===(t=arguments.length>1?Array.from(arguments).map(t=>t.trim()).filter(t=>t.length).join("-"):t.trim()).length)return"";if(1===t.length)return t.toLowerCase();if(/^[a-z0-9]+$/.test(t))return t;const e=t!==t.toLowerCase();return e&&(t=n(t)),t.replace(/^[_.\- ]+/,"").toLowerCase().replace(/[_.\- ]+(\w|$)/g,(t,e)=>e.toUpperCase())}},function(t,e,r){"use strict";class n{constructor(t){if(!((t=Object.assign({},t)).maxSize&&t.maxSize>0))throw new TypeError("`maxSize` must be a number greater than 0");this.maxSize=t.maxSize,this.cache=new Map,this.oldCache=new Map,this._size=0}_set(t,e){this.cache.set(t,e),this._size++,this._size>=this.maxSize&&(this._size=0,this.oldCache=this.cache,this.cache=new Map)}get(t){if(this.cache.has(t))return this.cache.get(t);if(this.oldCache.has(t)){const e=this.oldCache.get(t);return this._set(t,e),e}}set(t,e){return this.cache.has(t)?this.cache.set(t,e):this._set(t,e),this}has(t){return this.cache.has(t)||this.oldCache.has(t)}peek(t){return this.cache.has(t)?this.cache.get(t):this.oldCache.has(t)?this.oldCache.get(t):void 0}delete(t){this.cache.delete(t)&&this._size--,this.oldCache.delete(t)}clear(){this.cache.clear(),this.oldCache.clear(),this._size=0}*keys(){for(const t of this)yield t[0]}*values(){for(const t of this)yield t[1]}*[Symbol.iterator](){for(const t of this.cache)yield t;for(const t of this.oldCache)this.cache.has(t[0])||(yield t)}get size(){let t=0;for(const e of this.oldCache)this.cache.has(e[0])||t++;return this._size+t}}t.exports=n},function(t,e,r){var n=r(58);t.exports=function(t){return n(t).replace(/\s/g,"_")}},function(t,e,r){var n=r(59);t.exports=function(t){return n(t).replace(/[\W_]+(.|$)/g,(function(t,e){return e?" "+e:""}))}},function(t,e){t.exports=function(t){if(r.test(t))return t.toLowerCase();i.test(t)&&(t=function(t){return t.replace(o,(function(t,e){return e?" "+e:""}))}(t));n.test(t)&&(t=function(t){return t.replace(s,(function(t,e,r){return e+" "+r.toLowerCase().split("").join(" ")}))}(t));return t.toLowerCase()};var r=/\s/,n=/[a-z][A-Z]/,i=/[\W_]/;var o=/[\W_]+(.|$)/g;var s=/(.)([A-Z]+)/g},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){return{format:function(){return(0,n.getFullTimeStr)((0,n.getOffsetDate)(8))}}};var n=r(7);t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={encode:t=>t};e.default=n,t.exports=e.default},function(t,e,r){var n;t.exports=(n=r(0),r(11),r(63),r(64),r(5),r(65),r(6),r(14),r(23),r(66),r(24),r(67),r(68),r(69),r(15),r(70),r(4),r(1),r(71),r(72),r(73),r(74),r(75),r(76),r(77),r(78),r(79),r(80),r(81),r(82),r(83),r(84),r(85),r(86),n)},function(t,e,r){var n;t.exports=(n=r(0),function(){if("function"==typeof ArrayBuffer){var t=n.lib.WordArray,e=t.init;(t.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var r=t.byteLength,n=[],i=0;i<r;i++)n[i>>>2]|=t[i]<<24-i%4*8;e.call(this,n,r)}else e.apply(this,arguments)}).prototype=t}}(),n.lib.WordArray)},function(t,e,r){var n;t.exports=(n=r(0),function(){var t=n,e=t.lib.WordArray,r=t.enc;function i(t){return t<<8&4278255360|t>>>8&16711935}r.Utf16=r.Utf16BE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],i=0;i<r;i+=2){var o=e[i>>>2]>>>16-i%4*8&65535;n.push(String.fromCharCode(o))}return n.join("")},parse:function(t){for(var r=t.length,n=[],i=0;i<r;i++)n[i>>>1]|=t.charCodeAt(i)<<16-i%2*16;return e.create(n,2*r)}},r.Utf16LE={stringify:function(t){for(var e=t.words,r=t.sigBytes,n=[],o=0;o<r;o+=2){var s=i(e[o>>>2]>>>16-o%4*8&65535);n.push(String.fromCharCode(s))}return n.join("")},parse:function(t){for(var r=t.length,n=[],o=0;o<r;o++)n[o>>>1]|=i(t.charCodeAt(o)<<16-o%2*16);return e.create(n,2*r)}}}(),n.enc.Utf16)},function(t,e,r){var n,i,o;t.exports=(o=r(0),i=(n=o).lib.WordArray,n.enc.Base64url={stringify:function(t,e=!0){var r=t.words,n=t.sigBytes,i=e?this._safe_map:this._map;t.clamp();for(var o=[],s=0;s<n;s+=3)for(var a=(r[s>>>2]>>>24-s%4*8&255)<<16|(r[s+1>>>2]>>>24-(s+1)%4*8&255)<<8|r[s+2>>>2]>>>24-(s+2)%4*8&255,u=0;u<4&&s+.75*u<n;u++)o.push(i.charAt(a>>>6*(3-u)&63));var c=i.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(t,e=!0){var r=t.length,n=e?this._safe_map:this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var s=0;s<n.length;s++)o[n.charCodeAt(s)]=s}var a=n.charAt(64);if(a){var u=t.indexOf(a);-1!==u&&(r=u)}return function(t,e,r){for(var n=[],o=0,s=0;s<e;s++)if(s%4){var a=r[t.charCodeAt(s-1)]<<s%4*2,u=r[t.charCodeAt(s)]>>>6-s%4*2,c=a|u;n[o>>>2]|=c<<24-o%4*8,o++}return i.create(n,o)}(t,r,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",_safe_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_"},o.enc.Base64url)},function(t,e,r){var n,i,o,s,a,u;t.exports=(u=r(0),r(23),i=(n=u).lib.WordArray,o=n.algo,s=o.SHA256,a=o.SHA224=s.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=s._doFinalize.call(this);return t.sigBytes-=4,t}}),n.SHA224=s._createHelper(a),n.HmacSHA224=s._createHmacHelper(a),u.SHA224)},function(t,e,r){var n,i,o,s,a,u,c,f;t.exports=(f=r(0),r(11),r(24),i=(n=f).x64,o=i.Word,s=i.WordArray,a=n.algo,u=a.SHA512,c=a.SHA384=u.extend({_doReset:function(){this._hash=new s.init([new o.init(3418070365,3238371032),new o.init(1654270250,914150663),new o.init(2438529370,812702999),new o.init(355462360,4144912697),new o.init(1731405415,4290775857),new o.init(2394180231,1750603025),new o.init(3675008525,1694076839),new o.init(1203062813,3204075428)])},_doFinalize:function(){var t=u._doFinalize.call(this);return t.sigBytes-=16,t}}),n.SHA384=u._createHelper(c),n.HmacSHA384=u._createHmacHelper(c),f.SHA384)},function(t,e,r){var n;t.exports=(n=r(0),r(11),function(t){var e=n,r=e.lib,i=r.WordArray,o=r.Hasher,s=e.x64.Word,a=e.algo,u=[],c=[],f=[];!function(){for(var t=1,e=0,r=0;r<24;r++){u[t+5*e]=(r+1)*(r+2)/2%64;var n=(2*t+3*e)%5;t=e%5,e=n}for(t=0;t<5;t++)for(e=0;e<5;e++)c[t+5*e]=e+(2*t+3*e)%5*5;for(var i=1,o=0;o<24;o++){for(var a=0,l=0,h=0;h<7;h++){if(1&i){var d=(1<<h)-1;d<32?l^=1<<d:a^=1<<d-32}128&i?i=i<<1^113:i<<=1}f[o]=s.create(a,l)}}();var l=[];!function(){for(var t=0;t<25;t++)l[t]=s.create()}();var h=a.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new s.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var r=this._state,n=this.blockSize/2,i=0;i<n;i++){var o=t[e+2*i],s=t[e+2*i+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),s=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),(C=r[i]).high^=s,C.low^=o}for(var a=0;a<24;a++){for(var h=0;h<5;h++){for(var d=0,p=0,y=0;y<5;y++)d^=(C=r[h+5*y]).high,p^=C.low;var g=l[h];g.high=d,g.low=p}for(h=0;h<5;h++){var v=l[(h+4)%5],m=l[(h+1)%5],_=m.high,b=m.low;for(d=v.high^(_<<1|b>>>31),p=v.low^(b<<1|_>>>31),y=0;y<5;y++)(C=r[h+5*y]).high^=d,C.low^=p}for(var w=1;w<25;w++){var S=(C=r[w]).high,E=C.low,I=u[w];I<32?(d=S<<I|E>>>32-I,p=E<<I|S>>>32-I):(d=E<<I-32|S>>>64-I,p=S<<I-32|E>>>64-I);var A=l[c[w]];A.high=d,A.low=p}var N=l[0],T=r[0];for(N.high=T.high,N.low=T.low,h=0;h<5;h++)for(y=0;y<5;y++){var C=r[w=h+5*y],R=l[w],P=l[(h+1)%5+5*y],x=l[(h+2)%5+5*y];C.high=R.high^~P.high&x.high,C.low=R.low^~P.low&x.low}C=r[0];var O=f[a];C.high^=O.high,C.low^=O.low}},_doFinalize:function(){var e=this._data,r=e.words,n=(this._nDataBytes,8*e.sigBytes),o=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(t.ceil((n+1)/o)*o>>>5)-1]|=128,e.sigBytes=4*r.length,this._process();for(var s=this._state,a=this.cfg.outputLength/8,u=a/8,c=[],f=0;f<u;f++){var l=s[f],h=l.high,d=l.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),c.push(d),c.push(h)}return new i.init(c,a)},clone:function(){for(var t=o.clone.call(this),e=t._state=this._state.slice(0),r=0;r<25;r++)e[r]=e[r].clone();return t}});e.SHA3=o._createHelper(h),e.HmacSHA3=o._createHmacHelper(h)}(Math),n.SHA3)},function(t,e,r){var n;t.exports=(n=r(0),
/** @preserve
	(c) 2012 by Cédric Mesnil. All rights reserved.

	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:

	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
	    - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.

	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
	*/
function(t){var e=n,r=e.lib,i=r.WordArray,o=r.Hasher,s=e.algo,a=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),u=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),f=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),l=i.create([0,1518500249,1859775393,2400959708,2840853838]),h=i.create([1352829926,1548603684,1836072691,2053994217,0]),d=s.RIPEMD160=o.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var r=0;r<16;r++){var n=e+r,i=t[n];t[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o,s,d,b,w,S,E,I,A,N,T,C=this._hash.words,R=l.words,P=h.words,x=a.words,O=u.words,B=c.words,D=f.words;for(S=o=C[0],E=s=C[1],I=d=C[2],A=b=C[3],N=w=C[4],r=0;r<80;r+=1)T=o+t[e+x[r]]|0,T+=r<16?p(s,d,b)+R[0]:r<32?y(s,d,b)+R[1]:r<48?g(s,d,b)+R[2]:r<64?v(s,d,b)+R[3]:m(s,d,b)+R[4],T=(T=_(T|=0,B[r]))+w|0,o=w,w=b,b=_(d,10),d=s,s=T,T=S+t[e+O[r]]|0,T+=r<16?m(E,I,A)+P[0]:r<32?v(E,I,A)+P[1]:r<48?g(E,I,A)+P[2]:r<64?y(E,I,A)+P[3]:p(E,I,A)+P[4],T=(T=_(T|=0,D[r]))+N|0,S=N,N=A,A=_(I,10),I=E,E=T;T=C[1]+d+A|0,C[1]=C[2]+b+N|0,C[2]=C[3]+w+S|0,C[3]=C[4]+o+E|0,C[4]=C[0]+s+I|0,C[0]=T},_doFinalize:function(){var t=this._data,e=t.words,r=8*this._nDataBytes,n=8*t.sigBytes;e[n>>>5]|=128<<24-n%32,e[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(e.length+1),this._process();for(var i=this._hash,o=i.words,s=0;s<5;s++){var a=o[s];o[s]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}return i},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});function p(t,e,r){return t^e^r}function y(t,e,r){return t&e|~t&r}function g(t,e,r){return(t|~e)^r}function v(t,e,r){return t&r|e&~r}function m(t,e,r){return t^(e|~r)}function _(t,e){return t<<e|t>>>32-e}e.RIPEMD160=o._createHelper(d),e.HmacRIPEMD160=o._createHmacHelper(d)}(Math),n.RIPEMD160)},function(t,e,r){var n,i,o,s,a,u,c,f,l;t.exports=(l=r(0),r(14),r(15),i=(n=l).lib,o=i.Base,s=i.WordArray,a=n.algo,u=a.SHA1,c=a.HMAC,f=a.PBKDF2=o.extend({cfg:o.extend({keySize:4,hasher:u,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var r=this.cfg,n=c.create(r.hasher,t),i=s.create(),o=s.create([1]),a=i.words,u=o.words,f=r.keySize,l=r.iterations;a.length<f;){var h=n.update(e).finalize(o);n.reset();for(var d=h.words,p=d.length,y=h,g=1;g<l;g++){y=n.finalize(y),n.reset();for(var v=y.words,m=0;m<p;m++)d[m]^=v[m]}i.concat(h),u[0]++}return i.sigBytes=4*f,i}}),n.PBKDF2=function(t,e,r){return f.create(r).compute(t,e)},l.PBKDF2)},function(t,e,r){var n;t.exports=(n=r(0),r(1),n.mode.CFB=function(){var t=n.lib.BlockCipherMode.extend();function e(t,e,r,n){var i,o=this._iv;o?(i=o.slice(0),this._iv=void 0):i=this._prevBlock,n.encryptBlock(i,0);for(var s=0;s<r;s++)t[e+s]^=i[s]}return t.Encryptor=t.extend({processBlock:function(t,r){var n=this._cipher,i=n.blockSize;e.call(this,t,r,i,n),this._prevBlock=t.slice(r,r+i)}}),t.Decryptor=t.extend({processBlock:function(t,r){var n=this._cipher,i=n.blockSize,o=t.slice(r,r+i);e.call(this,t,r,i,n),this._prevBlock=o}}),t}(),n.mode.CFB)},function(t,e,r){var n,i,o;t.exports=(o=r(0),r(1),o.mode.CTR=(n=o.lib.BlockCipherMode.extend(),i=n.Encryptor=n.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0);var s=o.slice(0);r.encryptBlock(s,0),o[n-1]=o[n-1]+1|0;for(var a=0;a<n;a++)t[e+a]^=s[a]}}),n.Decryptor=i,n),o.mode.CTR)},function(t,e,r){var n;t.exports=(n=r(0),r(1),
/** @preserve
	 * Counter block mode compatible with  Dr Brian Gladman fileenc.c
	 * derived from CryptoJS.mode.CTR
	 * <NAME_EMAIL>
	 */
n.mode.CTRGladman=function(){var t=n.lib.BlockCipherMode.extend();function e(t){if(255==(t>>24&255)){var e=t>>16&255,r=t>>8&255,n=255&t;255===e?(e=0,255===r?(r=0,255===n?n=0:++n):++r):++e,t=0,t+=e<<16,t+=r<<8,t+=n}else t+=1<<24;return t}var r=t.Encryptor=t.extend({processBlock:function(t,r){var n=this._cipher,i=n.blockSize,o=this._iv,s=this._counter;o&&(s=this._counter=o.slice(0),this._iv=void 0),function(t){0===(t[0]=e(t[0]))&&(t[1]=e(t[1]))}(s);var a=s.slice(0);n.encryptBlock(a,0);for(var u=0;u<i;u++)t[r+u]^=a[u]}});return t.Decryptor=r,t}(),n.mode.CTRGladman)},function(t,e,r){var n,i,o;t.exports=(o=r(0),r(1),o.mode.OFB=(n=o.lib.BlockCipherMode.extend(),i=n.Encryptor=n.extend({processBlock:function(t,e){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._keystream;i&&(o=this._keystream=i.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var s=0;s<n;s++)t[e+s]^=o[s]}}),n.Decryptor=i,n),o.mode.OFB)},function(t,e,r){var n,i;t.exports=(i=r(0),r(1),i.mode.ECB=((n=i.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),n.Decryptor=n.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),n),i.mode.ECB)},function(t,e,r){var n;t.exports=(n=r(0),r(1),n.pad.AnsiX923={pad:function(t,e){var r=t.sigBytes,n=4*e,i=n-r%n,o=r+i-1;t.clamp(),t.words[o>>>2]|=i<<24-o%4*8,t.sigBytes+=i},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},n.pad.Ansix923)},function(t,e,r){var n;t.exports=(n=r(0),r(1),n.pad.Iso10126={pad:function(t,e){var r=4*e,i=r-t.sigBytes%r;t.concat(n.lib.WordArray.random(i-1)).concat(n.lib.WordArray.create([i<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},n.pad.Iso10126)},function(t,e,r){var n;t.exports=(n=r(0),r(1),n.pad.Iso97971={pad:function(t,e){t.concat(n.lib.WordArray.create([2147483648],1)),n.pad.ZeroPadding.pad(t,e)},unpad:function(t){n.pad.ZeroPadding.unpad(t),t.sigBytes--}},n.pad.Iso97971)},function(t,e,r){var n;t.exports=(n=r(0),r(1),n.pad.ZeroPadding={pad:function(t,e){var r=4*e;t.clamp(),t.sigBytes+=r-(t.sigBytes%r||r)},unpad:function(t){var e=t.words,r=t.sigBytes-1;for(r=t.sigBytes-1;r>=0;r--)if(e[r>>>2]>>>24-r%4*8&255){t.sigBytes=r+1;break}}},n.pad.ZeroPadding)},function(t,e,r){var n;t.exports=(n=r(0),r(1),n.pad.NoPadding={pad:function(){},unpad:function(){}},n.pad.NoPadding)},function(t,e,r){var n,i,o,s;t.exports=(s=r(0),r(1),i=(n=s).lib.CipherParams,o=n.enc.Hex,n.format.Hex={stringify:function(t){return t.ciphertext.toString(o)},parse:function(t){var e=o.parse(t);return i.create({ciphertext:e})}},s.format.Hex)},function(t,e,r){var n;t.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var t=n,e=t.lib.BlockCipher,r=t.algo,i=[],o=[],s=[],a=[],u=[],c=[],f=[],l=[],h=[],d=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var r=0,n=0;for(e=0;e<256;e++){var p=n^n<<1^n<<2^n<<3^n<<4;p=p>>>8^255&p^99,i[r]=p,o[p]=r;var y=t[r],g=t[y],v=t[g],m=257*t[p]^16843008*p;s[r]=m<<24|m>>>8,a[r]=m<<16|m>>>16,u[r]=m<<8|m>>>24,c[r]=m,m=16843009*v^65537*g^257*y^16843008*r,f[p]=m<<24|m>>>8,l[p]=m<<16|m>>>16,h[p]=m<<8|m>>>24,d[p]=m,r?(r=y^t[t[t[v^y]]],n^=t[t[n]]):r=n=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],y=r.AES=e.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,r=t.sigBytes/4,n=4*((this._nRounds=r+6)+1),o=this._keySchedule=[],s=0;s<n;s++)s<r?o[s]=e[s]:(c=o[s-1],s%r?r>6&&s%r==4&&(c=i[c>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c]):(c=i[(c=c<<8|c>>>24)>>>24]<<24|i[c>>>16&255]<<16|i[c>>>8&255]<<8|i[255&c],c^=p[s/r|0]<<24),o[s]=o[s-r]^c);for(var a=this._invKeySchedule=[],u=0;u<n;u++){if(s=n-u,u%4)var c=o[s];else c=o[s-4];a[u]=u<4||s<=4?c:f[i[c>>>24]]^l[i[c>>>16&255]]^h[i[c>>>8&255]]^d[i[255&c]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,s,a,u,c,i)},decryptBlock:function(t,e){var r=t[e+1];t[e+1]=t[e+3],t[e+3]=r,this._doCryptBlock(t,e,this._invKeySchedule,f,l,h,d,o),r=t[e+1],t[e+1]=t[e+3],t[e+3]=r},_doCryptBlock:function(t,e,r,n,i,o,s,a){for(var u=this._nRounds,c=t[e]^r[0],f=t[e+1]^r[1],l=t[e+2]^r[2],h=t[e+3]^r[3],d=4,p=1;p<u;p++){var y=n[c>>>24]^i[f>>>16&255]^o[l>>>8&255]^s[255&h]^r[d++],g=n[f>>>24]^i[l>>>16&255]^o[h>>>8&255]^s[255&c]^r[d++],v=n[l>>>24]^i[h>>>16&255]^o[c>>>8&255]^s[255&f]^r[d++],m=n[h>>>24]^i[c>>>16&255]^o[f>>>8&255]^s[255&l]^r[d++];c=y,f=g,l=v,h=m}y=(a[c>>>24]<<24|a[f>>>16&255]<<16|a[l>>>8&255]<<8|a[255&h])^r[d++],g=(a[f>>>24]<<24|a[l>>>16&255]<<16|a[h>>>8&255]<<8|a[255&c])^r[d++],v=(a[l>>>24]<<24|a[h>>>16&255]<<16|a[c>>>8&255]<<8|a[255&f])^r[d++],m=(a[h>>>24]<<24|a[c>>>16&255]<<16|a[f>>>8&255]<<8|a[255&l])^r[d++],t[e]=y,t[e+1]=g,t[e+2]=v,t[e+3]=m},keySize:8});t.AES=e._createHelper(y)}(),n.AES)},function(t,e,r){var n;t.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var t=n,e=t.lib,r=e.WordArray,i=e.BlockCipher,o=t.algo,s=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],u=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],f=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],l=o.DES=i.extend({_doReset:function(){for(var t=this._key.words,e=[],r=0;r<56;r++){var n=s[r]-1;e[r]=t[n>>>5]>>>31-n%32&1}for(var i=this._subKeys=[],o=0;o<16;o++){var c=i[o]=[],f=u[o];for(r=0;r<24;r++)c[r/6|0]|=e[(a[r]-1+f)%28]<<31-r%6,c[4+(r/6|0)]|=e[28+(a[r+24]-1+f)%28]<<31-r%6;for(c[0]=c[0]<<1|c[0]>>>31,r=1;r<7;r++)c[r]=c[r]>>>4*(r-1)+3;c[7]=c[7]<<5|c[7]>>>27}var l=this._invSubKeys=[];for(r=0;r<16;r++)l[r]=i[15-r]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,r){this._lBlock=t[e],this._rBlock=t[e+1],h.call(this,4,252645135),h.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),h.call(this,1,1431655765);for(var n=0;n<16;n++){for(var i=r[n],o=this._lBlock,s=this._rBlock,a=0,u=0;u<8;u++)a|=c[u][((s^i[u])&f[u])>>>0];this._lBlock=s,this._rBlock=o^a}var l=this._lBlock;this._lBlock=this._rBlock,this._rBlock=l,h.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(t,e){var r=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=r,this._lBlock^=r<<t}function d(t,e){var r=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=r,this._rBlock^=r<<t}t.DES=i._createHelper(l);var p=o.TripleDES=i.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),n=t.length<4?t.slice(0,2):t.slice(2,4),i=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=l.createEncryptor(r.create(e)),this._des2=l.createEncryptor(r.create(n)),this._des3=l.createEncryptor(r.create(i))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=i._createHelper(p)}(),n.TripleDES)},function(t,e,r){var n;t.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var t=n,e=t.lib.StreamCipher,r=t.algo,i=r.RC4=e.extend({_doReset:function(){for(var t=this._key,e=t.words,r=t.sigBytes,n=this._S=[],i=0;i<256;i++)n[i]=i;i=0;for(var o=0;i<256;i++){var s=i%r,a=e[s>>>2]>>>24-s%4*8&255;o=(o+n[i]+a)%256;var u=n[i];n[i]=n[o],n[o]=u}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var t=this._S,e=this._i,r=this._j,n=0,i=0;i<4;i++){r=(r+t[e=(e+1)%256])%256;var o=t[e];t[e]=t[r],t[r]=o,n|=t[(t[e]+t[r])%256]<<24-8*i}return this._i=e,this._j=r,n}t.RC4=e._createHelper(i);var s=r.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)o.call(this)}});t.RC4Drop=e._createHelper(s)}(),n.RC4)},function(t,e,r){var n;t.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var t=n,e=t.lib.StreamCipher,r=t.algo,i=[],o=[],s=[],a=r.Rabbit=e.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,r=0;r<4;r++)t[r]=16711935&(t[r]<<8|t[r]>>>24)|4278255360&(t[r]<<24|t[r]>>>8);var n=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],i=this._C=[t[2]<<16|t[2]>>>16,**********&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,**********&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,**********&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,**********&t[3]|65535&t[0]];for(this._b=0,r=0;r<4;r++)u.call(this);for(r=0;r<8;r++)i[r]^=n[r+4&7];if(e){var o=e.words,s=o[0],a=o[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),f=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=c>>>16|**********&f,h=f<<16|65535&c;for(i[0]^=c,i[1]^=l,i[2]^=f,i[3]^=h,i[4]^=c,i[5]^=l,i[6]^=f,i[7]^=h,r=0;r<4;r++)u.call(this)}},_doProcessBlock:function(t,e){var r=this._X;u.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),t[e+n]^=i[n]},blockSize:4,ivSize:2});function u(){for(var t=this._X,e=this._C,r=0;r<8;r++)o[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<o[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<o[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<o[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<o[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<o[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<o[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<o[6]>>>0?1:0)|0,this._b=e[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=t[r]+e[r],i=65535&n,a=n>>>16,u=((i*i>>>17)+i*a>>>15)+a*a,c=((**********&n)*n|0)+((65535&n)*n|0);s[r]=u^c}t[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,t[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,t[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,t[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,t[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,t[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,t[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,t[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.Rabbit=e._createHelper(a)}(),n.Rabbit)},function(t,e,r){var n;t.exports=(n=r(0),r(5),r(6),r(4),r(1),function(){var t=n,e=t.lib.StreamCipher,r=t.algo,i=[],o=[],s=[],a=r.RabbitLegacy=e.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,r=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],n=this._C=[t[2]<<16|t[2]>>>16,**********&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,**********&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,**********&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,**********&t[3]|65535&t[0]];this._b=0;for(var i=0;i<4;i++)u.call(this);for(i=0;i<8;i++)n[i]^=r[i+4&7];if(e){var o=e.words,s=o[0],a=o[1],c=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),f=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=c>>>16|**********&f,h=f<<16|65535&c;for(n[0]^=c,n[1]^=l,n[2]^=f,n[3]^=h,n[4]^=c,n[5]^=l,n[6]^=f,n[7]^=h,i=0;i<4;i++)u.call(this)}},_doProcessBlock:function(t,e){var r=this._X;u.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),t[e+n]^=i[n]},blockSize:4,ivSize:2});function u(){for(var t=this._X,e=this._C,r=0;r<8;r++)o[r]=e[r];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<o[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<o[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<o[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<o[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<o[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<o[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<o[6]>>>0?1:0)|0,this._b=e[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=t[r]+e[r],i=65535&n,a=n>>>16,u=((i*i>>>17)+i*a>>>15)+a*a,c=((**********&n)*n|0)+((65535&n)*n|0);s[r]=u^c}t[0]=s[0]+(s[7]<<16|s[7]>>>16)+(s[6]<<16|s[6]>>>16)|0,t[1]=s[1]+(s[0]<<8|s[0]>>>24)+s[7]|0,t[2]=s[2]+(s[1]<<16|s[1]>>>16)+(s[0]<<16|s[0]>>>16)|0,t[3]=s[3]+(s[2]<<8|s[2]>>>24)+s[1]|0,t[4]=s[4]+(s[3]<<16|s[3]>>>16)+(s[2]<<16|s[2]>>>16)|0,t[5]=s[5]+(s[4]<<8|s[4]>>>24)+s[3]|0,t[6]=s[6]+(s[5]<<16|s[5]>>>16)+(s[4]<<16|s[4]>>>16)|0,t[7]=s[7]+(s[6]<<8|s[6]>>>24)+s[5]|0}t.RabbitLegacy=e._createHelper(a)}(),n.RabbitLegacy)},function(t,e,r){(function(t){var n;
/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */(function(){var i="Expected a function",o="__lodash_placeholder__",s=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]],a="[object Arguments]",u="[object Array]",c="[object Boolean]",f="[object Date]",l="[object Error]",h="[object Function]",d="[object GeneratorFunction]",p="[object Map]",y="[object Number]",g="[object Object]",v="[object RegExp]",m="[object Set]",_="[object String]",b="[object Symbol]",w="[object WeakMap]",S="[object ArrayBuffer]",E="[object DataView]",I="[object Float32Array]",A="[object Float64Array]",N="[object Int8Array]",T="[object Int16Array]",C="[object Int32Array]",R="[object Uint8Array]",P="[object Uint16Array]",x="[object Uint32Array]",O=/\b__p \+= '';/g,B=/\b(__p \+=) '' \+/g,D=/(__e\(.*?\)|\b__t\)) \+\n'';/g,F=/&(?:amp|lt|gt|quot|#39);/g,k=/[&<>"']/g,U=RegExp(F.source),M=RegExp(k.source),j=/<%-([\s\S]+?)%>/g,K=/<%([\s\S]+?)%>/g,L=/<%=([\s\S]+?)%>/g,q=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,z=/^\w*$/,V=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,$=/[\\^$.*+?()[\]{}|]/g,H=RegExp($.source),G=/^\s+/,W=/\s/,J=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Y=/\{\n\/\* \[wrapped with (.+)\] \*/,Z=/,? & /,X=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,Q=/[()=,{}\[\]\/\s]/,tt=/\\(\\)?/g,et=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,rt=/\w*$/,nt=/^[-+]0x[0-9a-f]+$/i,it=/^0b[01]+$/i,ot=/^\[object .+?Constructor\]$/,st=/^0o[0-7]+$/i,at=/^(?:0|[1-9]\d*)$/,ut=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,ct=/($^)/,ft=/['\n\r\u2028\u2029\\]/g,lt="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",ht="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",dt="[\\ud800-\\udfff]",pt="["+ht+"]",yt="["+lt+"]",gt="\\d+",vt="[\\u2700-\\u27bf]",mt="[a-z\\xdf-\\xf6\\xf8-\\xff]",_t="[^\\ud800-\\udfff"+ht+gt+"\\u2700-\\u27bfa-z\\xdf-\\xf6\\xf8-\\xffA-Z\\xc0-\\xd6\\xd8-\\xde]",bt="\\ud83c[\\udffb-\\udfff]",wt="[^\\ud800-\\udfff]",St="(?:\\ud83c[\\udde6-\\uddff]){2}",Et="[\\ud800-\\udbff][\\udc00-\\udfff]",It="[A-Z\\xc0-\\xd6\\xd8-\\xde]",At="(?:"+mt+"|"+_t+")",Nt="(?:"+It+"|"+_t+")",Tt="(?:"+yt+"|"+bt+")"+"?",Ct="[\\ufe0e\\ufe0f]?"+Tt+("(?:\\u200d(?:"+[wt,St,Et].join("|")+")[\\ufe0e\\ufe0f]?"+Tt+")*"),Rt="(?:"+[vt,St,Et].join("|")+")"+Ct,Pt="(?:"+[wt+yt+"?",yt,St,Et,dt].join("|")+")",xt=RegExp("['’]","g"),Ot=RegExp(yt,"g"),Bt=RegExp(bt+"(?="+bt+")|"+Pt+Ct,"g"),Dt=RegExp([It+"?"+mt+"+(?:['’](?:d|ll|m|re|s|t|ve))?(?="+[pt,It,"$"].join("|")+")",Nt+"+(?:['’](?:D|LL|M|RE|S|T|VE))?(?="+[pt,It+At,"$"].join("|")+")",It+"?"+At+"+(?:['’](?:d|ll|m|re|s|t|ve))?",It+"+(?:['’](?:D|LL|M|RE|S|T|VE))?","\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])","\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",gt,Rt].join("|"),"g"),Ft=RegExp("[\\u200d\\ud800-\\udfff"+lt+"\\ufe0e\\ufe0f]"),kt=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Ut=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Mt=-1,jt={};jt[I]=jt[A]=jt[N]=jt[T]=jt[C]=jt[R]=jt["[object Uint8ClampedArray]"]=jt[P]=jt[x]=!0,jt[a]=jt[u]=jt[S]=jt[c]=jt[E]=jt[f]=jt[l]=jt[h]=jt[p]=jt[y]=jt[g]=jt[v]=jt[m]=jt[_]=jt[w]=!1;var Kt={};Kt[a]=Kt[u]=Kt[S]=Kt[E]=Kt[c]=Kt[f]=Kt[I]=Kt[A]=Kt[N]=Kt[T]=Kt[C]=Kt[p]=Kt[y]=Kt[g]=Kt[v]=Kt[m]=Kt[_]=Kt[b]=Kt[R]=Kt["[object Uint8ClampedArray]"]=Kt[P]=Kt[x]=!0,Kt[l]=Kt[h]=Kt[w]=!1;var Lt={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},qt=parseFloat,zt=parseInt,Vt="object"==typeof global&&global&&global.Object===Object&&global,$t="object"==typeof self&&self&&self.Object===Object&&self,Ht=Vt||$t||Function("return this")(),Gt=e&&!e.nodeType&&e,Wt=Gt&&"object"==typeof t&&t&&!t.nodeType&&t,Jt=Wt&&Wt.exports===Gt,Yt=Jt&&Vt.process,Zt=function(){try{var t=Wt&&Wt.require&&Wt.require("util").types;return t||Yt&&Yt.binding&&Yt.binding("util")}catch(t){}}(),Xt=Zt&&Zt.isArrayBuffer,Qt=Zt&&Zt.isDate,te=Zt&&Zt.isMap,ee=Zt&&Zt.isRegExp,re=Zt&&Zt.isSet,ne=Zt&&Zt.isTypedArray;function ie(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}function oe(t,e,r,n){for(var i=-1,o=null==t?0:t.length;++i<o;){var s=t[i];e(n,s,r(s),t)}return n}function se(t,e){for(var r=-1,n=null==t?0:t.length;++r<n&&!1!==e(t[r],r,t););return t}function ae(t,e){for(var r=null==t?0:t.length;r--&&!1!==e(t[r],r,t););return t}function ue(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}function ce(t,e){for(var r=-1,n=null==t?0:t.length,i=0,o=[];++r<n;){var s=t[r];e(s,r,t)&&(o[i++]=s)}return o}function fe(t,e){return!!(null==t?0:t.length)&&be(t,e,0)>-1}function le(t,e,r){for(var n=-1,i=null==t?0:t.length;++n<i;)if(r(e,t[n]))return!0;return!1}function he(t,e){for(var r=-1,n=null==t?0:t.length,i=Array(n);++r<n;)i[r]=e(t[r],r,t);return i}function de(t,e){for(var r=-1,n=e.length,i=t.length;++r<n;)t[i+r]=e[r];return t}function pe(t,e,r,n){var i=-1,o=null==t?0:t.length;for(n&&o&&(r=t[++i]);++i<o;)r=e(r,t[i],i,t);return r}function ye(t,e,r,n){var i=null==t?0:t.length;for(n&&i&&(r=t[--i]);i--;)r=e(r,t[i],i,t);return r}function ge(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}var ve=Ie("length");function me(t,e,r){var n;return r(t,(function(t,r,i){if(e(t,r,i))return n=r,!1})),n}function _e(t,e,r,n){for(var i=t.length,o=r+(n?1:-1);n?o--:++o<i;)if(e(t[o],o,t))return o;return-1}function be(t,e,r){return e==e?function(t,e,r){var n=r-1,i=t.length;for(;++n<i;)if(t[n]===e)return n;return-1}(t,e,r):_e(t,Se,r)}function we(t,e,r,n){for(var i=r-1,o=t.length;++i<o;)if(n(t[i],e))return i;return-1}function Se(t){return t!=t}function Ee(t,e){var r=null==t?0:t.length;return r?Te(t,e)/r:NaN}function Ie(t){return function(e){return null==e?void 0:e[t]}}function Ae(t){return function(e){return null==t?void 0:t[e]}}function Ne(t,e,r,n,i){return i(t,(function(t,i,o){r=n?(n=!1,t):e(r,t,i,o)})),r}function Te(t,e){for(var r,n=-1,i=t.length;++n<i;){var o=e(t[n]);void 0!==o&&(r=void 0===r?o:r+o)}return r}function Ce(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}function Re(t){return t?t.slice(0,Ge(t)+1).replace(G,""):t}function Pe(t){return function(e){return t(e)}}function xe(t,e){return he(e,(function(e){return t[e]}))}function Oe(t,e){return t.has(e)}function Be(t,e){for(var r=-1,n=t.length;++r<n&&be(e,t[r],0)>-1;);return r}function De(t,e){for(var r=t.length;r--&&be(e,t[r],0)>-1;);return r}function Fe(t,e){for(var r=t.length,n=0;r--;)t[r]===e&&++n;return n}var ke=Ae({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),Ue=Ae({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"});function Me(t){return"\\"+Lt[t]}function je(t){return Ft.test(t)}function Ke(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}function Le(t,e){return function(r){return t(e(r))}}function qe(t,e){for(var r=-1,n=t.length,i=0,s=[];++r<n;){var a=t[r];a!==e&&a!==o||(t[r]=o,s[i++]=r)}return s}function ze(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}function Ve(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=[t,t]})),r}function $e(t){return je(t)?function(t){var e=Bt.lastIndex=0;for(;Bt.test(t);)++e;return e}(t):ve(t)}function He(t){return je(t)?function(t){return t.match(Bt)||[]}(t):function(t){return t.split("")}(t)}function Ge(t){for(var e=t.length;e--&&W.test(t.charAt(e)););return e}var We=Ae({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"});var Je=function t(e){var r,n=(e=null==e?Ht:Je.defaults(Ht.Object(),e,Je.pick(Ht,Ut))).Array,W=e.Date,lt=e.Error,ht=e.Function,dt=e.Math,pt=e.Object,yt=e.RegExp,gt=e.String,vt=e.TypeError,mt=n.prototype,_t=ht.prototype,bt=pt.prototype,wt=e["__core-js_shared__"],St=_t.toString,Et=bt.hasOwnProperty,It=0,At=(r=/[^.]+$/.exec(wt&&wt.keys&&wt.keys.IE_PROTO||""))?"Symbol(src)_1."+r:"",Nt=bt.toString,Tt=St.call(pt),Ct=Ht._,Rt=yt("^"+St.call(Et).replace($,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Pt=Jt?e.Buffer:void 0,Bt=e.Symbol,Ft=e.Uint8Array,Lt=Pt?Pt.allocUnsafe:void 0,Vt=Le(pt.getPrototypeOf,pt),$t=pt.create,Gt=bt.propertyIsEnumerable,Wt=mt.splice,Yt=Bt?Bt.isConcatSpreadable:void 0,Zt=Bt?Bt.iterator:void 0,ve=Bt?Bt.toStringTag:void 0,Ae=function(){try{var t=to(pt,"defineProperty");return t({},"",{}),t}catch(t){}}(),Ye=e.clearTimeout!==Ht.clearTimeout&&e.clearTimeout,Ze=W&&W.now!==Ht.Date.now&&W.now,Xe=e.setTimeout!==Ht.setTimeout&&e.setTimeout,Qe=dt.ceil,tr=dt.floor,er=pt.getOwnPropertySymbols,rr=Pt?Pt.isBuffer:void 0,nr=e.isFinite,ir=mt.join,or=Le(pt.keys,pt),sr=dt.max,ar=dt.min,ur=W.now,cr=e.parseInt,fr=dt.random,lr=mt.reverse,hr=to(e,"DataView"),dr=to(e,"Map"),pr=to(e,"Promise"),yr=to(e,"Set"),gr=to(e,"WeakMap"),vr=to(pt,"create"),mr=gr&&new gr,_r={},br=Co(hr),wr=Co(dr),Sr=Co(pr),Er=Co(yr),Ir=Co(gr),Ar=Bt?Bt.prototype:void 0,Nr=Ar?Ar.valueOf:void 0,Tr=Ar?Ar.toString:void 0;function Cr(t){if($s(t)&&!Ds(t)&&!(t instanceof Or)){if(t instanceof xr)return t;if(Et.call(t,"__wrapped__"))return Ro(t)}return new xr(t)}var Rr=function(){function t(){}return function(e){if(!Vs(e))return{};if($t)return $t(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();function Pr(){}function xr(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=void 0}function Or(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=4294967295,this.__views__=[]}function Br(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function Dr(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function Fr(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function kr(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new Fr;++e<r;)this.add(t[e])}function Ur(t){var e=this.__data__=new Dr(t);this.size=e.size}function Mr(t,e){var r=Ds(t),n=!r&&Bs(t),i=!r&&!n&&Ms(t),o=!r&&!n&&!i&&Qs(t),s=r||n||i||o,a=s?Ce(t.length,gt):[],u=a.length;for(var c in t)!e&&!Et.call(t,c)||s&&("length"==c||i&&("offset"==c||"parent"==c)||o&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||ao(c,u))||a.push(c);return a}function jr(t){var e=t.length;return e?t[Un(0,e-1)]:void 0}function Kr(t,e){return Ao(mi(t),Jr(e,0,t.length))}function Lr(t){return Ao(mi(t))}function qr(t,e,r){(void 0!==r&&!Ps(t[e],r)||void 0===r&&!(e in t))&&Gr(t,e,r)}function zr(t,e,r){var n=t[e];Et.call(t,e)&&Ps(n,r)&&(void 0!==r||e in t)||Gr(t,e,r)}function Vr(t,e){for(var r=t.length;r--;)if(Ps(t[r][0],e))return r;return-1}function $r(t,e,r,n){return tn(t,(function(t,i,o){e(n,t,r(t),o)})),n}function Hr(t,e){return t&&_i(e,wa(e),t)}function Gr(t,e,r){"__proto__"==e&&Ae?Ae(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}function Wr(t,e){for(var r=-1,i=e.length,o=n(i),s=null==t;++r<i;)o[r]=s?void 0:ga(t,e[r]);return o}function Jr(t,e,r){return t==t&&(void 0!==r&&(t=t<=r?t:r),void 0!==e&&(t=t>=e?t:e)),t}function Yr(t,e,r,n,i,o){var s,u=1&e,l=2&e,w=4&e;if(r&&(s=i?r(t,n,i,o):r(t)),void 0!==s)return s;if(!Vs(t))return t;var O=Ds(t);if(O){if(s=function(t){var e=t.length,r=new t.constructor(e);e&&"string"==typeof t[0]&&Et.call(t,"index")&&(r.index=t.index,r.input=t.input);return r}(t),!u)return mi(t,s)}else{var B=no(t),D=B==h||B==d;if(Ms(t))return hi(t,u);if(B==g||B==a||D&&!i){if(s=l||D?{}:oo(t),!u)return l?function(t,e){return _i(t,ro(t),e)}(t,function(t,e){return t&&_i(e,Sa(e),t)}(s,t)):function(t,e){return _i(t,eo(t),e)}(t,Hr(s,t))}else{if(!Kt[B])return i?t:{};s=function(t,e,r){var n=t.constructor;switch(e){case S:return di(t);case c:case f:return new n(+t);case E:return function(t,e){var r=e?di(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}(t,r);case I:case A:case N:case T:case C:case R:case"[object Uint8ClampedArray]":case P:case x:return pi(t,r);case p:return new n;case y:case _:return new n(t);case v:return function(t){var e=new t.constructor(t.source,rt.exec(t));return e.lastIndex=t.lastIndex,e}(t);case m:return new n;case b:return i=t,Nr?pt(Nr.call(i)):{}}var i}(t,B,u)}}o||(o=new Ur);var F=o.get(t);if(F)return F;o.set(t,s),Ys(t)?t.forEach((function(n){s.add(Yr(n,e,r,n,t,o))})):Hs(t)&&t.forEach((function(n,i){s.set(i,Yr(n,e,r,i,t,o))}));var k=O?void 0:(w?l?Gi:Hi:l?Sa:wa)(t);return se(k||t,(function(n,i){k&&(n=t[i=n]),zr(s,i,Yr(n,e,r,i,t,o))})),s}function Zr(t,e,r){var n=r.length;if(null==t)return!n;for(t=pt(t);n--;){var i=r[n],o=e[i],s=t[i];if(void 0===s&&!(i in t)||!o(s))return!1}return!0}function Xr(t,e,r){if("function"!=typeof t)throw new vt(i);return wo((function(){t.apply(void 0,r)}),e)}function Qr(t,e,r,n){var i=-1,o=fe,s=!0,a=t.length,u=[],c=e.length;if(!a)return u;r&&(e=he(e,Pe(r))),n?(o=le,s=!1):e.length>=200&&(o=Oe,s=!1,e=new kr(e));t:for(;++i<a;){var f=t[i],l=null==r?f:r(f);if(f=n||0!==f?f:0,s&&l==l){for(var h=c;h--;)if(e[h]===l)continue t;u.push(f)}else o(e,l,n)||u.push(f)}return u}Cr.templateSettings={escape:j,evaluate:K,interpolate:L,variable:"",imports:{_:Cr}},Cr.prototype=Pr.prototype,Cr.prototype.constructor=Cr,xr.prototype=Rr(Pr.prototype),xr.prototype.constructor=xr,Or.prototype=Rr(Pr.prototype),Or.prototype.constructor=Or,Br.prototype.clear=function(){this.__data__=vr?vr(null):{},this.size=0},Br.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},Br.prototype.get=function(t){var e=this.__data__;if(vr){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return Et.call(e,t)?e[t]:void 0},Br.prototype.has=function(t){var e=this.__data__;return vr?void 0!==e[t]:Et.call(e,t)},Br.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=vr&&void 0===e?"__lodash_hash_undefined__":e,this},Dr.prototype.clear=function(){this.__data__=[],this.size=0},Dr.prototype.delete=function(t){var e=this.__data__,r=Vr(e,t);return!(r<0)&&(r==e.length-1?e.pop():Wt.call(e,r,1),--this.size,!0)},Dr.prototype.get=function(t){var e=this.__data__,r=Vr(e,t);return r<0?void 0:e[r][1]},Dr.prototype.has=function(t){return Vr(this.__data__,t)>-1},Dr.prototype.set=function(t,e){var r=this.__data__,n=Vr(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this},Fr.prototype.clear=function(){this.size=0,this.__data__={hash:new Br,map:new(dr||Dr),string:new Br}},Fr.prototype.delete=function(t){var e=Xi(this,t).delete(t);return this.size-=e?1:0,e},Fr.prototype.get=function(t){return Xi(this,t).get(t)},Fr.prototype.has=function(t){return Xi(this,t).has(t)},Fr.prototype.set=function(t,e){var r=Xi(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this},kr.prototype.add=kr.prototype.push=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this},kr.prototype.has=function(t){return this.__data__.has(t)},Ur.prototype.clear=function(){this.__data__=new Dr,this.size=0},Ur.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},Ur.prototype.get=function(t){return this.__data__.get(t)},Ur.prototype.has=function(t){return this.__data__.has(t)},Ur.prototype.set=function(t,e){var r=this.__data__;if(r instanceof Dr){var n=r.__data__;if(!dr||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new Fr(n)}return r.set(t,e),this.size=r.size,this};var tn=Si(cn),en=Si(fn,!0);function rn(t,e){var r=!0;return tn(t,(function(t,n,i){return r=!!e(t,n,i)})),r}function nn(t,e,r){for(var n=-1,i=t.length;++n<i;){var o=t[n],s=e(o);if(null!=s&&(void 0===a?s==s&&!Xs(s):r(s,a)))var a=s,u=o}return u}function on(t,e){var r=[];return tn(t,(function(t,n,i){e(t,n,i)&&r.push(t)})),r}function sn(t,e,r,n,i){var o=-1,s=t.length;for(r||(r=so),i||(i=[]);++o<s;){var a=t[o];e>0&&r(a)?e>1?sn(a,e-1,r,n,i):de(i,a):n||(i[i.length]=a)}return i}var an=Ei(),un=Ei(!0);function cn(t,e){return t&&an(t,e,wa)}function fn(t,e){return t&&un(t,e,wa)}function ln(t,e){return ce(e,(function(e){return Ls(t[e])}))}function hn(t,e){for(var r=0,n=(e=ui(e,t)).length;null!=t&&r<n;)t=t[To(e[r++])];return r&&r==n?t:void 0}function dn(t,e,r){var n=e(t);return Ds(t)?n:de(n,r(t))}function pn(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":ve&&ve in pt(t)?function(t){var e=Et.call(t,ve),r=t[ve];try{t[ve]=void 0;var n=!0}catch(t){}var i=Nt.call(t);n&&(e?t[ve]=r:delete t[ve]);return i}(t):function(t){return Nt.call(t)}(t)}function yn(t,e){return t>e}function gn(t,e){return null!=t&&Et.call(t,e)}function vn(t,e){return null!=t&&e in pt(t)}function mn(t,e,r){for(var i=r?le:fe,o=t[0].length,s=t.length,a=s,u=n(s),c=1/0,f=[];a--;){var l=t[a];a&&e&&(l=he(l,Pe(e))),c=ar(l.length,c),u[a]=!r&&(e||o>=120&&l.length>=120)?new kr(a&&l):void 0}l=t[0];var h=-1,d=u[0];t:for(;++h<o&&f.length<c;){var p=l[h],y=e?e(p):p;if(p=r||0!==p?p:0,!(d?Oe(d,y):i(f,y,r))){for(a=s;--a;){var g=u[a];if(!(g?Oe(g,y):i(t[a],y,r)))continue t}d&&d.push(y),f.push(p)}}return f}function _n(t,e,r){var n=null==(t=vo(t,e=ui(e,t)))?t:t[To(Ko(e))];return null==n?void 0:ie(n,t,r)}function bn(t){return $s(t)&&pn(t)==a}function wn(t,e,r,n,i){return t===e||(null==t||null==e||!$s(t)&&!$s(e)?t!=t&&e!=e:function(t,e,r,n,i,o){var s=Ds(t),h=Ds(e),d=s?u:no(t),w=h?u:no(e),I=(d=d==a?g:d)==g,A=(w=w==a?g:w)==g,N=d==w;if(N&&Ms(t)){if(!Ms(e))return!1;s=!0,I=!1}if(N&&!I)return o||(o=new Ur),s||Qs(t)?Vi(t,e,r,n,i,o):function(t,e,r,n,i,o,s){switch(r){case E:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case S:return!(t.byteLength!=e.byteLength||!o(new Ft(t),new Ft(e)));case c:case f:case y:return Ps(+t,+e);case l:return t.name==e.name&&t.message==e.message;case v:case _:return t==e+"";case p:var a=Ke;case m:var u=1&n;if(a||(a=ze),t.size!=e.size&&!u)return!1;var h=s.get(t);if(h)return h==e;n|=2,s.set(t,e);var d=Vi(a(t),a(e),n,i,o,s);return s.delete(t),d;case b:if(Nr)return Nr.call(t)==Nr.call(e)}return!1}(t,e,d,r,n,i,o);if(!(1&r)){var T=I&&Et.call(t,"__wrapped__"),C=A&&Et.call(e,"__wrapped__");if(T||C){var R=T?t.value():t,P=C?e.value():e;return o||(o=new Ur),i(R,P,r,n,o)}}if(!N)return!1;return o||(o=new Ur),function(t,e,r,n,i,o){var s=1&r,a=Hi(t),u=a.length,c=Hi(e).length;if(u!=c&&!s)return!1;var f=u;for(;f--;){var l=a[f];if(!(s?l in e:Et.call(e,l)))return!1}var h=o.get(t),d=o.get(e);if(h&&d)return h==e&&d==t;var p=!0;o.set(t,e),o.set(e,t);var y=s;for(;++f<u;){l=a[f];var g=t[l],v=e[l];if(n)var m=s?n(v,g,l,e,t,o):n(g,v,l,t,e,o);if(!(void 0===m?g===v||i(g,v,r,n,o):m)){p=!1;break}y||(y="constructor"==l)}if(p&&!y){var _=t.constructor,b=e.constructor;_==b||!("constructor"in t)||!("constructor"in e)||"function"==typeof _&&_ instanceof _&&"function"==typeof b&&b instanceof b||(p=!1)}return o.delete(t),o.delete(e),p}(t,e,r,n,i,o)}(t,e,r,n,wn,i))}function Sn(t,e,r,n){var i=r.length,o=i,s=!n;if(null==t)return!o;for(t=pt(t);i--;){var a=r[i];if(s&&a[2]?a[1]!==t[a[0]]:!(a[0]in t))return!1}for(;++i<o;){var u=(a=r[i])[0],c=t[u],f=a[1];if(s&&a[2]){if(void 0===c&&!(u in t))return!1}else{var l=new Ur;if(n)var h=n(c,f,u,t,e,l);if(!(void 0===h?wn(f,c,3,n,l):h))return!1}}return!0}function En(t){return!(!Vs(t)||(e=t,At&&At in e))&&(Ls(t)?Rt:ot).test(Co(t));var e}function In(t){return"function"==typeof t?t:null==t?Ga:"object"==typeof t?Ds(t)?Pn(t[0],t[1]):Rn(t):ru(t)}function An(t){if(!ho(t))return or(t);var e=[];for(var r in pt(t))Et.call(t,r)&&"constructor"!=r&&e.push(r);return e}function Nn(t){if(!Vs(t))return function(t){var e=[];if(null!=t)for(var r in pt(t))e.push(r);return e}(t);var e=ho(t),r=[];for(var n in t)("constructor"!=n||!e&&Et.call(t,n))&&r.push(n);return r}function Tn(t,e){return t<e}function Cn(t,e){var r=-1,i=ks(t)?n(t.length):[];return tn(t,(function(t,n,o){i[++r]=e(t,n,o)})),i}function Rn(t){var e=Qi(t);return 1==e.length&&e[0][2]?yo(e[0][0],e[0][1]):function(r){return r===t||Sn(r,t,e)}}function Pn(t,e){return co(t)&&po(e)?yo(To(t),e):function(r){var n=ga(r,t);return void 0===n&&n===e?va(r,t):wn(e,n,3)}}function xn(t,e,r,n,i){t!==e&&an(e,(function(o,s){if(i||(i=new Ur),Vs(o))!function(t,e,r,n,i,o,s){var a=_o(t,r),u=_o(e,r),c=s.get(u);if(c)return void qr(t,r,c);var f=o?o(a,u,r+"",t,e,s):void 0,l=void 0===f;if(l){var h=Ds(u),d=!h&&Ms(u),p=!h&&!d&&Qs(u);f=u,h||d||p?Ds(a)?f=a:Us(a)?f=mi(a):d?(l=!1,f=hi(u,!0)):p?(l=!1,f=pi(u,!0)):f=[]:Ws(u)||Bs(u)?(f=a,Bs(a)?f=aa(a):Vs(a)&&!Ls(a)||(f=oo(u))):l=!1}l&&(s.set(u,f),i(f,u,n,o,s),s.delete(u));qr(t,r,f)}(t,e,s,r,xn,n,i);else{var a=n?n(_o(t,s),o,s+"",t,e,i):void 0;void 0===a&&(a=o),qr(t,s,a)}}),Sa)}function On(t,e){var r=t.length;if(r)return ao(e+=e<0?r:0,r)?t[e]:void 0}function Bn(t,e,r){e=e.length?he(e,(function(t){return Ds(t)?function(e){return hn(e,1===t.length?t[0]:t)}:t})):[Ga];var n=-1;return e=he(e,Pe(Zi())),function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}(Cn(t,(function(t,r,i){return{criteria:he(e,(function(e){return e(t)})),index:++n,value:t}})),(function(t,e){return function(t,e,r){var n=-1,i=t.criteria,o=e.criteria,s=i.length,a=r.length;for(;++n<s;){var u=yi(i[n],o[n]);if(u){if(n>=a)return u;var c=r[n];return u*("desc"==c?-1:1)}}return t.index-e.index}(t,e,r)}))}function Dn(t,e,r){for(var n=-1,i=e.length,o={};++n<i;){var s=e[n],a=hn(t,s);r(a,s)&&qn(o,ui(s,t),a)}return o}function Fn(t,e,r,n){var i=n?we:be,o=-1,s=e.length,a=t;for(t===e&&(e=mi(e)),r&&(a=he(t,Pe(r)));++o<s;)for(var u=0,c=e[o],f=r?r(c):c;(u=i(a,f,u,n))>-1;)a!==t&&Wt.call(a,u,1),Wt.call(t,u,1);return t}function kn(t,e){for(var r=t?e.length:0,n=r-1;r--;){var i=e[r];if(r==n||i!==o){var o=i;ao(i)?Wt.call(t,i,1):ti(t,i)}}return t}function Un(t,e){return t+tr(fr()*(e-t+1))}function Mn(t,e){var r="";if(!t||e<1||e>9007199254740991)return r;do{e%2&&(r+=t),(e=tr(e/2))&&(t+=t)}while(e);return r}function jn(t,e){return So(go(t,e,Ga),t+"")}function Kn(t){return jr(Pa(t))}function Ln(t,e){var r=Pa(t);return Ao(r,Jr(e,0,r.length))}function qn(t,e,r,n){if(!Vs(t))return t;for(var i=-1,o=(e=ui(e,t)).length,s=o-1,a=t;null!=a&&++i<o;){var u=To(e[i]),c=r;if("__proto__"===u||"constructor"===u||"prototype"===u)return t;if(i!=s){var f=a[u];void 0===(c=n?n(f,u,a):void 0)&&(c=Vs(f)?f:ao(e[i+1])?[]:{})}zr(a,u,c),a=a[u]}return t}var zn=mr?function(t,e){return mr.set(t,e),t}:Ga,Vn=Ae?function(t,e){return Ae(t,"toString",{configurable:!0,enumerable:!1,value:Va(e),writable:!0})}:Ga;function $n(t){return Ao(Pa(t))}function Hn(t,e,r){var i=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var s=n(o);++i<o;)s[i]=t[i+e];return s}function Gn(t,e){var r;return tn(t,(function(t,n,i){return!(r=e(t,n,i))})),!!r}function Wn(t,e,r){var n=0,i=null==t?n:t.length;if("number"==typeof e&&e==e&&i<=2147483647){for(;n<i;){var o=n+i>>>1,s=t[o];null!==s&&!Xs(s)&&(r?s<=e:s<e)?n=o+1:i=o}return i}return Jn(t,e,Ga,r)}function Jn(t,e,r,n){var i=0,o=null==t?0:t.length;if(0===o)return 0;for(var s=(e=r(e))!=e,a=null===e,u=Xs(e),c=void 0===e;i<o;){var f=tr((i+o)/2),l=r(t[f]),h=void 0!==l,d=null===l,p=l==l,y=Xs(l);if(s)var g=n||p;else g=c?p&&(n||h):a?p&&h&&(n||!d):u?p&&h&&!d&&(n||!y):!d&&!y&&(n?l<=e:l<e);g?i=f+1:o=f}return ar(o,4294967294)}function Yn(t,e){for(var r=-1,n=t.length,i=0,o=[];++r<n;){var s=t[r],a=e?e(s):s;if(!r||!Ps(a,u)){var u=a;o[i++]=0===s?0:s}}return o}function Zn(t){return"number"==typeof t?t:Xs(t)?NaN:+t}function Xn(t){if("string"==typeof t)return t;if(Ds(t))return he(t,Xn)+"";if(Xs(t))return Tr?Tr.call(t):"";var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}function Qn(t,e,r){var n=-1,i=fe,o=t.length,s=!0,a=[],u=a;if(r)s=!1,i=le;else if(o>=200){var c=e?null:Mi(t);if(c)return ze(c);s=!1,i=Oe,u=new kr}else u=e?[]:a;t:for(;++n<o;){var f=t[n],l=e?e(f):f;if(f=r||0!==f?f:0,s&&l==l){for(var h=u.length;h--;)if(u[h]===l)continue t;e&&u.push(l),a.push(f)}else i(u,l,r)||(u!==a&&u.push(l),a.push(f))}return a}function ti(t,e){return null==(t=vo(t,e=ui(e,t)))||delete t[To(Ko(e))]}function ei(t,e,r,n){return qn(t,e,r(hn(t,e)),n)}function ri(t,e,r,n){for(var i=t.length,o=n?i:-1;(n?o--:++o<i)&&e(t[o],o,t););return r?Hn(t,n?0:o,n?o+1:i):Hn(t,n?o+1:0,n?i:o)}function ni(t,e){var r=t;return r instanceof Or&&(r=r.value()),pe(e,(function(t,e){return e.func.apply(e.thisArg,de([t],e.args))}),r)}function ii(t,e,r){var i=t.length;if(i<2)return i?Qn(t[0]):[];for(var o=-1,s=n(i);++o<i;)for(var a=t[o],u=-1;++u<i;)u!=o&&(s[o]=Qr(s[o]||a,t[u],e,r));return Qn(sn(s,1),e,r)}function oi(t,e,r){for(var n=-1,i=t.length,o=e.length,s={};++n<i;){var a=n<o?e[n]:void 0;r(s,t[n],a)}return s}function si(t){return Us(t)?t:[]}function ai(t){return"function"==typeof t?t:Ga}function ui(t,e){return Ds(t)?t:co(t,e)?[t]:No(ua(t))}var ci=jn;function fi(t,e,r){var n=t.length;return r=void 0===r?n:r,!e&&r>=n?t:Hn(t,e,r)}var li=Ye||function(t){return Ht.clearTimeout(t)};function hi(t,e){if(e)return t.slice();var r=t.length,n=Lt?Lt(r):new t.constructor(r);return t.copy(n),n}function di(t){var e=new t.constructor(t.byteLength);return new Ft(e).set(new Ft(t)),e}function pi(t,e){var r=e?di(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}function yi(t,e){if(t!==e){var r=void 0!==t,n=null===t,i=t==t,o=Xs(t),s=void 0!==e,a=null===e,u=e==e,c=Xs(e);if(!a&&!c&&!o&&t>e||o&&s&&u&&!a&&!c||n&&s&&u||!r&&u||!i)return 1;if(!n&&!o&&!c&&t<e||c&&r&&i&&!n&&!o||a&&r&&i||!s&&i||!u)return-1}return 0}function gi(t,e,r,i){for(var o=-1,s=t.length,a=r.length,u=-1,c=e.length,f=sr(s-a,0),l=n(c+f),h=!i;++u<c;)l[u]=e[u];for(;++o<a;)(h||o<s)&&(l[r[o]]=t[o]);for(;f--;)l[u++]=t[o++];return l}function vi(t,e,r,i){for(var o=-1,s=t.length,a=-1,u=r.length,c=-1,f=e.length,l=sr(s-u,0),h=n(l+f),d=!i;++o<l;)h[o]=t[o];for(var p=o;++c<f;)h[p+c]=e[c];for(;++a<u;)(d||o<s)&&(h[p+r[a]]=t[o++]);return h}function mi(t,e){var r=-1,i=t.length;for(e||(e=n(i));++r<i;)e[r]=t[r];return e}function _i(t,e,r,n){var i=!r;r||(r={});for(var o=-1,s=e.length;++o<s;){var a=e[o],u=n?n(r[a],t[a],a,r,t):void 0;void 0===u&&(u=t[a]),i?Gr(r,a,u):zr(r,a,u)}return r}function bi(t,e){return function(r,n){var i=Ds(r)?oe:$r,o=e?e():{};return i(r,t,Zi(n,2),o)}}function wi(t){return jn((function(e,r){var n=-1,i=r.length,o=i>1?r[i-1]:void 0,s=i>2?r[2]:void 0;for(o=t.length>3&&"function"==typeof o?(i--,o):void 0,s&&uo(r[0],r[1],s)&&(o=i<3?void 0:o,i=1),e=pt(e);++n<i;){var a=r[n];a&&t(e,a,n,o)}return e}))}function Si(t,e){return function(r,n){if(null==r)return r;if(!ks(r))return t(r,n);for(var i=r.length,o=e?i:-1,s=pt(r);(e?o--:++o<i)&&!1!==n(s[o],o,s););return r}}function Ei(t){return function(e,r,n){for(var i=-1,o=pt(e),s=n(e),a=s.length;a--;){var u=s[t?a:++i];if(!1===r(o[u],u,o))break}return e}}function Ii(t){return function(e){var r=je(e=ua(e))?He(e):void 0,n=r?r[0]:e.charAt(0),i=r?fi(r,1).join(""):e.slice(1);return n[t]()+i}}function Ai(t){return function(e){return pe(La(Ba(e).replace(xt,"")),t,"")}}function Ni(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var r=Rr(t.prototype),n=t.apply(r,e);return Vs(n)?n:r}}function Ti(t){return function(e,r,n){var i=pt(e);if(!ks(e)){var o=Zi(r,3);e=wa(e),r=function(t){return o(i[t],t,i)}}var s=t(e,r,n);return s>-1?i[o?e[s]:s]:void 0}}function Ci(t){return $i((function(e){var r=e.length,n=r,o=xr.prototype.thru;for(t&&e.reverse();n--;){var s=e[n];if("function"!=typeof s)throw new vt(i);if(o&&!a&&"wrapper"==Ji(s))var a=new xr([],!0)}for(n=a?n:r;++n<r;){var u=Ji(s=e[n]),c="wrapper"==u?Wi(s):void 0;a=c&&fo(c[0])&&424==c[1]&&!c[4].length&&1==c[9]?a[Ji(c[0])].apply(a,c[3]):1==s.length&&fo(s)?a[u]():a.thru(s)}return function(){var t=arguments,n=t[0];if(a&&1==t.length&&Ds(n))return a.plant(n).value();for(var i=0,o=r?e[i].apply(this,t):n;++i<r;)o=e[i].call(this,o);return o}}))}function Ri(t,e,r,i,o,s,a,u,c,f){var l=128&e,h=1&e,d=2&e,p=24&e,y=512&e,g=d?void 0:Ni(t);return function v(){for(var m=arguments.length,_=n(m),b=m;b--;)_[b]=arguments[b];if(p)var w=Yi(v),S=Fe(_,w);if(i&&(_=gi(_,i,o,p)),s&&(_=vi(_,s,a,p)),m-=S,p&&m<f){var E=qe(_,w);return ki(t,e,Ri,v.placeholder,r,_,E,u,c,f-m)}var I=h?r:this,A=d?I[t]:t;return m=_.length,u?_=mo(_,u):y&&m>1&&_.reverse(),l&&c<m&&(_.length=c),this&&this!==Ht&&this instanceof v&&(A=g||Ni(A)),A.apply(I,_)}}function Pi(t,e){return function(r,n){return function(t,e,r,n){return cn(t,(function(t,i,o){e(n,r(t),i,o)})),n}(r,t,e(n),{})}}function xi(t,e){return function(r,n){var i;if(void 0===r&&void 0===n)return e;if(void 0!==r&&(i=r),void 0!==n){if(void 0===i)return n;"string"==typeof r||"string"==typeof n?(r=Xn(r),n=Xn(n)):(r=Zn(r),n=Zn(n)),i=t(r,n)}return i}}function Oi(t){return $i((function(e){return e=he(e,Pe(Zi())),jn((function(r){var n=this;return t(e,(function(t){return ie(t,n,r)}))}))}))}function Bi(t,e){var r=(e=void 0===e?" ":Xn(e)).length;if(r<2)return r?Mn(e,t):e;var n=Mn(e,Qe(t/$e(e)));return je(e)?fi(He(n),0,t).join(""):n.slice(0,t)}function Di(t){return function(e,r,i){return i&&"number"!=typeof i&&uo(e,r,i)&&(r=i=void 0),e=na(e),void 0===r?(r=e,e=0):r=na(r),function(t,e,r,i){for(var o=-1,s=sr(Qe((e-t)/(r||1)),0),a=n(s);s--;)a[i?s:++o]=t,t+=r;return a}(e,r,i=void 0===i?e<r?1:-1:na(i),t)}}function Fi(t){return function(e,r){return"string"==typeof e&&"string"==typeof r||(e=sa(e),r=sa(r)),t(e,r)}}function ki(t,e,r,n,i,o,s,a,u,c){var f=8&e;e|=f?32:64,4&(e&=~(f?64:32))||(e&=-4);var l=[t,e,i,f?o:void 0,f?s:void 0,f?void 0:o,f?void 0:s,a,u,c],h=r.apply(void 0,l);return fo(t)&&bo(h,l),h.placeholder=n,Eo(h,t,e)}function Ui(t){var e=dt[t];return function(t,r){if(t=sa(t),(r=null==r?0:ar(ia(r),292))&&nr(t)){var n=(ua(t)+"e").split("e");return+((n=(ua(e(n[0]+"e"+(+n[1]+r)))+"e").split("e"))[0]+"e"+(+n[1]-r))}return e(t)}}var Mi=yr&&1/ze(new yr([,-0]))[1]==1/0?function(t){return new yr(t)}:Xa;function ji(t){return function(e){var r=no(e);return r==p?Ke(e):r==m?Ve(e):function(t,e){return he(e,(function(e){return[e,t[e]]}))}(e,t(e))}}function Ki(t,e,r,s,a,u,c,f){var l=2&e;if(!l&&"function"!=typeof t)throw new vt(i);var h=s?s.length:0;if(h||(e&=-97,s=a=void 0),c=void 0===c?c:sr(ia(c),0),f=void 0===f?f:ia(f),h-=a?a.length:0,64&e){var d=s,p=a;s=a=void 0}var y=l?void 0:Wi(t),g=[t,e,r,s,a,d,p,u,c,f];if(y&&function(t,e){var r=t[1],n=e[1],i=r|n,s=i<131,a=128==n&&8==r||128==n&&256==r&&t[7].length<=e[8]||384==n&&e[7].length<=e[8]&&8==r;if(!s&&!a)return t;1&n&&(t[2]=e[2],i|=1&r?0:4);var u=e[3];if(u){var c=t[3];t[3]=c?gi(c,u,e[4]):u,t[4]=c?qe(t[3],o):e[4]}(u=e[5])&&(c=t[5],t[5]=c?vi(c,u,e[6]):u,t[6]=c?qe(t[5],o):e[6]);(u=e[7])&&(t[7]=u);128&n&&(t[8]=null==t[8]?e[8]:ar(t[8],e[8]));null==t[9]&&(t[9]=e[9]);t[0]=e[0],t[1]=i}(g,y),t=g[0],e=g[1],r=g[2],s=g[3],a=g[4],!(f=g[9]=void 0===g[9]?l?0:t.length:sr(g[9]-h,0))&&24&e&&(e&=-25),e&&1!=e)v=8==e||16==e?function(t,e,r){var i=Ni(t);return function o(){for(var s=arguments.length,a=n(s),u=s,c=Yi(o);u--;)a[u]=arguments[u];var f=s<3&&a[0]!==c&&a[s-1]!==c?[]:qe(a,c);if((s-=f.length)<r)return ki(t,e,Ri,o.placeholder,void 0,a,f,void 0,void 0,r-s);var l=this&&this!==Ht&&this instanceof o?i:t;return ie(l,this,a)}}(t,e,f):32!=e&&33!=e||a.length?Ri.apply(void 0,g):function(t,e,r,i){var o=1&e,s=Ni(t);return function e(){for(var a=-1,u=arguments.length,c=-1,f=i.length,l=n(f+u),h=this&&this!==Ht&&this instanceof e?s:t;++c<f;)l[c]=i[c];for(;u--;)l[c++]=arguments[++a];return ie(h,o?r:this,l)}}(t,e,r,s);else var v=function(t,e,r){var n=1&e,i=Ni(t);return function e(){var o=this&&this!==Ht&&this instanceof e?i:t;return o.apply(n?r:this,arguments)}}(t,e,r);return Eo((y?zn:bo)(v,g),t,e)}function Li(t,e,r,n){return void 0===t||Ps(t,bt[r])&&!Et.call(n,r)?e:t}function qi(t,e,r,n,i,o){return Vs(t)&&Vs(e)&&(o.set(e,t),xn(t,e,void 0,qi,o),o.delete(e)),t}function zi(t){return Ws(t)?void 0:t}function Vi(t,e,r,n,i,o){var s=1&r,a=t.length,u=e.length;if(a!=u&&!(s&&u>a))return!1;var c=o.get(t),f=o.get(e);if(c&&f)return c==e&&f==t;var l=-1,h=!0,d=2&r?new kr:void 0;for(o.set(t,e),o.set(e,t);++l<a;){var p=t[l],y=e[l];if(n)var g=s?n(y,p,l,e,t,o):n(p,y,l,t,e,o);if(void 0!==g){if(g)continue;h=!1;break}if(d){if(!ge(e,(function(t,e){if(!Oe(d,e)&&(p===t||i(p,t,r,n,o)))return d.push(e)}))){h=!1;break}}else if(p!==y&&!i(p,y,r,n,o)){h=!1;break}}return o.delete(t),o.delete(e),h}function $i(t){return So(go(t,void 0,Fo),t+"")}function Hi(t){return dn(t,wa,eo)}function Gi(t){return dn(t,Sa,ro)}var Wi=mr?function(t){return mr.get(t)}:Xa;function Ji(t){for(var e=t.name+"",r=_r[e],n=Et.call(_r,e)?r.length:0;n--;){var i=r[n],o=i.func;if(null==o||o==t)return i.name}return e}function Yi(t){return(Et.call(Cr,"placeholder")?Cr:t).placeholder}function Zi(){var t=Cr.iteratee||Wa;return t=t===Wa?In:t,arguments.length?t(arguments[0],arguments[1]):t}function Xi(t,e){var r,n,i=t.__data__;return("string"==(n=typeof(r=e))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?i["string"==typeof e?"string":"hash"]:i.map}function Qi(t){for(var e=wa(t),r=e.length;r--;){var n=e[r],i=t[n];e[r]=[n,i,po(i)]}return e}function to(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return En(r)?r:void 0}var eo=er?function(t){return null==t?[]:(t=pt(t),ce(er(t),(function(e){return Gt.call(t,e)})))}:ou,ro=er?function(t){for(var e=[];t;)de(e,eo(t)),t=Vt(t);return e}:ou,no=pn;function io(t,e,r){for(var n=-1,i=(e=ui(e,t)).length,o=!1;++n<i;){var s=To(e[n]);if(!(o=null!=t&&r(t,s)))break;t=t[s]}return o||++n!=i?o:!!(i=null==t?0:t.length)&&zs(i)&&ao(s,i)&&(Ds(t)||Bs(t))}function oo(t){return"function"!=typeof t.constructor||ho(t)?{}:Rr(Vt(t))}function so(t){return Ds(t)||Bs(t)||!!(Yt&&t&&t[Yt])}function ao(t,e){var r=typeof t;return!!(e=null==e?9007199254740991:e)&&("number"==r||"symbol"!=r&&at.test(t))&&t>-1&&t%1==0&&t<e}function uo(t,e,r){if(!Vs(r))return!1;var n=typeof e;return!!("number"==n?ks(r)&&ao(e,r.length):"string"==n&&e in r)&&Ps(r[e],t)}function co(t,e){if(Ds(t))return!1;var r=typeof t;return!("number"!=r&&"symbol"!=r&&"boolean"!=r&&null!=t&&!Xs(t))||(z.test(t)||!q.test(t)||null!=e&&t in pt(e))}function fo(t){var e=Ji(t),r=Cr[e];if("function"!=typeof r||!(e in Or.prototype))return!1;if(t===r)return!0;var n=Wi(r);return!!n&&t===n[0]}(hr&&no(new hr(new ArrayBuffer(1)))!=E||dr&&no(new dr)!=p||pr&&"[object Promise]"!=no(pr.resolve())||yr&&no(new yr)!=m||gr&&no(new gr)!=w)&&(no=function(t){var e=pn(t),r=e==g?t.constructor:void 0,n=r?Co(r):"";if(n)switch(n){case br:return E;case wr:return p;case Sr:return"[object Promise]";case Er:return m;case Ir:return w}return e});var lo=wt?Ls:su;function ho(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||bt)}function po(t){return t==t&&!Vs(t)}function yo(t,e){return function(r){return null!=r&&(r[t]===e&&(void 0!==e||t in pt(r)))}}function go(t,e,r){return e=sr(void 0===e?t.length-1:e,0),function(){for(var i=arguments,o=-1,s=sr(i.length-e,0),a=n(s);++o<s;)a[o]=i[e+o];o=-1;for(var u=n(e+1);++o<e;)u[o]=i[o];return u[e]=r(a),ie(t,this,u)}}function vo(t,e){return e.length<2?t:hn(t,Hn(e,0,-1))}function mo(t,e){for(var r=t.length,n=ar(e.length,r),i=mi(t);n--;){var o=e[n];t[n]=ao(o,r)?i[o]:void 0}return t}function _o(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}var bo=Io(zn),wo=Xe||function(t,e){return Ht.setTimeout(t,e)},So=Io(Vn);function Eo(t,e,r){var n=e+"";return So(t,function(t,e){var r=e.length;if(!r)return t;var n=r-1;return e[n]=(r>1?"& ":"")+e[n],e=e.join(r>2?", ":" "),t.replace(J,"{\n/* [wrapped with "+e+"] */\n")}(n,function(t,e){return se(s,(function(r){var n="_."+r[0];e&r[1]&&!fe(t,n)&&t.push(n)})),t.sort()}(function(t){var e=t.match(Y);return e?e[1].split(Z):[]}(n),r)))}function Io(t){var e=0,r=0;return function(){var n=ur(),i=16-(n-r);if(r=n,i>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}function Ao(t,e){var r=-1,n=t.length,i=n-1;for(e=void 0===e?n:e;++r<e;){var o=Un(r,i),s=t[o];t[o]=t[r],t[r]=s}return t.length=e,t}var No=function(t){var e=Is(t,(function(t){return 500===r.size&&r.clear(),t})),r=e.cache;return e}((function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(V,(function(t,r,n,i){e.push(n?i.replace(tt,"$1"):r||t)})),e}));function To(t){if("string"==typeof t||Xs(t))return t;var e=t+"";return"0"==e&&1/t==-1/0?"-0":e}function Co(t){if(null!=t){try{return St.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function Ro(t){if(t instanceof Or)return t.clone();var e=new xr(t.__wrapped__,t.__chain__);return e.__actions__=mi(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e}var Po=jn((function(t,e){return Us(t)?Qr(t,sn(e,1,Us,!0)):[]})),xo=jn((function(t,e){var r=Ko(e);return Us(r)&&(r=void 0),Us(t)?Qr(t,sn(e,1,Us,!0),Zi(r,2)):[]})),Oo=jn((function(t,e){var r=Ko(e);return Us(r)&&(r=void 0),Us(t)?Qr(t,sn(e,1,Us,!0),void 0,r):[]}));function Bo(t,e,r){var n=null==t?0:t.length;if(!n)return-1;var i=null==r?0:ia(r);return i<0&&(i=sr(n+i,0)),_e(t,Zi(e,3),i)}function Do(t,e,r){var n=null==t?0:t.length;if(!n)return-1;var i=n-1;return void 0!==r&&(i=ia(r),i=r<0?sr(n+i,0):ar(i,n-1)),_e(t,Zi(e,3),i,!0)}function Fo(t){return(null==t?0:t.length)?sn(t,1):[]}function ko(t){return t&&t.length?t[0]:void 0}var Uo=jn((function(t){var e=he(t,si);return e.length&&e[0]===t[0]?mn(e):[]})),Mo=jn((function(t){var e=Ko(t),r=he(t,si);return e===Ko(r)?e=void 0:r.pop(),r.length&&r[0]===t[0]?mn(r,Zi(e,2)):[]})),jo=jn((function(t){var e=Ko(t),r=he(t,si);return(e="function"==typeof e?e:void 0)&&r.pop(),r.length&&r[0]===t[0]?mn(r,void 0,e):[]}));function Ko(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}var Lo=jn(qo);function qo(t,e){return t&&t.length&&e&&e.length?Fn(t,e):t}var zo=$i((function(t,e){var r=null==t?0:t.length,n=Wr(t,e);return kn(t,he(e,(function(t){return ao(t,r)?+t:t})).sort(yi)),n}));function Vo(t){return null==t?t:lr.call(t)}var $o=jn((function(t){return Qn(sn(t,1,Us,!0))})),Ho=jn((function(t){var e=Ko(t);return Us(e)&&(e=void 0),Qn(sn(t,1,Us,!0),Zi(e,2))})),Go=jn((function(t){var e=Ko(t);return e="function"==typeof e?e:void 0,Qn(sn(t,1,Us,!0),void 0,e)}));function Wo(t){if(!t||!t.length)return[];var e=0;return t=ce(t,(function(t){if(Us(t))return e=sr(t.length,e),!0})),Ce(e,(function(e){return he(t,Ie(e))}))}function Jo(t,e){if(!t||!t.length)return[];var r=Wo(t);return null==e?r:he(r,(function(t){return ie(e,void 0,t)}))}var Yo=jn((function(t,e){return Us(t)?Qr(t,e):[]})),Zo=jn((function(t){return ii(ce(t,Us))})),Xo=jn((function(t){var e=Ko(t);return Us(e)&&(e=void 0),ii(ce(t,Us),Zi(e,2))})),Qo=jn((function(t){var e=Ko(t);return e="function"==typeof e?e:void 0,ii(ce(t,Us),void 0,e)})),ts=jn(Wo);var es=jn((function(t){var e=t.length,r=e>1?t[e-1]:void 0;return r="function"==typeof r?(t.pop(),r):void 0,Jo(t,r)}));function rs(t){var e=Cr(t);return e.__chain__=!0,e}function ns(t,e){return e(t)}var is=$i((function(t){var e=t.length,r=e?t[0]:0,n=this.__wrapped__,i=function(e){return Wr(e,t)};return!(e>1||this.__actions__.length)&&n instanceof Or&&ao(r)?((n=n.slice(r,+r+(e?1:0))).__actions__.push({func:ns,args:[i],thisArg:void 0}),new xr(n,this.__chain__).thru((function(t){return e&&!t.length&&t.push(void 0),t}))):this.thru(i)}));var os=bi((function(t,e,r){Et.call(t,r)?++t[r]:Gr(t,r,1)}));var ss=Ti(Bo),as=Ti(Do);function us(t,e){return(Ds(t)?se:tn)(t,Zi(e,3))}function cs(t,e){return(Ds(t)?ae:en)(t,Zi(e,3))}var fs=bi((function(t,e,r){Et.call(t,r)?t[r].push(e):Gr(t,r,[e])}));var ls=jn((function(t,e,r){var i=-1,o="function"==typeof e,s=ks(t)?n(t.length):[];return tn(t,(function(t){s[++i]=o?ie(e,t,r):_n(t,e,r)})),s})),hs=bi((function(t,e,r){Gr(t,r,e)}));function ds(t,e){return(Ds(t)?he:Cn)(t,Zi(e,3))}var ps=bi((function(t,e,r){t[r?0:1].push(e)}),(function(){return[[],[]]}));var ys=jn((function(t,e){if(null==t)return[];var r=e.length;return r>1&&uo(t,e[0],e[1])?e=[]:r>2&&uo(e[0],e[1],e[2])&&(e=[e[0]]),Bn(t,sn(e,1),[])})),gs=Ze||function(){return Ht.Date.now()};function vs(t,e,r){return e=r?void 0:e,Ki(t,128,void 0,void 0,void 0,void 0,e=t&&null==e?t.length:e)}function ms(t,e){var r;if("function"!=typeof e)throw new vt(i);return t=ia(t),function(){return--t>0&&(r=e.apply(this,arguments)),t<=1&&(e=void 0),r}}var _s=jn((function(t,e,r){var n=1;if(r.length){var i=qe(r,Yi(_s));n|=32}return Ki(t,n,e,r,i)})),bs=jn((function(t,e,r){var n=3;if(r.length){var i=qe(r,Yi(bs));n|=32}return Ki(e,n,t,r,i)}));function ws(t,e,r){var n,o,s,a,u,c,f=0,l=!1,h=!1,d=!0;if("function"!=typeof t)throw new vt(i);function p(e){var r=n,i=o;return n=o=void 0,f=e,a=t.apply(i,r)}function y(t){return f=t,u=wo(v,e),l?p(t):a}function g(t){var r=t-c;return void 0===c||r>=e||r<0||h&&t-f>=s}function v(){var t=gs();if(g(t))return m(t);u=wo(v,function(t){var r=e-(t-c);return h?ar(r,s-(t-f)):r}(t))}function m(t){return u=void 0,d&&n?p(t):(n=o=void 0,a)}function _(){var t=gs(),r=g(t);if(n=arguments,o=this,c=t,r){if(void 0===u)return y(c);if(h)return li(u),u=wo(v,e),p(c)}return void 0===u&&(u=wo(v,e)),a}return e=sa(e)||0,Vs(r)&&(l=!!r.leading,s=(h="maxWait"in r)?sr(sa(r.maxWait)||0,e):s,d="trailing"in r?!!r.trailing:d),_.cancel=function(){void 0!==u&&li(u),f=0,n=c=o=u=void 0},_.flush=function(){return void 0===u?a:m(gs())},_}var Ss=jn((function(t,e){return Xr(t,1,e)})),Es=jn((function(t,e,r){return Xr(t,sa(e)||0,r)}));function Is(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new vt(i);var r=function(){var n=arguments,i=e?e.apply(this,n):n[0],o=r.cache;if(o.has(i))return o.get(i);var s=t.apply(this,n);return r.cache=o.set(i,s)||o,s};return r.cache=new(Is.Cache||Fr),r}function As(t){if("function"!=typeof t)throw new vt(i);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}Is.Cache=Fr;var Ns=ci((function(t,e){var r=(e=1==e.length&&Ds(e[0])?he(e[0],Pe(Zi())):he(sn(e,1),Pe(Zi()))).length;return jn((function(n){for(var i=-1,o=ar(n.length,r);++i<o;)n[i]=e[i].call(this,n[i]);return ie(t,this,n)}))})),Ts=jn((function(t,e){return Ki(t,32,void 0,e,qe(e,Yi(Ts)))})),Cs=jn((function(t,e){return Ki(t,64,void 0,e,qe(e,Yi(Cs)))})),Rs=$i((function(t,e){return Ki(t,256,void 0,void 0,void 0,e)}));function Ps(t,e){return t===e||t!=t&&e!=e}var xs=Fi(yn),Os=Fi((function(t,e){return t>=e})),Bs=bn(function(){return arguments}())?bn:function(t){return $s(t)&&Et.call(t,"callee")&&!Gt.call(t,"callee")},Ds=n.isArray,Fs=Xt?Pe(Xt):function(t){return $s(t)&&pn(t)==S};function ks(t){return null!=t&&zs(t.length)&&!Ls(t)}function Us(t){return $s(t)&&ks(t)}var Ms=rr||su,js=Qt?Pe(Qt):function(t){return $s(t)&&pn(t)==f};function Ks(t){if(!$s(t))return!1;var e=pn(t);return e==l||"[object DOMException]"==e||"string"==typeof t.message&&"string"==typeof t.name&&!Ws(t)}function Ls(t){if(!Vs(t))return!1;var e=pn(t);return e==h||e==d||"[object AsyncFunction]"==e||"[object Proxy]"==e}function qs(t){return"number"==typeof t&&t==ia(t)}function zs(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}function Vs(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function $s(t){return null!=t&&"object"==typeof t}var Hs=te?Pe(te):function(t){return $s(t)&&no(t)==p};function Gs(t){return"number"==typeof t||$s(t)&&pn(t)==y}function Ws(t){if(!$s(t)||pn(t)!=g)return!1;var e=Vt(t);if(null===e)return!0;var r=Et.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&St.call(r)==Tt}var Js=ee?Pe(ee):function(t){return $s(t)&&pn(t)==v};var Ys=re?Pe(re):function(t){return $s(t)&&no(t)==m};function Zs(t){return"string"==typeof t||!Ds(t)&&$s(t)&&pn(t)==_}function Xs(t){return"symbol"==typeof t||$s(t)&&pn(t)==b}var Qs=ne?Pe(ne):function(t){return $s(t)&&zs(t.length)&&!!jt[pn(t)]};var ta=Fi(Tn),ea=Fi((function(t,e){return t<=e}));function ra(t){if(!t)return[];if(ks(t))return Zs(t)?He(t):mi(t);if(Zt&&t[Zt])return function(t){for(var e,r=[];!(e=t.next()).done;)r.push(e.value);return r}(t[Zt]());var e=no(t);return(e==p?Ke:e==m?ze:Pa)(t)}function na(t){return t?(t=sa(t))===1/0||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}function ia(t){var e=na(t),r=e%1;return e==e?r?e-r:e:0}function oa(t){return t?Jr(ia(t),0,4294967295):0}function sa(t){if("number"==typeof t)return t;if(Xs(t))return NaN;if(Vs(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=Vs(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=Re(t);var r=it.test(t);return r||st.test(t)?zt(t.slice(2),r?2:8):nt.test(t)?NaN:+t}function aa(t){return _i(t,Sa(t))}function ua(t){return null==t?"":Xn(t)}var ca=wi((function(t,e){if(ho(e)||ks(e))_i(e,wa(e),t);else for(var r in e)Et.call(e,r)&&zr(t,r,e[r])})),fa=wi((function(t,e){_i(e,Sa(e),t)})),la=wi((function(t,e,r,n){_i(e,Sa(e),t,n)})),ha=wi((function(t,e,r,n){_i(e,wa(e),t,n)})),da=$i(Wr);var pa=jn((function(t,e){t=pt(t);var r=-1,n=e.length,i=n>2?e[2]:void 0;for(i&&uo(e[0],e[1],i)&&(n=1);++r<n;)for(var o=e[r],s=Sa(o),a=-1,u=s.length;++a<u;){var c=s[a],f=t[c];(void 0===f||Ps(f,bt[c])&&!Et.call(t,c))&&(t[c]=o[c])}return t})),ya=jn((function(t){return t.push(void 0,qi),ie(Ia,void 0,t)}));function ga(t,e,r){var n=null==t?void 0:hn(t,e);return void 0===n?r:n}function va(t,e){return null!=t&&io(t,e,vn)}var ma=Pi((function(t,e,r){null!=e&&"function"!=typeof e.toString&&(e=Nt.call(e)),t[e]=r}),Va(Ga)),_a=Pi((function(t,e,r){null!=e&&"function"!=typeof e.toString&&(e=Nt.call(e)),Et.call(t,e)?t[e].push(r):t[e]=[r]}),Zi),ba=jn(_n);function wa(t){return ks(t)?Mr(t):An(t)}function Sa(t){return ks(t)?Mr(t,!0):Nn(t)}var Ea=wi((function(t,e,r){xn(t,e,r)})),Ia=wi((function(t,e,r,n){xn(t,e,r,n)})),Aa=$i((function(t,e){var r={};if(null==t)return r;var n=!1;e=he(e,(function(e){return e=ui(e,t),n||(n=e.length>1),e})),_i(t,Gi(t),r),n&&(r=Yr(r,7,zi));for(var i=e.length;i--;)ti(r,e[i]);return r}));var Na=$i((function(t,e){return null==t?{}:function(t,e){return Dn(t,e,(function(e,r){return va(t,r)}))}(t,e)}));function Ta(t,e){if(null==t)return{};var r=he(Gi(t),(function(t){return[t]}));return e=Zi(e),Dn(t,r,(function(t,r){return e(t,r[0])}))}var Ca=ji(wa),Ra=ji(Sa);function Pa(t){return null==t?[]:xe(t,wa(t))}var xa=Ai((function(t,e,r){return e=e.toLowerCase(),t+(r?Oa(e):e)}));function Oa(t){return Ka(ua(t).toLowerCase())}function Ba(t){return(t=ua(t))&&t.replace(ut,ke).replace(Ot,"")}var Da=Ai((function(t,e,r){return t+(r?"-":"")+e.toLowerCase()})),Fa=Ai((function(t,e,r){return t+(r?" ":"")+e.toLowerCase()})),ka=Ii("toLowerCase");var Ua=Ai((function(t,e,r){return t+(r?"_":"")+e.toLowerCase()}));var Ma=Ai((function(t,e,r){return t+(r?" ":"")+Ka(e)}));var ja=Ai((function(t,e,r){return t+(r?" ":"")+e.toUpperCase()})),Ka=Ii("toUpperCase");function La(t,e,r){return t=ua(t),void 0===(e=r?void 0:e)?function(t){return kt.test(t)}(t)?function(t){return t.match(Dt)||[]}(t):function(t){return t.match(X)||[]}(t):t.match(e)||[]}var qa=jn((function(t,e){try{return ie(t,void 0,e)}catch(t){return Ks(t)?t:new lt(t)}})),za=$i((function(t,e){return se(e,(function(e){e=To(e),Gr(t,e,_s(t[e],t))})),t}));function Va(t){return function(){return t}}var $a=Ci(),Ha=Ci(!0);function Ga(t){return t}function Wa(t){return In("function"==typeof t?t:Yr(t,1))}var Ja=jn((function(t,e){return function(r){return _n(r,t,e)}})),Ya=jn((function(t,e){return function(r){return _n(t,r,e)}}));function Za(t,e,r){var n=wa(e),i=ln(e,n);null!=r||Vs(e)&&(i.length||!n.length)||(r=e,e=t,t=this,i=ln(e,wa(e)));var o=!(Vs(r)&&"chain"in r&&!r.chain),s=Ls(t);return se(i,(function(r){var n=e[r];t[r]=n,s&&(t.prototype[r]=function(){var e=this.__chain__;if(o||e){var r=t(this.__wrapped__),i=r.__actions__=mi(this.__actions__);return i.push({func:n,args:arguments,thisArg:t}),r.__chain__=e,r}return n.apply(t,de([this.value()],arguments))})})),t}function Xa(){}var Qa=Oi(he),tu=Oi(ue),eu=Oi(ge);function ru(t){return co(t)?Ie(To(t)):function(t){return function(e){return hn(e,t)}}(t)}var nu=Di(),iu=Di(!0);function ou(){return[]}function su(){return!1}var au=xi((function(t,e){return t+e}),0),uu=Ui("ceil"),cu=xi((function(t,e){return t/e}),1),fu=Ui("floor");var lu,hu=xi((function(t,e){return t*e}),1),du=Ui("round"),pu=xi((function(t,e){return t-e}),0);return Cr.after=function(t,e){if("function"!=typeof e)throw new vt(i);return t=ia(t),function(){if(--t<1)return e.apply(this,arguments)}},Cr.ary=vs,Cr.assign=ca,Cr.assignIn=fa,Cr.assignInWith=la,Cr.assignWith=ha,Cr.at=da,Cr.before=ms,Cr.bind=_s,Cr.bindAll=za,Cr.bindKey=bs,Cr.castArray=function(){if(!arguments.length)return[];var t=arguments[0];return Ds(t)?t:[t]},Cr.chain=rs,Cr.chunk=function(t,e,r){e=(r?uo(t,e,r):void 0===e)?1:sr(ia(e),0);var i=null==t?0:t.length;if(!i||e<1)return[];for(var o=0,s=0,a=n(Qe(i/e));o<i;)a[s++]=Hn(t,o,o+=e);return a},Cr.compact=function(t){for(var e=-1,r=null==t?0:t.length,n=0,i=[];++e<r;){var o=t[e];o&&(i[n++]=o)}return i},Cr.concat=function(){var t=arguments.length;if(!t)return[];for(var e=n(t-1),r=arguments[0],i=t;i--;)e[i-1]=arguments[i];return de(Ds(r)?mi(r):[r],sn(e,1))},Cr.cond=function(t){var e=null==t?0:t.length,r=Zi();return t=e?he(t,(function(t){if("function"!=typeof t[1])throw new vt(i);return[r(t[0]),t[1]]})):[],jn((function(r){for(var n=-1;++n<e;){var i=t[n];if(ie(i[0],this,r))return ie(i[1],this,r)}}))},Cr.conforms=function(t){return function(t){var e=wa(t);return function(r){return Zr(r,t,e)}}(Yr(t,1))},Cr.constant=Va,Cr.countBy=os,Cr.create=function(t,e){var r=Rr(t);return null==e?r:Hr(r,e)},Cr.curry=function t(e,r,n){var i=Ki(e,8,void 0,void 0,void 0,void 0,void 0,r=n?void 0:r);return i.placeholder=t.placeholder,i},Cr.curryRight=function t(e,r,n){var i=Ki(e,16,void 0,void 0,void 0,void 0,void 0,r=n?void 0:r);return i.placeholder=t.placeholder,i},Cr.debounce=ws,Cr.defaults=pa,Cr.defaultsDeep=ya,Cr.defer=Ss,Cr.delay=Es,Cr.difference=Po,Cr.differenceBy=xo,Cr.differenceWith=Oo,Cr.drop=function(t,e,r){var n=null==t?0:t.length;return n?Hn(t,(e=r||void 0===e?1:ia(e))<0?0:e,n):[]},Cr.dropRight=function(t,e,r){var n=null==t?0:t.length;return n?Hn(t,0,(e=n-(e=r||void 0===e?1:ia(e)))<0?0:e):[]},Cr.dropRightWhile=function(t,e){return t&&t.length?ri(t,Zi(e,3),!0,!0):[]},Cr.dropWhile=function(t,e){return t&&t.length?ri(t,Zi(e,3),!0):[]},Cr.fill=function(t,e,r,n){var i=null==t?0:t.length;return i?(r&&"number"!=typeof r&&uo(t,e,r)&&(r=0,n=i),function(t,e,r,n){var i=t.length;for((r=ia(r))<0&&(r=-r>i?0:i+r),(n=void 0===n||n>i?i:ia(n))<0&&(n+=i),n=r>n?0:oa(n);r<n;)t[r++]=e;return t}(t,e,r,n)):[]},Cr.filter=function(t,e){return(Ds(t)?ce:on)(t,Zi(e,3))},Cr.flatMap=function(t,e){return sn(ds(t,e),1)},Cr.flatMapDeep=function(t,e){return sn(ds(t,e),1/0)},Cr.flatMapDepth=function(t,e,r){return r=void 0===r?1:ia(r),sn(ds(t,e),r)},Cr.flatten=Fo,Cr.flattenDeep=function(t){return(null==t?0:t.length)?sn(t,1/0):[]},Cr.flattenDepth=function(t,e){return(null==t?0:t.length)?sn(t,e=void 0===e?1:ia(e)):[]},Cr.flip=function(t){return Ki(t,512)},Cr.flow=$a,Cr.flowRight=Ha,Cr.fromPairs=function(t){for(var e=-1,r=null==t?0:t.length,n={};++e<r;){var i=t[e];n[i[0]]=i[1]}return n},Cr.functions=function(t){return null==t?[]:ln(t,wa(t))},Cr.functionsIn=function(t){return null==t?[]:ln(t,Sa(t))},Cr.groupBy=fs,Cr.initial=function(t){return(null==t?0:t.length)?Hn(t,0,-1):[]},Cr.intersection=Uo,Cr.intersectionBy=Mo,Cr.intersectionWith=jo,Cr.invert=ma,Cr.invertBy=_a,Cr.invokeMap=ls,Cr.iteratee=Wa,Cr.keyBy=hs,Cr.keys=wa,Cr.keysIn=Sa,Cr.map=ds,Cr.mapKeys=function(t,e){var r={};return e=Zi(e,3),cn(t,(function(t,n,i){Gr(r,e(t,n,i),t)})),r},Cr.mapValues=function(t,e){var r={};return e=Zi(e,3),cn(t,(function(t,n,i){Gr(r,n,e(t,n,i))})),r},Cr.matches=function(t){return Rn(Yr(t,1))},Cr.matchesProperty=function(t,e){return Pn(t,Yr(e,1))},Cr.memoize=Is,Cr.merge=Ea,Cr.mergeWith=Ia,Cr.method=Ja,Cr.methodOf=Ya,Cr.mixin=Za,Cr.negate=As,Cr.nthArg=function(t){return t=ia(t),jn((function(e){return On(e,t)}))},Cr.omit=Aa,Cr.omitBy=function(t,e){return Ta(t,As(Zi(e)))},Cr.once=function(t){return ms(2,t)},Cr.orderBy=function(t,e,r,n){return null==t?[]:(Ds(e)||(e=null==e?[]:[e]),Ds(r=n?void 0:r)||(r=null==r?[]:[r]),Bn(t,e,r))},Cr.over=Qa,Cr.overArgs=Ns,Cr.overEvery=tu,Cr.overSome=eu,Cr.partial=Ts,Cr.partialRight=Cs,Cr.partition=ps,Cr.pick=Na,Cr.pickBy=Ta,Cr.property=ru,Cr.propertyOf=function(t){return function(e){return null==t?void 0:hn(t,e)}},Cr.pull=Lo,Cr.pullAll=qo,Cr.pullAllBy=function(t,e,r){return t&&t.length&&e&&e.length?Fn(t,e,Zi(r,2)):t},Cr.pullAllWith=function(t,e,r){return t&&t.length&&e&&e.length?Fn(t,e,void 0,r):t},Cr.pullAt=zo,Cr.range=nu,Cr.rangeRight=iu,Cr.rearg=Rs,Cr.reject=function(t,e){return(Ds(t)?ce:on)(t,As(Zi(e,3)))},Cr.remove=function(t,e){var r=[];if(!t||!t.length)return r;var n=-1,i=[],o=t.length;for(e=Zi(e,3);++n<o;){var s=t[n];e(s,n,t)&&(r.push(s),i.push(n))}return kn(t,i),r},Cr.rest=function(t,e){if("function"!=typeof t)throw new vt(i);return jn(t,e=void 0===e?e:ia(e))},Cr.reverse=Vo,Cr.sampleSize=function(t,e,r){return e=(r?uo(t,e,r):void 0===e)?1:ia(e),(Ds(t)?Kr:Ln)(t,e)},Cr.set=function(t,e,r){return null==t?t:qn(t,e,r)},Cr.setWith=function(t,e,r,n){return n="function"==typeof n?n:void 0,null==t?t:qn(t,e,r,n)},Cr.shuffle=function(t){return(Ds(t)?Lr:$n)(t)},Cr.slice=function(t,e,r){var n=null==t?0:t.length;return n?(r&&"number"!=typeof r&&uo(t,e,r)?(e=0,r=n):(e=null==e?0:ia(e),r=void 0===r?n:ia(r)),Hn(t,e,r)):[]},Cr.sortBy=ys,Cr.sortedUniq=function(t){return t&&t.length?Yn(t):[]},Cr.sortedUniqBy=function(t,e){return t&&t.length?Yn(t,Zi(e,2)):[]},Cr.split=function(t,e,r){return r&&"number"!=typeof r&&uo(t,e,r)&&(e=r=void 0),(r=void 0===r?4294967295:r>>>0)?(t=ua(t))&&("string"==typeof e||null!=e&&!Js(e))&&!(e=Xn(e))&&je(t)?fi(He(t),0,r):t.split(e,r):[]},Cr.spread=function(t,e){if("function"!=typeof t)throw new vt(i);return e=null==e?0:sr(ia(e),0),jn((function(r){var n=r[e],i=fi(r,0,e);return n&&de(i,n),ie(t,this,i)}))},Cr.tail=function(t){var e=null==t?0:t.length;return e?Hn(t,1,e):[]},Cr.take=function(t,e,r){return t&&t.length?Hn(t,0,(e=r||void 0===e?1:ia(e))<0?0:e):[]},Cr.takeRight=function(t,e,r){var n=null==t?0:t.length;return n?Hn(t,(e=n-(e=r||void 0===e?1:ia(e)))<0?0:e,n):[]},Cr.takeRightWhile=function(t,e){return t&&t.length?ri(t,Zi(e,3),!1,!0):[]},Cr.takeWhile=function(t,e){return t&&t.length?ri(t,Zi(e,3)):[]},Cr.tap=function(t,e){return e(t),t},Cr.throttle=function(t,e,r){var n=!0,o=!0;if("function"!=typeof t)throw new vt(i);return Vs(r)&&(n="leading"in r?!!r.leading:n,o="trailing"in r?!!r.trailing:o),ws(t,e,{leading:n,maxWait:e,trailing:o})},Cr.thru=ns,Cr.toArray=ra,Cr.toPairs=Ca,Cr.toPairsIn=Ra,Cr.toPath=function(t){return Ds(t)?he(t,To):Xs(t)?[t]:mi(No(ua(t)))},Cr.toPlainObject=aa,Cr.transform=function(t,e,r){var n=Ds(t),i=n||Ms(t)||Qs(t);if(e=Zi(e,4),null==r){var o=t&&t.constructor;r=i?n?new o:[]:Vs(t)&&Ls(o)?Rr(Vt(t)):{}}return(i?se:cn)(t,(function(t,n,i){return e(r,t,n,i)})),r},Cr.unary=function(t){return vs(t,1)},Cr.union=$o,Cr.unionBy=Ho,Cr.unionWith=Go,Cr.uniq=function(t){return t&&t.length?Qn(t):[]},Cr.uniqBy=function(t,e){return t&&t.length?Qn(t,Zi(e,2)):[]},Cr.uniqWith=function(t,e){return e="function"==typeof e?e:void 0,t&&t.length?Qn(t,void 0,e):[]},Cr.unset=function(t,e){return null==t||ti(t,e)},Cr.unzip=Wo,Cr.unzipWith=Jo,Cr.update=function(t,e,r){return null==t?t:ei(t,e,ai(r))},Cr.updateWith=function(t,e,r,n){return n="function"==typeof n?n:void 0,null==t?t:ei(t,e,ai(r),n)},Cr.values=Pa,Cr.valuesIn=function(t){return null==t?[]:xe(t,Sa(t))},Cr.without=Yo,Cr.words=La,Cr.wrap=function(t,e){return Ts(ai(e),t)},Cr.xor=Zo,Cr.xorBy=Xo,Cr.xorWith=Qo,Cr.zip=ts,Cr.zipObject=function(t,e){return oi(t||[],e||[],zr)},Cr.zipObjectDeep=function(t,e){return oi(t||[],e||[],qn)},Cr.zipWith=es,Cr.entries=Ca,Cr.entriesIn=Ra,Cr.extend=fa,Cr.extendWith=la,Za(Cr,Cr),Cr.add=au,Cr.attempt=qa,Cr.camelCase=xa,Cr.capitalize=Oa,Cr.ceil=uu,Cr.clamp=function(t,e,r){return void 0===r&&(r=e,e=void 0),void 0!==r&&(r=(r=sa(r))==r?r:0),void 0!==e&&(e=(e=sa(e))==e?e:0),Jr(sa(t),e,r)},Cr.clone=function(t){return Yr(t,4)},Cr.cloneDeep=function(t){return Yr(t,5)},Cr.cloneDeepWith=function(t,e){return Yr(t,5,e="function"==typeof e?e:void 0)},Cr.cloneWith=function(t,e){return Yr(t,4,e="function"==typeof e?e:void 0)},Cr.conformsTo=function(t,e){return null==e||Zr(t,e,wa(e))},Cr.deburr=Ba,Cr.defaultTo=function(t,e){return null==t||t!=t?e:t},Cr.divide=cu,Cr.endsWith=function(t,e,r){t=ua(t),e=Xn(e);var n=t.length,i=r=void 0===r?n:Jr(ia(r),0,n);return(r-=e.length)>=0&&t.slice(r,i)==e},Cr.eq=Ps,Cr.escape=function(t){return(t=ua(t))&&M.test(t)?t.replace(k,Ue):t},Cr.escapeRegExp=function(t){return(t=ua(t))&&H.test(t)?t.replace($,"\\$&"):t},Cr.every=function(t,e,r){var n=Ds(t)?ue:rn;return r&&uo(t,e,r)&&(e=void 0),n(t,Zi(e,3))},Cr.find=ss,Cr.findIndex=Bo,Cr.findKey=function(t,e){return me(t,Zi(e,3),cn)},Cr.findLast=as,Cr.findLastIndex=Do,Cr.findLastKey=function(t,e){return me(t,Zi(e,3),fn)},Cr.floor=fu,Cr.forEach=us,Cr.forEachRight=cs,Cr.forIn=function(t,e){return null==t?t:an(t,Zi(e,3),Sa)},Cr.forInRight=function(t,e){return null==t?t:un(t,Zi(e,3),Sa)},Cr.forOwn=function(t,e){return t&&cn(t,Zi(e,3))},Cr.forOwnRight=function(t,e){return t&&fn(t,Zi(e,3))},Cr.get=ga,Cr.gt=xs,Cr.gte=Os,Cr.has=function(t,e){return null!=t&&io(t,e,gn)},Cr.hasIn=va,Cr.head=ko,Cr.identity=Ga,Cr.includes=function(t,e,r,n){t=ks(t)?t:Pa(t),r=r&&!n?ia(r):0;var i=t.length;return r<0&&(r=sr(i+r,0)),Zs(t)?r<=i&&t.indexOf(e,r)>-1:!!i&&be(t,e,r)>-1},Cr.indexOf=function(t,e,r){var n=null==t?0:t.length;if(!n)return-1;var i=null==r?0:ia(r);return i<0&&(i=sr(n+i,0)),be(t,e,i)},Cr.inRange=function(t,e,r){return e=na(e),void 0===r?(r=e,e=0):r=na(r),function(t,e,r){return t>=ar(e,r)&&t<sr(e,r)}(t=sa(t),e,r)},Cr.invoke=ba,Cr.isArguments=Bs,Cr.isArray=Ds,Cr.isArrayBuffer=Fs,Cr.isArrayLike=ks,Cr.isArrayLikeObject=Us,Cr.isBoolean=function(t){return!0===t||!1===t||$s(t)&&pn(t)==c},Cr.isBuffer=Ms,Cr.isDate=js,Cr.isElement=function(t){return $s(t)&&1===t.nodeType&&!Ws(t)},Cr.isEmpty=function(t){if(null==t)return!0;if(ks(t)&&(Ds(t)||"string"==typeof t||"function"==typeof t.splice||Ms(t)||Qs(t)||Bs(t)))return!t.length;var e=no(t);if(e==p||e==m)return!t.size;if(ho(t))return!An(t).length;for(var r in t)if(Et.call(t,r))return!1;return!0},Cr.isEqual=function(t,e){return wn(t,e)},Cr.isEqualWith=function(t,e,r){var n=(r="function"==typeof r?r:void 0)?r(t,e):void 0;return void 0===n?wn(t,e,void 0,r):!!n},Cr.isError=Ks,Cr.isFinite=function(t){return"number"==typeof t&&nr(t)},Cr.isFunction=Ls,Cr.isInteger=qs,Cr.isLength=zs,Cr.isMap=Hs,Cr.isMatch=function(t,e){return t===e||Sn(t,e,Qi(e))},Cr.isMatchWith=function(t,e,r){return r="function"==typeof r?r:void 0,Sn(t,e,Qi(e),r)},Cr.isNaN=function(t){return Gs(t)&&t!=+t},Cr.isNative=function(t){if(lo(t))throw new lt("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return En(t)},Cr.isNil=function(t){return null==t},Cr.isNull=function(t){return null===t},Cr.isNumber=Gs,Cr.isObject=Vs,Cr.isObjectLike=$s,Cr.isPlainObject=Ws,Cr.isRegExp=Js,Cr.isSafeInteger=function(t){return qs(t)&&t>=-9007199254740991&&t<=9007199254740991},Cr.isSet=Ys,Cr.isString=Zs,Cr.isSymbol=Xs,Cr.isTypedArray=Qs,Cr.isUndefined=function(t){return void 0===t},Cr.isWeakMap=function(t){return $s(t)&&no(t)==w},Cr.isWeakSet=function(t){return $s(t)&&"[object WeakSet]"==pn(t)},Cr.join=function(t,e){return null==t?"":ir.call(t,e)},Cr.kebabCase=Da,Cr.last=Ko,Cr.lastIndexOf=function(t,e,r){var n=null==t?0:t.length;if(!n)return-1;var i=n;return void 0!==r&&(i=(i=ia(r))<0?sr(n+i,0):ar(i,n-1)),e==e?function(t,e,r){for(var n=r+1;n--;)if(t[n]===e)return n;return n}(t,e,i):_e(t,Se,i,!0)},Cr.lowerCase=Fa,Cr.lowerFirst=ka,Cr.lt=ta,Cr.lte=ea,Cr.max=function(t){return t&&t.length?nn(t,Ga,yn):void 0},Cr.maxBy=function(t,e){return t&&t.length?nn(t,Zi(e,2),yn):void 0},Cr.mean=function(t){return Ee(t,Ga)},Cr.meanBy=function(t,e){return Ee(t,Zi(e,2))},Cr.min=function(t){return t&&t.length?nn(t,Ga,Tn):void 0},Cr.minBy=function(t,e){return t&&t.length?nn(t,Zi(e,2),Tn):void 0},Cr.stubArray=ou,Cr.stubFalse=su,Cr.stubObject=function(){return{}},Cr.stubString=function(){return""},Cr.stubTrue=function(){return!0},Cr.multiply=hu,Cr.nth=function(t,e){return t&&t.length?On(t,ia(e)):void 0},Cr.noConflict=function(){return Ht._===this&&(Ht._=Ct),this},Cr.noop=Xa,Cr.now=gs,Cr.pad=function(t,e,r){t=ua(t);var n=(e=ia(e))?$e(t):0;if(!e||n>=e)return t;var i=(e-n)/2;return Bi(tr(i),r)+t+Bi(Qe(i),r)},Cr.padEnd=function(t,e,r){t=ua(t);var n=(e=ia(e))?$e(t):0;return e&&n<e?t+Bi(e-n,r):t},Cr.padStart=function(t,e,r){t=ua(t);var n=(e=ia(e))?$e(t):0;return e&&n<e?Bi(e-n,r)+t:t},Cr.parseInt=function(t,e,r){return r||null==e?e=0:e&&(e=+e),cr(ua(t).replace(G,""),e||0)},Cr.random=function(t,e,r){if(r&&"boolean"!=typeof r&&uo(t,e,r)&&(e=r=void 0),void 0===r&&("boolean"==typeof e?(r=e,e=void 0):"boolean"==typeof t&&(r=t,t=void 0)),void 0===t&&void 0===e?(t=0,e=1):(t=na(t),void 0===e?(e=t,t=0):e=na(e)),t>e){var n=t;t=e,e=n}if(r||t%1||e%1){var i=fr();return ar(t+i*(e-t+qt("1e-"+((i+"").length-1))),e)}return Un(t,e)},Cr.reduce=function(t,e,r){var n=Ds(t)?pe:Ne,i=arguments.length<3;return n(t,Zi(e,4),r,i,tn)},Cr.reduceRight=function(t,e,r){var n=Ds(t)?ye:Ne,i=arguments.length<3;return n(t,Zi(e,4),r,i,en)},Cr.repeat=function(t,e,r){return e=(r?uo(t,e,r):void 0===e)?1:ia(e),Mn(ua(t),e)},Cr.replace=function(){var t=arguments,e=ua(t[0]);return t.length<3?e:e.replace(t[1],t[2])},Cr.result=function(t,e,r){var n=-1,i=(e=ui(e,t)).length;for(i||(i=1,t=void 0);++n<i;){var o=null==t?void 0:t[To(e[n])];void 0===o&&(n=i,o=r),t=Ls(o)?o.call(t):o}return t},Cr.round=du,Cr.runInContext=t,Cr.sample=function(t){return(Ds(t)?jr:Kn)(t)},Cr.size=function(t){if(null==t)return 0;if(ks(t))return Zs(t)?$e(t):t.length;var e=no(t);return e==p||e==m?t.size:An(t).length},Cr.snakeCase=Ua,Cr.some=function(t,e,r){var n=Ds(t)?ge:Gn;return r&&uo(t,e,r)&&(e=void 0),n(t,Zi(e,3))},Cr.sortedIndex=function(t,e){return Wn(t,e)},Cr.sortedIndexBy=function(t,e,r){return Jn(t,e,Zi(r,2))},Cr.sortedIndexOf=function(t,e){var r=null==t?0:t.length;if(r){var n=Wn(t,e);if(n<r&&Ps(t[n],e))return n}return-1},Cr.sortedLastIndex=function(t,e){return Wn(t,e,!0)},Cr.sortedLastIndexBy=function(t,e,r){return Jn(t,e,Zi(r,2),!0)},Cr.sortedLastIndexOf=function(t,e){if(null==t?0:t.length){var r=Wn(t,e,!0)-1;if(Ps(t[r],e))return r}return-1},Cr.startCase=Ma,Cr.startsWith=function(t,e,r){return t=ua(t),r=null==r?0:Jr(ia(r),0,t.length),e=Xn(e),t.slice(r,r+e.length)==e},Cr.subtract=pu,Cr.sum=function(t){return t&&t.length?Te(t,Ga):0},Cr.sumBy=function(t,e){return t&&t.length?Te(t,Zi(e,2)):0},Cr.template=function(t,e,r){var n=Cr.templateSettings;r&&uo(t,e,r)&&(e=void 0),t=ua(t),e=la({},e,n,Li);var i,o,s=la({},e.imports,n.imports,Li),a=wa(s),u=xe(s,a),c=0,f=e.interpolate||ct,l="__p += '",h=yt((e.escape||ct).source+"|"+f.source+"|"+(f===L?et:ct).source+"|"+(e.evaluate||ct).source+"|$","g"),d="//# sourceURL="+(Et.call(e,"sourceURL")?(e.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Mt+"]")+"\n";t.replace(h,(function(e,r,n,s,a,u){return n||(n=s),l+=t.slice(c,u).replace(ft,Me),r&&(i=!0,l+="' +\n__e("+r+") +\n'"),a&&(o=!0,l+="';\n"+a+";\n__p += '"),n&&(l+="' +\n((__t = ("+n+")) == null ? '' : __t) +\n'"),c=u+e.length,e})),l+="';\n";var p=Et.call(e,"variable")&&e.variable;if(p){if(Q.test(p))throw new lt("Invalid `variable` option passed into `_.template`")}else l="with (obj) {\n"+l+"\n}\n";l=(o?l.replace(O,""):l).replace(B,"$1").replace(D,"$1;"),l="function("+(p||"obj")+") {\n"+(p?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+l+"return __p\n}";var y=qa((function(){return ht(a,d+"return "+l).apply(void 0,u)}));if(y.source=l,Ks(y))throw y;return y},Cr.times=function(t,e){if((t=ia(t))<1||t>9007199254740991)return[];var r=4294967295,n=ar(t,4294967295);t-=4294967295;for(var i=Ce(n,e=Zi(e));++r<t;)e(r);return i},Cr.toFinite=na,Cr.toInteger=ia,Cr.toLength=oa,Cr.toLower=function(t){return ua(t).toLowerCase()},Cr.toNumber=sa,Cr.toSafeInteger=function(t){return t?Jr(ia(t),-9007199254740991,9007199254740991):0===t?t:0},Cr.toString=ua,Cr.toUpper=function(t){return ua(t).toUpperCase()},Cr.trim=function(t,e,r){if((t=ua(t))&&(r||void 0===e))return Re(t);if(!t||!(e=Xn(e)))return t;var n=He(t),i=He(e);return fi(n,Be(n,i),De(n,i)+1).join("")},Cr.trimEnd=function(t,e,r){if((t=ua(t))&&(r||void 0===e))return t.slice(0,Ge(t)+1);if(!t||!(e=Xn(e)))return t;var n=He(t);return fi(n,0,De(n,He(e))+1).join("")},Cr.trimStart=function(t,e,r){if((t=ua(t))&&(r||void 0===e))return t.replace(G,"");if(!t||!(e=Xn(e)))return t;var n=He(t);return fi(n,Be(n,He(e))).join("")},Cr.truncate=function(t,e){var r=30,n="...";if(Vs(e)){var i="separator"in e?e.separator:i;r="length"in e?ia(e.length):r,n="omission"in e?Xn(e.omission):n}var o=(t=ua(t)).length;if(je(t)){var s=He(t);o=s.length}if(r>=o)return t;var a=r-$e(n);if(a<1)return n;var u=s?fi(s,0,a).join(""):t.slice(0,a);if(void 0===i)return u+n;if(s&&(a+=u.length-a),Js(i)){if(t.slice(a).search(i)){var c,f=u;for(i.global||(i=yt(i.source,ua(rt.exec(i))+"g")),i.lastIndex=0;c=i.exec(f);)var l=c.index;u=u.slice(0,void 0===l?a:l)}}else if(t.indexOf(Xn(i),a)!=a){var h=u.lastIndexOf(i);h>-1&&(u=u.slice(0,h))}return u+n},Cr.unescape=function(t){return(t=ua(t))&&U.test(t)?t.replace(F,We):t},Cr.uniqueId=function(t){var e=++It;return ua(t)+e},Cr.upperCase=ja,Cr.upperFirst=Ka,Cr.each=us,Cr.eachRight=cs,Cr.first=ko,Za(Cr,(lu={},cn(Cr,(function(t,e){Et.call(Cr.prototype,e)||(lu[e]=t)})),lu),{chain:!1}),Cr.VERSION="4.17.21",se(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(t){Cr[t].placeholder=Cr})),se(["drop","take"],(function(t,e){Or.prototype[t]=function(r){r=void 0===r?1:sr(ia(r),0);var n=this.__filtered__&&!e?new Or(this):this.clone();return n.__filtered__?n.__takeCount__=ar(r,n.__takeCount__):n.__views__.push({size:ar(r,4294967295),type:t+(n.__dir__<0?"Right":"")}),n},Or.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),se(["filter","map","takeWhile"],(function(t,e){var r=e+1,n=1==r||3==r;Or.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:Zi(t,3),type:r}),e.__filtered__=e.__filtered__||n,e}})),se(["head","last"],(function(t,e){var r="take"+(e?"Right":"");Or.prototype[t]=function(){return this[r](1).value()[0]}})),se(["initial","tail"],(function(t,e){var r="drop"+(e?"":"Right");Or.prototype[t]=function(){return this.__filtered__?new Or(this):this[r](1)}})),Or.prototype.compact=function(){return this.filter(Ga)},Or.prototype.find=function(t){return this.filter(t).head()},Or.prototype.findLast=function(t){return this.reverse().find(t)},Or.prototype.invokeMap=jn((function(t,e){return"function"==typeof t?new Or(this):this.map((function(r){return _n(r,t,e)}))})),Or.prototype.reject=function(t){return this.filter(As(Zi(t)))},Or.prototype.slice=function(t,e){t=ia(t);var r=this;return r.__filtered__&&(t>0||e<0)?new Or(r):(t<0?r=r.takeRight(-t):t&&(r=r.drop(t)),void 0!==e&&(r=(e=ia(e))<0?r.dropRight(-e):r.take(e-t)),r)},Or.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},Or.prototype.toArray=function(){return this.take(4294967295)},cn(Or.prototype,(function(t,e){var r=/^(?:filter|find|map|reject)|While$/.test(e),n=/^(?:head|last)$/.test(e),i=Cr[n?"take"+("last"==e?"Right":""):e],o=n||/^find/.test(e);i&&(Cr.prototype[e]=function(){var e=this.__wrapped__,s=n?[1]:arguments,a=e instanceof Or,u=s[0],c=a||Ds(e),f=function(t){var e=i.apply(Cr,de([t],s));return n&&l?e[0]:e};c&&r&&"function"==typeof u&&1!=u.length&&(a=c=!1);var l=this.__chain__,h=!!this.__actions__.length,d=o&&!l,p=a&&!h;if(!o&&c){e=p?e:new Or(this);var y=t.apply(e,s);return y.__actions__.push({func:ns,args:[f],thisArg:void 0}),new xr(y,l)}return d&&p?t.apply(this,s):(y=this.thru(f),d?n?y.value()[0]:y.value():y)})})),se(["pop","push","shift","sort","splice","unshift"],(function(t){var e=mt[t],r=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",n=/^(?:pop|shift)$/.test(t);Cr.prototype[t]=function(){var t=arguments;if(n&&!this.__chain__){var i=this.value();return e.apply(Ds(i)?i:[],t)}return this[r]((function(r){return e.apply(Ds(r)?r:[],t)}))}})),cn(Or.prototype,(function(t,e){var r=Cr[e];if(r){var n=r.name+"";Et.call(_r,n)||(_r[n]=[]),_r[n].push({name:e,func:r})}})),_r[Ri(void 0,2).name]=[{name:"wrapper",func:void 0}],Or.prototype.clone=function(){var t=new Or(this.__wrapped__);return t.__actions__=mi(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=mi(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=mi(this.__views__),t},Or.prototype.reverse=function(){if(this.__filtered__){var t=new Or(this);t.__dir__=-1,t.__filtered__=!0}else(t=this.clone()).__dir__*=-1;return t},Or.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,r=Ds(t),n=e<0,i=r?t.length:0,o=function(t,e,r){var n=-1,i=r.length;for(;++n<i;){var o=r[n],s=o.size;switch(o.type){case"drop":t+=s;break;case"dropRight":e-=s;break;case"take":e=ar(e,t+s);break;case"takeRight":t=sr(t,e-s)}}return{start:t,end:e}}(0,i,this.__views__),s=o.start,a=o.end,u=a-s,c=n?a:s-1,f=this.__iteratees__,l=f.length,h=0,d=ar(u,this.__takeCount__);if(!r||!n&&i==u&&d==u)return ni(t,this.__actions__);var p=[];t:for(;u--&&h<d;){for(var y=-1,g=t[c+=e];++y<l;){var v=f[y],m=v.iteratee,_=v.type,b=m(g);if(2==_)g=b;else if(!b){if(1==_)continue t;break t}}p[h++]=g}return p},Cr.prototype.at=is,Cr.prototype.chain=function(){return rs(this)},Cr.prototype.commit=function(){return new xr(this.value(),this.__chain__)},Cr.prototype.next=function(){void 0===this.__values__&&(this.__values__=ra(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?void 0:this.__values__[this.__index__++]}},Cr.prototype.plant=function(t){for(var e,r=this;r instanceof Pr;){var n=Ro(r);n.__index__=0,n.__values__=void 0,e?i.__wrapped__=n:e=n;var i=n;r=r.__wrapped__}return i.__wrapped__=t,e},Cr.prototype.reverse=function(){var t=this.__wrapped__;if(t instanceof Or){var e=t;return this.__actions__.length&&(e=new Or(this)),(e=e.reverse()).__actions__.push({func:ns,args:[Vo],thisArg:void 0}),new xr(e,this.__chain__)}return this.thru(Vo)},Cr.prototype.toJSON=Cr.prototype.valueOf=Cr.prototype.value=function(){return ni(this.__wrapped__,this.__actions__)},Cr.prototype.first=Cr.prototype.head,Zt&&(Cr.prototype[Zt]=function(){return this}),Cr}();Ht._=Je,void 0===(n=function(){return Je}.call(e,r,e,t))||(t.exports=n)}).call(this)}).call(this,r(88)(t))},function(t,e){t.exports=function(t){return t.webpackPolyfill||(t.deprecate=function(){},t.paths=[],t.children||(t.children=[]),Object.defineProperty(t,"loaded",{enumerable:!0,get:function(){return t.l}}),Object.defineProperty(t,"id",{enumerable:!0,get:function(){return t.i}}),t.webpackPolyfill=1),t}},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0});const n=r(8),i=r(90),o=r(2),s=r(12);function a(t,e=!1){const r="string"==typeof t?Buffer.from(t):t;if(e)return function(t){const e=s.Certificate.fromPEMs(t);let r="";return e.forEach(t=>{if(t.signatureOID.startsWith("1.2.840.113549.1.1")){const e=u(t);0===r.length?r+=e:r+="_"+e}}),r}(r);return u(s.Certificate.fromPEM(r))}function u(t){const{issuer:e,serialNumber:r}=t,n=e.attributes.reduceRight((t,e)=>{const{shortName:r,value:n}=e;return`${t}${r}=${n},`},"").slice(0,-1),s=new i.default(r,16).toString(10);return o.createHash("md5").update(n+s,"utf8").digest("hex")}e.loadPublicKeyFromPath=function(t){const e=n.readFileSync(t);return s.Certificate.fromPEM(e).publicKeyRaw.toString("base64")},e.loadPublicKey=function(t){const e="string"==typeof t?Buffer.from(t):t;return s.Certificate.fromPEM(e).publicKeyRaw.toString("base64")},e.getSNFromPath=function(t,e=!1){return a(n.readFileSync(t),e)},e.getSN=a},function(t,e,r){"use strict";r.r(e),r.d(e,"BigNumber",(function(){return m}));var n=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,i=Math.ceil,o=Math.floor,s="[BigNumber Error] ",a=s+"Number primitive has more than 15 significant digits: ",u=1e14,c=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],f=1e9;function l(t){var e=0|t;return t>0||t===e?e:e-1}function h(t){for(var e,r,n=1,i=t.length,o=t[0]+"";n<i;){for(r=14-(e=t[n++]+"").length;r--;e="0"+e);o+=e}for(i=o.length;48===o.charCodeAt(--i););return o.slice(0,i+1||1)}function d(t,e){var r,n,i=t.c,o=e.c,s=t.s,a=e.s,u=t.e,c=e.e;if(!s||!a)return null;if(r=i&&!i[0],n=o&&!o[0],r||n)return r?n?0:-a:s;if(s!=a)return s;if(r=s<0,n=u==c,!i||!o)return n?0:!i^r?1:-1;if(!n)return u>c^r?1:-1;for(a=(u=i.length)<(c=o.length)?u:c,s=0;s<a;s++)if(i[s]!=o[s])return i[s]>o[s]^r?1:-1;return u==c?0:u>c^r?1:-1}function p(t,e,r,n){if(t<e||t>r||t!==o(t))throw Error(s+(n||"Argument")+("number"==typeof t?t<e||t>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(t))}function y(t){var e=t.c.length-1;return l(t.e/14)==e&&t.c[e]%2!=0}function g(t,e){return(t.length>1?t.charAt(0)+"."+t.slice(1):t)+(e<0?"e":"e+")+e}function v(t,e,r){var n,i;if(e<0){for(i=r+".";++e;i+=r);t=i+t}else if(++e>(n=t.length)){for(i=r,e-=n;--e;i+=r);t+=i}else e<n&&(t=t.slice(0,e)+"."+t.slice(e));return t}var m=function t(e){var r,m,_,b,w,S,E,I,A,N=K.prototype={constructor:K,toString:null,valueOf:null},T=new K(1),C=20,R=4,P=-7,x=21,O=-1e7,B=1e7,D=!1,F=1,k=0,U={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:" ",suffix:""},M="0123456789abcdefghijklmnopqrstuvwxyz",j=!0;function K(t,e){var r,i,s,u,c,f,l,h,d=this;if(!(d instanceof K))return new K(t,e);if(null==e){if(t&&!0===t._isBigNumber)return d.s=t.s,void(!t.c||t.e>B?d.c=d.e=null:t.e<O?d.c=[d.e=0]:(d.e=t.e,d.c=t.c.slice()));if((f="number"==typeof t)&&0*t==0){if(d.s=1/t<0?(t=-t,-1):1,t===~~t){for(u=0,c=t;c>=10;c/=10,u++);return void(u>B?d.c=d.e=null:(d.e=u,d.c=[t]))}h=String(t)}else{if(!n.test(h=String(t)))return _(d,h,f);d.s=45==h.charCodeAt(0)?(h=h.slice(1),-1):1}(u=h.indexOf("."))>-1&&(h=h.replace(".","")),(c=h.search(/e/i))>0?(u<0&&(u=c),u+=+h.slice(c+1),h=h.substring(0,c)):u<0&&(u=h.length)}else{if(p(e,2,M.length,"Base"),10==e&&j)return V(d=new K(t),C+d.e+1,R);if(h=String(t),f="number"==typeof t){if(0*t!=0)return _(d,h,f,e);if(d.s=1/t<0?(h=h.slice(1),-1):1,K.DEBUG&&h.replace(/^0\.0*|\./,"").length>15)throw Error(a+t)}else d.s=45===h.charCodeAt(0)?(h=h.slice(1),-1):1;for(r=M.slice(0,e),u=c=0,l=h.length;c<l;c++)if(r.indexOf(i=h.charAt(c))<0){if("."==i){if(c>u){u=l;continue}}else if(!s&&(h==h.toUpperCase()&&(h=h.toLowerCase())||h==h.toLowerCase()&&(h=h.toUpperCase()))){s=!0,c=-1,u=0;continue}return _(d,String(t),f,e)}f=!1,(u=(h=m(h,e,10,d.s)).indexOf("."))>-1?h=h.replace(".",""):u=h.length}for(c=0;48===h.charCodeAt(c);c++);for(l=h.length;48===h.charCodeAt(--l););if(h=h.slice(c,++l)){if(l-=c,f&&K.DEBUG&&l>15&&(t>9007199254740991||t!==o(t)))throw Error(a+d.s*t);if((u=u-c-1)>B)d.c=d.e=null;else if(u<O)d.c=[d.e=0];else{if(d.e=u,d.c=[],c=(u+1)%14,u<0&&(c+=14),c<l){for(c&&d.c.push(+h.slice(0,c)),l-=14;c<l;)d.c.push(+h.slice(c,c+=14));c=14-(h=h.slice(c)).length}else c-=l;for(;c--;h+="0");d.c.push(+h)}}else d.c=[d.e=0]}function L(t,e,r,n){var i,o,s,a,u;if(null==r?r=R:p(r,0,8),!t.c)return t.toString();if(i=t.c[0],s=t.e,null==e)u=h(t.c),u=1==n||2==n&&(s<=P||s>=x)?g(u,s):v(u,s,"0");else if(o=(t=V(new K(t),e,r)).e,a=(u=h(t.c)).length,1==n||2==n&&(e<=o||o<=P)){for(;a<e;u+="0",a++);u=g(u,o)}else if(e-=s,u=v(u,o,"0"),o+1>a){if(--e>0)for(u+=".";e--;u+="0");}else if((e+=o-a)>0)for(o+1==a&&(u+=".");e--;u+="0");return t.s<0&&i?"-"+u:u}function q(t,e){for(var r,n=1,i=new K(t[0]);n<t.length;n++){if(!(r=new K(t[n])).s){i=r;break}e.call(i,r)&&(i=r)}return i}function z(t,e,r){for(var n=1,i=e.length;!e[--i];e.pop());for(i=e[0];i>=10;i/=10,n++);return(r=n+14*r-1)>B?t.c=t.e=null:r<O?t.c=[t.e=0]:(t.e=r,t.c=e),t}function V(t,e,r,n){var s,a,f,l,h,d,p,y=t.c,g=c;if(y){t:{for(s=1,l=y[0];l>=10;l/=10,s++);if((a=e-s)<0)a+=14,f=e,p=(h=y[d=0])/g[s-f-1]%10|0;else if((d=i((a+1)/14))>=y.length){if(!n)break t;for(;y.length<=d;y.push(0));h=p=0,s=1,f=(a%=14)-14+1}else{for(h=l=y[d],s=1;l>=10;l/=10,s++);p=(f=(a%=14)-14+s)<0?0:h/g[s-f-1]%10|0}if(n=n||e<0||null!=y[d+1]||(f<0?h:h%g[s-f-1]),n=r<4?(p||n)&&(0==r||r==(t.s<0?3:2)):p>5||5==p&&(4==r||n||6==r&&(a>0?f>0?h/g[s-f]:0:y[d-1])%10&1||r==(t.s<0?8:7)),e<1||!y[0])return y.length=0,n?(e-=t.e+1,y[0]=g[(14-e%14)%14],t.e=-e||0):y[0]=t.e=0,t;if(0==a?(y.length=d,l=1,d--):(y.length=d+1,l=g[14-a],y[d]=f>0?o(h/g[s-f]%g[f])*l:0),n)for(;;){if(0==d){for(a=1,f=y[0];f>=10;f/=10,a++);for(f=y[0]+=l,l=1;f>=10;f/=10,l++);a!=l&&(t.e++,y[0]==u&&(y[0]=1));break}if(y[d]+=l,y[d]!=u)break;y[d--]=0,l=1}for(a=y.length;0===y[--a];y.pop());}t.e>B?t.c=t.e=null:t.e<O&&(t.c=[t.e=0])}return t}function $(t){var e,r=t.e;return null===r?t.toString():(e=h(t.c),e=r<=P||r>=x?g(e,r):v(e,r,"0"),t.s<0?"-"+e:e)}return K.clone=t,K.ROUND_UP=0,K.ROUND_DOWN=1,K.ROUND_CEIL=2,K.ROUND_FLOOR=3,K.ROUND_HALF_UP=4,K.ROUND_HALF_DOWN=5,K.ROUND_HALF_EVEN=6,K.ROUND_HALF_CEIL=7,K.ROUND_HALF_FLOOR=8,K.EUCLID=9,K.config=K.set=function(t){var e,r;if(null!=t){if("object"!=typeof t)throw Error(s+"Object expected: "+t);if(t.hasOwnProperty(e="DECIMAL_PLACES")&&(p(r=t[e],0,f,e),C=r),t.hasOwnProperty(e="ROUNDING_MODE")&&(p(r=t[e],0,8,e),R=r),t.hasOwnProperty(e="EXPONENTIAL_AT")&&((r=t[e])&&r.pop?(p(r[0],-f,0,e),p(r[1],0,f,e),P=r[0],x=r[1]):(p(r,-f,f,e),P=-(x=r<0?-r:r))),t.hasOwnProperty(e="RANGE"))if((r=t[e])&&r.pop)p(r[0],-f,-1,e),p(r[1],1,f,e),O=r[0],B=r[1];else{if(p(r,-f,f,e),!r)throw Error(s+e+" cannot be zero: "+r);O=-(B=r<0?-r:r)}if(t.hasOwnProperty(e="CRYPTO")){if((r=t[e])!==!!r)throw Error(s+e+" not true or false: "+r);if(r){if("undefined"==typeof crypto||!crypto||!crypto.getRandomValues&&!crypto.randomBytes)throw D=!r,Error(s+"crypto unavailable");D=r}else D=r}if(t.hasOwnProperty(e="MODULO_MODE")&&(p(r=t[e],0,9,e),F=r),t.hasOwnProperty(e="POW_PRECISION")&&(p(r=t[e],0,f,e),k=r),t.hasOwnProperty(e="FORMAT")){if("object"!=typeof(r=t[e]))throw Error(s+e+" not an object: "+r);U=r}if(t.hasOwnProperty(e="ALPHABET")){if("string"!=typeof(r=t[e])||/^.?$|[+\-.\s]|(.).*\1/.test(r))throw Error(s+e+" invalid: "+r);j="0123456789"==r.slice(0,10),M=r}}return{DECIMAL_PLACES:C,ROUNDING_MODE:R,EXPONENTIAL_AT:[P,x],RANGE:[O,B],CRYPTO:D,MODULO_MODE:F,POW_PRECISION:k,FORMAT:U,ALPHABET:M}},K.isBigNumber=function(t){if(!t||!0!==t._isBigNumber)return!1;if(!K.DEBUG)return!0;var e,r,n=t.c,i=t.e,a=t.s;t:if("[object Array]"=={}.toString.call(n)){if((1===a||-1===a)&&i>=-f&&i<=f&&i===o(i)){if(0===n[0]){if(0===i&&1===n.length)return!0;break t}if((e=(i+1)%14)<1&&(e+=14),String(n[0]).length==e){for(e=0;e<n.length;e++)if((r=n[e])<0||r>=u||r!==o(r))break t;if(0!==r)return!0}}}else if(null===n&&null===i&&(null===a||1===a||-1===a))return!0;throw Error(s+"Invalid BigNumber: "+t)},K.maximum=K.max=function(){return q(arguments,N.lt)},K.minimum=K.min=function(){return q(arguments,N.gt)},K.random=(b=9007199254740992*Math.random()&2097151?function(){return o(9007199254740992*Math.random())}:function(){return 8388608*(1073741824*Math.random()|0)+(8388608*Math.random()|0)},function(t){var e,r,n,a,u,l=0,h=[],d=new K(T);if(null==t?t=C:p(t,0,f),a=i(t/14),D)if(crypto.getRandomValues){for(e=crypto.getRandomValues(new Uint32Array(a*=2));l<a;)(u=131072*e[l]+(e[l+1]>>>11))>=9e15?(r=crypto.getRandomValues(new Uint32Array(2)),e[l]=r[0],e[l+1]=r[1]):(h.push(u%1e14),l+=2);l=a/2}else{if(!crypto.randomBytes)throw D=!1,Error(s+"crypto unavailable");for(e=crypto.randomBytes(a*=7);l<a;)(u=281474976710656*(31&e[l])+1099511627776*e[l+1]+4294967296*e[l+2]+16777216*e[l+3]+(e[l+4]<<16)+(e[l+5]<<8)+e[l+6])>=9e15?crypto.randomBytes(7).copy(e,l):(h.push(u%1e14),l+=7);l=a/7}if(!D)for(;l<a;)(u=b())<9e15&&(h[l++]=u%1e14);for(t%=14,(a=h[--l])&&t&&(u=c[14-t],h[l]=o(a/u)*u);0===h[l];h.pop(),l--);if(l<0)h=[n=0];else{for(n=-1;0===h[0];h.splice(0,1),n-=14);for(l=1,u=h[0];u>=10;u/=10,l++);l<14&&(n-=14-l)}return d.e=n,d.c=h,d}),K.sum=function(){for(var t=1,e=arguments,r=new K(e[0]);t<e.length;)r=r.plus(e[t++]);return r},m=function(){function t(t,e,r,n){for(var i,o,s=[0],a=0,u=t.length;a<u;){for(o=s.length;o--;s[o]*=e);for(s[0]+=n.indexOf(t.charAt(a++)),i=0;i<s.length;i++)s[i]>r-1&&(null==s[i+1]&&(s[i+1]=0),s[i+1]+=s[i]/r|0,s[i]%=r)}return s.reverse()}return function(e,n,i,o,s){var a,u,c,f,l,d,p,y,g=e.indexOf("."),m=C,_=R;for(g>=0&&(f=k,k=0,e=e.replace(".",""),d=(y=new K(n)).pow(e.length-g),k=f,y.c=t(v(h(d.c),d.e,"0"),10,i,"0123456789"),y.e=y.c.length),c=f=(p=t(e,n,i,s?(a=M,"0123456789"):(a="0123456789",M))).length;0==p[--f];p.pop());if(!p[0])return a.charAt(0);if(g<0?--c:(d.c=p,d.e=c,d.s=o,p=(d=r(d,y,m,_,i)).c,l=d.r,c=d.e),g=p[u=c+m+1],f=i/2,l=l||u<0||null!=p[u+1],l=_<4?(null!=g||l)&&(0==_||_==(d.s<0?3:2)):g>f||g==f&&(4==_||l||6==_&&1&p[u-1]||_==(d.s<0?8:7)),u<1||!p[0])e=l?v(a.charAt(1),-m,a.charAt(0)):a.charAt(0);else{if(p.length=u,l)for(--i;++p[--u]>i;)p[u]=0,u||(++c,p=[1].concat(p));for(f=p.length;!p[--f];);for(g=0,e="";g<=f;e+=a.charAt(p[g++]));e=v(e,c,a.charAt(0))}return e}}(),r=function(){function t(t,e,r){var n,i,o,s,a=0,u=t.length,c=e%1e7,f=e/1e7|0;for(t=t.slice();u--;)a=((i=c*(o=t[u]%1e7)+(n=f*o+(s=t[u]/1e7|0)*c)%1e7*1e7+a)/r|0)+(n/1e7|0)+f*s,t[u]=i%r;return a&&(t=[a].concat(t)),t}function e(t,e,r,n){var i,o;if(r!=n)o=r>n?1:-1;else for(i=o=0;i<r;i++)if(t[i]!=e[i]){o=t[i]>e[i]?1:-1;break}return o}function r(t,e,r,n){for(var i=0;r--;)t[r]-=i,i=t[r]<e[r]?1:0,t[r]=i*n+t[r]-e[r];for(;!t[0]&&t.length>1;t.splice(0,1));}return function(n,i,s,a,c){var f,h,d,p,y,g,v,m,_,b,w,S,E,I,A,N,T,C=n.s==i.s?1:-1,R=n.c,P=i.c;if(!(R&&R[0]&&P&&P[0]))return new K(n.s&&i.s&&(R?!P||R[0]!=P[0]:P)?R&&0==R[0]||!P?0*C:C/0:NaN);for(_=(m=new K(C)).c=[],C=s+(h=n.e-i.e)+1,c||(c=u,h=l(n.e/14)-l(i.e/14),C=C/14|0),d=0;P[d]==(R[d]||0);d++);if(P[d]>(R[d]||0)&&h--,C<0)_.push(1),p=!0;else{for(I=R.length,N=P.length,d=0,C+=2,(y=o(c/(P[0]+1)))>1&&(P=t(P,y,c),R=t(R,y,c),N=P.length,I=R.length),E=N,w=(b=R.slice(0,N)).length;w<N;b[w++]=0);T=P.slice(),T=[0].concat(T),A=P[0],P[1]>=c/2&&A++;do{if(y=0,(f=e(P,b,N,w))<0){if(S=b[0],N!=w&&(S=S*c+(b[1]||0)),(y=o(S/A))>1)for(y>=c&&(y=c-1),v=(g=t(P,y,c)).length,w=b.length;1==e(g,b,v,w);)y--,r(g,N<v?T:P,v,c),v=g.length,f=1;else 0==y&&(f=y=1),v=(g=P.slice()).length;if(v<w&&(g=[0].concat(g)),r(b,g,w,c),w=b.length,-1==f)for(;e(P,b,N,w)<1;)y++,r(b,N<w?T:P,w,c),w=b.length}else 0===f&&(y++,b=[0]);_[d++]=y,b[0]?b[w++]=R[E]||0:(b=[R[E]],w=1)}while((E++<I||null!=b[0])&&C--);p=null!=b[0],_[0]||_.splice(0,1)}if(c==u){for(d=1,C=_[0];C>=10;C/=10,d++);V(m,s+(m.e=d+14*h-1)+1,a,p)}else m.e=h,m.r=+p;return m}}(),w=/^(-?)0([xbo])(?=\w[\w.]*$)/i,S=/^([^.]+)\.$/,E=/^\.([^.]+)$/,I=/^-?(Infinity|NaN)$/,A=/^\s*\+(?=[\w.])|^\s+|\s+$/g,_=function(t,e,r,n){var i,o=r?e:e.replace(A,"");if(I.test(o))t.s=isNaN(o)?null:o<0?-1:1;else{if(!r&&(o=o.replace(w,(function(t,e,r){return i="x"==(r=r.toLowerCase())?16:"b"==r?2:8,n&&n!=i?t:e})),n&&(i=n,o=o.replace(S,"$1").replace(E,"0.$1")),e!=o))return new K(o,i);if(K.DEBUG)throw Error(s+"Not a"+(n?" base "+n:"")+" number: "+e);t.s=null}t.c=t.e=null},N.absoluteValue=N.abs=function(){var t=new K(this);return t.s<0&&(t.s=1),t},N.comparedTo=function(t,e){return d(this,new K(t,e))},N.decimalPlaces=N.dp=function(t,e){var r,n,i,o=this;if(null!=t)return p(t,0,f),null==e?e=R:p(e,0,8),V(new K(o),t+o.e+1,e);if(!(r=o.c))return null;if(n=14*((i=r.length-1)-l(this.e/14)),i=r[i])for(;i%10==0;i/=10,n--);return n<0&&(n=0),n},N.dividedBy=N.div=function(t,e){return r(this,new K(t,e),C,R)},N.dividedToIntegerBy=N.idiv=function(t,e){return r(this,new K(t,e),0,1)},N.exponentiatedBy=N.pow=function(t,e){var r,n,a,u,c,f,l,h,d=this;if((t=new K(t)).c&&!t.isInteger())throw Error(s+"Exponent not an integer: "+$(t));if(null!=e&&(e=new K(e)),c=t.e>14,!d.c||!d.c[0]||1==d.c[0]&&!d.e&&1==d.c.length||!t.c||!t.c[0])return h=new K(Math.pow(+$(d),c?2-y(t):+$(t))),e?h.mod(e):h;if(f=t.s<0,e){if(e.c?!e.c[0]:!e.s)return new K(NaN);(n=!f&&d.isInteger()&&e.isInteger())&&(d=d.mod(e))}else{if(t.e>9&&(d.e>0||d.e<-1||(0==d.e?d.c[0]>1||c&&d.c[1]>=24e7:d.c[0]<8e13||c&&d.c[0]<=9999975e7)))return u=d.s<0&&y(t)?-0:0,d.e>-1&&(u=1/u),new K(f?1/u:u);k&&(u=i(k/14+2))}for(c?(r=new K(.5),f&&(t.s=1),l=y(t)):l=(a=Math.abs(+$(t)))%2,h=new K(T);;){if(l){if(!(h=h.times(d)).c)break;u?h.c.length>u&&(h.c.length=u):n&&(h=h.mod(e))}if(a){if(0===(a=o(a/2)))break;l=a%2}else if(V(t=t.times(r),t.e+1,1),t.e>14)l=y(t);else{if(0===(a=+$(t)))break;l=a%2}d=d.times(d),u?d.c&&d.c.length>u&&(d.c.length=u):n&&(d=d.mod(e))}return n?h:(f&&(h=T.div(h)),e?h.mod(e):u?V(h,k,R,void 0):h)},N.integerValue=function(t){var e=new K(this);return null==t?t=R:p(t,0,8),V(e,e.e+1,t)},N.isEqualTo=N.eq=function(t,e){return 0===d(this,new K(t,e))},N.isFinite=function(){return!!this.c},N.isGreaterThan=N.gt=function(t,e){return d(this,new K(t,e))>0},N.isGreaterThanOrEqualTo=N.gte=function(t,e){return 1===(e=d(this,new K(t,e)))||0===e},N.isInteger=function(){return!!this.c&&l(this.e/14)>this.c.length-2},N.isLessThan=N.lt=function(t,e){return d(this,new K(t,e))<0},N.isLessThanOrEqualTo=N.lte=function(t,e){return-1===(e=d(this,new K(t,e)))||0===e},N.isNaN=function(){return!this.s},N.isNegative=function(){return this.s<0},N.isPositive=function(){return this.s>0},N.isZero=function(){return!!this.c&&0==this.c[0]},N.minus=function(t,e){var r,n,i,o,s=this,a=s.s;if(e=(t=new K(t,e)).s,!a||!e)return new K(NaN);if(a!=e)return t.s=-e,s.plus(t);var c=s.e/14,f=t.e/14,h=s.c,d=t.c;if(!c||!f){if(!h||!d)return h?(t.s=-e,t):new K(d?s:NaN);if(!h[0]||!d[0])return d[0]?(t.s=-e,t):new K(h[0]?s:3==R?-0:0)}if(c=l(c),f=l(f),h=h.slice(),a=c-f){for((o=a<0)?(a=-a,i=h):(f=c,i=d),i.reverse(),e=a;e--;i.push(0));i.reverse()}else for(n=(o=(a=h.length)<(e=d.length))?a:e,a=e=0;e<n;e++)if(h[e]!=d[e]){o=h[e]<d[e];break}if(o&&(i=h,h=d,d=i,t.s=-t.s),(e=(n=d.length)-(r=h.length))>0)for(;e--;h[r++]=0);for(e=u-1;n>a;){if(h[--n]<d[n]){for(r=n;r&&!h[--r];h[r]=e);--h[r],h[n]+=u}h[n]-=d[n]}for(;0==h[0];h.splice(0,1),--f);return h[0]?z(t,h,f):(t.s=3==R?-1:1,t.c=[t.e=0],t)},N.modulo=N.mod=function(t,e){var n,i,o=this;return t=new K(t,e),!o.c||!t.s||t.c&&!t.c[0]?new K(NaN):!t.c||o.c&&!o.c[0]?new K(o):(9==F?(i=t.s,t.s=1,n=r(o,t,0,3),t.s=i,n.s*=i):n=r(o,t,0,F),(t=o.minus(n.times(t))).c[0]||1!=F||(t.s=o.s),t)},N.multipliedBy=N.times=function(t,e){var r,n,i,o,s,a,c,f,h,d,p,y,g,v,m=this,_=m.c,b=(t=new K(t,e)).c;if(!(_&&b&&_[0]&&b[0]))return!m.s||!t.s||_&&!_[0]&&!b||b&&!b[0]&&!_?t.c=t.e=t.s=null:(t.s*=m.s,_&&b?(t.c=[0],t.e=0):t.c=t.e=null),t;for(n=l(m.e/14)+l(t.e/14),t.s*=m.s,(c=_.length)<(d=b.length)&&(g=_,_=b,b=g,i=c,c=d,d=i),i=c+d,g=[];i--;g.push(0));for(v=u,1e7,i=d;--i>=0;){for(r=0,p=b[i]%1e7,y=b[i]/1e7|0,o=i+(s=c);o>i;)r=((f=p*(f=_[--s]%1e7)+(a=y*f+(h=_[s]/1e7|0)*p)%1e7*1e7+g[o]+r)/v|0)+(a/1e7|0)+y*h,g[o--]=f%v;g[o]=r}return r?++n:g.splice(0,1),z(t,g,n)},N.negated=function(){var t=new K(this);return t.s=-t.s||null,t},N.plus=function(t,e){var r,n=this,i=n.s;if(e=(t=new K(t,e)).s,!i||!e)return new K(NaN);if(i!=e)return t.s=-e,n.minus(t);var o=n.e/14,s=t.e/14,a=n.c,c=t.c;if(!o||!s){if(!a||!c)return new K(i/0);if(!a[0]||!c[0])return c[0]?t:new K(a[0]?n:0*i)}if(o=l(o),s=l(s),a=a.slice(),i=o-s){for(i>0?(s=o,r=c):(i=-i,r=a),r.reverse();i--;r.push(0));r.reverse()}for((i=a.length)-(e=c.length)<0&&(r=c,c=a,a=r,e=i),i=0;e;)i=(a[--e]=a[e]+c[e]+i)/u|0,a[e]=u===a[e]?0:a[e]%u;return i&&(a=[i].concat(a),++s),z(t,a,s)},N.precision=N.sd=function(t,e){var r,n,i,o=this;if(null!=t&&t!==!!t)return p(t,1,f),null==e?e=R:p(e,0,8),V(new K(o),t,e);if(!(r=o.c))return null;if(n=14*(i=r.length-1)+1,i=r[i]){for(;i%10==0;i/=10,n--);for(i=r[0];i>=10;i/=10,n++);}return t&&o.e+1>n&&(n=o.e+1),n},N.shiftedBy=function(t){return p(t,-9007199254740991,9007199254740991),this.times("1e"+t)},N.squareRoot=N.sqrt=function(){var t,e,n,i,o,s=this,a=s.c,u=s.s,c=s.e,f=C+4,d=new K("0.5");if(1!==u||!a||!a[0])return new K(!u||u<0&&(!a||a[0])?NaN:a?s:1/0);if(0==(u=Math.sqrt(+$(s)))||u==1/0?(((e=h(a)).length+c)%2==0&&(e+="0"),u=Math.sqrt(+e),c=l((c+1)/2)-(c<0||c%2),n=new K(e=u==1/0?"5e"+c:(e=u.toExponential()).slice(0,e.indexOf("e")+1)+c)):n=new K(u+""),n.c[0])for((u=(c=n.e)+f)<3&&(u=0);;)if(o=n,n=d.times(o.plus(r(s,o,f,1))),h(o.c).slice(0,u)===(e=h(n.c)).slice(0,u)){if(n.e<c&&--u,"9999"!=(e=e.slice(u-3,u+1))&&(i||"4999"!=e)){+e&&(+e.slice(1)||"5"!=e.charAt(0))||(V(n,n.e+C+2,1),t=!n.times(n).eq(s));break}if(!i&&(V(o,o.e+C+2,0),o.times(o).eq(s))){n=o;break}f+=4,u+=4,i=1}return V(n,n.e+C+1,R,t)},N.toExponential=function(t,e){return null!=t&&(p(t,0,f),t++),L(this,t,e,1)},N.toFixed=function(t,e){return null!=t&&(p(t,0,f),t=t+this.e+1),L(this,t,e)},N.toFormat=function(t,e,r){var n,i=this;if(null==r)null!=t&&e&&"object"==typeof e?(r=e,e=null):t&&"object"==typeof t?(r=t,t=e=null):r=U;else if("object"!=typeof r)throw Error(s+"Argument not an object: "+r);if(n=i.toFixed(t,e),i.c){var o,a=n.split("."),u=+r.groupSize,c=+r.secondaryGroupSize,f=r.groupSeparator||"",l=a[0],h=a[1],d=i.s<0,p=d?l.slice(1):l,y=p.length;if(c&&(o=u,u=c,c=o,y-=o),u>0&&y>0){for(o=y%u||u,l=p.substr(0,o);o<y;o+=u)l+=f+p.substr(o,u);c>0&&(l+=f+p.slice(o)),d&&(l="-"+l)}n=h?l+(r.decimalSeparator||"")+((c=+r.fractionGroupSize)?h.replace(new RegExp("\\d{"+c+"}\\B","g"),"$&"+(r.fractionGroupSeparator||"")):h):l}return(r.prefix||"")+n+(r.suffix||"")},N.toFraction=function(t){var e,n,i,o,a,u,f,l,d,p,y,g,v=this,m=v.c;if(null!=t&&(!(f=new K(t)).isInteger()&&(f.c||1!==f.s)||f.lt(T)))throw Error(s+"Argument "+(f.isInteger()?"out of range: ":"not an integer: ")+$(f));if(!m)return new K(v);for(e=new K(T),d=n=new K(T),i=l=new K(T),g=h(m),a=e.e=g.length-v.e-1,e.c[0]=c[(u=a%14)<0?14+u:u],t=!t||f.comparedTo(e)>0?a>0?e:d:f,u=B,B=1/0,f=new K(g),l.c[0]=0;p=r(f,e,0,1),1!=(o=n.plus(p.times(i))).comparedTo(t);)n=i,i=o,d=l.plus(p.times(o=d)),l=o,e=f.minus(p.times(o=e)),f=o;return o=r(t.minus(n),i,0,1),l=l.plus(o.times(d)),n=n.plus(o.times(i)),l.s=d.s=v.s,y=r(d,i,a*=2,R).minus(v).abs().comparedTo(r(l,n,a,R).minus(v).abs())<1?[d,i]:[l,n],B=u,y},N.toNumber=function(){return+$(this)},N.toPrecision=function(t,e){return null!=t&&p(t,1,f),L(this,t,e,2)},N.toString=function(t){var e,r=this,n=r.s,i=r.e;return null===i?n?(e="Infinity",n<0&&(e="-"+e)):e="NaN":(null==t?e=i<=P||i>=x?g(h(r.c),i):v(h(r.c),i,"0"):10===t&&j?e=v(h((r=V(new K(r),C+i+1,R)).c),r.e,"0"):(p(t,2,M.length,"Base"),e=m(v(h(r.c),i,"0"),10,t,n,!0)),n<0&&r.c[0]&&(e="-"+e)),e},N.valueOf=N.toJSON=function(){return $(this)},N._isBigNumber=!0,N[Symbol.toStringTag]="BigNumber",N[Symbol.for("nodejs.util.inspect.custom")]=N.valueOf,null!=e&&K.set(e),K}();e.default=m},function(t){t.exports=JSON.parse('{"name":"alipay-sdk","version":"3.2.0","description":"蚂蚁金服开放平台 node sdk","main":"lib/alipay.js","scripts":{"build":"npm run tsc","tsc":"tsc -p ./tsconfig.json","tsc:watch":"tsc -w","lint":"tslint -p ./tsconfig.json --fix","lint:no-fix":"tslint -p ./tsconfig.json","test":"npm run build && mocha","ci":"npm run tsc && npm run lint:no-fix && nyc mocha -t 6000","prepublishOnly":"npm run tsc && npm run test"},"author":"dersoncheng","homepage":"https://github.com/ali-sdk/alipay-sdk","bugs":"https://github.com/ali-sdk/alipay-sdk/issues","license":"ISC","publishConfig":{"registry":"https://registry.npmjs.org"},"dependencies":{"@fidm/x509":"^1.2.1","@types/node":"^9.6.0","bignumber.js":"^9.0.0","camelcase-keys":"^4.2.0","crypto-js":"^4.0.0","decamelize":"^2.0.0","is":"^3.2.1","is-json":"^2.0.1","isuri":"^2.0.3","lodash":"^4.17.20","moment":"^2.16.0","request":"^2.86.0","snakecase-keys":"^1.1.1","urllib":"^2.17.0"},"nyc":{"extends":"@istanbuljs/nyc-config-typescript","include":["lib"],"extension":[".ts"],"check-coverage":true,"reporter":["text-summary","json","html"],"sourceMap":true},"ci":{"version":"8, 10, 12, 14"},"devDependencies":{"@istanbuljs/nyc-config-typescript":"^0.1.3","mocha":"^3.1.2","nyc":"^14.1.1","query-string":"^6.5.0","should":"^11.1.1","sinon":"^1.17.7","tslint":"^5.8.0","tslint-config-airbnb":"^5.4.2","typescript":"3.5.1"}}')},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(3);function i(t,e,r){e.forEach(e=>{void 0!==t[e]&&(t[e]=(0,n.accMul)(Number(t[e]),r))})}function o(t,e){e.forEach(e=>{void 0!==t[e]&&(t[e]=t[e].replace(/[-+:\s]/g,""))})}var s={unifiedOrder:{args:{_pre:t=>(i(t,["totalFee"],.01),t),totalAmount:"totalFee",buyerId:"openid"},returnValue:{transactionId:"tradeNo"}},getOrderInfo:{args:{_pre:t=>(i(t,["totalFee"],.01),t),buyerId:"openid",totalAmount:"totalFee"},returnValue:t=>("object"==typeof t&&t.qrCode&&(t.codeUrl=t.qrCode,delete t.qrCode),t)},orderQuery:{args:{tradeNo:"transactionId"},returnValue:{_pre:t=>(i(t,["totalAmount","settleAmount","buyerPayAmount","payAmount","pointAmount","invoiceAmount","receiptAmount","chargeAmount","mdiscountAmount","discountAmount"],100),o(t,["sendPayDate"]),t),transactionId:"tradeNo",openid:"buyerUserId",tradeState:function(t){switch(t.tradeStatus){case"WAIT_BUYER_PAY":return"USERPAYING";case"TRADE_CLOSED":return"CLOSED";case"TRADE_SUCCESS":return"SUCCESS";case"TRADE_FINISHED":return"FINISHED";default:return t.tradeStatus}},totalFee:"totalAmount",settlementTotalFee:"settleAmount",feeType:"transCurrency",cashFeeType:"payCurrency",cashFee:"buyerPayAmount",fundBillList:function(t){return t.fundBillList?t.fundBillList.map(t=>(t.amount=100*Number(t.amount),t.realAmount=100*Number(t.realAmount),t)):[]},tradeSettleDetailList:function(t){return t.tradeSettleDetailList?t.tradeSettleDetailList.map(t=>(t.amount=100*Number(t.amount),t)):[]},timeEnd:"sendPayDate",_purify:{shouldDelete:["tradeStatus"]}}},cancelOrder:{args:{tradeNo:"transactionId"},returnValue:{transactionId:"tradeNo"}},closeOrder:{args:{tradeNo:"transactionId"},returnValue:{transactionId:"tradeNo"}},refund:{args:{_pre:t=>(i(t,["refundFee","sendBackFee"],.01),t),tradeNo:"transactionId",refundAmount:"refundFee",outRequestNo:"outRefundNo",refundCurrency:"refundFeeType",refundReason:"refundDesc",goodsDetail:function(t){return t.goodsDetail?t.goodsDetail.map(t=>(t.price=Number(t.price)/100,t)):[]},refundRoyaltyParameters:function(t){return t.refundRoyaltyParameters?t.refundRoyaltyParameters.map(t=>(t.amount=Number(t.amount)/100,t)):[]}},returnValue:{_pre:t=>(i(t,["refundFee","presentRefundBuyerAmount","presentRefundDiscountAmount","presentRefundMdiscountAmount"],100),t),transactionId:"tradeNo",openid:"buyerUserId",cashRefundFee:"presentRefundBuyerAmount",refundId:"refundSettlementId",cashFeeType:"refundCurrency",refundDetailItemList:function(t){return t.refundDetailItemList?t.refundDetailItemList.map(t=>(t.amount=100*Number(t.amount),t.realAmount=100*Number(t.realAmount),t)):[]},refundPresetPaytoolList:function(t){return t.refundPresetPaytoolList?t.refundPresetPaytoolList.map(t=>(t.amount=100*Number(t.amount),t)):[]}}},refundQuery:{args:{tradeNo:"transactionId",outRequestNo:"outRefundNo"},returnValue:{_pre:t=>(i(t,["totalAmount","refundAmount","sendBackFee","presentRefundBuyerAmount","presentRefundBuyerAmount","presentRefundMdiscountAmount"],100),t),transactionId:"tradeNo",outRefundNo:"outRequestNo",totalFee:"totalAmount",refundFee:"refundAmount",refundDesc:"refundReason",refundId:"refundSettlementId",refundSuccessTime:"gmtRefundPay",refundRoyaltys:function(t){return t.refundRoyaltys?t.refundRoyaltys.map(t=>(t.refundAmount=100*Number(t.refundAmount),t)):[]},refundDetailItemList:function(t){return t.refundDetailItemList?t.refundDetailItemList.map(t=>(t.amount=100*Number(t.amount),t.realAmount=100*Number(t.realAmount),t)):[]}}},verifyPaymentNotify:{returnValue:{_pre:t=>(i(t,["invoiceAmount","receiptAmount","buyerPayAmount","totalAmount","pointAmount"],100),o(t,["gmtPayment"]),t),openid:"buyerId",transactionId:"tradeNo",totalFee:"totalAmount",cashFee:"buyerPayAmount",timeEnd:"gmtPayment",resultCode:function(t){return t.tradeStatus.replace("TRADE_","")},fundBillList:function(t){return t.fundBillList?JSON.parse(t.fundBillList).map(t=>(t.amount=100*Number(t.amount),t)):[]}}},verifyRefundNotify:{returnValue:{_pre:t=>(i(t,["totalAmount","refundFee"],100),t),openid:"buyerId",outRefundNo:"outBizNo",transactionId:"tradeNo",totalFee:"totalAmount",successTime:function(t){t.successTime=t.gmtRefund.replace("+","").split(".")[0]},resultCode:function(t){return t.tradeStatus.replace("TRADE_","")},fundBillList:function(t){return t.fundBillList?JSON.parse(t.fundBillList).map(t=>(t.amount=100*Number(t.amount),t)):[]},_purify:{shouldDelete:["gmtRefund"]}}},queryAccountBalance:{returnValue:{_pre(t){const e=JSON.parse(JSON.stringify(t)),r=parseFloat(e.availableAmount),n=parseFloat(e.freezeAmount),i="number"!=typeof r||isNaN(r)?-1:0;return{code:i,availableAmount:r,freezeAmount:n,msg:0===i?`可用余额：${r} 元\n冻结余额：${n} 元`:e.subMsg,originalResult:e}}}},codepay:{args:{_pre:t=>(i(t,["totalFee"],.01),t),shouldDelete:["openid","tradeType"],totalAmount:"totalFee"},returnValue(t){let e,r;return"10000"===t.code?(e="SUCCESS",r="支付成功"):"10003"===t.code?(e="USERPAYING",r="用户支付中"):(e="PAYERROR",r=`支付失败 ${t.code}:${t.msg}`),{tradeState:e,tradeStateDesc:r}}}};e.default=s,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=class{constructor(t){t.sandbox&&(t.gateway="https://sandbox.itunes.apple.com/verifyReceipt"),t=Object.assign({gateway:"https://buy.itunes.apple.com/verifyReceipt",timeout:5e3,password:""},t),this.options=t}async _request(t){const e={method:"POST",contentType:"json",dataType:"json",data:t,timeout:this.options.timeout},{status:r,data:n}=await uniCloud.httpclient.request(this.options.gateway,e);if(200!==r)throw new Error("request fail");return this._parse(n)}_parse(t){const e=this._tradeState(t.status);return 0===t.status?{transactionId:t.receipt.transaction_id,receipt:t.receipt,...e}:e}_tradeState(t){let e="PAYERROR",r="";switch(t){case-1:e="NOTPAY";break;case 0:e="SUCCESS";break;default:r="Error status ["+t+"]"}return{tradeState:e,errMsg:r}}async verifyReceipt(t){const e=(t={"receipt-data":t.receiptData}).password||this.options.password;e&&(t.password=e);return await this._request(t)}},t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var r=u(e);if(r&&r.has(t))return r.get(t);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if("default"!==o&&Object.prototype.hasOwnProperty.call(t,o)){var s=i?Object.getOwnPropertyDescriptor(t,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=t[o]}n.default=t,r&&r.set(t,n);return n}(r(95)),i=a(r(96)),o=a(r(97)),s=r(3);function a(t){return t&&t.__esModule?t:{default:t}}function u(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(u=function(t){return t?r:e})(t)}var c=class extends class{constructor(t){if(this.options=t,!t.appId)throw new Error("appId required");if(!t.mchId)throw new Error("mchId required");if(!t.v3Key)throw new Error("v3Key required");if(32!==t.v3Key.length)throw new Error("微信支付v3Key的长度必须是32位，请检查");if(!t.appPrivateKeyPath&&!t.appPrivateKeyContent)throw new Error("missing appPrivateKeyPath or appPrivateKeyContent");if(!t.appCertPath&&!t.appCertContent)throw new Error("missing appCertPath or appCertContent");this._protocols=o.default,this.platformCertificate=[],this._baseURL="https://api.mch.weixin.qq.com",this._cert=t.appCertPath?i.default.loadCertFromPath(t.appCertPath):i.default.loadCertFromContent(i.default.formatKey(t.appCertContent,"CERTIFICATE")),this._privateKey=t.appPrivateKeyPath?i.default.loadPrivateKeyFromPath(t.appPrivateKeyPath):i.default.loadPrivateKeyFromContent(i.default.formatKey(t.appPrivateKeyContent,"PRIVATE KEY"));let e=t.wxpayPublicKeyContent;t.wxpayPublicKeyPath&&!e&&(e=i.default.getFileBuffer(t.wxpayPublicKeyPath)),e&&e.length>80&&(this._wxpayPublicKey=i.default.loadPublicKeyFromContent(i.default.formatKey(e,"PUBLIC KEY"))),this._wxpayPublicKeyId=t.wxpayPublicKeyId}_getAuthorization(t="GET",e="",r={}){const n=i.default.getNonceStr(),o=Date.parse(new Date)/1e3,s=[t,"GET"===t&&Object.keys(r).length>0?`${e}?${i.default.getQueryStr(r)}`:e,o,n,"GET"===t?"":JSON.stringify(r)].reduce((t,e)=>t+=e+"\n",""),a=i.default.rsaPrivateKeySign(this._privateKey.toPEM(),s).toString("base64"),u=this._cert.serialNumber.toUpperCase();return`WECHATPAY2-SHA256-RSA2048 mchid="${this.options.mchId}",nonce_str="${n}",signature="${a}",timestamp="${o}",serial_no="${u}"`}async _request(t,e,r="GET",n=200,i=!1){((e=(0,s.camel2snakeJson)(e)).sp_appid||e.sp_mchid)&&(t=t.replace("v3/pay/transactions/","v3/pay/partner/transactions/"),e.payer&&e.payer.openid&&(this.options.subAppId?e.payer.sub_openid=e.payer.openid:e.payer.sp_openid=e.payer.openid,delete e.payer.openid));const o={Accept:"application/json","content-type":"application/json",Authorization:this._getAuthorization(r,t,e)},a=`${this._baseURL}${t}`;let u;if(i){const t=uniCloud.httpProxyForEip[r.toLowerCase()];if(!t)throw new Error(`使用固定IP时不支持${r}方式请求`);u=await t(a,JSON.stringify(e),o),u.data=u.body,u.status=u.statusCodeValue}else u=await uniCloud.httpclient.request(a,{method:r,data:e,headers:o,dataType:"json",timeout:this.options.timeout});let{status:c,data:f={},headers:l}=u;if("number"==typeof n&&c!==n||"object"==typeof n&&-1===n.indexOf(c))throw new s.UniCloudError({code:f.code,message:f.message});return f||(f={}),await this._verifyResponseSign(l,f),f.appid&&(f.appId=f.appid),f.mchid&&(f.mchId=f.mchid),(0,s.snake2camelJson)(f)}_publicParams(t){const{appId:e,mchId:r,subAppId:n,subMchId:i}=this.options;if(i){const o={sp_appid:e,sp_mchid:r,subAppid:n,subMchid:i};return Object.assign(o,t)}{const n={appid:e,mchid:r};return Object.assign(n,t)}}_getPayParamsByPrepayId(t,e){let r;switch(e){case"APP":r={appid:this.options.subAppId?this.options.subAppId:this.options.appId,partnerid:this.options.mchId,prepayid:t,package:"Sign=WXPay",noncestr:i.default.getNonceStr(),timestamp:""+(Date.now()/1e3|0)},r.sign=this._clientPaySign(r,e);break;case"JSAPI":default:{const n=""+(Date.now()/1e3|0);r={appId:this.options.subAppId?this.options.subAppId:this.options.appId,nonceStr:i.default.getNonceStr(),package:"prepay_id="+t,timeStamp:n},r.signType="RSA",r.paySign=this._clientPaySign(r,e),r.timestamp=n;break}}return r}_clientPaySign(t,e){const r=[t.appid||t.appId,t.timestamp||t.timeStamp,t.noncestr||t.nonceStr,"JSAPI"===e?t.package:t.prepayid].reduce((t,e)=>t+=e+"\n","");return i.default.rsaPrivateKeySign(this._privateKey.toPEM(),r).toString("base64")}async _getPlatformCert(t=""){if(t.indexOf("PUB_KEY_ID_")>-1){if(!this._wxpayPublicKey)throw new Error("missing wxpayPublicKeyPath or wxpayPublicKeyContent");if(this._wxpayPublicKeyId!==t)throw new Error("invalid wxpayPublicKeyId");return{certificate:{publicKey:this._wxpayPublicKey}}}if(this.platformCertificate.length<=0){var e,r;const t="/v3/certificates",{status:n,data:o={}}=await uniCloud.httpclient.request(`${this._baseURL}${t}`,{method:"GET",headers:{Accept:"application/json","content-type":"application/json",Authorization:this._getAuthorization("GET",t)},dataType:"json",timeout:this.options.timeout});if(200!==n)throw new Error("request fail："+o.message);this.platformCertificate=null!==(e=null===(r=o.data)||void 0===r?void 0:r.reduce((t,e)=>{if(e.encrypt_certificate)try{const{nonce:t,associated_data:r,ciphertext:n}=e.encrypt_certificate,o=i.default.decryptCiphertext(n,this.options.v3Key,t,r);e.certificate=i.default.loadCertFromContent(o)}catch(t){throw t.message&&"Unsupported state or unable to authenticate data"===t.message?new Error("v3Key错误，请配置正确的v3Key"):t}return t.push(e),t},[]))&&void 0!==e?e:[]}return this.platformCertificate=this.platformCertificate.filter(t=>new Date(t.expire_time).getTime()>Date.now()),this.platformCertificate.find(e=>e.serial_no===t)}async _verifyResponseSign(t,e={}){const{"wechatpay-timestamp":r,"wechatpay-nonce":n,"wechatpay-signature":o,"wechatpay-serial":s}=t,a=await this._getPlatformCert(s),u=[r,n,Object.keys(e).length?JSON.stringify(e):""].reduce((t,e)=>t+=e+"\n","");if(!i.default.rsaPublicKeyVerifySign(a.certificate.publicKey.toPEM(),u,o))throw new Error("response signature verification failed")}_downloadFile(t){const e=n.parse(t);return uniCloud.httpclient.request(t,{method:"GET",headers:{Accept:"application/json","content-type":"application/json",Authorization:this._getAuthorization("GET",e.path)},dataType:"text",timeout:this.options.timeout})}}{async getOrderInfo(t){if((t=this._publicParams(t)).sceneInfo.payerClientIp=t.sceneInfo.payerClientIp||"127.0.0.1","JSAPI"!==t.tradeType&&delete t.openid,!t.tradeType)throw new Error("tradeType required");const{tradeType:e,...r}=t,n=await this._request("/v3/pay/transactions/"+("MWEB"===t.tradeType?"h5":t.tradeType.toLowerCase()),r,"POST");if("NATIVE"===t.tradeType||"MWEB"===t.tradeType)return n;if(!n.prepayId)throw new Error(n.errMsg||"获取prepayId失败");return this._getPayParamsByPrepayId(n.prepayId,t.tradeType)}async orderQuery(t){var e;const{mchId:r,subMchId:n}=this.options,i=n?{sp_mchid:r,sub_mchid:n}:{mchid:r},o=await this._request(t.transactionId?"/v3/pay/transactions/id/"+t.transactionId:"/v3/pay/transactions/out-trade-no/"+t.outTradeNo,i);if(o.settlementTotalFee=0,(null===(e=o.promotion_detail)||void 0===e?void 0:e.length)>0){const t=o.promotion_detail.reduce((t,e)=>("NOCASH"===e.type&&(t+=e.amount),t),0);"object"==typeof o.amount&&(o.settlementTotalFeeres=o.amount.total-t)}return o.amount||(o.amount=0),o}async closeOrder(t){const{mchId:e,subMchId:r}=this.options,n=r?{sp_mchid:e,sub_mchid:r}:{mchid:e};return await this._request(`/v3/pay/transactions/out-trade-no/${t.outTradeNo}/close`,n,"POST",204)}async refund(t){const{subMchId:e}=this.options,r=e?{sub_mchid:e}:{};return t=Object.assign(r,t),await this._request("/v3/refund/domestic/refunds",t,"POST")}async refundQuery(t){const{subMchId:e}=this.options,r=e?{sub_mchid:e}:{};return await this._request("/v3/refund/domestic/refunds/"+t.outRefundNo,r)}async downloadBill(t){const{subMchId:e}=this.options,r=e?{sub_mchid:e}:{};return t=Object.assign(r,t),this._request("/v3/bill/tradebill",t).then(t=>this._downloadFile(t.downloadUrl)).then(t=>Promise.resolve({content:t.data}))}async codepay(t){return t=this._publicParams(t),await this._request("/v3/pay/transactions/codepay",t,"POST",[200,202])}async downloadFundflow(t){const{subMchId:e}=this.options,r=e?{sub_mchid:e}:{};return t=Object.assign(r,t),this._request("/v3/bill/fundflowbill",t).then(t=>this._downloadFile(t.downloadUrl)).then(t=>Promise.resolve({content:t.data}))}async checkNotifyType(t){const{headers:e}=t,r="string"==typeof t.body?JSON.parse(t.body):t.body;await this._verifyResponseSign(e,r);const{resource:n}=r;switch(null==n?void 0:n.original_type){case"transaction":default:return"payment";case"refund":return"refund"}}async verifyPaymentNotify(t){const{headers:e}=t,r="string"==typeof t.body?JSON.parse(t.body):t.body;await this._verifyResponseSign(e,r);const{resource:n}=r,o=i.default.decryptCiphertext(n.ciphertext,this.options.v3Key,n.nonce,n.associated_data);return(0,s.snake2camelJson)(JSON.parse(o))}async verifyRefundNotify(t){const{headers:e}=t,r="string"==typeof t.body?JSON.parse(t.body):t.body;await this._verifyResponseSign(e,r);const{resource:n}=r,o=i.default.decryptCiphertext(n.ciphertext,this.options.v3Key,n.nonce,n.associated_data);return(0,s.snake2camelJson)(JSON.parse(o))}async verifyTransferNotify(t){const{headers:e}=t,r="string"==typeof t.body?JSON.parse(t.body):t.body;await this._verifyResponseSign(e,r);const{resource:n}=r,o=i.default.decryptCiphertext(n.ciphertext,this.options.v3Key,n.nonce,n.associated_data);return(0,s.snake2camelJson)(JSON.parse(o))}async request(t){const{url:e,data:r={},method:n="GET",successStatusCode:i=200,useEipProxy:o=!1}=t;var s;try{s=JSON.parse(JSON.stringify(r))}catch(t){s=r}return await this._request(e,s,n,i,o)}};e.default=c,t.exports=e.default},function(t,e){t.exports=require("url")},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n},o=function(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var r=u(e);if(r&&r.has(t))return r.get(t);var n={},i=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in t)if("default"!==o&&Object.prototype.hasOwnProperty.call(t,o)){var s=i?Object.getOwnPropertyDescriptor(t,o):null;s&&(s.get||s.set)?Object.defineProperty(n,o,s):n[o]=t[o]}n.default=t,r&&r.set(t,n);return n}(r(8)),s=r(12),a=r(3);function u(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(u=function(t){return t?r:e})(t)}var c={decryptData:function(t,e,r=""){const n=i.default.createDecipheriv("aes-256-ecb",e,r);n.setAutoPadding(!0);let o=n.update(t,"base64","utf8");return o+=n.final("utf8"),o},md5:function(t,e="utf8"){return i.default.createHash("md5").update(t,e).digest("hex")},sha256:function(t,e,r="utf8"){return i.default.createHmac("sha256",e).update(t,r).digest("hex")},getQueryStr:function(t){return Object.keys(t).map(e=>e+"="+((0,a.isPlainObject)(t[e])?JSON.stringify(t[e]):t[e])).join("&")},getNonceStr:function(t=16){let e="";for(;e.length<t;)e+=Math.random().toString(32).substring(2);return e.substring(0,t)},decodeBase64:function(t){return Buffer.from(t,"base64").toString("utf-8")},decryptCiphertext:function(t,e,r,n){const o=Buffer.from(t,"base64"),s=i.default.createDecipheriv("aes-256-gcm",e,r);return s.setAuthTag(o.slice(-16)),s.setAAD(Buffer.from(n)),Buffer.concat([s.update(o.slice(0,-16)),s.final()]).toString()},loadCertFromPath:function(t){return s.Certificate.fromPEM(o.readFileSync(t))},loadCertFromContent:function(t){return"string"==typeof t&&(t=Buffer.from(t)),s.Certificate.fromPEM(t)},loadPrivateKeyFromPath:function(t){return s.PrivateKey.fromPEM(o.readFileSync(t))},loadPrivateKeyFromContent:function(t){return"string"==typeof t&&(t=Buffer.from(t)),s.PrivateKey.fromPEM(t)},loadPublicKeyFromPath:function(t){return s.PublicKey.fromPEM(o.readFileSync(t))},loadPublicKeyFromContent:function(t){return"string"==typeof t&&(t=Buffer.from(t)),s.PublicKey.fromPEM(t)},getFileBuffer:function(t){try{return o.readFileSync(t)}catch(t){return Buffer.alloc(0)}},rsaPrivateKeySign:function(t,e,r="base64"){return i.default.createSign("RSA-SHA256").update(Buffer.from(e),"utf8").sign(t)},rsaPublicKeyVerifySign:function(t,e,r){return i.default.createVerify("RSA-SHA256").update(e,"utf8").verify(t,r,"base64")},rsaPublicKeyEncryptData:function(t,e){return i.default.publicEncrypt({key:e,padding:i.default.constants.RSA_PKCS1_OAEP_PADDING},Buffer.from(t))},rsaPrivateKeyDecryptData:function(t,e){return"string"==typeof t&&(t=Buffer.from(t)),i.default.privateDecrypt({key:e,padding:i.default.constants.RSA_PKCS1_OAEP_PADDING},t).toString()},formatKey:function(t,e){return t.indexOf(`-----BEGIN ${e}-----`)>-1?t:`-----BEGIN ${e}-----\n${t}\n-----END ${e}-----`}};e.default=c,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default={getOrderInfo:{args:{_purify:{shouldDelete:["subject"]},"amount.total":"totalFee","payer.openid":"openid",description:"body","sceneInfo.payerClientIp":"spbillCreateIp"}},orderQuery:{returnValue:{_purify:{shouldDelete:["appid","mchid","sceneInfo","promotionDetail"]},totalFee:"amount.total",cashFee:"amount.payer_total"}},refund:{args:{"amount.total":"totalFee","amount.refund":"refundFee","amount.currency":"refundFeeType",reason:"refundDesc"},returnValue:{refundFee:"amount.refund",cashRefundFee:"amount.payer_refund"}},refundQuery:{args:{shouldDelete:["outTradeNo","transactionId","refundId"]},returnValue:{totalFee:"amount.total",refundFee:"amount.refund"}},downloadFundflow:{args:{accountType:t=>t.accountType.toUpperCase()}},codepay:{args:{_purify:{shouldDelete:["subject","tradeType","notifyUrl"]},"amount.total":"totalFee","payer.auth_code":"authCode",description:"body","scene_info.store_info.id":"store_id"}},verifyPaymentNotify:{returnValue:{totalFee:"amount.total",cashFee:"amount.payer_total",feeType:"amount.currency",timeEnd:"success_time",openid:"payer.openid",returnCode:"trade_state"}},verifyRefundNotify:{returnValue:{totalFee:"amount.total",refundFee:"amount.refund",settlementTotalFee:"amount.payer_total",settlementRefundFee:"amount.payer_refund",refundRecvAccout:"user_received_account"}}},t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(r(99)),i=a(r(100)),o=r(3),s=a(r(101));function a(t){return t&&t.__esModule?t:{default:t}}var u=class extends class{constructor(t){if(this.options={currencyType:"CNY",rate:100,timeout:1e4,...t},!t.appId)throw new Error("appId required");if(!t.mchId)throw new Error("mchId required");if(!t.sandbox&&!t.appKey)throw new Error("appKey required");if(t.sandbox&&!t.sandboxAppKey)throw new Error("sandboxAppKey required");if(!t.offerId)throw new Error("offerId required");if(!t.accessToken)throw new Error("accessToken required");if(!t.token)throw new Error("token required");if(!t.encodingAESKey)throw new Error("encodingAESKey required");this.appKey=t.sandbox?t.sandboxAppKey:t.appKey,this._protocols=i.default,this._baseURL="https://api.weixin.qq.com",this._wxCrypto=new s.default({appId:t.appId,encodingAESKey:t.encodingAESKey,token:t.token})}async _request(t,e,r="GET",i=200){e=this._publicParams(e),e=(0,o.camel2snakeJson)(e),e=JSON.parse(JSON.stringify(e)),this.options.debug&&console.log("params",e);const s=JSON.stringify(e),a=this.appKey,u=n.default.getPaySig(t,s,a),c=this.options.accessToken;let f=`${this._baseURL}${t}?access_token=${c}&pay_sig=${u}`;if(e.session_key){f+="&signature="+n.default.getSignature(s,e.session_key)}const{status:l,data:h={}}=await uniCloud.httpclient.request(f,{method:r,data:e,headers:{"content-type":"application/json"},dataType:"json",timeout:this.options.timeout});if(l!==i)throw new o.UniCloudError({code:h.code,message:h.message});if(h.errcode&&(h.errCode=h.errcode),h.errmsg&&(h.errMsg=h.errmsg),delete h.errcode,delete h.errmsg,h.errCode||(h.errCode=0),h.code=h.errCode,h.errMsg&&(h.msg=h.errMsg),h.errCode)throw new o.UniCloudError({code:h.errCode,message:h.errMsg});const d=(0,o.snake2camelJson)(h);return d.appId||(d.appId=this.options.appId),d.mchId||(d.mchId=this.options.mchId),this.options.debug&&console.log("res",d),d}_publicParams(t){const e={env:this.options.sandbox?1:0};return Object.assign(e,t)}async _verifyResponseSign(t){const{queryStringParameters:e={}}=t,{signature:r,timestamp:n,nonce:i}=e;return this._wxCrypto.verifyResponseSign({signature:r,timestamp:n,nonce:i})}async _decrypt(t={}){const{msgSignature:e,timestamp:r,nonce:n,body:i={}}=t,{Encrypt:o}=i,s=this._wxCrypto;if(!s.verifyMsgSign({timestamp:r,nonce:n,encrypt:o,msg_signature:e}))return null;if(!o)return i;const a=s.decrypt(o);return this.options.appId!==a.appId?null:a.value}}{async getOrderInfo(t){t=this._publicParams(t);const{sessionKey:e,mode:r,outTradeNo:i,buyQuantity:o,productId:s,goodsPrice:a,attach:u}=t;if(!r)throw new Error("mode required");if("short_series_goods"===r){if(!s)throw new Error("productId required");if(!a)throw new Error("goodsPrice required")}if(!e)throw new Error("sessionKey required");if(!i)throw new Error("outTradeNo required");if(!o)throw new Error("buyQuantity required");const c=JSON.stringify({offerId:this.options.offerId,env:this.options.sandbox?1:0,currencyType:this.options.currencyType,buyQuantity:o,outTradeNo:i,productId:s,goodsPrice:a,attach:u}),f=this.appKey;return{signData:c,mode:r,paySig:n.default.getPaySig("requestVirtualPayment",c,f),signature:n.default.getSignature(c,e)}}async orderQuery(t){return await this._request("/xpay/query_order",t,"POST")}async closeOrder(t){return{}}async refund(t){const e=await this._request("/xpay/refund_order",t,"POST");return"OK"===e.errMsg&&(e.refundFee=t.refundFee),e}async refundQuery(t){return await this._request("/xpay/query_order",t,"POST")}checkNotifyType(t){let{body:e={},queryStringParameters:r={}}=t;if(t.isBase64Encoded&&(e=Buffer.from(e,"base64").toString("utf-8")),e&&"string"==typeof e)try{e=JSON.parse(e)}catch(t){e={}}const{Event:n}=e;return!n&&r.echostr?"token":["xpay_coin_pay_notify","xpay_goods_deliver_notify"].indexOf(n)>-1?"payment":["xpay_refund_notify"].indexOf(n)>-1?"refund":"unknown"}async verifyTokenNotify(t){const{queryStringParameters:e={}}=t,{echostr:r}=e;if(!await this._verifyResponseSign(t))return null;return{appId:this.options.appId,mchId:this.options.mchId,echostr:r}}async _verifyNotify(t){let{body:e={}}=t;if(t.isBase64Encoded&&(e=Buffer.from(e,"base64").toString("utf-8")),"string"==typeof e)try{e=JSON.parse(e)}catch(t){}if(!await this._verifyResponseSign(t))return null;const{queryStringParameters:r={}}=t,{msg_signature:n,timestamp:i,nonce:s}=r,a=this._decrypt({msgSignature:n,timestamp:i,nonce:s,body:e}),u=(0,o.snake2camelJson)(a);return u.appId=this.options.appId,u.mchId=this.options.mchId,u}async verifyPaymentNotify(t){return"payment"===this.checkNotifyType(t)&&await this._verifyNotify(t)}async verifyRefundNotify(t){return"refund"===this.checkNotifyType(t)&&await this._verifyNotify(t)}async notifyProvideGoods(t){return await this._request("/xpay/notify_provide_goods",t,"POST")}async queryUserBalance(t){if(!t.openid)throw new Error("参数 openid 必填");if(!t.userIp)throw new Error("参数 userIp 必填");return await this._request("/xpay/query_user_balance",t,"POST")}async currencyPay(t){if(!t.sessionKey)throw new Error("接口 currencyPay 的参数 sessionKey 必填");if(!t.openid)throw new Error("参数 openid 必填");if(!t.userIp)throw new Error("参数 userIp 必填");if(!t.amount)throw new Error("参数 amount 必填");if(!t.orderId)throw new Error("参数 outTradeNo 必填");if(!t.deviceType)throw new Error("参数 deviceType 必填");return await this._request("/xpay/currency_pay",t,"POST")}async cancelCurrencyPay(t){if(!t.openid)throw new Error("参数 openid 必填");if(!t.userIp)throw new Error("参数 userIp 必填");if(!t.amount)throw new Error("参数 amount 必填");if(!t.payOrderId)throw new Error("参数 outTradeNo 必填");if(!t.orderId)throw new Error("参数 outRefundNo 必填");if(!t.deviceType)throw new Error("参数 deviceType 必填");return await this._request("/xpay/cancel_currency_pay",t,"POST")}async presentCurrency(t){if(!t.openid)throw new Error("参数 openid 必填");if(!t.userIp)throw new Error("参数 userIp 必填");if(!t.orderId)throw new Error("参数 outTradeNo 必填");if(!t.amount)throw new Error("参数 amount 必填");if(!t.deviceType)throw new Error("参数 deviceType 必填");return await this._request("/xpay/present_currency",t,"POST")}};e.default=u,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n};function o(t,e,r="utf8"){return i.default.createHmac("sha256",e).update(t,r).digest("hex")}var s={sha256:o,getPaySig:function(t,e,r){return o(t+"&"+e,r)},getSignature:function(t,e){return o(t,e)}};e.default=s,t.exports=e.default},function(t,e,r){"use strict";function n(t,e="Asia/Shanghai"){const r=function(t){const e=(new Date).getTimezoneOffset();return"UTC"===t?0:-e}(e),n=new Date(t+60*r*1e3);return`${n.getUTCFullYear()}${(n.getUTCMonth()+1).toString().padStart(2,"0")}${n.getUTCDate().toString().padStart(2,"0")}${n.getUTCHours().toString().padStart(2,"0")}${n.getUTCMinutes().toString().padStart(2,"0")}${n.getUTCSeconds().toString().padStart(2,"0")}`}function i(t){const e={};return Object.keys(t).forEach(r=>{const n=r.charAt(0).toLowerCase()+r.slice(1);e[n]=t[r]}),e}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o={getOrderInfo:{args:{_purify:{shouldDelete:["subject","tradeType"]}}},orderQuery:{args:{orderId:"outTradeNo",wxOrderId:"transactionId"},returnValue:{_pre(t){const e=JSON.parse(JSON.stringify(t.order));let r="",n="";[0,1].indexOf(e.status)>-1?(r="NOTPAY",n="未支付"):[6].indexOf(e.status)>-1?(r="PAYERROR",n="支付失败"):[2,3,4].indexOf(e.status)>-1?(r="SUCCESS",n="支付成功"):[5,8,9,10].indexOf(e.status)>-1?(r="REFUND",n="订单发生过退款"):[7].indexOf(e.status)>-1?(r="REFUNDRROR",n="订单退款失败"):(r="NOTPAY",n="未支付");let i={outTradeNo:e.order_id,transactionId:e.wx_order_id,totalFee:e.order_fee,cashFee:e.paid_fee,leftFee:e.left_fee,couponFee:e.coupon_fee,tradeState:r,tradeStateDesc:n,refundFee:e.refund_fee,originalResult:e};return i=JSON.parse(JSON.stringify(i)),i}}},refund:{args:{_purify:{shouldDelete:["totalFee","refundFeeType","refundDesc"]},orderId:"outTradeNo",wxOrderId:"transactionId",refundOrderId:"outRefundNo",refundFee:"refundFee"},returnValue:{}},refundQuery:{args:{orderId:"outRefundNo",wxOrderId:"transactionId"},returnValue:{_pre(t){const e=JSON.parse(JSON.stringify(t.order));let r="",n="";[0,1].indexOf(e.status)>-1?(r="NOTPAY",n="未支付"):[6].indexOf(e.status)>-1?(r="PAYERROR",n="支付失败"):[2,3,4].indexOf(e.status)>-1?(r="SUCCESS",n="支付成功"):[5,8,9,10].indexOf(e.status)>-1?(r="REFUND",n="订单发生过退款"):[7].indexOf(e.status)>-1?(r="REFUNDRROR",n="订单退款失败"):(r="NOTPAY",n="未支付");let i={outTradeNo:e.order_id,transactionId:e.wx_order_id,totalFee:e.order_fee,cashFee:e.paid_fee,couponFee:e.coupon_fee,tradeState:r,tradeStateDesc:n,refundFee:e.refund_fee,originalResult:e};return i=JSON.parse(JSON.stringify(i)),i}}},verifyPaymentNotify:{returnValue:{_pre(t){const e=t.OutTradeNo,r=t.WeChatPayInfo?1e3*t.WeChatPayInfo.PaidTime:Date.now(),o=t.WeChatPayInfo?t.WeChatPayInfo.MchOrderNo:void 0,s=t.OpenId,a=t.appId,u=t.Env;return"xpay_goods_deliver_notify"===t.Event?{outTradeNo:e,transactionId:o,tradeState:"SUCCESS",openid:s,appId:a,totalFee:t.GoodsInfo.Quantity*t.GoodsInfo.OrigPrice,cashFee:t.GoodsInfo.Quantity*t.GoodsInfo.ActualPrice,attach:t.GoodsInfo.Attach,timeEnd:n(r),goodsInfo:i(t.GoodsInfo),weChatPayInfo:i(t.WeChatPayInfo),env:u}:"xpay_coin_pay_notify"===t.Event?{outTradeNo:e,transactionId:o,tradeState:"SUCCESS",openid:s,appId:a,totalFee:t.CoinInfo.Quantity*t.CoinInfo.OrigPrice,cashFee:t.CoinInfo.Quantity*t.CoinInfo.ActualPrice,attach:t.CoinInfo.Attach,timeEnd:n(r),coinInfo:i(t.CoinInfo),weChatPayInfo:i(t.WeChatPayInfo),env:u}:void 0}}},verifyRefundNotify:{returnValue:{_pre:t=>({outTradeNo:t.MchOrderId,transactionId:t.WxpayRefundTransactionId,openid:t.OpenId,appId:t.appId,refundFee:t.RefundFee,outRefundNo:t.MchRefundId,refundStatus:0===t.RetCode?"SUCCESS":"CHANGE",retMsg:t.RetMsg,retryCount:t.RetryTimes})}},notifyProvideGoods:{args:{orderId:"outTradeNo",wxOrderId:"transactionId"},returnValue:{}},currencyPay:{args:{orderId:"outTradeNo"},returnValue:{outTradeNo:"orderId"}},cancelCurrencyPay:{args:{payOrderId:"outTradeNo",orderId:"outRefundNo"},returnValue:{outRefundNo:"orderId"}},presentCurrency:{args:{orderId:"outTradeNo"},returnValue:{outTradeNo:"orderId"}}};e.default=o,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n};var o=class{constructor(t={}){const{appId:e,encodingAESKey:r,token:n}=t,i=Buffer.from(r+"=","base64"),o=i.slice(0,16);this.data={appId:e,token:n,key:i,iv:o}}encrypt(t){"string"!=typeof t&&(t=JSON.stringify(t));const{appId:e,key:r,iv:n}=this.data,o=i.default.randomBytes(16),s=Buffer.alloc(4);s.writeUInt32BE(Buffer.byteLength(t),0);const a=Buffer.from(t),u=Buffer.from(e);let c=Buffer.concat([o,s,a,u]);const f=i.default.createCipheriv("aes-256-cbc",r,n);f.setAutoPadding(!1),c=this.PKCS7Encode(c);return Buffer.concat([f.update(c),f.final()]).toString("base64")}decrypt(t){const{key:e,iv:r}=this.data,n=Buffer.from(t,"base64"),o=i.default.createDecipheriv("aes-256-cbc",e,r);o.setAutoPadding(!1);let s=Buffer.concat([o.update(n),o.final()]);s=this.PKCS7Decode(s);const a=20+s.readUInt32BE(16),u=s.slice(20,a),c=s.slice(a),f=u.toString(),l=c.toString();let h;try{h=JSON.parse(f)}catch(t){h=f}return{value:h,text:f,appId:l}}getMsgSign(t){const{token:e}=this.data,{timestamp:r,nonce:n,encrypt:o}=t,s=[e,r,n,o].sort().join("");return i.default.createHash("sha1").update(s).digest("hex")}verifyMsgSign(t){return this.getMsgSign(t)===t.msg_signature}getVerifyResponseSign(t){const{token:e}=this.data,{timestamp:r,nonce:n}=t,o=[e,r,n];o.sort((t,e)=>t.localeCompare(e));const s=o.join("");return i.default.createHash("sha1").update(s).digest("hex")}verifyResponseSign(t){return this.getVerifyResponseSign(t)===t.signature}PKCS7Decode(t){const e=t[t.length-1];return t.slice(0,t.length-e)}PKCS7Encode(t){const e=32-t.length%32,r=e,n=Buffer.alloc(e,r);return Buffer.concat([t,n])}};e.default=o,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=r(3),o=(n=r(103))&&n.__esModule?n:{default:n};const s=["app-harmony","mp-harmony"];var a=class extends class{constructor(t){if(this.options=t,this._protocols=o.default,!t.appId)throw new Error("appId is required");if(!t.mchId)throw new Error("mchId is required");if(!t.mchAuthId)throw new Error("mchAuthId is required");if(!t.mchPrivateKey)throw new Error("mchPrivateKey is required");if(!s.includes(t.clientType))throw new Error(`clientType ${t.clientType} not supported`);this._mchPrivateKey=(0,i.loadPrivateKeyFromContent)(t.mchPrivateKey),this._isApp="app-harmony"===t.clientType}_sign(t={},e){const r=(0,i.getQueryStr)(t,!0);return!r&&e?(0,i.signSha256WithRsaPSS)(this._mchPrivateKey.toPEM(),e):(0,i.signSha256WithRsaPSS)(this._mchPrivateKey.toPEM(),e?`${e}?${r}`:r)}async _request({action:t,data:e,method:r="GET",headers:n={}}){const o="https://petalpay-developer.cloud.huawei.com.cn"+t,s=Object.assign({},{"content-type":"application/json"},n),a={callerId:this.options.mchId,traceId:(0,i.getNonceStr)(32),time:Date.now(),authId:this.options.mchAuthId,bodySign:this._sign(e,"GET"===r?t:null)};a.headerSign=this._sign(a),s.PayMercAuth=JSON.stringify(a);const u=await uniCloud.httpclient.request(o,{method:r,data:e,headers:s,dataType:"json",timeout:this.options.timeout||1e4}),{data:c,status:f}=u;var l;if(200!==f)throw new i.UniCloudError({code:-1,message:`${(null===(l=u.res)||void 0===l?void 0:l.message)||"请求异常"}(${f})`});if("000000"!==c.resultCode)throw new i.UniCloudError({code:c.resultCode,message:c.resultDesc+(c.subDesc?": "+c.subDesc:"")});return(0,i.snake2camelJson)(c)}_getPayParamsByPrepayId(t){const e={app_id:this.options.appId,merc_no:this.options.mchId,auth_id:this.options.mchAuthId,prepay_id:t,noncestr:(0,i.getNonceStr)(),timestamp:String(Date.now())};return e.sign=this._sign(e),JSON.stringify(e)}_verifyResponseSign(t){if(!this.options.platformPublicKey)return!0;const{sign:e,...r}=t,n=(0,i.getQueryStr)(r,!0);if(!(0,i.sm2VerifySign)(n,e,this.options.platformPublicKey))throw new Error("response signature verification failed")}}{async getOrderInfo(t){const{tradeType:e,...r}=t,{prepayId:n}=await this._request({action:"/api/v2/aggr/preorder/create/"+(this._isApp?"app":"fa"),method:"POST",data:{appId:this.options.appId,mercNo:this.options.mchId,...r}});return this._getPayParamsByPrepayId(n)}async orderQuery(t){return this._request({action:t.sysTransOrderNo?"/api/v2/aggr/transactions/orders/"+t.sysTransOrderNo:"/api/v2/aggr/transactions/merc-orders/"+t.mercOrderNo,method:"GET"})}async closeOrder(){throw new Error("not supported")}async cancelOrder(){throw new Error("not supported")}async refund(t){return this._request({action:"/api/v1/aggr/transactions/refunds",method:"POST",data:t})}async refundQuery(t){return this._request({action:t.sysRefundOrderNo?"/api/v1/aggr/transactions/refunds/orders/"+t.sysRefundOrderNo:"/api/v1/aggr/transactions/refunds/merc-orders/"+t.mercRefundOrderNo,method:"GET"})}async checkNotifyType(t){const e="string"==typeof t.body?JSON.parse(t.body):t.body;this._verifyResponseSign(e);const{sysTransOrderNo:r,mercOrderNo:n}=e;return r&&n?"payment":"refund"}async verifyPaymentNotify(t){const e="string"==typeof t.body?JSON.parse(t.body):t.body;return"payment"===await this.checkNotifyType(t)&&e}async verifyRefundNotify(t){const e="string"==typeof t.body?JSON.parse(t.body):t.body;return"refund"===await this.checkNotifyType(t)&&e}returnNotifySuccess(){return{resultCode:"000000",resultDesc:"Success."}}async request(t){const{url:e,data:r={},method:n="GET",headers:i}=t;return this._request({action:e,method:n,data:r,headers:i})}};e.default=a,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;const n=["resultCode","resultDesc","sign"];var i={getOrderInfo:{args:{_purify:{shouldDelete:["body","openid"]},_post:t=>(t.payer&&!t.payer.userClientIp&&delete t.payer,t),mercOrderNo:"outTradeNo",tradeSummary:"subject",totalAmount:"totalFee",callbackUrl:"notifyUrl","payer.userClientIp":"spbillCreateIp"}},orderQuery:{args:{sysTransOrderNo:"transactionId",mercOrderNo:"outTradeNo"},returnValue:{_purify:{shouldDelete:[...n,"orderStatus"]},appId:"appId",mchId:"mercNo",outTradeNo:"mercOrderNo",transactionId:"sysTransOrderNo",totalFee:"totalAmount",cashFee:"payerAmount",tradeState:({orderStatus:t})=>{switch(t){case"TRX_SUCCESS":return"SUCCESS";case"TRX_FAILED":return"FAIL";case"TRX_APPLY":case"TRX_PROC":return"PROCESSING"}}}},refund:{args:{_purify:{shouldDelete:["totalFee","refundFeeType"]},sysTransOrderNo:"transactionId",mercOrderNo:"outTradeNo",mercRefundOrderNo:"outRefundNo",refundAmount:"refundFee",reason:"refundDesc",callbackUrl:"notifyUrl"},returnValue:{_purify:{shouldDelete:[...n,"payerRefundAmount"]},outTradeNo:"mercOrderNo",transactionId:"sysTransOrderNo",outRefundNo:"mercRefundOrderNo",refundId:"sysRefundOrderNo",refundFee:"refundAmount"}},refundQuery:{args:{_purify:{shouldDelete:["outTradeNo","transactionId","offset"]},mercRefundOrderNo:"outRefundNo",sysRefundOrderNo:"refundId"},returnValue:{_purify:{shouldDelete:[...n,"sysRefundOrderNo","mercRefundOrderNo","refundOrderStatus","finishTime","promotionRefundAmount","payerRefundAmount"]},outTradeNo:"mercOrderNo",transactionId:"sysTransOrderNo",totalFee:"totalFee",refundId:"sysRefundOrderNo",refundFee:"refundAmount",refundStatus:({refundOrderStatus:t})=>{switch(t){case"REFUND_SUCCESS":return"SUCCESS";case"REFUND_FAILED":return"FAIL";case"REFUND_CHL_PROC":return"PROCESSING"}}}},verifyPaymentNotify:{returnValue:{_purify:{shouldDelete:["payload","promotionAmount","paymentTools","promotionDetail"]},outTradeNo:"mercOrderNo",transactionId:"sysTransOrderNo",totalFee:"totalAmount",cashFee:"payerAmount",feeType:"currency",timeEnd:"finishTime",openid:"payer.openId",returnCode:({orderStatus:t})=>{switch(t){case"TRX_SUCCESS":return"SUCCESS";case"TRX_FAILED":return"FAIL"}}}},verifyRefundNotify:{returnValue:{_purify:{shouldDelete:["promotionRefundAmount","currency","mercNo","finishTime","payload"]},refundId:"sysRefundNo",outRefundNo:"mercRefundNo",refundFee:"refundAmount",settlementRefundFee:"payerRefundAmount",refundStatus:({refundOrderStatus:t})=>{switch(t){case"REFUND_SUCCESS":return"SUCCESS";case"REFUND_FAILED":return"FAIL";case"REFUND_CHL_PROC":return"PROCESSING"}}}}};e.default=i,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=a(r(105)),i=a(r(106)),o=r(3),s=a(r(107));function a(t){return t&&t.__esModule?t:{default:t}}var u=class extends class{constructor(t){if(this.options={timeout:1e4,...t},!t.appId)throw new Error("appId required");if(!t.mchId)throw new Error("mchId required");if(!t.salt)throw new Error("salt required");if(!t.token)throw new Error("token required");this._protocols=i.default,this._baseURL=t.sandbox?"https://open-sandbox.douyin.com":"https://developer.toutiao.com",this._crypto=new s.default(t)}async _request(t,e={},r="GET",n=200){e=this._publicParams(e),e=(0,o.camel2snakeJson)(e),e=JSON.parse(JSON.stringify(e)),this.options.debug&&console.log("params",e);const i=this._crypto.getSign(e);e.sign=i;let s="";0===t.indexOf("http")?s=t:(0!==t.indexOf("/")&&(t="/"+t),s=`${this._baseURL}${t}`);const{status:a,data:u={}}=await uniCloud.httpclient.request(s,{method:r,data:e,headers:{"content-type":"application/json"},dataType:"json",timeout:this.options.timeout}),c=u.err_no||0,f=u.err_tips;if(delete u.err_no,delete u.err_tips,u.errCode=c,u.errMsg=f,u.code=c,u.msg=f,a!==n)throw new o.UniCloudError({code:c||n,message:f});if(u.errCode)throw new o.UniCloudError({code:c,message:f});const l=(0,o.snake2camelJson)(u);return l.appId||(l.appId=this.options.appId),l.mchId||(l.mchId=this.options.mchId),this.options.debug&&console.log("res",l),l}_publicParams(t){const e={app_id:this.options.appId};return Object.assign(e,t)}async _verifyResponseSign(t){const e=n.default.getBody(t),{timestamp:r,nonce:i,msg:o,type:s,msg_signature:a}=e;return this._crypto.verifyResponseSign({timestamp:r,nonce:i,msg:o,type:s,signature:a})}async _verifyNotify(t){const e=n.default.getBody(t);if(!await this._verifyResponseSign(t))return null;const r=(0,o.snake2camelJson)(e);return r.appId=this.options.appId,r.mchId=this.options.mchId,r}}{async getOrderInfo(t){return t=Object.assign({valid_time:900},t),await this._request("/api/apps/ecpay/v1/create_order",t,"POST")}async orderQuery(t){return await this._request("/api/apps/ecpay/v1/query_order",t,"POST")}async closeOrder(t){return{}}async refund(t){const e=await this._request("/api/apps/ecpay/v1/create_refund",t,"POST");return"success"===e.errMsg&&(e.refundFee=t.refundAmount),e}async refundQuery(t){return await this._request("/api/apps/ecpay/v1/query_refund",t,"POST")}checkNotifyType(t){const e=n.default.getBody(t),{type:r}=e;return["payment"].indexOf(r)>-1?"payment":["refund"].indexOf(r)>-1?"refund":["settle"].indexOf(r)>-1?"settle":["create_merchant"].indexOf(r)>-1?"create_merchant":["withdraw"].indexOf(r)>-1?"withdraw":"unknown"}async verifyPaymentNotify(t){return"payment"===this.checkNotifyType(t)&&await this._verifyNotify(t)}async verifyRefundNotify(t){return"refund"===this.checkNotifyType(t)&&await this._verifyNotify(t)}returnNotifySUCCESS(){return{err_no:0,err_tips:"success"}}async queryAccountBalance(t={}){return(t=(0,o.snake2camelJson)(t)).merchantUid||(t.merchantUid=this.options.mchId),t.channelType||(t.channelType="yzt"),"yzt"===t.channelType&&(t.merchantEntity=2),await this._request("/api/apps/ecpay/saas/query_merchant_balance",t,"POST")}async request(t){const{url:e,action:r,data:n,method:i}=t,o=r||e;return await this._request(o,n,i)}};e.default=u,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n={getBody:function(t){let{body:e={},isBase64Encoded:r}=t;if(r&&(e=Buffer.from(e,"base64").toString("utf-8")),e&&"string"==typeof e)try{e=JSON.parse(e)}catch(t){e={}}return e}};e.default=n,t.exports=e.default},function(t,e,r){"use strict";function n(t,e="Asia/Shanghai"){const r=function(t){const e=(new Date).getTimezoneOffset();return"UTC"===t?0:-e}(e),n=new Date(t+60*r*1e3);return`${n.getUTCFullYear()}${(n.getUTCMonth()+1).toString().padStart(2,"0")}${n.getUTCDate().toString().padStart(2,"0")}${n.getUTCHours().toString().padStart(2,"0")}${n.getUTCMinutes().toString().padStart(2,"0")}${n.getUTCSeconds().toString().padStart(2,"0")}`}function i(t){const e={1:"wxpay",2:"alipay",10:"douyin"};return e[t]||e[String(t)]}function o(t){return parseFloat((t/100).toFixed(2))}Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s={getOrderInfo:{args:{_purify:{shouldDelete:["tradeType","openid","spbillCreateIp"]},outOrderNo:"outTradeNo",totalAmount:"totalFee"},returnValue:{_pre:t=>t.data}},orderQuery:{args:{_purify:{shouldDelete:["transactionId"]},outOrderNo:"outTradeNo"},returnValue:{_pre(t){const e=JSON.parse(JSON.stringify(t)),{paymentInfo:r}=e;let n="",o="";const s=r.orderStatus;["FAIL"].indexOf(s)>-1?(n="PAYERROR",o="支付失败"):["SUCCESS"].indexOf(s)>-1?(n="SUCCESS",o="支付成功"):["PROCESSING"].indexOf(s)>-1?(n="PROCESSING",o="处理中"):(n="NOTPAY",o="未支付");let a={outTradeNo:e.outOrderNo,transactionId:e.orderId,totalFee:r.totalFee,cashFee:r.totalFee,mchInfo:r.sellerUid,tradeState:n,tradeStateDesc:o,payMethod:i(r.way),channelNo:r.channelNo,originalResult:e};return a=JSON.parse(JSON.stringify(a)),a}}},refund:{args:{_purify:{shouldDelete:["transactionId","totalFee","refundFeeType"]},outOrderNo:"outTradeNo",outRefundNo:"outRefundNo",reason:"refundDesc",refundAmount:"refundFee",refundFee:"refundFee"}},refundQuery:{args:{outRefundNo:"outRefundNo"},returnValue:{_pre(t){const e=JSON.parse(JSON.stringify(t.refundInfo));let r="",n="";const i=e.refund_status;["SUCCESS"].indexOf(i)>-1?(r="REFUND",n="退款成功"):["PROCESSING"].indexOf(i)>-1?(r="PROCESSING",n="处理中"):(r="FAIL",n="退款失败");let o={refundId:e.refund_no,tradeState:r,tradeStateDesc:n,refundFee:e.refund_amount,originalResult:e};return o=JSON.parse(JSON.stringify(o)),o}}},verifyPaymentNotify:{returnValue:{_pre(t){const e="string"==typeof t.msg?JSON.parse(t.msg):t.msg,r=e.cp_orderno,o=e.paid_at,s=e.order_id,a=e.appid,u=i(e.way);return{outTradeNo:r,transactionId:s,tradeState:"SUCCESS",appId:a,totalFee:e.total_amount,cashFee:e.total_amount,attach:e.cp_extra,timeEnd:n(o),payMethod:u,channelNo:e.channel_no,paymentOrderNo:e.payment_order_no,itemId:e.item_id}}}},verifyRefundNotify:{returnValue:{_pre(t){const e=t.msg,r=e.cp_refundno;return{transactionId:e.order_id,appId:e.appid,refundFee:e.refund_amount,outRefundNo:r,attach:e.cp_extra,refundStatus:"SUCCESS"===e.status?"SUCCESS":"CHANGE",refundStatusDesc:e.message}}}},queryAccountBalance:{returnValue:{_pre(t){const e=JSON.parse(JSON.stringify(t)),r=e.accountInfo,n=o(r.onlineBalance),i=o(r.withdrawableBalacne),s=o(r.freezeBalance);return{code:0,onlineAmount:n,withdrawableAmount:i,freezeAmount:s,msg:`在途余额：${n} 元\n可提现余额：${i} 元\n冻结余额：${s} 元`,originalResult:e}}}}};e.default=s,t.exports=e.default},function(t,e,r){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n,i=(n=r(2))&&n.__esModule?n:{default:n};var o=class{constructor(t={}){const{salt:e,token:r}=t;this.options={salt:e,token:r}}getSignStr(t){const e=[];for(const r in t)if(Object.prototype.hasOwnProperty.call(t,r)&&-1===["sign","app_id","thirdparty_id","prod_id"].indexOf(r)){const n=t[r];""!==n&&void 0!==n&&e.push(n)}const{salt:r}=this.options;e.push(r);return e.sort().join("&")}getSign(t){const e=this.getSignStr(t);return i.default.createHash("md5").update(e).digest("hex")}getVerifyResponseSign(t){const{timestamp:e,nonce:n,msg:i}=t,{token:o}=this.options,s=[o,e,n,i].sort().join("");return r(2).createHash("sha1").update(s).digest("hex")}verifyResponseSign(t){return this.getVerifyResponseSign(t)===t.signature}};e.default=o,t.exports=e.default},function(t,e,r){"use strict";t.exports={VIRTUAL:"100001",GOODS:"100002",PREPAID_ACCOUNT:"100003",TRAVEL:"100004",TICKET:"100005",BUSINESS:"100006",LIFE:"100007",RENT:"100008",MEMBERSHIP:"100009",OTHER_BUSINESS:"100011",PUBLIC:"100037"}}]));