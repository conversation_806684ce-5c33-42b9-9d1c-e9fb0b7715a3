const formRules = require("../../util/formRules.js");
'use strict';
module.exports = {
	/**
	 * XXXnameXXX
	 * @url client/club/site/kh/add 前端调用的url参数地址
	 * data 请求参数
	 * @param {String} params1  参数1
	 */
	main: async (event) => {
		let { data = {}, userInfo, util, filterResponse, originalParam } = event;
		let { customUtil, uniID, config, pubFun, vk, db, _ } = util;
		let { uid, images, name, club_id, tags, location, address, address_name } = data;
		let formRulesRes = await formRules.site_add(event);
		if (formRulesRes.code !== 0) { return formRulesRes; }
		let res = {
			code: 0,
			msg: ""
		}; // 业务逻辑开始-----------------------------------------------------------
		if (!location.type || location.type !== 'Point') location = new db.Geo.Point(location.longitude, location.latitude)
		let id = await vk.baseDao.add({
			dbName: "club-site-data",
			dataJson: {
				images,
				name,
				club_id,
				location,
				tags,
				address,
				address_name,
			}
		});
		res.msg = "新增成功"
		// 业务逻辑结束-----------------------------------------------------------
		return res;
	}
}